# CONFIG_INPUT is not set
# CONFIG_WIFI is not set
# CONFIG_LV_COLOR_16_SWAP is not set
CONFIG_LV_DPI_DEF=130
CONFIG_ADC_INIT_PRIORITY=50
# CONFIG_MIPI_DSI is not set
# CONFIG_MODEM is not set
# CONFIG_UART_INTERRUPT_DRIVEN is not set
# CONFIG_SPI is not set
CONFIG_BT_HCI_ACL_FLOW_CONTROL=y
CONFIG_BT_HCI_VS_EXT=y
CONFIG_BOARD="nrf5340dk_nrf5340_cpuapp"
CONFIG_FLASH_LOAD_SIZE=0x30000
CONFIG_SRAM_SIZE=192
CONFIG_FLASH_LOAD_OFFSET=0x50000
CONFIG_MBOX_NRFX_IPC=y
CONFIG_HEAP_MEM_POOL_SIZE=4096
# CONFIG_BT_CTLR is not set
CONFIG_NUM_IRQS=69
CONFIG_SOC_SERIES="nrf53"
CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC=32768
CONFIG_SOC="nRF5340_CPUAPP_QKAA"
CONFIG_CLOCK_CONTROL_INIT_PRIORITY=30
CONFIG_FLASH_SIZE=1024
CONFIG_FLASH_BASE_ADDRESS=0x0
CONFIG_ICACHE_LINE_SIZE=32
CONFIG_DCACHE_LINE_SIZE=32
CONFIG_ROM_START_OFFSET=0
CONFIG_PINCTRL=y
CONFIG_CLOCK_CONTROL=y
# CONFIG_WATCHDOG is not set
# CONFIG_RESET is not set
CONFIG_GPIO=y
CONFIG_SOC_HAS_TIMING_FUNCTIONS=y
CONFIG_ARCH_HAS_CUSTOM_BUSY_WAIT=y
# CONFIG_PM_DEVICE is not set
CONFIG_IPC_SERVICE_BACKEND_RPMSG_SHMEM_RESET=y
CONFIG_LOG_DOMAIN_NAME=""
CONFIG_NRF_RTC_TIMER=y
CONFIG_SYS_CLOCK_TICKS_PER_SEC=32768
CONFIG_BUILD_OUTPUT_HEX=y
CONFIG_SERIAL_INIT_PRIORITY=55
# CONFIG_FPU is not set
# CONFIG_MBEDTLS is not set
# CONFIG_MEMC is not set
# CONFIG_CODE_DATA_RELOCATION is not set
# CONFIG_CPU_HAS_CUSTOM_FIXED_SOC_MPU_REGIONS is not set
CONFIG_TINYCRYPT=y
CONFIG_SERIAL=y
CONFIG_MAIN_STACK_SIZE=1024
# CONFIG_SRAM_VECTOR_TABLE is not set
# CONFIG_USE_DT_CODE_PARTITION is not set
CONFIG_PLATFORM_SPECIFIC_INIT=y
CONFIG_IDLE_STACK_SIZE=320
# CONFIG_BT_USER_PHY_UPDATE is not set
CONFIG_BUILD_OUTPUT_BIN=y
CONFIG_MP_MAX_NUM_CPUS=1
CONFIG_HAS_DTS=y

#
# Devicetree Info
#
CONFIG_DT_HAS_ARDUINO_UNO_ADC_ENABLED=y
CONFIG_DT_HAS_ARDUINO_HEADER_R3_ENABLED=y
CONFIG_DT_HAS_ARM_ARMV8M_MPU_ENABLED=y
CONFIG_DT_HAS_ARM_CORTEX_M33F_ENABLED=y
CONFIG_DT_HAS_ARM_V8M_NVIC_ENABLED=y
CONFIG_DT_HAS_FIXED_PARTITIONS_ENABLED=y
CONFIG_DT_HAS_GPIO_KEYS_ENABLED=y
CONFIG_DT_HAS_GPIO_LEDS_ENABLED=y
CONFIG_DT_HAS_MMIO_SRAM_ENABLED=y
CONFIG_DT_HAS_NORDIC_MBOX_NRF_IPC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_CLOCK_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_CTRLAPPERI_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_DCNF_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_DPPIC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_EGU_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_GPIO_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_GPIO_FORWARDER_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_GPIOTE_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_GPREGRET_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_IEEE802154_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_IPC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_KMU_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_MUTEX_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_NFCT_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_OSCILLATORS_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_PINCTRL_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_POWER_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_PWM_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_QSPI_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_REGULATORS_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_RESET_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_SAADC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_SPIM_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_TWIM_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_UARTE_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_USBD_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_USBREG_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_VMC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_WDT_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF53_FLASH_CONTROLLER_ENABLED=y
CONFIG_DT_HAS_NORDIC_QSPI_NOR_ENABLED=y
CONFIG_DT_HAS_PWM_LEDS_ENABLED=y
CONFIG_DT_HAS_SOC_NV_FLASH_ENABLED=y
CONFIG_DT_HAS_ZEPHYR_BT_HCI_ENTROPY_ENABLED=y
CONFIG_DT_HAS_ZEPHYR_IPC_OPENAMP_STATIC_VRINGS_ENABLED=y
CONFIG_DT_HAS_ZEPHYR_PSA_CRYPTO_RNG_ENABLED=y
# end of Devicetree Info

#
# Modules
#

#
# Available modules.
#

#
# nrf (C:/ncs/v2.5.99-dev1/nrf)
#
CONFIG_NUM_METAIRQ_PRIORITIES=0
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=2048
CONFIG_LOG_BUFFER_SIZE=1024
# CONFIG_INIT_STACKS is not set
# CONFIG_MBEDTLS_CIPHER_MODE_CBC is not set
# CONFIG_MBEDTLS_CIPHER_MODE_CTR is not set
CONFIG_MBEDTLS_CIPHER_MODE_CFB=y
CONFIG_MBEDTLS_CIPHER_MODE_OFB=y
# CONFIG_MBEDTLS_CHACHA20_C is not set
# CONFIG_MBEDTLS_POLY1305_C is not set
# CONFIG_MBEDTLS_DHM_C is not set
# CONFIG_MBEDTLS_RSA_C is not set
CONFIG_MBEDTLS_SHA512_C=y
# CONFIG_MBEDTLS_GCM_C is not set
# CONFIG_MBEDTLS_CIPHER_MODE_XTS is not set
# CONFIG_MBEDTLS_SHA1_C is not set
CONFIG_MBEDTLS_MD_C=y
# CONFIG_MBEDTLS_TLS_LIBRARY is not set
# CONFIG_MBEDTLS_X509_LIBRARY is not set
# CONFIG_MBEDTLS_ENABLE_HEAP is not set
# CONFIG_MBEDTLS_ECP_C is not set
# CONFIG_MBEDTLS_CTR_DRBG_C is not set
# CONFIG_MBEDTLS_CMAC_C is not set
# CONFIG_MBEDTLS_CCM_C is not set
CONFIG_MBEDTLS_LEGACY_CRYPTO_C=y
CONFIG_TFM_MCUBOOT_IMAGE_NUMBER=1
CONFIG_TFM_CRYPTO_RNG_MODULE_ENABLED=y
CONFIG_TFM_PARTITION_CRYPTO=y
CONFIG_TFM_PARTITION_PLATFORM=y

#
# Nordic nRF Connect
#
CONFIG_BOOT_BANNER_STRING="Booting nRF Connect SDK"
CONFIG_WARN_EXPERIMENTAL=y
CONFIG_PRIVILEGED_STACK_SIZE=1024
CONFIG_BT_BUF_CMD_TX_COUNT=10
CONFIG_ENTROPY_GENERATOR=y
CONFIG_INIT_ARCH_HW_AT_BOOT=y
CONFIG_NORDIC_QSPI_NOR=y
CONFIG_NORDIC_QSPI_NOR_FLASH_LAYOUT_PAGE_SIZE=4096
# CONFIG_GETOPT is not set
# CONFIG_NCS_SAMPLES_DEFAULTS is not set
# CONFIG_NRF700X_RADIO_TEST_COMBO is not set
CONFIG_NCS_INCLUDE_RPMSG_CHILD_IMAGE=y
CONFIG_NCS_SAMPLE_HCI_RPMSG_CHILD_IMAGE=y

#
# Image build variants
#
# CONFIG_NCS_MCUBOOT_IN_BUILD is not set
# end of Image build variants

CONFIG_NORDIC_QSPI_NOR_STACK_WRITE_BUFFER_SIZE=4
# CONFIG_NCS_SAMPLE_MCUMGR_BT_OTA_DFU_SPEEDUP is not set
CONFIG_BT_L2CAP_TX_MTU=247
CONFIG_BT_BUF_ACL_TX_SIZE=251
CONFIG_BT_BUF_ACL_RX_SIZE=251

#
# Subsystems
#

#
# Bootloader
#
# CONFIG_BUILD_S1_VARIANT is not set
# CONFIG_SECURE_BOOT is not set
CONFIG_PM_PARTITION_SIZE_PROVISION=0x280
# CONFIG_B0_MIN_PARTITION_SIZE is not set
CONFIG_PM_PARTITION_SIZE_B0_IMAGE=0x8000
# CONFIG_SECURE_BOOT_CRYPTO is not set

#
# Secure Boot firmware validation
#
CONFIG_SB_VALIDATION_INFO_MAGIC=0x86518483
CONFIG_SB_VALIDATION_POINTER_MAGIC=0x6919b47e
CONFIG_SB_VALIDATION_INFO_CRYPTO_ID=1
CONFIG_SB_VALIDATION_INFO_VERSION=2
CONFIG_SB_VALIDATION_METADATA_OFFSET=0
CONFIG_SB_VALIDATE_FW_SIGNATURE=y
# end of Secure Boot firmware validation

# CONFIG_SECURE_BOOT_STORAGE is not set
# end of Bootloader

#
# Bluetooth Low Energy
#
CONFIG_BT_MAX_CONN=1
CONFIG_BT_LL_SOFTDEVICE_HEADERS_INCLUDE=y
CONFIG_BT_HCI_TX_STACK_SIZE=1024
CONFIG_BT_RX_STACK_SIZE=2200

#
# BLE Libraries
#
# CONFIG_BT_GATT_POOL is not set
# CONFIG_BT_GATT_DM is not set
# CONFIG_BT_ADV_PROV is not set
# CONFIG_BT_CONN_CTX is not set

#
# Bluetooth Services
#
# CONFIG_BT_AMS_CLIENT is not set
# CONFIG_BT_ANCS_CLIENT is not set
# CONFIG_BT_BAS_CLIENT is not set
# CONFIG_BT_CTS_CLIENT is not set
# CONFIG_BT_DFU_SMP is not set
# CONFIG_BT_GATTP is not set
# CONFIG_BT_HIDS is not set
# CONFIG_BT_HOGP is not set
# CONFIG_BT_LBS is not set
# CONFIG_BT_NSMS is not set
# CONFIG_BT_NUS is not set
# CONFIG_BT_NUS_CLIENT is not set
# CONFIG_BT_RSCS is not set
# CONFIG_BT_THROUGHPUT is not set
# CONFIG_BT_LATENCY is not set
# CONFIG_BT_LATENCY_CLIENT is not set
# CONFIG_BT_HRS_CLIENT is not set
# CONFIG_BT_DDFS is not set
# CONFIG_BT_MDS is not set
# CONFIG_BT_CGMS is not set
# CONFIG_BT_FAST_PAIR is not set
CONFIG_BT_DIS=y
# CONFIG_BT_DIS_FW_REV is not set
CONFIG_BT_GATT_AUTO_SEC_REQ=y
# end of Bluetooth Services

#
# BLE over nRF RPC
#
CONFIG_BT_DRIVERS=y
# CONFIG_BT_RPC_STACK is not set
# CONFIG_BT_CENTRAL is not set
CONFIG_BT_PERIPHERAL=y
# CONFIG_BT_OBSERVER is not set
CONFIG_BT_BROADCASTER=y
CONFIG_BT_CONN=y
# CONFIG_BT_REMOTE_VERSION is not set
CONFIG_BT_PHY_UPDATE=y
CONFIG_BT_DATA_LEN_UPDATE=y
# CONFIG_BT_EXT_ADV is not set
# CONFIG_BT_DIS_SETTINGS is not set
CONFIG_BT_DIS_MODEL="nRF5340_CPUAPP_QKAA"
CONFIG_BT_DIS_MANUF="Manufacturer"
CONFIG_BT_DIS_PNP=y
CONFIG_BT_DIS_PNP_VID_SRC=1
CONFIG_BT_DIS_PNP_VID=0
CONFIG_BT_DIS_PNP_PID=0
CONFIG_BT_DIS_PNP_VER=1
# CONFIG_BT_DIS_SERIAL_NUMBER is not set
# CONFIG_BT_DIS_HW_REV is not set
# CONFIG_BT_DIS_SW_REV is not set
# CONFIG_BT_BAS is not set
# CONFIG_BT_HRS is not set
# CONFIG_BT_TPS is not set
# CONFIG_BT_IAS is not set
# CONFIG_BT_IAS_CLIENT is not set
# CONFIG_BT_OTS is not set
# CONFIG_BT_OTS_CLIENT is not set
CONFIG_BT_BUF_ACL_TX_COUNT=3
CONFIG_BT_BUF_ACL_RX_COUNT=6
CONFIG_BT_BUF_EVT_RX_SIZE=68
CONFIG_BT_BUF_EVT_RX_COUNT=10
CONFIG_BT_BUF_EVT_DISCARDABLE_SIZE=43
CONFIG_BT_BUF_EVT_DISCARDABLE_COUNT=3
CONFIG_BT_BUF_CMD_TX_SIZE=65
CONFIG_BT_HAS_HCI_VS=y
CONFIG_BT_HCI_VS=y
# CONFIG_BT_HCI_VS_EVT is not set
# CONFIG_BT_HCI_VS_FATAL_ERROR is not set
# CONFIG_BT_HCI_VS_EXT_DETECT is not set
# CONFIG_BT_WAIT_NOP is not set
CONFIG_BT_RPA=y
CONFIG_BT_ASSERT=y
CONFIG_BT_ASSERT_VERBOSE=y
# CONFIG_BT_ASSERT_PANIC is not set
CONFIG_BT_DEBUG_NONE=y
# CONFIG_BT_DEBUG_LOG is not set
# CONFIG_BT_DEBUG_MONITOR_UART is not set
# CONFIG_BT_DEBUG_MONITOR_RTT is not set
CONFIG_BT_LONG_WQ=y
CONFIG_BT_LONG_WQ_STACK_SIZE=1300
CONFIG_BT_LONG_WQ_PRIO=10
CONFIG_BT_LONG_WQ_INIT_PRIO=50
CONFIG_BT_HCI_HOST=y
# CONFIG_BT_HCI_TX_STACK_SIZE_WITH_PROMPT is not set
CONFIG_BT_HCI_TX_PRIO=7
CONFIG_BT_HCI_RESERVE=1
# CONFIG_BT_RECV_BLOCKING is not set
# CONFIG_BT_RECV_WORKQ_SYS is not set
CONFIG_BT_RECV_WORKQ_BT=y
CONFIG_BT_RX_PRIO=8
CONFIG_BT_DRIVER_RX_HIGH_PRIO=6
# CONFIG_BT_AUDIO is not set
CONFIG_BT_HOST_CRYPTO=y
CONFIG_BT_HOST_CRYPTO_PRNG=y
CONFIG_BT_SETTINGS=y
CONFIG_BT_SETTINGS_CCC_LAZY_LOADING=y
CONFIG_BT_SETTINGS_DELAYED_STORE=y
CONFIG_BT_SETTINGS_DELAYED_STORE_MS=1000
CONFIG_BT_SETTINGS_CCC_STORE_ON_WRITE=y
CONFIG_BT_SETTINGS_CF_STORE_ON_WRITE=y
CONFIG_BT_SETTINGS_USE_PRINTK=y
# CONFIG_BT_FILTER_ACCEPT_LIST is not set
CONFIG_BT_LIM_ADV_TIMEOUT=30
CONFIG_BT_CONN_TX_USER_DATA_SIZE=8
CONFIG_BT_CONN_TX_MAX=3
# CONFIG_BT_CONN_PARAM_ANY is not set
CONFIG_BT_AUTO_PHY_UPDATE=y
# CONFIG_BT_USER_DATA_LEN_UPDATE is not set
CONFIG_BT_AUTO_DATA_LEN_UPDATE=y
# CONFIG_BT_REMOTE_INFO is not set
CONFIG_BT_SMP=y
# CONFIG_BT_PASSKEY_KEYPRESS is not set
CONFIG_BT_PRIVACY=y
# CONFIG_BT_PRIVACY_RANDOMIZE_IR is not set
CONFIG_BT_RPA_TIMEOUT=900
# CONFIG_BT_RPA_TIMEOUT_DYNAMIC is not set
CONFIG_BT_SIGNING=y
# CONFIG_BT_SMP_APP_PAIRING_ACCEPT is not set
# CONFIG_BT_SMP_SC_PAIR_ONLY is not set
# CONFIG_BT_SMP_SC_ONLY is not set
# CONFIG_BT_SMP_OOB_LEGACY_PAIR_ONLY is not set
# CONFIG_BT_SMP_DISABLE_LEGACY_JW_PASSKEY is not set
# CONFIG_BT_SMP_ALLOW_UNAUTH_OVERWRITE is not set
# CONFIG_BT_ID_UNPAIR_MATCHING_BONDS is not set
# CONFIG_BT_ID_ALLOW_UNAUTH_OVERWRITE is not set
# CONFIG_BT_FIXED_PASSKEY is not set
# CONFIG_BT_USE_DEBUG_KEYS is not set
CONFIG_BT_BONDABLE=y
# CONFIG_BT_BONDING_REQUIRED is not set
# CONFIG_BT_BONDABLE_PER_CONNECTION is not set
# CONFIG_BT_STORE_DEBUG_KEYS is not set
CONFIG_BT_SMP_ENFORCE_MITM=y
CONFIG_BT_KEYS_OVERWRITE_OLDEST=y
# CONFIG_BT_KEYS_SAVE_AGING_COUNTER_ON_PAIRING is not set
CONFIG_BT_SMP_MIN_ENC_KEY_SIZE=7
CONFIG_BT_L2CAP_TX_BUF_COUNT=3
CONFIG_BT_L2CAP_TX_FRAG_COUNT=2
CONFIG_BT_L2CAP_RESCHED_MS=1000
# CONFIG_BT_L2CAP_DYNAMIC_CHANNEL is not set
# CONFIG_BT_L2CAP_SEG_RECV is not set
CONFIG_BT_ATT_ENFORCE_FLOW=y
CONFIG_BT_ATT_PREPARE_COUNT=1
CONFIG_BT_ATT_RETRY_ON_SEC_ERR=y
CONFIG_BT_GATT_SERVICE_CHANGED=y
# CONFIG_BT_GATT_DYNAMIC_DB is not set
CONFIG_BT_GATT_CACHING=y
# CONFIG_BT_GATT_NOTIFY_MULTIPLE is not set
# CONFIG_BT_GATT_ENFORCE_CHANGE_UNAWARE is not set
CONFIG_BT_GATT_ENFORCE_SUBSCRIPTION=y
# CONFIG_BT_GATT_CLIENT is not set
CONFIG_BT_GATT_READ_MULTIPLE=y
CONFIG_BT_GATT_READ_MULT_VAR_LEN=y
CONFIG_BT_GAP_AUTO_UPDATE_CONN_PARAMS=y
CONFIG_BT_GAP_PERIPHERAL_PREF_PARAMS=y
CONFIG_BT_PERIPHERAL_PREF_MIN_INT=24
CONFIG_BT_PERIPHERAL_PREF_MAX_INT=40
CONFIG_BT_PERIPHERAL_PREF_LATENCY=0
CONFIG_BT_PERIPHERAL_PREF_TIMEOUT=42
CONFIG_BT_MAX_PAIRED=1
CONFIG_BT_CREATE_CONN_TIMEOUT=3
CONFIG_BT_CONN_PARAM_UPDATE_TIMEOUT=5000
CONFIG_BT_CONN_PARAM_RETRY_COUNT=3
CONFIG_BT_CONN_PARAM_RETRY_TIMEOUT=5000
# CONFIG_BT_DEVICE_NAME_DYNAMIC is not set
CONFIG_BT_DEVICE_NAME="BASE"
# CONFIG_BT_DEVICE_APPEARANCE_DYNAMIC is not set
CONFIG_BT_DEVICE_APPEARANCE=833
CONFIG_BT_ID_MAX=1
CONFIG_BT_DF=y
# CONFIG_BT_DF_CONNECTIONLESS_CTE_RX is not set
# CONFIG_BT_DF_CONNECTIONLESS_CTE_TX is not set
# CONFIG_BT_DF_CONNECTION_CTE_RX is not set
CONFIG_BT_DF_CONNECTION_CTE_TX=y
CONFIG_BT_DF_CONNECTION_CTE_RSP=y
CONFIG_BT_DF_CTE_TX_AOD=y
CONFIG_BT_ECC=y
# CONFIG_BT_TINYCRYPT_ECC is not set
# CONFIG_BT_HOST_CCM is not set
# CONFIG_BT_LOG_SNIFFER_INFO is not set
# CONFIG_BT_TESTING is not set
# CONFIG_BT_BREDR is not set
# CONFIG_BT_HCI_VS_EVT_USER is not set
CONFIG_BT_CRYPTO=y
# end of BLE over nRF RPC
# end of Bluetooth Low Energy

#
# DFU
#
# CONFIG_DFU_MULTI_IMAGE is not set
# CONFIG_DFU_TARGET is not set
# end of DFU

# CONFIG_ESB is not set
# CONFIG_EMDS is not set

#
# Peripheral CPU DFU (PCD)
#
# CONFIG_PCD is not set
# CONFIG_PCD_APP is not set
CONFIG_PCD_VERSION_PAGE_BUF_SIZE=2046
# CONFIG_PCD_NET is not set
# end of Peripheral CPU DFU (PCD)

#
# Networking
#

#
# Application protocols
#

#
# nRF Cloud
#

#
# Client ID (nRF Cloud Device ID)
#
CONFIG_NRF_CLOUD_CLIENT_ID_SRC_COMPILE_TIME=y
CONFIG_NRF_CLOUD_CLIENT_ID="my-client-id"
# end of Client ID (nRF Cloud Device ID)

# CONFIG_NRF_CLOUD_MQTT is not set
# CONFIG_NRF_CLOUD_FOTA is not set
# CONFIG_NRF_CLOUD_FOTA_FULL_MODEM_UPDATE is not set
# CONFIG_NRF_CLOUD_REST is not set
# CONFIG_NRF_CLOUD_ALERT is not set
# CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL_OFF is not set
# CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL_ERR is not set
# CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL_WRN is not set
# CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL_INF is not set
# CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL_DBG is not set
CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL_DEFAULT=y
CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL=0
CONFIG_NRF_CLOUD_LOG_OUTPUT_LEVEL=1
# CONFIG_NRF_CLOUD_LOG_INCLUDE_LEVEL_0 is not set
CONFIG_NRF_CLOUD_LOG_BUF_SIZE=256
# CONFIG_NRF_CLOUD_LOG_LOG_LEVEL_OFF is not set
# CONFIG_NRF_CLOUD_LOG_LOG_LEVEL_ERR is not set
# CONFIG_NRF_CLOUD_LOG_LOG_LEVEL_WRN is not set
# CONFIG_NRF_CLOUD_LOG_LOG_LEVEL_INF is not set
# CONFIG_NRF_CLOUD_LOG_LOG_LEVEL_DBG is not set
CONFIG_NRF_CLOUD_LOG_LOG_LEVEL_DEFAULT=y
CONFIG_NRF_CLOUD_LOG_LOG_LEVEL=0
# CONFIG_NRF_CLOUD_COAP is not set
# CONFIG_NRF_CLOUD_GATEWAY is not set
# CONFIG_NRF_CLOUD_LOG_LEVEL_OFF is not set
# CONFIG_NRF_CLOUD_LOG_LEVEL_ERR is not set
# CONFIG_NRF_CLOUD_LOG_LEVEL_WRN is not set
# CONFIG_NRF_CLOUD_LOG_LEVEL_INF is not set
# CONFIG_NRF_CLOUD_LOG_LEVEL_DBG is not set
CONFIG_NRF_CLOUD_LOG_LEVEL_DEFAULT=y
CONFIG_NRF_CLOUD_LOG_LEVEL=0
# end of nRF Cloud

# CONFIG_REST_CLIENT is not set
# CONFIG_DOWNLOAD_CLIENT is not set
# CONFIG_AWS_IOT is not set
# CONFIG_AWS_JOBS is not set
# CONFIG_AZURE_IOT_HUB is not set

#
# Self-Registration (Zi ZHu Ce)
#
# end of Self-Registration (Zi ZHu Ce)

# CONFIG_ICAL_PARSER is not set
# CONFIG_FTP_CLIENT is not set
# CONFIG_LWM2M_CLIENT_UTILS is not set
# CONFIG_WIFI_CREDENTIALS is not set
# CONFIG_WIFI_CREDENTIALS_STATIC is not set
# CONFIG_MQTT_HELPER is not set
# CONFIG_NRF_PROVISIONING is not set
# CONFIG_NRF_MCUMGR_SMP_CLIENT is not set
# end of Application protocols
# end of Networking

#
# NFC
#
# CONFIG_NFC_NDEF is not set
# CONFIG_NFC_NDEF_PARSER is not set
# CONFIG_NFC_NDEF_PAYLOAD_TYPE_COMMON is not set
# CONFIG_NFC_T2T_PARSER is not set
# CONFIG_NFC_T4T_NDEF_FILE is not set
# CONFIG_NFC_T4T_ISODEP is not set
# CONFIG_NFC_T4T_APDU is not set
# CONFIG_NFC_T4T_CC_FILE is not set
# CONFIG_NFC_T4T_HL_PROCEDURE is not set
# CONFIG_NFC_PLATFORM is not set
# CONFIG_NFC_TNEP_TAG is not set
# CONFIG_NFC_TNEP_POLLER is not set
# CONFIG_NFC_TNEP_CH is not set
# end of NFC

# CONFIG_APP_EVENT_MANAGER is not set
# CONFIG_NRF_PROFILER is not set
# CONFIG_FW_INFO is not set

#
# Debug
#
# CONFIG_CPU_LOAD is not set
# CONFIG_PPI_TRACE is not set
# end of Debug

# CONFIG_SHELL_BT_NUS is not set
# CONFIG_SHELL_IPC is not set

#
# Multiprotocol service layer (MPSL)
#
# CONFIG_MPSL_FEM_ONLY is not set
# CONFIG_MPSL_FEM_DEVICE_CONFIG_254 is not set
# CONFIG_MPSL_FEM_LOG_LEVEL_OFF is not set
# CONFIG_MPSL_FEM_LOG_LEVEL_ERR is not set
# CONFIG_MPSL_FEM_LOG_LEVEL_WRN is not set
# CONFIG_MPSL_FEM_LOG_LEVEL_INF is not set
# CONFIG_MPSL_FEM_LOG_LEVEL_DBG is not set
CONFIG_MPSL_FEM_LOG_LEVEL_DEFAULT=y
CONFIG_MPSL_FEM_LOG_LEVEL=0
CONFIG_MPSL_THREAD_COOP_PRIO=8
CONFIG_MPSL_WORK_STACK_SIZE=1024
CONFIG_MPSL_TIMESLOT_SESSION_COUNT=0
# CONFIG_MPSL_ASSERT_HANDLER is not set
# CONFIG_MPSL_LOG_LEVEL_OFF is not set
# CONFIG_MPSL_LOG_LEVEL_ERR is not set
# CONFIG_MPSL_LOG_LEVEL_WRN is not set
# CONFIG_MPSL_LOG_LEVEL_INF is not set
# CONFIG_MPSL_LOG_LEVEL_DBG is not set
CONFIG_MPSL_LOG_LEVEL_DEFAULT=y
CONFIG_MPSL_LOG_LEVEL=0
# end of Multiprotocol service layer (MPSL)

#
# Partition Manager
#
CONFIG_PARTITION_MANAGER_ENABLED=y
CONFIG_FLASH_MAP_CUSTOM=y
CONFIG_SRAM_BASE_ADDRESS=0x20040000

#
# Zephyr subsystem configurations
#
CONFIG_RPMSG_NRF53_SRAM_SIZE=0x10000
CONFIG_PM_PARTITION_SIZE_SETTINGS_STORAGE=0x2000
CONFIG_PM_PARTITION_ALIGN_SETTINGS_STORAGE=0x4000
# end of Zephyr subsystem configurations

#
# NCS subsystem configurations
#
CONFIG_PM_SINGLE_IMAGE=y
CONFIG_PM_EXTERNAL_FLASH_HAS_DRIVER=y
CONFIG_PM_EXTERNAL_FLASH_BASE=0
CONFIG_PM_EXTERNAL_FLASH_PATH=""
CONFIG_PM_EXTERNAL_FLASH_SIZE_BITS=0
# CONFIG_PM_OVERRIDE_EXTERNAL_DRIVER_CHECK is not set
CONFIG_PM_SRAM_BASE=0x20000000
CONFIG_PM_SRAM_SIZE=0x80000
# end of Partition Manager

#
# nRF RPC (Remote Procedure Call) library
#
# end of nRF RPC (Remote Procedure Call) library

# CONFIG_ZIGBEE is not set

#
# Full Modem Firmware Update Management(FMFU)
#
# CONFIG_MGMT_FMFU_LOG_LEVEL_OFF is not set
# CONFIG_MGMT_FMFU_LOG_LEVEL_ERR is not set
# CONFIG_MGMT_FMFU_LOG_LEVEL_WRN is not set
# CONFIG_MGMT_FMFU_LOG_LEVEL_INF is not set
# CONFIG_MGMT_FMFU_LOG_LEVEL_DBG is not set
CONFIG_MGMT_FMFU_LOG_LEVEL_DEFAULT=y
CONFIG_MGMT_FMFU_LOG_LEVEL=0
# end of Full Modem Firmware Update Management(FMFU)

# CONFIG_CAF is not set

#
# Nordic IEEE 802.15.4
#
# end of Nordic IEEE 802.15.4

# CONFIG_DM_MODULE is not set

#
# nRF Security
#
CONFIG_NORDIC_SECURITY_BACKEND=y
CONFIG_NRF_SECURITY=y
CONFIG_MBEDTLS_CFG_FILE="nrf-config.h"
CONFIG_MBEDTLS_USER_CONFIG_FILE="nrf-config-user.h"
CONFIG_GENERATE_MBEDTLS_CFG_FILE=y
CONFIG_MBEDTLS_PSA_CRYPTO_C=y

#
# PSA RNG support
#
CONFIG_PSA_WANT_GENERATE_RANDOM=y
CONFIG_PSA_WANT_ALG_CTR_DRBG=y
# CONFIG_PSA_WANT_ALG_HMAC_DRBG is not set
# end of PSA RNG support

#
# PSA key type support
#
# CONFIG_PSA_WANT_KEY_TYPE_DERIVE is not set
# CONFIG_PSA_WANT_KEY_TYPE_RAW_DATA is not set
# CONFIG_PSA_WANT_KEY_TYPE_HMAC is not set
# CONFIG_PSA_WANT_KEY_TYPE_PASSWORD is not set
# CONFIG_PSA_WANT_KEY_TYPE_PASSWORD_HASH is not set
# CONFIG_PSA_WANT_KEY_TYPE_PEPPER is not set
# CONFIG_PSA_WANT_KEY_TYPE_AES is not set
# CONFIG_PSA_WANT_KEY_TYPE_ARIA is not set
# CONFIG_PSA_WANT_KEY_TYPE_DES is not set
# CONFIG_PSA_WANT_KEY_TYPE_CAMELLIA is not set
# CONFIG_PSA_WANT_KEY_TYPE_SM4 is not set
# CONFIG_PSA_WANT_KEY_TYPE_ARC4 is not set
# CONFIG_PSA_WANT_KEY_TYPE_ECC_KEY_PAIR is not set
# CONFIG_PSA_WANT_KEY_TYPE_ECC_PUBLIC_KEY is not set
# CONFIG_PSA_WANT_KEY_TYPE_RSA_KEY_PAIR is not set
# CONFIG_PSA_WANT_KEY_TYPE_RSA_PUBLIC_KEY is not set
# CONFIG_PSA_WANT_KEY_TYPE_DH_KEY_PAIR is not set
# CONFIG_PSA_WANT_KEY_TYPE_DH_PUBLIC_KEY is not set
# end of PSA key type support

#
# PSA AEAD support
#
# CONFIG_PSA_WANT_ALG_CCM is not set
# CONFIG_PSA_WANT_ALG_GCM is not set
# CONFIG_PSA_WANT_ALG_CHACHA20_POLY1305 is not set
# end of PSA AEAD support

#
# PSA MAC support
#
# CONFIG_PSA_WANT_ALG_CBC_MAC is not set
# CONFIG_PSA_WANT_ALG_CMAC is not set
# CONFIG_PSA_WANT_ALG_HMAC is not set
# end of PSA MAC support

#
# PSA Hash support
#
# CONFIG_PSA_WANT_ALG_SHA_1 is not set
# CONFIG_PSA_WANT_ALG_SHA_224 is not set
# CONFIG_PSA_WANT_ALG_SHA_256 is not set
# CONFIG_PSA_WANT_ALG_SHA_384 is not set
# CONFIG_PSA_WANT_ALG_SHA_512 is not set
# CONFIG_PSA_WANT_ALG_SHA_512_224 is not set
# CONFIG_PSA_WANT_ALG_SHA_512_256 is not set
# CONFIG_PSA_WANT_ALG_SHA3_224 is not set
# CONFIG_PSA_WANT_ALG_SHA3_256 is not set
# CONFIG_PSA_WANT_ALG_SHA3_384 is not set
# CONFIG_PSA_WANT_ALG_SHA3_512 is not set
# CONFIG_PSA_WANT_ALG_SM3 is not set
# CONFIG_PSA_WANT_ALG_SHAKE256_512 is not set
# CONFIG_PSA_WANT_ALG_RIPEMD160 is not set
# CONFIG_PSA_WANT_ALG_MD2 is not set
# CONFIG_PSA_WANT_ALG_MD4 is not set
# CONFIG_PSA_WANT_ALG_MD5 is not set
# end of PSA Hash support

#
# PSA Cipher support
#
# CONFIG_PSA_WANT_ALG_ECB_NO_PADDING is not set
# CONFIG_PSA_WANT_ALG_CBC_NO_PADDING is not set
# CONFIG_PSA_WANT_ALG_CBC_PKCS7 is not set
# CONFIG_PSA_WANT_ALG_CFB is not set
# CONFIG_PSA_WANT_ALG_CTR is not set
# CONFIG_PSA_WANT_ALG_OFB is not set
# CONFIG_PSA_WANT_ALG_XTS is not set
# CONFIG_PSA_WANT_ALG_CCM_STAR_NO_TAG is not set
# CONFIG_PSA_WANT_ALG_STREAM_CIPHER is not set
# end of PSA Cipher support

#
# PSA Key agreement support
#
# CONFIG_PSA_WANT_ALG_ECDH is not set
# CONFIG_PSA_WANT_ALG_FFDH is not set
# end of PSA Key agreement support

#
# PSA Key derivation support
#
# CONFIG_PSA_WANT_ALG_HKDF_EXTRACT is not set
# CONFIG_PSA_WANT_ALG_HKDF_EXPAND is not set
# CONFIG_PSA_WANT_ALG_TLS12_PRF is not set
# CONFIG_PSA_WANT_ALG_TLS12_PSK_TO_MS is not set
# CONFIG_PSA_WANT_ALG_TLS12_ECJPAKE_TO_PMS is not set
# end of PSA Key derivation support

#
# PSA Asymmetric support
#
# CONFIG_PSA_WANT_ALG_ECDSA is not set
# CONFIG_PSA_WANT_ALG_ECDSA_ANY is not set
# CONFIG_PSA_WANT_ALG_DETERMINISTIC_ECDSA is not set

#
# Elliptic Curve type support
#
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_160 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_192 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_224 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_256 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_320 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_384 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_512 is not set
# CONFIG_PSA_WANT_ECC_MONTGOMERY_255 is not set
# CONFIG_PSA_WANT_ECC_MONTGOMERY_448 is not set
# CONFIG_PSA_WANT_ECC_TWISTED_EDWARDS_255 is not set
# CONFIG_PSA_WANT_ECC_TWISTED_EDWARDS_448 is not set
# CONFIG_PSA_WANT_ECC_SECP_K1_192 is not set
# CONFIG_PSA_WANT_ECC_SECP_K1_224 is not set
# CONFIG_PSA_WANT_ECC_SECP_K1_256 is not set
# CONFIG_PSA_WANT_ECC_SECP_R1_192 is not set
# CONFIG_PSA_WANT_ECC_SECP_R1_224 is not set
# CONFIG_PSA_WANT_ECC_SECP_R1_256 is not set
# CONFIG_PSA_WANT_ECC_SECP_R1_384 is not set
# CONFIG_PSA_WANT_ECC_SECP_R1_521 is not set
# CONFIG_PSA_WANT_ECC_SECP_R2_160 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_163 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_233 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_239 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_283 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_409 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_571 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_163 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_233 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_283 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_409 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_571 is not set
# CONFIG_PSA_WANT_ECC_SECT_R2_163 is not set
# CONFIG_PSA_WANT_ECC_FRP_V1_256 is not set
# end of Elliptic Curve type support

# CONFIG_PSA_WANT_ALG_RSA_OAEP is not set
# CONFIG_PSA_WANT_ALG_RSA_PKCS1V15_CRYPT is not set
# CONFIG_PSA_WANT_ALG_RSA_PKCS1V15_SIGN is not set
# CONFIG_PSA_WANT_ALG_RSA_PKCS1V15_SIGN_RAW is not set
# CONFIG_PSA_WANT_ALG_RSA_PSS is not set
# CONFIG_PSA_WANT_ALG_RSA_PSS_ANY_SALT is not set
# end of PSA Asymmetric support

# CONFIG_PSA_WANT_ALG_JPAKE is not set
# CONFIG_PSA_WANT_ALG_SPAKE2P is not set
# CONFIG_PSA_WANT_ALG_SRP_6 is not set
# CONFIG_PSA_WANT_ALG_PURE_EDDSA is not set
# CONFIG_PSA_WANT_ALG_ED25519PH is not set
# CONFIG_PSA_WANT_ALG_ED448PH is not set
CONFIG_MBEDTLS_PSA_CRYPTO_EXTERNAL_RNG=y
CONFIG_PSA_CORE_OBERON=y

#
# PSA Driver Support
#
CONFIG_MBEDTLS_PSA_CRYPTO_DRIVERS=y
CONFIG_PSA_DEFAULT_OFF=y
CONFIG_MBEDTLS_PSA_CRYPTO_CLIENT=y
CONFIG_PSA_CRYPTO_DRIVER_OBERON=y
# CONFIG_PSA_CRYPTO_DRIVER_CC3XX is not set

#
# Choose DRBG algorithm
#
CONFIG_PSA_USE_CTR_DRBG_DRIVER=y
# end of Choose DRBG algorithm

#
# CryptoCell PSA Driver Configuration
#
CONFIG_PSA_USE_CC3XX_CTR_DRBG_DRIVER=y
# end of CryptoCell PSA Driver Configuration

#
# AES key size configuration
#
CONFIG_PSA_WANT_AES_KEY_SIZE_128=y
CONFIG_PSA_WANT_AES_KEY_SIZE_192=y
CONFIG_PSA_WANT_AES_KEY_SIZE_256=y
# end of AES key size configuration

#
# RSA key size configuration
#
CONFIG_PSA_WANT_RSA_KEY_SIZE_1024=y
# CONFIG_PSA_WANT_RSA_KEY_SIZE_1536 is not set
CONFIG_PSA_WANT_RSA_KEY_SIZE_2048=y
CONFIG_PSA_WANT_RSA_KEY_SIZE_3072=y
# CONFIG_PSA_WANT_RSA_KEY_SIZE_4096 is not set
# CONFIG_PSA_WANT_RSA_KEY_SIZE_6144 is not set
# CONFIG_PSA_WANT_RSA_KEY_SIZE_8192 is not set
CONFIG_PSA_MAX_RSA_KEY_BITS=3072
# end of RSA key size configuration

CONFIG_PSA_ACCEL_GENERATE_RANDOM=y
CONFIG_PSA_NEED_CC3XX_CTR_DRBG_DRIVER=y

#
# PSA API support
#
CONFIG_MBEDTLS_USE_PSA_CRYPTO=y
CONFIG_MBEDTLS_PSA_KEY_SLOT_COUNT=32
# end of PSA API support

CONFIG_MBEDTLS_PLATFORM_MEMORY=y
CONFIG_MBEDTLS_PLATFORM_C=y
CONFIG_MBEDTLS_MEMORY_C=y
CONFIG_MBEDTLS_MEMORY_BUFFER_ALLOC_C=y
CONFIG_MBEDTLS_ENTROPY_HARDWARE_ALT=y
CONFIG_MBEDTLS_AES_SETKEY_ENC_ALT=y
CONFIG_MBEDTLS_AES_SETKEY_DEC_ALT=y
CONFIG_MBEDTLS_AES_ENCRYPT_ALT=y
CONFIG_MBEDTLS_AES_DECRYPT_ALT=y
CONFIG_MBEDTLS_CHACHA20_ALT=y
CONFIG_MBEDTLS_POLY1305_ALT=y
CONFIG_MBEDTLS_ECDH_GEN_PUBLIC_ALT=y
CONFIG_MBEDTLS_ECDH_COMPUTE_SHARED_ALT=y
CONFIG_MBEDTLS_ECDSA_GENKEY_ALT=y
CONFIG_MBEDTLS_ECDSA_SIGN_ALT=y
CONFIG_MBEDTLS_ECDSA_VERIFY_ALT=y
CONFIG_MBEDTLS_ECJPAKE_ALT=y
CONFIG_MBEDTLS_SHA1_ALT=y
CONFIG_MBEDTLS_SHA224_ALT=y
CONFIG_MBEDTLS_SHA256_ALT=y
CONFIG_MBEDTLS_ENTROPY_FORCE_SHA256=y
CONFIG_MBEDTLS_ENTROPY_MAX_SOURCES=1
CONFIG_MBEDTLS_NO_PLATFORM_ENTROPY=y
# CONFIG_NRF_SECURITY_ADVANCED is not set
CONFIG_OBERON_ONLY_PSA_ENABLED=y
CONFIG_OBERON_ONLY_ENABLED=y

#
# Legacy mbed TLS crypto APIs
#
CONFIG_MBEDTLS_MPI_WINDOW_SIZE=6
CONFIG_MBEDTLS_MPI_MAX_SIZE=256
# CONFIG_CC3XX_BACKEND is not set
CONFIG_OBERON_BACKEND=y
CONFIG_MBEDTLS_HMAC_DRBG_C=y
CONFIG_MBEDTLS_AES_C=y

#
# Cipher Selection
#

#
# AEAD  - Authenticated Encryption with Associated Data
#
# end of AEAD  - Authenticated Encryption with Associated Data

CONFIG_MBEDTLS_HKDF_C=y

#
# SHA - Secure Hash Algorithm
#
CONFIG_MBEDTLS_SHA224_C=y
CONFIG_MBEDTLS_SHA256_C=y
CONFIG_MBEDTLS_SHA384_C=y
# end of SHA - Secure Hash Algorithm

CONFIG_MBEDTLS_CIPHER_C=y
# CONFIG_MBEDTLS_PK_C is not set
CONFIG_MBEDTLS_PKCS5_C=y
# CONFIG_MBEDTLS_PK_WRITE_C is not set
# CONFIG_MBEDTLS_PK_PARSE_C is not set
# end of Legacy mbed TLS crypto APIs
# end of nRF Security

# CONFIG_NET_CORE_MONITOR is not set

#
# Audio Module
#
# CONFIG_AUDIO_MODULE_TEST is not set
CONFIG_AUDIO_MODULE_NAME_SIZE=20
# CONFIG_AUDIO_MODULE_LOG_LEVEL_OFF is not set
# CONFIG_AUDIO_MODULE_LOG_LEVEL_ERR is not set
# CONFIG_AUDIO_MODULE_LOG_LEVEL_WRN is not set
# CONFIG_AUDIO_MODULE_LOG_LEVEL_INF is not set
# CONFIG_AUDIO_MODULE_LOG_LEVEL_DBG is not set
CONFIG_AUDIO_MODULE_LOG_LEVEL_DEFAULT=y
CONFIG_AUDIO_MODULE_LOG_LEVEL=0
# end of Audio Module
# end of Subsystems

#
# TF-M
#
CONFIG_TFM_BOARD="C:/ncs/v2.5.99-dev1/nrf/modules/tfm/tfm/boards/nrf5340_cpuapp"

#
# Platform partition component configs
#
CONFIG_TFM_PLATFORM_SERVICE_INPUT_BUFFER_SIZE=64
CONFIG_TFM_PLATFORM_SERVICE_OUTPUT_BUFFER_SIZE=64
CONFIG_TFM_PLATFORM_SP_STACK_SIZE=0x500
CONFIG_TFM_PLATFORM_NV_COUNTER_MODULE_DISABLED=y
# end of Platform partition component configs

#
# Crypto component configs
#
# CONFIG_TFM_CRYPTO_SINGLE_PART_FUNCS_DISABLED is not set
# end of Crypto component configs

# CONFIG_TFM_ITS_MAX_ASSET_SIZE_OVERRIDE is not set

#
# TF-M SPM component configs
#
CONFIG_TFM_CONN_HANDLE_MAX_NUM=8
# CONFIG_TFM_DOORBELL_API is not set
# end of TF-M SPM component configs
# end of TF-M

# CONFIG_WPA_SUPP is not set
CONFIG_POSIX_MAX_FDS=4

#
# Libraries
#

#
# Binary libraries
#
# CONFIG_BT_LL_ACS_NRF53 is not set

#
# Log levels
#
# CONFIG_BLE_HCI_VSC_LOG_LEVEL_OFF is not set
# CONFIG_BLE_HCI_VSC_LOG_LEVEL_ERR is not set
# CONFIG_BLE_HCI_VSC_LOG_LEVEL_WRN is not set
# CONFIG_BLE_HCI_VSC_LOG_LEVEL_INF is not set
# CONFIG_BLE_HCI_VSC_LOG_LEVEL_DBG is not set
CONFIG_BLE_HCI_VSC_LOG_LEVEL_DEFAULT=y
CONFIG_BLE_HCI_VSC_LOG_LEVEL=0
# end of Log levels
# end of Binary libraries

# CONFIG_ADP536X is not set
# CONFIG_AT_MONITOR is not set
# CONFIG_LTE_LINK_CONTROL is not set
CONFIG_NRF_SPU_FLASH_REGION_SIZE=0x4000
CONFIG_FPROTECT_BLOCK_SIZE=0x4000
# CONFIG_FPROTECT is not set
# CONFIG_AT_CMD_CUSTOM is not set
# CONFIG_DK_LIBRARY is not set
# CONFIG_AT_CMD_PARSER is not set
# CONFIG_MODEM_INFO is not set
CONFIG_RESET_ON_FATAL_ERROR=y
# CONFIG_FATAL_ERROR_LOG_LEVEL_OFF is not set
# CONFIG_FATAL_ERROR_LOG_LEVEL_ERR is not set
# CONFIG_FATAL_ERROR_LOG_LEVEL_WRN is not set
# CONFIG_FATAL_ERROR_LOG_LEVEL_INF is not set
# CONFIG_FATAL_ERROR_LOG_LEVEL_DBG is not set
CONFIG_FATAL_ERROR_LOG_LEVEL_DEFAULT=y
CONFIG_FATAL_ERROR_LOG_LEVEL=0
# CONFIG_SMS is not set
# CONFIG_SUPL_CLIENT_LIB is not set
# CONFIG_DATE_TIME is not set
# CONFIG_HW_ID_LIBRARY is not set
# CONFIG_RAM_POWER_DOWN_LIBRARY is not set
# CONFIG_WAVE_GEN_LIB is not set
CONFIG_HW_UNIQUE_KEY_PARTITION_SIZE=0
# CONFIG_MODEM_JWT is not set
# CONFIG_LOCATION is not set
# CONFIG_QOS is not set
# CONFIG_SFLOAT is not set
# CONFIG_CONTIN_ARRAY is not set
# CONFIG_PCM_MIX is not set
# CONFIG_TONE is not set
# CONFIG_PSCM is not set
# CONFIG_DATA_FIFO is not set
# CONFIG_FEM_AL_LIB is not set
# end of Libraries

#
# Device Drivers
#
# CONFIG_BT_DRIVER_QUIRK_NO_AUTO_DLE is not set
# CONFIG_ETH_RTT is not set
CONFIG_SENSOR=y
# CONFIG_BH1749 is not set
# CONFIG_SENSOR_SIM is not set
# CONFIG_SENSOR_STUB is not set
# CONFIG_PMW3360 is not set
# CONFIG_PAW3212 is not set
# CONFIG_BME68X_IAQ is not set
# CONFIG_NRF_SW_LPUART is not set
CONFIG_NRFX_GPIOTE_NUM_OF_EVT_HANDLERS=1
# CONFIG_IPC_UART is not set
# end of Device Drivers

#
# External libraries
#
# end of External libraries

#
# Test
#
CONFIG_ZTEST_MULTICORE_DEFAULT_SETTINGS=y
# CONFIG_UNITY is not set

#
# Mocks
#
# CONFIG_MOCK_NRF_MODEM_AT is not set
# end of Mocks
# end of Test
# end of Nordic nRF Connect

CONFIG_ZEPHYR_NRF_MODULE=y
# end of nrf (C:/ncs/v2.5.99-dev1/nrf)

#
# wfa-qt-control-app (C:/ncs/v2.5.99-dev1/modules/lib/wfa-qt-control-app)
#
# CONFIG_WFA_QT_LOG_LEVEL_OFF is not set
# CONFIG_WFA_QT_LOG_LEVEL_ERR is not set
# CONFIG_WFA_QT_LOG_LEVEL_WRN is not set
# CONFIG_WFA_QT_LOG_LEVEL_INF is not set
# CONFIG_WFA_QT_LOG_LEVEL_DBG is not set
CONFIG_WFA_QT_LOG_LEVEL_DEFAULT=y
CONFIG_WFA_QT_LOG_LEVEL=0
# CONFIG_WFA_QT_CONTROL_APP is not set
CONFIG_WFA_QT_THREAD_STACK_SIZE=4096
CONFIG_ZEPHYR_WFA_QT_CONTROL_APP_MODULE=y
# end of wfa-qt-control-app (C:/ncs/v2.5.99-dev1/modules/lib/wfa-qt-control-app)

#
# mcuboot (C:/ncs/v2.5.99-dev1/bootloader/mcuboot)
#

#
# MCUboot
#
CONFIG_BOOT_SIGNATURE_KEY_FILE=""
CONFIG_DT_FLASH_WRITE_BLOCK_SIZE=4
CONFIG_MCUBOOT_USB_SUPPORT=y
# CONFIG_MCUBOOT_USE_ALL_AVAILABLE_RAM is not set
# end of MCUboot

CONFIG_ZEPHYR_MCUBOOT_MODULE=y
# end of mcuboot (C:/ncs/v2.5.99-dev1/bootloader/mcuboot)

#
# mbedtls (C:/ncs/v2.5.99-dev1/modules/crypto/mbedtls)
#
CONFIG_ZEPHYR_MBEDTLS_MODULE=y

#
# PSA RNG support
#
# end of PSA RNG support

#
# PSA key type support
#
# end of PSA key type support

#
# PSA AEAD support
#
# end of PSA AEAD support

#
# PSA MAC support
#
# end of PSA MAC support

#
# PSA Hash support
#
# end of PSA Hash support

#
# PSA Cipher support
#
# end of PSA Cipher support

#
# PSA Key agreement support
#
# end of PSA Key agreement support

#
# PSA Key derivation support
#
# end of PSA Key derivation support

#
# PSA Asymmetric support
#

#
# Elliptic Curve type support
#
# end of Elliptic Curve type support
# end of PSA Asymmetric support
# end of mbedtls (C:/ncs/v2.5.99-dev1/modules/crypto/mbedtls)

#
# trusted-firmware-m (C:/ncs/v2.5.99-dev1/modules/tee/tf-m/trusted-firmware-m)
#
CONFIG_TFM_ISOLATION_LEVEL=1
# CONFIG_TFM_BL2 is not set
# CONFIG_TFM_REGRESSION_NS is not set
# CONFIG_TFM_LOG_LEVEL_SILENCE is not set
CONFIG_TFM_SECURE_UART=y
# CONFIG_TFM_SECURE_UART_SHARE_INSTANCE is not set
# CONFIG_TFM_SECURE_UART0 is not set
CONFIG_TFM_SECURE_UART1=y

#
# Peripheral Secure mapping
#
# CONFIG_NRF_FPU_SECURE is not set
# CONFIG_NRF_DCNF_SECURE is not set
# CONFIG_NRF_OSCILLATORS_SECURE is not set
# CONFIG_NRF_REGULATORS_SECURE is not set
# CONFIG_NRF_CLOCK_SECURE is not set
# CONFIG_NRF_POWER_SECURE is not set
# CONFIG_NRF_RESET_SECURE is not set
# CONFIG_NRF_CTRLAP_SECURE is not set
# CONFIG_NRF_SPIM0_SECURE is not set
# CONFIG_NRF_TWIM0_SECURE is not set
# CONFIG_NRF_UARTE0_SECURE is not set
# CONFIG_NRF_SPIM1_SECURE is not set
# CONFIG_NRF_TWIM1_SECURE is not set
CONFIG_NRF_UARTE1_SECURE=y
# CONFIG_NRF_SPIM4_SECURE is not set
# CONFIG_NRF_SPIM2_SECURE is not set
# CONFIG_NRF_TWIM2_SECURE is not set
# CONFIG_NRF_UARTE2_SECURE is not set
# CONFIG_NRF_SPIM3_SECURE is not set
# CONFIG_NRF_TWIM3_SECURE is not set
# CONFIG_NRF_UARTE3_SECURE is not set
# CONFIG_NRF_SAADC_SECURE is not set
# CONFIG_NRF_TIMER0_SECURE is not set
# CONFIG_NRF_TIMER1_SECURE is not set
# CONFIG_NRF_TIMER2_SECURE is not set
# CONFIG_NRF_RTC0_SECURE is not set
# CONFIG_NRF_RTC1_SECURE is not set
# CONFIG_NRF_DPPI_SECURE is not set
# CONFIG_NRF_WDT0_SECURE is not set
# CONFIG_NRF_WDT1_SECURE is not set
# CONFIG_NRF_COMP_SECURE is not set
# CONFIG_NRF_EGU0_SECURE is not set
# CONFIG_NRF_EGU1_SECURE is not set
# CONFIG_NRF_EGU2_SECURE is not set
# CONFIG_NRF_EGU3_SECURE is not set
# CONFIG_NRF_EGU4_SECURE is not set
# CONFIG_NRF_EGU5_SECURE is not set
# CONFIG_NRF_PWM0_SECURE is not set
# CONFIG_NRF_PWM1_SECURE is not set
# CONFIG_NRF_PWM2_SECURE is not set
# CONFIG_NRF_PWM3_SECURE is not set
# CONFIG_NRF_PDM_SECURE is not set
# CONFIG_NRF_I2S_SECURE is not set
# CONFIG_NRF_IPC_SECURE is not set
# CONFIG_NRF_QSPI_SECURE is not set
# CONFIG_NRF_NFCT_SECURE is not set
# CONFIG_NRF_MUTEX_SECURE is not set
# CONFIG_NRF_QDEC0_SECURE is not set
# CONFIG_NRF_QDEC1_SECURE is not set
# CONFIG_NRF_USBD_SECURE is not set
# CONFIG_NRF_USBREG_SECURE is not set
# CONFIG_NRF_NVMC_SECURE is not set
# CONFIG_NRF_GPIO0_SECURE is not set
# CONFIG_NRF_GPIO1_SECURE is not set
# CONFIG_NRF_VMC_SECURE is not set
# CONFIG_NRF_GPIOTE0_SECURE is not set
CONFIG_NRF_GPIO0_PIN_MASK_SECURE=0x00000000
CONFIG_NRF_GPIO1_PIN_MASK_SECURE=0x00000000
CONFIG_NRF_DPPI_CHANNEL_MASK_SECURE=0x00000000
# end of Peripheral Secure mapping

CONFIG_TFM_PROFILE_TYPE_MINIMAL=y
CONFIG_TFM_QCBOR_PATH="C:/ncs/v2.5.99-dev1/modules/tee/tf-m/trusted-firmware-m/../qcbor"
CONFIG_TFM_CRYPTO_ENGINE_BUF_SIZE=1
CONFIG_TFM_CRYPTO_CONC_OPER_NUM=1
CONFIG_TFM_CRYPTO_IOVEC_BUFFER_SIZE=1024
CONFIG_TFM_CRYPTO_PARTITION_STACK_SIZE=0x800
# CONFIG_TFM_ITS_ENCRYPTED is not set
# CONFIG_TFM_HW_INIT_RESET_ON_BOOT is not set
# CONFIG_TFM_HALT_ON_CORE_PANIC is not set
CONFIG_TFM_ALLOW_NON_SECURE_RESET=y
CONFIG_TFM_S_CODE_VECTOR_TABLE_SIZE=0x154
# CONFIG_BOOTLOADER_MCUBOOT is not set
CONFIG_ZEPHYR_TRUSTED_FIRMWARE_M_MODULE=y
CONFIG_BUILD_WITH_TFM=y
CONFIG_TFM_KEY_FILE_S="C:/ncs/v2.5.99-dev1/modules/tee/tf-m/trusted-firmware-m/bl2/ext/mcuboot/root-RSA-3072.pem"
CONFIG_TFM_KEY_FILE_NS="C:/ncs/v2.5.99-dev1/modules/tee/tf-m/trusted-firmware-m/bl2/ext/mcuboot/root-RSA-3072_1.pem"
# CONFIG_TFM_PROFILE_TYPE_NOT_SET is not set
# CONFIG_TFM_PROFILE_TYPE_SMALL is not set
# CONFIG_TFM_PROFILE_TYPE_MEDIUM is not set
# CONFIG_TFM_PROFILE_TYPE_LARGE is not set
# CONFIG_TFM_CMAKE_BUILD_TYPE_RELEASE is not set
# CONFIG_TFM_CMAKE_BUILD_TYPE_RELWITHDEBINFO is not set
CONFIG_TFM_CMAKE_BUILD_TYPE_MINSIZEREL=y
# CONFIG_TFM_CMAKE_BUILD_TYPE_DEBUG is not set
# CONFIG_TFM_ITS_NUM_ASSETS_OVERRIDE is not set
CONFIG_TFM_PARTITION_PLATFORM_CUSTOM_REBOOT=y
CONFIG_TFM_IMAGE_VERSION_S="0.0.0+0"
CONFIG_TFM_IMAGE_VERSION_NS="0.0.0+0"
# CONFIG_TFM_BUILD_NS is not set
# CONFIG_TFM_CONNECTION_BASED_SERVICE_API is not set
CONFIG_TFM_IPC=y
# CONFIG_TFM_SFN is not set
# CONFIG_TFM_REGRESSION_S is not set
# CONFIG_TFM_PSA_TEST_PROTECTED_STORAGE is not set
# CONFIG_TFM_PSA_TEST_INTERNAL_TRUSTED_STORAGE is not set
# CONFIG_TFM_PSA_TEST_STORAGE is not set
CONFIG_TFM_PSA_TEST_NONE=y
CONFIG_TFM_FLASH_MERGED_BINARY=y
# CONFIG_TFM_SPM_LOG_LEVEL_DEBUG is not set
CONFIG_TFM_SPM_LOG_LEVEL_INFO=y
# CONFIG_TFM_SPM_LOG_LEVEL_ERROR is not set
# CONFIG_TFM_SPM_LOG_LEVEL_SILENCE is not set
CONFIG_TFM_EXCEPTION_INFO_DUMP=y
CONFIG_PM_PARTITION_SIZE_TFM_SRAM=0x8000
CONFIG_PM_PARTITION_SIZE_BL2=0
CONFIG_PM_PARTITION_SIZE_TFM=0x8000
CONFIG_PM_PARTITION_SIZE_TFM_PROTECTED_STORAGE=0
CONFIG_PM_PARTITION_SIZE_TFM_INTERNAL_TRUSTED_STORAGE=0
CONFIG_PM_PARTITION_SIZE_TFM_OTP_NV_COUNTERS=0
# CONFIG_TFM_PSA_FRAMEWORK_HAS_MM_IOVEC is not set
# end of trusted-firmware-m (C:/ncs/v2.5.99-dev1/modules/tee/tf-m/trusted-firmware-m)

#
# cjson (C:/ncs/v2.5.99-dev1/modules/lib/cjson)
#
# CONFIG_CJSON_LIB is not set
CONFIG_ZEPHYR_CJSON_MODULE=y
# end of cjson (C:/ncs/v2.5.99-dev1/modules/lib/cjson)

#
# azure-sdk-for-c (C:/ncs/v2.5.99-dev1/modules/lib/azure-sdk-for-c)
#
# CONFIG_AZURE_SDK is not set
CONFIG_ZEPHYR_AZURE_SDK_FOR_C_MODULE=y
# end of azure-sdk-for-c (C:/ncs/v2.5.99-dev1/modules/lib/azure-sdk-for-c)

#
# cirrus-logic (C:/ncs/v2.5.99-dev1/modules/hal/cirrus-logic)
#
# CONFIG_HW_CODEC_CIRRUS_LOGIC is not set
CONFIG_ZEPHYR_CIRRUS_LOGIC_MODULE=y
# end of cirrus-logic (C:/ncs/v2.5.99-dev1/modules/hal/cirrus-logic)

#
# openthread (C:/ncs/v2.5.99-dev1/modules/lib/openthread)
#
# CONFIG_OPENTHREAD is not set
CONFIG_ZEPHYR_OPENTHREAD_MODULE=y
# end of openthread (C:/ncs/v2.5.99-dev1/modules/lib/openthread)

#
# memfault-firmware-sdk (C:/ncs/v2.5.99-dev1/modules/lib/memfault-firmware-sdk)
#
# CONFIG_MEMFAULT is not set
CONFIG_ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE=y
# end of memfault-firmware-sdk (C:/ncs/v2.5.99-dev1/modules/lib/memfault-firmware-sdk)

#
# canopennode (C:/ncs/v2.5.99-dev1/modules/lib/canopennode)
#
CONFIG_ZEPHYR_CANOPENNODE_MODULE=y
# end of canopennode (C:/ncs/v2.5.99-dev1/modules/lib/canopennode)

#
# chre (C:/ncs/v2.5.99-dev1/modules/lib/chre)
#
CONFIG_ZEPHYR_CHRE_MODULE=y
# CONFIG_CHRE is not set
# end of chre (C:/ncs/v2.5.99-dev1/modules/lib/chre)

#
# cmsis (C:/ncs/v2.5.99-dev1/modules/hal/cmsis)
#
CONFIG_HAS_CMSIS_CORE=y
CONFIG_HAS_CMSIS_CORE_M=y
# CONFIG_CMSIS_DSP is not set
# CONFIG_CMSIS_NN is not set
CONFIG_ZEPHYR_CMSIS_MODULE=y
# end of cmsis (C:/ncs/v2.5.99-dev1/modules/hal/cmsis)

#
# fatfs (C:/ncs/v2.5.99-dev1/modules/fs/fatfs)
#
CONFIG_ZEPHYR_FATFS_MODULE=y
# end of fatfs (C:/ncs/v2.5.99-dev1/modules/fs/fatfs)

#
# hal_nordic (C:/ncs/v2.5.99-dev1/modules/hal/nordic)
#
CONFIG_ZEPHYR_HAL_NORDIC_MODULE=y
CONFIG_HAS_NORDIC_DRIVERS=y

#
# Nordic drivers
#
# CONFIG_NRF_802154_SOURCE_HAL_NORDIC is not set
# CONFIG_NRF_802154_SER_HOST is not set
# end of Nordic drivers

CONFIG_HAS_NRFX=y

#
# nrfx drivers
#

#
# nrfx drivers logging
#
# CONFIG_NRFX_CLOCK_LOG is not set
# CONFIG_NRFX_DPPI_LOG is not set
# CONFIG_NRFX_GPIOTE_LOG is not set
# CONFIG_NRFX_IPC_LOG is not set
# CONFIG_NRFX_NVMC_LOG is not set
# CONFIG_NRFX_QSPI_LOG is not set
# CONFIG_NRFX_TWIM_LOG is not set
# end of nrfx drivers logging

CONFIG_NRFX_CLOCK=y
CONFIG_NRFX_CLOCK_LFXO_TWO_STAGE_ENABLED=y
# CONFIG_NRFX_COMP is not set
CONFIG_NRFX_DPPI=y
# CONFIG_NRFX_EGU0 is not set
# CONFIG_NRFX_EGU1 is not set
# CONFIG_NRFX_EGU2 is not set
# CONFIG_NRFX_EGU3 is not set
# CONFIG_NRFX_EGU4 is not set
# CONFIG_NRFX_EGU5 is not set
CONFIG_NRFX_GPIOTE=y
# CONFIG_NRFX_I2S0 is not set
CONFIG_NRFX_IPC=y
# CONFIG_NRFX_NFCT is not set
CONFIG_NRFX_NVMC=y
# CONFIG_NRFX_PDM is not set
# CONFIG_NRFX_POWER is not set
# CONFIG_NRFX_PWM0 is not set
# CONFIG_NRFX_PWM1 is not set
# CONFIG_NRFX_PWM2 is not set
# CONFIG_NRFX_PWM3 is not set
# CONFIG_NRFX_QDEC0 is not set
# CONFIG_NRFX_QDEC1 is not set
CONFIG_NRFX_QSPI=y
# CONFIG_NRFX_RTC0 is not set
# CONFIG_NRFX_RTC1 is not set
# CONFIG_NRFX_SAADC is not set
# CONFIG_NRFX_SPIM0 is not set
# CONFIG_NRFX_SPIM1 is not set
# CONFIG_NRFX_SPIM2 is not set
# CONFIG_NRFX_SPIM3 is not set
# CONFIG_NRFX_SPIM4 is not set
# CONFIG_NRFX_SYSTICK is not set
# CONFIG_NRFX_TIMER0 is not set
# CONFIG_NRFX_TIMER1 is not set
# CONFIG_NRFX_TIMER2 is not set
CONFIG_NRFX_TWIM=y
# CONFIG_NRFX_TWIM0 is not set
# CONFIG_NRFX_TWIM1 is not set
CONFIG_NRFX_TWIM2=y
# CONFIG_NRFX_TWIM3 is not set
# CONFIG_NRFX_UARTE0 is not set
# CONFIG_NRFX_UARTE1 is not set
# CONFIG_NRFX_UARTE2 is not set
# CONFIG_NRFX_UARTE3 is not set
# CONFIG_NRFX_USBD is not set
# CONFIG_NRFX_USBREG is not set
# CONFIG_NRFX_WDT0 is not set
# CONFIG_NRFX_WDT1 is not set

#
# Peripheral Resource Sharing module
#
# CONFIG_NRFX_PRS_BOX_0 is not set
# CONFIG_NRFX_PRS_BOX_1 is not set
# CONFIG_NRFX_PRS_BOX_2 is not set
# CONFIG_NRFX_PRS_BOX_3 is not set
# CONFIG_NRFX_PRS_BOX_4 is not set
# end of Peripheral Resource Sharing module
# end of nrfx drivers
# end of hal_nordic (C:/ncs/v2.5.99-dev1/modules/hal/nordic)

#
# liblc3 (C:/ncs/v2.5.99-dev1/modules/lib/liblc3)
#
CONFIG_ZEPHYR_LIBLC3_MODULE=y
# end of liblc3 (C:/ncs/v2.5.99-dev1/modules/lib/liblc3)

#
# littlefs (C:/ncs/v2.5.99-dev1/modules/fs/littlefs)
#
CONFIG_ZEPHYR_LITTLEFS_MODULE=y
# end of littlefs (C:/ncs/v2.5.99-dev1/modules/fs/littlefs)

#
# loramac-node (C:/ncs/v2.5.99-dev1/modules/lib/loramac-node)
#
CONFIG_ZEPHYR_LORAMAC_NODE_MODULE=y
# CONFIG_HAS_SEMTECH_RADIO_DRIVERS is not set
# end of loramac-node (C:/ncs/v2.5.99-dev1/modules/lib/loramac-node)

#
# lvgl (C:/ncs/v2.5.99-dev1/modules/lib/gui/lvgl)
#

#
# LVGL configuration
#
# CONFIG_LV_CONF_SKIP is not set
# CONFIG_LV_CONF_MINIMAL is not set

#
# Color settings
#
# CONFIG_LV_COLOR_DEPTH_32 is not set
CONFIG_LV_COLOR_DEPTH_16=y
# CONFIG_LV_COLOR_DEPTH_8 is not set
# CONFIG_LV_COLOR_DEPTH_1 is not set
CONFIG_LV_COLOR_DEPTH=16
# CONFIG_LV_COLOR_SCREEN_TRANSP is not set
CONFIG_LV_COLOR_MIX_ROUND_OFS=128
CONFIG_LV_COLOR_CHROMA_KEY_HEX=0x00FF00
# end of Color settings

#
# Memory settings
#
# CONFIG_LV_MEM_CUSTOM is not set
CONFIG_LV_MEM_SIZE_KILOBYTES=32
CONFIG_LV_MEM_ADDR=0x0
CONFIG_LV_MEM_BUF_MAX_NUM=16
# CONFIG_LV_MEMCPY_MEMSET_STD is not set
# end of Memory settings

#
# HAL Settings
#
CONFIG_LV_DISP_DEF_REFR_PERIOD=30
CONFIG_LV_INDEV_DEF_READ_PERIOD=30
# CONFIG_LV_TICK_CUSTOM is not set
# end of HAL Settings

#
# Feature configuration
#

#
# Drawing
#
CONFIG_LV_DRAW_COMPLEX=y
CONFIG_LV_SHADOW_CACHE_SIZE=0
CONFIG_LV_CIRCLE_CACHE_SIZE=4
CONFIG_LV_LAYER_SIMPLE_BUF_SIZE=24576
CONFIG_LV_IMG_CACHE_DEF_SIZE=0
CONFIG_LV_GRADIENT_MAX_STOPS=2
CONFIG_LV_GRAD_CACHE_DEF_SIZE=0
# CONFIG_LV_DITHER_GRADIENT is not set
CONFIG_LV_DISP_ROT_MAX_BUF=10240
# end of Drawing

#
# GPU
#
# CONFIG_LV_USE_GPU_ARM2D is not set
# CONFIG_LV_USE_GPU_STM32_DMA2D is not set
# CONFIG_LV_USE_GPU_SWM341_DMA2D is not set
# CONFIG_LV_USE_GPU_NXP_PXP is not set
# CONFIG_LV_USE_GPU_NXP_VG_LITE is not set
# CONFIG_LV_USE_GPU_SDL is not set
# end of GPU

#
# Logging
#
# CONFIG_LV_USE_LOG is not set
# end of Logging

#
# Asserts
#
CONFIG_LV_USE_ASSERT_NULL=y
CONFIG_LV_USE_ASSERT_MALLOC=y
# CONFIG_LV_USE_ASSERT_STYLE is not set
# CONFIG_LV_USE_ASSERT_MEM_INTEGRITY is not set
# CONFIG_LV_USE_ASSERT_OBJ is not set
CONFIG_LV_ASSERT_HANDLER_INCLUDE="assert.h"
# end of Asserts

#
# Others
#
# CONFIG_LV_USE_PERF_MONITOR is not set
# CONFIG_LV_USE_MEM_MONITOR is not set
# CONFIG_LV_USE_REFR_DEBUG is not set
# CONFIG_LV_SPRINTF_CUSTOM is not set
# CONFIG_LV_SPRINTF_USE_FLOAT is not set
CONFIG_LV_USE_USER_DATA=y
# CONFIG_LV_ENABLE_GC is not set
# end of Others

#
# Compiler settings
#
# CONFIG_LV_BIG_ENDIAN_SYSTEM is not set
CONFIG_LV_ATTRIBUTE_MEM_ALIGN_SIZE=1
# CONFIG_LV_ATTRIBUTE_FAST_MEM_USE_IRAM is not set
# CONFIG_LV_USE_LARGE_COORD is not set
# end of Compiler settings
# end of Feature configuration

#
# Font usage
#

#
# Enable built-in fonts
#
# CONFIG_LV_FONT_MONTSERRAT_8 is not set
# CONFIG_LV_FONT_MONTSERRAT_10 is not set
# CONFIG_LV_FONT_MONTSERRAT_12 is not set
CONFIG_LV_FONT_MONTSERRAT_14=y
# CONFIG_LV_FONT_MONTSERRAT_16 is not set
# CONFIG_LV_FONT_MONTSERRAT_18 is not set
# CONFIG_LV_FONT_MONTSERRAT_20 is not set
# CONFIG_LV_FONT_MONTSERRAT_22 is not set
# CONFIG_LV_FONT_MONTSERRAT_24 is not set
# CONFIG_LV_FONT_MONTSERRAT_26 is not set
# CONFIG_LV_FONT_MONTSERRAT_28 is not set
# CONFIG_LV_FONT_MONTSERRAT_30 is not set
# CONFIG_LV_FONT_MONTSERRAT_32 is not set
# CONFIG_LV_FONT_MONTSERRAT_34 is not set
# CONFIG_LV_FONT_MONTSERRAT_36 is not set
# CONFIG_LV_FONT_MONTSERRAT_38 is not set
# CONFIG_LV_FONT_MONTSERRAT_40 is not set
# CONFIG_LV_FONT_MONTSERRAT_42 is not set
# CONFIG_LV_FONT_MONTSERRAT_44 is not set
# CONFIG_LV_FONT_MONTSERRAT_46 is not set
# CONFIG_LV_FONT_MONTSERRAT_48 is not set
# CONFIG_LV_FONT_MONTSERRAT_12_SUBPX is not set
# CONFIG_LV_FONT_MONTSERRAT_28_COMPRESSED is not set
# CONFIG_LV_FONT_DEJAVU_16_PERSIAN_HEBREW is not set
# CONFIG_LV_FONT_SIMSUN_16_CJK is not set
# CONFIG_LV_FONT_UNSCII_8 is not set
# CONFIG_LV_FONT_UNSCII_16 is not set
# CONFIG_LV_FONT_CUSTOM is not set
# end of Enable built-in fonts

# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_8 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_12 is not set
CONFIG_LV_FONT_DEFAULT_MONTSERRAT_14=y
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_16 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_18 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_20 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_22 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_24 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_26 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_28 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_30 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_32 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_34 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_36 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_38 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_40 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_42 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_44 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_46 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_48 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_12_SUBPX is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_28_COMPRESSED is not set
# CONFIG_LV_FONT_DEFAULT_DEJAVU_16_PERSIAN_HEBREW is not set
# CONFIG_LV_FONT_DEFAULT_SIMSUN_16_CJK is not set
# CONFIG_LV_FONT_DEFAULT_UNSCII_8 is not set
# CONFIG_LV_FONT_DEFAULT_UNSCII_16 is not set
# CONFIG_LV_FONT_FMT_TXT_LARGE is not set
# CONFIG_LV_USE_FONT_COMPRESSED is not set
# CONFIG_LV_USE_FONT_SUBPX is not set
CONFIG_LV_USE_FONT_PLACEHOLDER=y
# end of Font usage

#
# Text Settings
#
CONFIG_LV_TXT_ENC_UTF8=y
# CONFIG_LV_TXT_ENC_ASCII is not set
CONFIG_LV_TXT_BREAK_CHARS=" ,.;:-_"
CONFIG_LV_TXT_LINE_BREAK_LONG_LEN=0
CONFIG_LV_TXT_COLOR_CMD="#"
# CONFIG_LV_USE_BIDI is not set
# CONFIG_LV_USE_ARABIC_PERSIAN_CHARS is not set
# end of Text Settings

#
# Widget usage
#
CONFIG_LV_USE_ARC=y
CONFIG_LV_USE_BAR=y
CONFIG_LV_USE_BTN=y
CONFIG_LV_USE_BTNMATRIX=y
CONFIG_LV_USE_CANVAS=y
CONFIG_LV_USE_CHECKBOX=y
CONFIG_LV_USE_DROPDOWN=y
CONFIG_LV_USE_IMG=y
CONFIG_LV_USE_LABEL=y
CONFIG_LV_LABEL_TEXT_SELECTION=y
CONFIG_LV_LABEL_LONG_TXT_HINT=y
CONFIG_LV_USE_LINE=y
CONFIG_LV_USE_ROLLER=y
CONFIG_LV_ROLLER_INF_PAGES=7
CONFIG_LV_USE_SLIDER=y
CONFIG_LV_USE_SWITCH=y
CONFIG_LV_USE_TEXTAREA=y
CONFIG_LV_TEXTAREA_DEF_PWD_SHOW_TIME=1500
CONFIG_LV_USE_TABLE=y
# end of Widget usage

#
# Extra Widgets
#
CONFIG_LV_USE_ANIMIMG=y
CONFIG_LV_USE_CALENDAR=y
# CONFIG_LV_CALENDAR_WEEK_STARTS_MONDAY is not set
CONFIG_LV_USE_CALENDAR_HEADER_ARROW=y
CONFIG_LV_USE_CALENDAR_HEADER_DROPDOWN=y
CONFIG_LV_USE_CHART=y
CONFIG_LV_USE_COLORWHEEL=y
CONFIG_LV_USE_IMGBTN=y
CONFIG_LV_USE_KEYBOARD=y
CONFIG_LV_USE_LED=y
CONFIG_LV_USE_LIST=y
CONFIG_LV_USE_MENU=y
CONFIG_LV_USE_METER=y
CONFIG_LV_USE_MSGBOX=y
CONFIG_LV_USE_SPAN=y
CONFIG_LV_SPAN_SNIPPET_STACK_SIZE=64
CONFIG_LV_USE_SPINBOX=y
CONFIG_LV_USE_SPINNER=y
CONFIG_LV_USE_TABVIEW=y
CONFIG_LV_USE_TILEVIEW=y
CONFIG_LV_USE_WIN=y
# end of Extra Widgets

#
# Themes
#
CONFIG_LV_USE_THEME_DEFAULT=y
# CONFIG_LV_THEME_DEFAULT_DARK is not set
CONFIG_LV_THEME_DEFAULT_GROW=y
CONFIG_LV_THEME_DEFAULT_TRANSITION_TIME=80
CONFIG_LV_USE_THEME_BASIC=y
# CONFIG_LV_USE_THEME_MONO is not set
# end of Themes

#
# Layouts
#
CONFIG_LV_USE_FLEX=y
CONFIG_LV_USE_GRID=y
# end of Layouts

#
# 3rd Party Libraries
#
# CONFIG_LV_USE_FS_STDIO is not set
# CONFIG_LV_USE_FS_POSIX is not set
# CONFIG_LV_USE_FS_WIN32 is not set
# CONFIG_LV_USE_FS_FATFS is not set
# CONFIG_LV_USE_PNG is not set
# CONFIG_LV_USE_BMP is not set
# CONFIG_LV_USE_SJPG is not set
# CONFIG_LV_USE_GIF is not set
# CONFIG_LV_USE_QRCODE is not set
# CONFIG_LV_USE_FREETYPE is not set
# CONFIG_LV_USE_RLOTTIE is not set
# CONFIG_LV_USE_FFMPEG is not set
# end of 3rd Party Libraries

#
# Others
#
CONFIG_LV_USE_SNAPSHOT=y
# CONFIG_LV_USE_MONKEY is not set
# CONFIG_LV_USE_GRIDNAV is not set
# CONFIG_LV_USE_FRAGMENT is not set
# CONFIG_LV_USE_IMGFONT is not set
# CONFIG_LV_USE_MSG is not set
# CONFIG_LV_USE_IME_PINYIN is not set
# end of Others

#
# Examples
#
CONFIG_LV_BUILD_EXAMPLES=y
# end of Examples

#
# Demos
#
# CONFIG_LV_USE_DEMO_WIDGETS is not set
# CONFIG_LV_USE_DEMO_KEYPAD_AND_ENCODER is not set
# CONFIG_LV_USE_DEMO_BENCHMARK is not set
# CONFIG_LV_USE_DEMO_STRESS is not set
# CONFIG_LV_USE_DEMO_MUSIC is not set
# end of Demos
# end of LVGL configuration

CONFIG_ZEPHYR_LVGL_MODULE=y
# end of lvgl (C:/ncs/v2.5.99-dev1/modules/lib/gui/lvgl)

#
# lz4 (C:/ncs/v2.5.99-dev1/modules/lib/lz4)
#
CONFIG_ZEPHYR_LZ4_MODULE=y
# CONFIG_LZ4 is not set
# end of lz4 (C:/ncs/v2.5.99-dev1/modules/lib/lz4)

#
# nanopb (C:/ncs/v2.5.99-dev1/modules/lib/nanopb)
#
CONFIG_ZEPHYR_NANOPB_MODULE=y
# CONFIG_NANOPB is not set
# end of nanopb (C:/ncs/v2.5.99-dev1/modules/lib/nanopb)

#
# picolibc (C:/ncs/v2.5.99-dev1/modules/lib/picolibc)
#
# CONFIG_PICOLIBC_MODULE is not set
CONFIG_ZEPHYR_PICOLIBC_MODULE=y
# end of picolibc (C:/ncs/v2.5.99-dev1/modules/lib/picolibc)

#
# segger (C:/ncs/v2.5.99-dev1/modules/debug/segger)
#
CONFIG_ZEPHYR_SEGGER_MODULE=y
CONFIG_HAS_SEGGER_RTT=y
CONFIG_USE_SEGGER_RTT=y
CONFIG_SEGGER_RTT_CUSTOM_LOCKING=y
CONFIG_SEGGER_RTT_MAX_NUM_UP_BUFFERS=3
CONFIG_SEGGER_RTT_MAX_NUM_DOWN_BUFFERS=3
CONFIG_SEGGER_RTT_BUFFER_SIZE_UP=1024
CONFIG_SEGGER_RTT_BUFFER_SIZE_DOWN=16
CONFIG_SEGGER_RTT_PRINTF_BUFFER_SIZE=64
CONFIG_SEGGER_RTT_MODE_NO_BLOCK_SKIP=y
# CONFIG_SEGGER_RTT_MODE_NO_BLOCK_TRIM is not set
# CONFIG_SEGGER_RTT_MODE_BLOCK_IF_FIFO_FULL is not set
CONFIG_SEGGER_RTT_MODE=0
# CONFIG_SEGGER_RTT_MEMCPY_USE_BYTELOOP is not set
CONFIG_SEGGER_RTT_SECTION_NONE=y
# CONFIG_SEGGER_RTT_SECTION_DTCM is not set
# end of segger (C:/ncs/v2.5.99-dev1/modules/debug/segger)

#
# TraceRecorder (C:/ncs/v2.5.99-dev1/modules/debug/TraceRecorder)
#
CONFIG_ZEPHYR_TRACERECORDER_MODULE=y
# end of TraceRecorder (C:/ncs/v2.5.99-dev1/modules/debug/TraceRecorder)

#
# uoscore-uedhoc (C:/ncs/v2.5.99-dev1/modules/lib/uoscore-uedhoc)
#
CONFIG_ZEPHYR_UOSCORE_UEDHOC_MODULE=y
# end of uoscore-uedhoc (C:/ncs/v2.5.99-dev1/modules/lib/uoscore-uedhoc)

#
# zcbor (C:/ncs/v2.5.99-dev1/modules/lib/zcbor)
#
CONFIG_ZEPHYR_ZCBOR_MODULE=y
# CONFIG_ZCBOR is not set
# end of zcbor (C:/ncs/v2.5.99-dev1/modules/lib/zcbor)

#
# zscilib (C:/ncs/v2.5.99-dev1/modules/lib/zscilib)
#
# CONFIG_ZSL is not set
CONFIG_ZEPHYR_ZSCILIB_MODULE=y
# end of zscilib (C:/ncs/v2.5.99-dev1/modules/lib/zscilib)

#
# nrfxlib (C:/ncs/v2.5.99-dev1/nrfxlib)
#

#
# Nordic nrfxlib
#

#
# nrf_modem (Modem library)
#
# end of nrf_modem (Modem library)

CONFIG_NRF_MODEM_SHMEM_CTRL_SIZE=0x4e8
# CONFIG_NFC_T2T_NRFXLIB is not set
# CONFIG_NFC_T4T_NRFXLIB is not set

#
# Crypto libraries for nRF5x SOCs.
#
CONFIG_HAS_HW_NRF_CC3XX=y
CONFIG_NRF_OBERON=y
# end of Crypto libraries for nRF5x SOCs.

# CONFIG_NRF_RPC is not set
CONFIG_NRF_802154_SOURCE_NRFXLIB=y
# CONFIG_GZLL is not set
# CONFIG_NRF_DM is not set
# CONFIG_LC3_PLC_DISABLED is not set
CONFIG_LC3_ENC_CHAN_MAX=1
CONFIG_LC3_DEC_CHAN_MAX=1

#
# Encoder sample rates
#
CONFIG_LC3_ENC_SAMPLE_RATE_8KHZ_SUPPORT=y
CONFIG_LC3_ENC_SAMPLE_RATE_16KHZ_SUPPORT=y
CONFIG_LC3_ENC_SAMPLE_RATE_24KHZ_SUPPORT=y
CONFIG_LC3_ENC_SAMPLE_RATE_32KHZ_SUPPORT=y
CONFIG_LC3_ENC_SAMPLE_RATE_441KHZ_SUPPORT=y
CONFIG_LC3_ENC_SAMPLE_RATE_48KHZ_SUPPORT=y
# end of Encoder sample rates

#
# Decoder sample rates
#
CONFIG_LC3_DEC_SAMPLE_RATE_8KHZ_SUPPORT=y
CONFIG_LC3_DEC_SAMPLE_RATE_16KHZ_SUPPORT=y
CONFIG_LC3_DEC_SAMPLE_RATE_24KHZ_SUPPORT=y
CONFIG_LC3_DEC_SAMPLE_RATE_32KHZ_SUPPORT=y
CONFIG_LC3_DEC_SAMPLE_RATE_441KHZ_SUPPORT=y
CONFIG_LC3_DEC_SAMPLE_RATE_48KHZ_SUPPORT=y
# end of Decoder sample rates

# CONFIG_NRF_FUEL_GAUGE is not set
# end of Nordic nrfxlib

CONFIG_ZEPHYR_NRFXLIB_MODULE=y
# end of nrfxlib (C:/ncs/v2.5.99-dev1/nrfxlib)

#
# connectedhomeip (C:/ncs/v2.5.99-dev1/modules/lib/matter)
#
# CONFIG_CHIP is not set
CONFIG_ZEPHYR_CONNECTEDHOMEIP_MODULE=y
# end of connectedhomeip (C:/ncs/v2.5.99-dev1/modules/lib/matter)

#
# Optional modules. Make sure they're installed, via the project manifest.
#
CONFIG_LIBMETAL=y
CONFIG_LIBMETAL_SRC_PATH="libmetal"
# CONFIG_LVGL is not set
# CONFIG_HAS_MEC_HAL is not set
# CONFIG_HAS_MPFS_HAL is not set
CONFIG_OPENAMP=y
CONFIG_OPENAMP_SRC_PATH="open-amp"
CONFIG_OPENAMP_MASTER=y
CONFIG_OPENAMP_SLAVE=y
# CONFIG_SOF is not set
# CONFIG_MIPI_SYST_LIB is not set
# CONFIG_HAS_TELINK_DRIVERS is not set
# CONFIG_TINYCRYPT_CTR_PRNG is not set
CONFIG_TINYCRYPT_SHA256=y
CONFIG_TINYCRYPT_SHA256_HMAC=y
CONFIG_TINYCRYPT_SHA256_HMAC_PRNG=y
# CONFIG_TINYCRYPT_ECC_DH is not set
# CONFIG_TINYCRYPT_ECC_DSA is not set
CONFIG_TINYCRYPT_AES=y
# CONFIG_TINYCRYPT_AES_CBC is not set
# CONFIG_TINYCRYPT_AES_CTR is not set
# CONFIG_TINYCRYPT_AES_CCM is not set
CONFIG_TINYCRYPT_AES_CMAC=y
# CONFIG_MCUBOOT_BOOTUTIL_LIB is not set

#
# Unavailable modules, please install those via the project manifest.
#

#
# hal_gigadevice module not available.
#

#
# Trusted-firmware-a module not available.
#

#
# THRIFT module not available.
#
# CONFIG_ACPI is not set
# end of Modules

CONFIG_BOARD_REVISION="$BOARD_REVISION"
# CONFIG_NET_DRIVERS is not set
# CONFIG_BOARD_NRF5340DK_NRF5340_CPUAPP is not set
CONFIG_BOARD_NRF5340DK_NRF5340_CPUAPP_NS=y

#
# Board Options
#
CONFIG_BOARD_ENABLE_DCDC_APP=y
CONFIG_BOARD_ENABLE_DCDC_NET=y
CONFIG_BOARD_ENABLE_DCDC_HV=y
CONFIG_BOARD_ENABLE_CPUNET=y
CONFIG_DOMAIN_CPUNET_BOARD="nrf5340dk_nrf5340_cpunet"
# end of Board Options

# CONFIG_SOC_SERIES_APOLLO4X is not set
# CONFIG_SOC_SERIES_BEETLE is not set
# CONFIG_SOC_SERIES_ARM_DESIGNSTART is not set
# CONFIG_SOC_SERIES_FVP_AEMV8R_AARCH32 is not set
# CONFIG_SOC_SERIES_MPS2 is not set
# CONFIG_SOC_SERIES_MPS3 is not set
# CONFIG_SOC_SERIES_MUSCA_B1 is not set
# CONFIG_SOC_SERIES_MUSCA_S1 is not set
# CONFIG_SOC_SERIES_AST10X0 is not set
# CONFIG_SOC_SERIES_SAMC20 is not set
# CONFIG_SOC_SERIES_SAMC21 is not set
# CONFIG_SOC_SERIES_SAMD20 is not set
# CONFIG_SOC_SERIES_SAMD21 is not set
# CONFIG_SOC_SERIES_SAMD51 is not set
# CONFIG_SOC_SERIES_SAME51 is not set
# CONFIG_SOC_SERIES_SAME53 is not set
# CONFIG_SOC_SERIES_SAME54 is not set
# CONFIG_SOC_SERIES_SAML21 is not set
# CONFIG_SOC_SERIES_SAMR21 is not set
# CONFIG_SOC_SERIES_SAMR34 is not set
# CONFIG_SOC_SERIES_SAMR35 is not set
# CONFIG_SOC_SERIES_SAM3X is not set
# CONFIG_SOC_SERIES_SAM4E is not set
# CONFIG_SOC_SERIES_SAM4L is not set
# CONFIG_SOC_SERIES_SAM4S is not set
# CONFIG_SOC_SERIES_SAME70 is not set
# CONFIG_SOC_SERIES_SAMV71 is not set
# CONFIG_SOC_SERIES_VALKYRIE is not set
# CONFIG_SOC_SERIES_VIPER is not set
# CONFIG_SOC_SERIES_PSOC62 is not set
# CONFIG_SOC_SERIES_PSOC63 is not set
# CONFIG_SOC_SERIES_GD32A50X is not set
# CONFIG_SOC_SERIES_GD32E10X is not set
# CONFIG_SOC_SERIES_GD32E50X is not set
# CONFIG_SOC_SERIES_GD32F3X0 is not set
# CONFIG_SOC_SERIES_GD32F403 is not set
# CONFIG_SOC_SERIES_GD32F4XX is not set
# CONFIG_SOC_SERIES_GD32L23X is not set
# CONFIG_SOC_SERIES_PSOC_60 is not set
# CONFIG_SOC_SERIES_PSOC_61 is not set
# CONFIG_SOC_SERIES_PSOC_62 is not set
# CONFIG_SOC_SERIES_PSOC_63 is not set
# CONFIG_SOC_SERIES_PSOC_64 is not set
# CONFIG_SOC_SERIES_XMC_4XXX is not set
# CONFIG_SOC_SERIES_CYCLONE5 is not set
# CONFIG_SOC_SERIES_MEC1501X is not set
# CONFIG_SOC_SERIES_MEC172X is not set
# CONFIG_SOC_SERIES_NRF51X is not set
# CONFIG_SOC_SERIES_NRF52X is not set
CONFIG_SOC_SERIES_NRF53X=y
# CONFIG_SOC_SERIES_NRF91X is not set
# CONFIG_SOC_SERIES_NPCX4 is not set
# CONFIG_SOC_SERIES_NPCX7 is not set
# CONFIG_SOC_SERIES_NPCX9 is not set
# CONFIG_SOC_SERIES_M46X is not set
# CONFIG_SOC_SERIES_M48X is not set
# CONFIG_SOC_SERIES_IMX_6X_M4 is not set
# CONFIG_SOC_SERIES_IMX7_M4 is not set
# CONFIG_SOC_SERIES_IMX8ML_M7 is not set
# CONFIG_SOC_SERIES_IMX8MM_M4 is not set
# CONFIG_SOC_SERIES_IMX8MQ_M4 is not set
# CONFIG_SOC_SERIES_IMX_RT5XX is not set
# CONFIG_SOC_SERIES_IMX_RT6XX is not set
# CONFIG_SOC_SERIES_IMX_RT is not set
# CONFIG_SOC_SERIES_KINETIS_K2X is not set
# CONFIG_SOC_SERIES_KINETIS_K6X is not set
# CONFIG_SOC_SERIES_KINETIS_K8X is not set
# CONFIG_SOC_SERIES_KINETIS_KE1XF is not set
# CONFIG_SOC_SERIES_KINETIS_KL2X is not set
# CONFIG_SOC_SERIES_KINETIS_KV5X is not set
# CONFIG_SOC_SERIES_KINETIS_KWX is not set
# CONFIG_SOC_SERIES_LPC11U6X is not set
# CONFIG_SOC_SERIES_LPC51U68 is not set
# CONFIG_SOC_SERIES_LPC54XXX is not set
# CONFIG_SOC_SERIES_LPC55XXX is not set
# CONFIG_SOC_SERIES_S32K3_M7 is not set
# CONFIG_SOC_SERIES_S32ZE_R52 is not set
# CONFIG_SOC_EOS_S3 is not set
# CONFIG_SOC_SERIES_RCAR_GEN3 is not set
# CONFIG_SOC_SERIES_DA1469X is not set
# CONFIG_SOC_SERIES_RP2XXX is not set
# CONFIG_SOC_SERIES_EFM32GG11B is not set
# CONFIG_SOC_SERIES_EFM32GG12B is not set
# CONFIG_SOC_SERIES_EFM32HG is not set
# CONFIG_SOC_SERIES_EFM32JG12B is not set
# CONFIG_SOC_SERIES_EFM32PG12B is not set
# CONFIG_SOC_SERIES_EFM32PG1B is not set
# CONFIG_SOC_SERIES_EFM32WG is not set
# CONFIG_SOC_SERIES_EFR32BG13P is not set
# CONFIG_SOC_SERIES_EFR32BG22 is not set
# CONFIG_SOC_SERIES_EFR32BG27 is not set
# CONFIG_SOC_SERIES_EFR32FG13P is not set
# CONFIG_SOC_SERIES_EFR32FG1P is not set
# CONFIG_SOC_SERIES_EFR32MG12P is not set
# CONFIG_SOC_SERIES_EFR32MG21 is not set
# CONFIG_SOC_SERIES_EFR32MG24 is not set
# CONFIG_SOC_SERIES_STM32C0X is not set
# CONFIG_SOC_SERIES_STM32F0X is not set
# CONFIG_SOC_SERIES_STM32F1X is not set
# CONFIG_SOC_SERIES_STM32F2X is not set
# CONFIG_SOC_SERIES_STM32F3X is not set
# CONFIG_SOC_SERIES_STM32F4X is not set
# CONFIG_SOC_SERIES_STM32F7X is not set
# CONFIG_SOC_SERIES_STM32G0X is not set
# CONFIG_SOC_SERIES_STM32G4X is not set
# CONFIG_SOC_SERIES_STM32H5X is not set
# CONFIG_SOC_SERIES_STM32H7X is not set
# CONFIG_SOC_SERIES_STM32L0X is not set
# CONFIG_SOC_SERIES_STM32L1X is not set
# CONFIG_SOC_SERIES_STM32L4X is not set
# CONFIG_SOC_SERIES_STM32L5X is not set
# CONFIG_SOC_SERIES_STM32MP1X is not set
# CONFIG_SOC_SERIES_STM32U5X is not set
# CONFIG_SOC_SERIES_STM32WBX is not set
# CONFIG_SOC_SERIES_STM32WBAX is not set
# CONFIG_SOC_SERIES_STM32WLX is not set
# CONFIG_SOC_SERIES_AM62X_M4 is not set
# CONFIG_SOC_TI_LM3S6965 is not set
# CONFIG_SOC_SERIES_CC13X2_CC26X2 is not set
# CONFIG_SOC_SERIES_CC13X2X7_CC26X2X7 is not set
# CONFIG_SOC_SERIES_CC32XX is not set
# CONFIG_SOC_SERIES_MSP432P4XX is not set
# CONFIG_SOC_SERIES_XILINX_XC7ZXXX is not set
# CONFIG_SOC_SERIES_XILINX_XC7ZXXXS is not set
# CONFIG_SOC_XILINX_ZYNQMP_RPU is not set

#
# Hardware Configuration
#
CONFIG_CPU_HAS_ARM_MPU=y
CONFIG_CPU_HAS_NRF_IDAU=y
CONFIG_NRF_SPU_RAM_REGION_SIZE=0x2000
CONFIG_HAS_SWO=y
CONFIG_SOC_FAMILY="nordic_nrf"
CONFIG_GPIO_INIT_PRIORITY=40
CONFIG_SOC_FAMILY_NRF=y
CONFIG_HAS_HW_NRF_CC312=y
CONFIG_HAS_HW_NRF_CLOCK=y
CONFIG_HAS_HW_NRF_CTRLAP=y
CONFIG_HAS_HW_NRF_DCNF=y
CONFIG_HAS_HW_NRF_DPPIC=y
CONFIG_HAS_HW_NRF_EGU0=y
CONFIG_HAS_HW_NRF_EGU1=y
CONFIG_HAS_HW_NRF_EGU2=y
CONFIG_HAS_HW_NRF_EGU3=y
CONFIG_HAS_HW_NRF_EGU4=y
CONFIG_HAS_HW_NRF_EGU5=y
CONFIG_HAS_HW_NRF_GPIO0=y
CONFIG_HAS_HW_NRF_GPIO1=y
CONFIG_HAS_HW_NRF_GPIOTE=y
CONFIG_HAS_HW_NRF_IPC=y
CONFIG_HAS_HW_NRF_KMU=y
CONFIG_HAS_HW_NRF_MUTEX=y
CONFIG_HAS_HW_NRF_NFCT=y
CONFIG_HAS_HW_NRF_NVMC_PE=y
CONFIG_HAS_HW_NRF_OSCILLATORS=y
CONFIG_HAS_HW_NRF_POWER=y
CONFIG_HAS_HW_NRF_PWM0=y
CONFIG_HAS_HW_NRF_QSPI=y
CONFIG_HAS_HW_NRF_REGULATORS=y
CONFIG_HAS_HW_NRF_RESET=y
CONFIG_HAS_HW_NRF_SAADC=y
CONFIG_HAS_HW_NRF_SPIM4=y
CONFIG_HAS_HW_NRF_TWIM2=y
CONFIG_HAS_HW_NRF_UARTE0=y
CONFIG_HAS_HW_NRF_USBD=y
CONFIG_HAS_HW_NRF_USBREG=y
CONFIG_HAS_HW_NRF_VMC=y
CONFIG_HAS_HW_NRF_WDT0=y
CONFIG_SOC_NRF5340_CPUAPP=y
CONFIG_SOC_NRF5340_CPUAPP_QKAA=y
# CONFIG_SOC_NRF5340_CPUNET_QKAA is not set
CONFIG_SOC_NRF53_ANOMALY_160_WORKAROUND_NEEDED=y
CONFIG_SOC_NRF53_ANOMALY_160_WORKAROUND=y
CONFIG_SOC_NRF53_RTC_PRETICK=y
CONFIG_SOC_NRF53_RTC_PRETICK_IPC_CH_FROM_NET=10
CONFIG_SOC_NRF53_RTC_PRETICK_IPC_CH_TO_NET=11
CONFIG_SOC_DCDC_NRF53X_APP=y
CONFIG_SOC_DCDC_NRF53X_NET=y
CONFIG_SOC_DCDC_NRF53X_HV=y
CONFIG_SOC_NRF_GPIO_FORWARDER_FOR_NRF5340=y
CONFIG_SOC_ENABLE_LFXO=y
# CONFIG_SOC_LFXO_CAP_EXTERNAL is not set
# CONFIG_SOC_LFXO_CAP_INT_6PF is not set
CONFIG_SOC_LFXO_CAP_INT_7PF=y
# CONFIG_SOC_LFXO_CAP_INT_9PF is not set
CONFIG_SOC_HFXO_CAP_DEFAULT=y
# CONFIG_SOC_HFXO_CAP_EXTERNAL is not set
# CONFIG_SOC_HFXO_CAP_INTERNAL is not set
CONFIG_NRF_ENABLE_CACHE=y
CONFIG_NRF53_SYNC_RTC=y
# CONFIG_SYNC_RTC_LOG_LEVEL_OFF is not set
# CONFIG_SYNC_RTC_LOG_LEVEL_ERR is not set
# CONFIG_SYNC_RTC_LOG_LEVEL_WRN is not set
# CONFIG_SYNC_RTC_LOG_LEVEL_INF is not set
# CONFIG_SYNC_RTC_LOG_LEVEL_DBG is not set
CONFIG_SYNC_RTC_LOG_LEVEL_DEFAULT=y
CONFIG_SYNC_RTC_LOG_LEVEL=0
CONFIG_NRF53_SYNC_RTC_INIT_PRIORITY=90
CONFIG_NRF_RTC_TIMER_USER_CHAN_COUNT=1
CONFIG_NRF53_SYNC_RTC_LOG_TIMESTAMP=y
CONFIG_NRF53_SYNC_RTC_IPM_OUT=7
CONFIG_NRF53_SYNC_RTC_IPM_IN=8
CONFIG_IPM_MSG_CH_8_ENABLE=y
CONFIG_IPM_MSG_CH_8_RX=y
CONFIG_NRF_SOC_SECURE_SUPPORTED=y
# CONFIG_NFCT_PINS_AS_GPIOS is not set
# CONFIG_NRF_TRACE_PORT is not set
# CONFIG_BUILD_OUTPUT_INFO_HEADER is not set
# CONFIG_SOC_LOG_LEVEL_OFF is not set
# CONFIG_SOC_LOG_LEVEL_ERR is not set
# CONFIG_SOC_LOG_LEVEL_WRN is not set
# CONFIG_SOC_LOG_LEVEL_INF is not set
# CONFIG_SOC_LOG_LEVEL_DBG is not set
CONFIG_SOC_LOG_LEVEL_DEFAULT=y
CONFIG_SOC_LOG_LEVEL=0
# end of Hardware Configuration

CONFIG_SOC_COMPATIBLE_NRF=y
CONFIG_SOC_COMPATIBLE_NRF53X=y
CONFIG_SOC_COMPATIBLE_NRF5340_CPUAPP=y

#
# ARM Options
#
CONFIG_ARCH="arm"
CONFIG_CPU_CORTEX=y
# CONFIG_CODE_DATA_RELOCATION_SRAM is not set
CONFIG_ARM_ON_ENTER_CPU_IDLE_HOOK=y
CONFIG_CPU_CORTEX_M=y
# CONFIG_ARM_ZIMAGE_HEADER is not set
CONFIG_ISA_THUMB2=y
CONFIG_ASSEMBLER_ISA_THUMB2=y
CONFIG_COMPILER_ISA_THUMB2=y
CONFIG_STACK_ALIGN_DOUBLE_WORD=y
# CONFIG_RUNTIME_NMI is not set
CONFIG_FAULT_DUMP=2
CONFIG_BUILTIN_STACK_GUARD=y
CONFIG_ARM_STACK_PROTECTION=y
CONFIG_ARM_NONSECURE_FIRMWARE=y
CONFIG_ARM_NONSECURE_PREEMPTIBLE_SECURE_CALLS=y
CONFIG_ARM_STORE_EXC_RETURN=y
CONFIG_FP16=y
CONFIG_FP16_IEEE=y
# CONFIG_FP16_ALT is not set
CONFIG_CPU_CORTEX_M33=y
CONFIG_CPU_CORTEX_M_HAS_SYSTICK=y
CONFIG_CPU_CORTEX_M_HAS_DWT=y
CONFIG_CPU_CORTEX_M_HAS_BASEPRI=y
CONFIG_CPU_CORTEX_M_HAS_VTOR=y
CONFIG_CPU_CORTEX_M_HAS_SPLIM=y
CONFIG_CPU_CORTEX_M_HAS_PROGRAMMABLE_FAULT_PRIOS=y
CONFIG_CPU_CORTEX_M_HAS_CMSE=y
CONFIG_ARMV7_M_ARMV8_M_MAINLINE=y
CONFIG_ARMV8_M_MAINLINE=y
CONFIG_ARMV8_M_SE=y
CONFIG_ARMV7_M_ARMV8_M_FP=y
CONFIG_ARMV8_M_DSP=y

#
# ARM Cortex-M0/M0+/M1/M3/M4/M7/M23/M33 options
#
CONFIG_GEN_ISR_TABLES=y
# CONFIG_ZERO_LATENCY_IRQS is not set
# CONFIG_SW_VECTOR_RELAY is not set
# CONFIG_CORTEX_M_DWT is not set
# CONFIG_CORTEX_M_DEBUG_MONITOR_HOOK is not set
# CONFIG_TRAP_UNALIGNED_ACCESS is not set
# end of ARM Cortex-M0/M0+/M1/M3/M4/M7/M23/M33 options

CONFIG_NULL_POINTER_EXCEPTION_DETECTION_NONE=y
CONFIG_ARM_TRUSTZONE_M=y

#
# ARM TrustZone-M Options
#

#
# Non-secure firmware
#
# CONFIG_ARM_FIRMWARE_USES_SECURE_ENTRY_FUNCS is not set
# end of ARM TrustZone-M Options

CONFIG_GEN_IRQ_VECTOR_TABLE=y
CONFIG_ARM_MPU=y
CONFIG_ARM_MPU_REGION_MIN_ALIGN_AND_SIZE=32
# CONFIG_MPU_STACK_GUARD is not set
CONFIG_MPU_ALLOW_FLASH_WRITE=y
# CONFIG_MPU_DISABLE_BACKGROUND_MAP is not set
# CONFIG_CUSTOM_SECTION_ALIGN is not set
CONFIG_CUSTOM_SECTION_MIN_ALIGN_SIZE=32
# end of ARM Options

CONFIG_ARM=y
CONFIG_ARCH_IS_SET=y

#
# General Architecture Options
#
# CONFIG_SEMIHOST is not set
# CONFIG_ARCH_LOG_LEVEL_OFF is not set
# CONFIG_ARCH_LOG_LEVEL_ERR is not set
# CONFIG_ARCH_LOG_LEVEL_WRN is not set
# CONFIG_ARCH_LOG_LEVEL_INF is not set
# CONFIG_ARCH_LOG_LEVEL_DBG is not set
CONFIG_ARCH_LOG_LEVEL_DEFAULT=y
CONFIG_ARCH_LOG_LEVEL=0
CONFIG_LITTLE_ENDIAN=y
# CONFIG_TRUSTED_EXECUTION_SECURE is not set
CONFIG_TRUSTED_EXECUTION_NONSECURE=y
CONFIG_HW_STACK_PROTECTION=y
# CONFIG_USERSPACE is not set
CONFIG_KOBJECT_TEXT_AREA=256
CONFIG_KOBJECT_DATA_AREA_RESERVE_EXTRA_PERCENT=100
CONFIG_KOBJECT_RODATA_AREA_EXTRA_BYTES=16
CONFIG_GEN_PRIV_STACKS=y
# CONFIG_STACK_GROWS_UP is not set

#
# Interrupt Configuration
#
# CONFIG_DYNAMIC_INTERRUPTS is not set
CONFIG_ARCH_IRQ_VECTOR_TABLE_ALIGN=4
CONFIG_IRQ_VECTOR_TABLE_JUMP_BY_ADDRESS=y
# CONFIG_IRQ_VECTOR_TABLE_JUMP_BY_CODE is not set
CONFIG_GEN_SW_ISR_TABLE=y
CONFIG_ARCH_SW_ISR_TABLE_ALIGN=4
CONFIG_GEN_IRQ_START_VECTOR=0
# CONFIG_EXTRA_EXCEPTION_INFO is not set
# CONFIG_SIMPLIFIED_EXCEPTION_CODES is not set
# end of Interrupt Configuration
# end of General Architecture Options

CONFIG_ARCH_HAS_SINGLE_THREAD_SUPPORT=y
CONFIG_ARCH_HAS_TIMING_FUNCTIONS=y
CONFIG_ARCH_HAS_TRUSTED_EXECUTION=y
CONFIG_ARCH_HAS_STACK_PROTECTION=y
CONFIG_ARCH_HAS_USERSPACE=y
CONFIG_ARCH_HAS_EXECUTABLE_PAGE_BIT=y
CONFIG_ARCH_HAS_RAMFUNC_SUPPORT=y
CONFIG_ARCH_HAS_NESTED_EXCEPTION_DETECTION=y
CONFIG_ARCH_SUPPORTS_COREDUMP=y
CONFIG_ARCH_SUPPORTS_ARCH_HW_INIT=y
CONFIG_ARCH_HAS_EXTRA_EXCEPTION_INFO=y
CONFIG_ARCH_HAS_THREAD_LOCAL_STORAGE=y
CONFIG_ARCH_HAS_SUSPEND_TO_RAM=y
CONFIG_ARCH_HAS_THREAD_ABORT=y
CONFIG_ARCH_HAS_CODE_DATA_RELOCATION=y
CONFIG_CPU_HAS_TEE=y
CONFIG_CPU_HAS_FPU=y
CONFIG_CPU_HAS_MPU=y
CONFIG_MPU=y
# CONFIG_MPU_LOG_LEVEL_OFF is not set
# CONFIG_MPU_LOG_LEVEL_ERR is not set
# CONFIG_MPU_LOG_LEVEL_WRN is not set
# CONFIG_MPU_LOG_LEVEL_INF is not set
# CONFIG_MPU_LOG_LEVEL_DBG is not set
CONFIG_MPU_LOG_LEVEL_DEFAULT=y
CONFIG_MPU_LOG_LEVEL=0
CONFIG_MPU_REQUIRES_NON_OVERLAPPING_REGIONS=y
CONFIG_MPU_GAP_FILLING=y
CONFIG_SRAM_REGION_PERMISSIONS=y

#
# Floating Point Options
#
# end of Floating Point Options

#
# Cache Options
#
# end of Cache Options

CONFIG_TOOLCHAIN_HAS_BUILTIN_FFS=y

#
# General Kernel Options
#
# CONFIG_KERNEL_LOG_LEVEL_OFF is not set
# CONFIG_KERNEL_LOG_LEVEL_ERR is not set
# CONFIG_KERNEL_LOG_LEVEL_WRN is not set
# CONFIG_KERNEL_LOG_LEVEL_INF is not set
# CONFIG_KERNEL_LOG_LEVEL_DBG is not set
CONFIG_KERNEL_LOG_LEVEL_DEFAULT=y
CONFIG_KERNEL_LOG_LEVEL=0
CONFIG_MULTITHREADING=y
CONFIG_NUM_COOP_PRIORITIES=16
CONFIG_NUM_PREEMPT_PRIORITIES=15
CONFIG_MAIN_THREAD_PRIORITY=0
CONFIG_COOP_ENABLED=y
CONFIG_PREEMPT_ENABLED=y
CONFIG_PRIORITY_CEILING=-127
# CONFIG_SCHED_DEADLINE is not set
# CONFIG_SCHED_CPU_MASK is not set
CONFIG_ISR_STACK_SIZE=2048
CONFIG_THREAD_STACK_INFO=y
# CONFIG_THREAD_CUSTOM_DATA is not set
# CONFIG_DYNAMIC_THREAD is not set
CONFIG_LIBC_ERRNO=y
CONFIG_ERRNO=y
CONFIG_SCHED_DUMB=y
# CONFIG_SCHED_SCALABLE is not set
# CONFIG_SCHED_MULTIQ is not set
# CONFIG_WAITQ_SCALABLE is not set
CONFIG_WAITQ_DUMB=y

#
# Kernel Debugging and Metrics
#
CONFIG_BOOT_BANNER=y
CONFIG_BOOT_DELAY=0
# CONFIG_THREAD_MONITOR is not set
# CONFIG_THREAD_NAME is not set
# CONFIG_THREAD_RUNTIME_STATS is not set
# end of Kernel Debugging and Metrics

#
# Work Queue Options
#
CONFIG_SYSTEM_WORKQUEUE_PRIORITY=-1
# CONFIG_SYSTEM_WORKQUEUE_NO_YIELD is not set
# end of Work Queue Options

#
# Barrier Operations
#
CONFIG_BARRIER_OPERATIONS_ARCH=y
# end of Barrier Operations

#
# Atomic Operations
#
CONFIG_ATOMIC_OPERATIONS_BUILTIN=y
# end of Atomic Operations

#
# Timer API Options
#
CONFIG_TIMESLICING=y
CONFIG_TIMESLICE_SIZE=0
CONFIG_TIMESLICE_PRIORITY=0
# CONFIG_TIMESLICE_PER_THREAD is not set
CONFIG_POLL=y
# end of Timer API Options

#
# Other Kernel Object Options
#
# CONFIG_MEM_SLAB_TRACE_MAX_UTILIZATION is not set
CONFIG_NUM_MBOX_ASYNC_MSGS=10
# CONFIG_EVENTS is not set
# CONFIG_PIPES is not set
CONFIG_KERNEL_MEM_POOL=y
# end of Other Kernel Object Options

CONFIG_ARCH_HAS_CUSTOM_SWAP_TO_MAIN=y
CONFIG_SWAP_NONATOMIC=y
CONFIG_SYS_CLOCK_EXISTS=y
CONFIG_TIMEOUT_64BIT=y
CONFIG_SYS_CLOCK_MAX_TIMEOUT_DAYS=365
CONFIG_XIP=y

#
# Initialization Priorities
#
CONFIG_KERNEL_INIT_PRIORITY_OBJECTS=30
CONFIG_KERNEL_INIT_PRIORITY_DEFAULT=40
CONFIG_KERNEL_INIT_PRIORITY_DEVICE=50
CONFIG_APPLICATION_INIT_PRIORITY=90
# end of Initialization Priorities

#
# Security Options
#
# CONFIG_STACK_CANARIES is not set
CONFIG_STACK_POINTER_RANDOM=0
# end of Security Options

#
# SMP Options
#
CONFIG_MP_NUM_CPUS=1
# end of SMP Options

CONFIG_TICKLESS_KERNEL=y
CONFIG_TOOLCHAIN_SUPPORTS_THREAD_LOCAL_STORAGE=y
CONFIG_THREAD_LOCAL_STORAGE=y
# end of General Kernel Options

#
# Device Options
#
# CONFIG_DEVICE_DEPS is not set
# end of Device Options

#
# Virtual Memory Support
#
# end of Virtual Memory Support

#
# Device Drivers
#
CONFIG_ADC=y
CONFIG_ADC_CONFIGURABLE_INPUTS=y
CONFIG_ADC_ASYNC=y
# CONFIG_ADC_LOG_LEVEL_OFF is not set
# CONFIG_ADC_LOG_LEVEL_ERR is not set
# CONFIG_ADC_LOG_LEVEL_WRN is not set
# CONFIG_ADC_LOG_LEVEL_INF is not set
# CONFIG_ADC_LOG_LEVEL_DBG is not set
CONFIG_ADC_LOG_LEVEL_DEFAULT=y
CONFIG_ADC_LOG_LEVEL=0
CONFIG_ADC_NRFX_SAADC=y
# CONFIG_AUXDISPLAY is not set
# CONFIG_AUDIO is not set
# CONFIG_BBRAM is not set

#
# Bluetooth HCI Driver Options
#
# CONFIG_BT_H4 is not set
# CONFIG_BT_H5 is not set
CONFIG_BT_RPMSG=y
# CONFIG_BT_STM32_IPM is not set
# CONFIG_BT_ESP32 is not set
# CONFIG_BT_B91 is not set
# CONFIG_BT_PSOC6_BLESS is not set
# CONFIG_BT_NO_DRIVER is not set
CONFIG_BT_DRV_TX_STACK_SIZE=256
CONFIG_BT_DRV_RX_STACK_SIZE=256
# CONFIG_CACHE is not set
# CONFIG_CAN is not set
# CONFIG_CLOCK_CONTROL_LOG_LEVEL_OFF is not set
# CONFIG_CLOCK_CONTROL_LOG_LEVEL_ERR is not set
# CONFIG_CLOCK_CONTROL_LOG_LEVEL_WRN is not set
# CONFIG_CLOCK_CONTROL_LOG_LEVEL_INF is not set
# CONFIG_CLOCK_CONTROL_LOG_LEVEL_DBG is not set
CONFIG_CLOCK_CONTROL_LOG_LEVEL_DEFAULT=y
CONFIG_CLOCK_CONTROL_LOG_LEVEL=0
CONFIG_CLOCK_CONTROL_NRF=y
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_RC is not set
CONFIG_CLOCK_CONTROL_NRF_K32SRC_XTAL=y
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_SYNTH is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_500PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_250PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_150PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_100PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_75PPM is not set
CONFIG_CLOCK_CONTROL_NRF_K32SRC_50PPM=y
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_30PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_20PPM is not set
CONFIG_CLOCK_CONTROL_NRF_ACCURACY=50
# CONFIG_CLOCK_CONTROL_FIXED_RATE_CLOCK is not set
CONFIG_CONSOLE=y
CONFIG_CONSOLE_INPUT_MAX_LINE_LEN=128
CONFIG_CONSOLE_HAS_DRIVER=y
CONFIG_CONSOLE_INIT_PRIORITY=40
# CONFIG_UART_CONSOLE is not set
# CONFIG_RAM_CONSOLE is not set
CONFIG_RTT_CONSOLE=y
CONFIG_RTT_TX_RETRY_CNT=2
CONFIG_RTT_TX_RETRY_DELAY_MS=2
# CONFIG_RTT_TX_RETRY_IN_INTERRUPT is not set
# CONFIG_IPM_CONSOLE_SENDER is not set
# CONFIG_IPM_CONSOLE_RECEIVER is not set
# CONFIG_UART_MCUMGR is not set
# CONFIG_UART_CONSOLE_LOG_LEVEL_OFF is not set
# CONFIG_UART_CONSOLE_LOG_LEVEL_ERR is not set
# CONFIG_UART_CONSOLE_LOG_LEVEL_WRN is not set
# CONFIG_UART_CONSOLE_LOG_LEVEL_INF is not set
# CONFIG_UART_CONSOLE_LOG_LEVEL_DBG is not set
CONFIG_UART_CONSOLE_LOG_LEVEL_DEFAULT=y
CONFIG_UART_CONSOLE_LOG_LEVEL=0
# CONFIG_GSM_MUX is not set
# CONFIG_EFI_CONSOLE is not set
# CONFIG_COREDUMP_DEVICE is not set
# CONFIG_COUNTER is not set
# CONFIG_CRYPTO is not set
# CONFIG_DAC is not set
# CONFIG_DAI is not set
# CONFIG_DISK_DRIVERS is not set
# CONFIG_DISPLAY is not set
# CONFIG_DMA is not set
# CONFIG_EDAC is not set
# CONFIG_EEPROM is not set
# CONFIG_ENTROPY_LOG_LEVEL_OFF is not set
# CONFIG_ENTROPY_LOG_LEVEL_ERR is not set
# CONFIG_ENTROPY_LOG_LEVEL_WRN is not set
# CONFIG_ENTROPY_LOG_LEVEL_INF is not set
# CONFIG_ENTROPY_LOG_LEVEL_DBG is not set
CONFIG_ENTROPY_LOG_LEVEL_DEFAULT=y
CONFIG_ENTROPY_LOG_LEVEL=0
CONFIG_ENTROPY_INIT_PRIORITY=50
CONFIG_ENTROPY_BT_HCI=y
CONFIG_ENTROPY_PSA_CRYPTO_RNG=y
CONFIG_ENTROPY_HAS_DRIVER=y
# CONFIG_ESPI is not set
CONFIG_FLASH_HAS_DRIVER_ENABLED=y
CONFIG_FLASH_HAS_PAGE_LAYOUT=y
CONFIG_FLASH_JESD216=y
CONFIG_FLASH=y
# CONFIG_FLASH_LOG_LEVEL_OFF is not set
# CONFIG_FLASH_LOG_LEVEL_ERR is not set
# CONFIG_FLASH_LOG_LEVEL_WRN is not set
# CONFIG_FLASH_LOG_LEVEL_INF is not set
# CONFIG_FLASH_LOG_LEVEL_DBG is not set
CONFIG_FLASH_LOG_LEVEL_DEFAULT=y
CONFIG_FLASH_LOG_LEVEL=0
# CONFIG_FLASH_JESD216_API is not set
CONFIG_FLASH_PAGE_LAYOUT=y
CONFIG_FLASH_INIT_PRIORITY=50
CONFIG_SOC_FLASH_NRF=y
CONFIG_SOC_FLASH_NRF_RADIO_SYNC_NONE=y
# CONFIG_SOC_FLASH_NRF_PARTIAL_ERASE is not set
# CONFIG_SOC_FLASH_NRF_EMULATE_ONE_BYTE_WRITE_ACCESS is not set
CONFIG_NORDIC_QSPI_NOR_INIT_PRIORITY=41
# CONFIG_NORDIC_QSPI_NOR_XIP is not set
# CONFIG_FPGA is not set
# CONFIG_FUEL_GAUGE is not set
# CONFIG_GPIO_LOG_LEVEL_OFF is not set
# CONFIG_GPIO_LOG_LEVEL_ERR is not set
# CONFIG_GPIO_LOG_LEVEL_WRN is not set
# CONFIG_GPIO_LOG_LEVEL_INF is not set
# CONFIG_GPIO_LOG_LEVEL_DBG is not set
CONFIG_GPIO_LOG_LEVEL_DEFAULT=y
CONFIG_GPIO_LOG_LEVEL=0
# CONFIG_GPIO_GET_DIRECTION is not set
# CONFIG_GPIO_GET_CONFIG is not set
# CONFIG_GPIO_HOGS is not set
# CONFIG_GPIO_ENABLE_DISABLE_INTERRUPT is not set
CONFIG_GPIO_NRFX=y
CONFIG_GPIO_NRFX_INTERRUPT=y
# CONFIG_HWINFO is not set
CONFIG_I2C=y
# CONFIG_I2C_DUMP_MESSAGES is not set
# CONFIG_I2C_CALLBACK is not set
# CONFIG_I2C_RTIO is not set
# CONFIG_I2C_TARGET is not set
CONFIG_I2C_NRFX=y
CONFIG_I2C_NRFX_TWIM=y
CONFIG_I2C_NRFX_TRANSFER_TIMEOUT=500
CONFIG_I2C_INIT_PRIORITY=50
# CONFIG_I2C_LOG_LEVEL_OFF is not set
# CONFIG_I2C_LOG_LEVEL_ERR is not set
# CONFIG_I2C_LOG_LEVEL_WRN is not set
# CONFIG_I2C_LOG_LEVEL_INF is not set
# CONFIG_I2C_LOG_LEVEL_DBG is not set
CONFIG_I2C_LOG_LEVEL_DEFAULT=y
CONFIG_I2C_LOG_LEVEL=0
# CONFIG_I2S is not set
# CONFIG_I3C is not set
# CONFIG_SMBUS is not set

#
# Interrupt controller drivers
#
CONFIG_INTC_INIT_PRIORITY=40
# CONFIG_INTC_LOG_LEVEL_OFF is not set
# CONFIG_INTC_LOG_LEVEL_ERR is not set
# CONFIG_INTC_LOG_LEVEL_WRN is not set
# CONFIG_INTC_LOG_LEVEL_INF is not set
# CONFIG_INTC_LOG_LEVEL_DBG is not set
CONFIG_INTC_LOG_LEVEL_DEFAULT=y
CONFIG_INTC_LOG_LEVEL=0
# CONFIG_MULTI_LEVEL_INTERRUPTS is not set
CONFIG_1ST_LEVEL_INTERRUPT_BITS=8
CONFIG_2ND_LEVEL_INTERRUPT_BITS=8
CONFIG_3RD_LEVEL_INTERRUPT_BITS=8
# CONFIG_INTC_ESP32 is not set
# CONFIG_PLIC_SUPPORTS_EDGE_IRQ is not set
# end of Interrupt controller drivers

# CONFIG_IPM is not set
# CONFIG_KSCAN is not set
# CONFIG_LED is not set
# CONFIG_LED_STRIP is not set
# CONFIG_LORA is not set
CONFIG_MBOX=y
CONFIG_MBOX_INIT_PRIORITY=40
# CONFIG_MBOX_LOG_LEVEL_OFF is not set
# CONFIG_MBOX_LOG_LEVEL_ERR is not set
# CONFIG_MBOX_LOG_LEVEL_WRN is not set
# CONFIG_MBOX_LOG_LEVEL_INF is not set
# CONFIG_MBOX_LOG_LEVEL_DBG is not set
CONFIG_MBOX_LOG_LEVEL_DEFAULT=y
CONFIG_MBOX_LOG_LEVEL=0
# CONFIG_MDIO is not set
# CONFIG_MFD is not set

#
# Miscellaneous Drivers
#
# CONFIG_GROVE_LCD_RGB is not set
# end of Miscellaneous Drivers

# CONFIG_MM_DRV is not set
# CONFIG_NEURAL_NET_ACCEL is not set
# CONFIG_PCIE is not set
# CONFIG_PCIE_ENDPOINT is not set
# CONFIG_PECI is not set
# CONFIG_PINCTRL_LOG_LEVEL_OFF is not set
# CONFIG_PINCTRL_LOG_LEVEL_ERR is not set
# CONFIG_PINCTRL_LOG_LEVEL_WRN is not set
# CONFIG_PINCTRL_LOG_LEVEL_INF is not set
# CONFIG_PINCTRL_LOG_LEVEL_DBG is not set
CONFIG_PINCTRL_LOG_LEVEL_DEFAULT=y
CONFIG_PINCTRL_LOG_LEVEL=0
CONFIG_PINCTRL_STORE_REG=y
# CONFIG_PINCTRL_DYNAMIC is not set
CONFIG_PINCTRL_NRF=y
# CONFIG_PM_CPU_OPS is not set
# CONFIG_POWER_DOMAIN is not set
# CONFIG_PS2 is not set
# CONFIG_PTP_CLOCK is not set
# CONFIG_PWM is not set
# CONFIG_REGULATOR is not set
# CONFIG_RETAINED_MEM is not set
# CONFIG_RTC is not set
# CONFIG_SDHC is not set
# CONFIG_SENSOR_LOG_LEVEL_OFF is not set
# CONFIG_SENSOR_LOG_LEVEL_ERR is not set
# CONFIG_SENSOR_LOG_LEVEL_WRN is not set
# CONFIG_SENSOR_LOG_LEVEL_INF is not set
# CONFIG_SENSOR_LOG_LEVEL_DBG is not set
CONFIG_SENSOR_LOG_LEVEL_DEFAULT=y
CONFIG_SENSOR_LOG_LEVEL=0
CONFIG_SENSOR_INIT_PRIORITY=90
# CONFIG_SENSOR_ASYNC_API is not set
# CONFIG_SENSOR_INFO is not set

#
# Device Drivers
#
# CONFIG_ICM42688_DECODER is not set
CONFIG_VCMP_IT8XXX2_INIT_PRIORITY=90
CONFIG_TMP112_FULL_SCALE_RUNTIME=y
CONFIG_TMP112_SAMPLING_FREQUENCY_RUNTIME=y

#
# Capabilities
#
CONFIG_SERIAL_HAS_DRIVER=y
CONFIG_SERIAL_SUPPORT_ASYNC=y
CONFIG_SERIAL_SUPPORT_INTERRUPT=y
# CONFIG_UART_LOG_LEVEL_OFF is not set
# CONFIG_UART_LOG_LEVEL_ERR is not set
# CONFIG_UART_LOG_LEVEL_WRN is not set
# CONFIG_UART_LOG_LEVEL_INF is not set
# CONFIG_UART_LOG_LEVEL_DBG is not set
CONFIG_UART_LOG_LEVEL_DEFAULT=y
CONFIG_UART_LOG_LEVEL=0
CONFIG_UART_USE_RUNTIME_CONFIGURE=y
# CONFIG_UART_ASYNC_API is not set
# CONFIG_UART_LINE_CTRL is not set
# CONFIG_UART_DRV_CMD is not set
# CONFIG_UART_WIDE_DATA is not set
# CONFIG_UART_PIPE is not set

#
# Serial Drivers
#
CONFIG_UART_NRFX=y
CONFIG_UART_NRFX_UARTE=y
CONFIG_UART_0_ENHANCED_POLL_OUT=y
# CONFIG_UART_0_NRF_PARITY_BIT is not set
CONFIG_UART_0_NRF_TX_BUFFER_SIZE=32
CONFIG_UART_ENHANCED_POLL_OUT=y
# CONFIG_SYSCON is not set

#
# Timer drivers
#
CONFIG_TIMER_READS_ITS_FREQUENCY_AT_RUNTIME=y
# CONFIG_SYSTEM_CLOCK_SLOPPY_IDLE is not set
CONFIG_SYSTEM_CLOCK_INIT_PRIORITY=0
CONFIG_TICKLESS_CAPABLE=y
CONFIG_SYSTEM_TIMER_HAS_DISABLE_SUPPORT=y
# CONFIG_NRF_RTC_TIMER_TRIGGER_OVERFLOW is not set
# CONFIG_SYSTEM_CLOCK_NO_WAIT is not set
# CONFIG_SYSTEM_CLOCK_WAIT_FOR_AVAILABILITY is not set
CONFIG_SYSTEM_CLOCK_WAIT_FOR_STABILITY=y
# end of Timer drivers

# CONFIG_USB_BC12 is not set
# CONFIG_UDC_DRIVER is not set
# CONFIG_UHC_DRIVER is not set
# CONFIG_UVB is not set
# CONFIG_USB_DEVICE_DRIVER is not set
# CONFIG_USBC_TCPC_DRIVER is not set
# CONFIG_USBC_LOG_LEVEL_OFF is not set
# CONFIG_USBC_LOG_LEVEL_ERR is not set
# CONFIG_USBC_LOG_LEVEL_WRN is not set
# CONFIG_USBC_LOG_LEVEL_INF is not set
# CONFIG_USBC_LOG_LEVEL_DBG is not set
CONFIG_USBC_LOG_LEVEL_DEFAULT=y
CONFIG_USBC_LOG_LEVEL=0
# CONFIG_USBC_VBUS_DRIVER is not set
# CONFIG_VIDEO is not set
# CONFIG_VIRTUALIZATION is not set
# CONFIG_W1 is not set
# CONFIG_HWSPINLOCK is not set
# end of Device Drivers

# CONFIG_REQUIRES_FULL_LIBC is not set
CONFIG_FULL_LIBC_SUPPORTED=y
CONFIG_MINIMAL_LIBC_SUPPORTED=y
CONFIG_NEWLIB_LIBC_SUPPORTED=y
CONFIG_PICOLIBC_SUPPORTED=y

#
# C Library
#
# CONFIG_MINIMAL_LIBC is not set
CONFIG_PICOLIBC=y
# CONFIG_NEWLIB_LIBC is not set
# CONFIG_EXTERNAL_LIBC is not set
CONFIG_HAS_NEWLIB_LIBC_NANO=y
CONFIG_COMMON_LIBC_ABORT=y
CONFIG_COMMON_LIBC_MALLOC=y
CONFIG_COMMON_LIBC_MALLOC_ARENA_SIZE=-1
CONFIG_COMMON_LIBC_CALLOC=y
CONFIG_COMMON_LIBC_REALLOCARRAY=y
# CONFIG_PICOLIBC_USE_MODULE is not set
CONFIG_PICOLIBC_HEAP_SIZE=-2
CONFIG_PICOLIBC_IO_LONG_LONG=y
CONFIG_PICOLIBC_IO_FLOAT=y
CONFIG_STDOUT_CONSOLE=y
# end of C Library

#
# C++ Language Support
#
# CONFIG_CPP is not set

#
# Deprecated
#
# CONFIG_CPLUSPLUS is not set
# CONFIG_LIB_CPLUSPLUS is not set
# end of Deprecated
# end of C++ Language Support

CONFIG_CRC=y

#
# Additional libraries
#

#
# Hash Function Support
#
# CONFIG_SYS_HASH_FUNC32 is not set
# end of Hash Function Support

#
# Hashmap (Hash Table) Support
#
# CONFIG_SYS_HASH_MAP is not set
# end of Hashmap (Hash Table) Support

#
# OS Support Library
#
# CONFIG_JSON_LIBRARY is not set
# CONFIG_RING_BUFFER is not set
CONFIG_NOTIFY=y
# CONFIG_BASE64 is not set
# CONFIG_PRINTK_SYNC is not set
CONFIG_MPSC_PBUF=y
CONFIG_ONOFF=y
# CONFIG_SPSC_PBUF is not set
# CONFIG_SHARED_MULTI_HEAP is not set
# CONFIG_WINSTREAM is not set
# CONFIG_MPSC_CLEAR_ALLOCATED is not set
CONFIG_REBOOT=y
CONFIG_HAS_POWEROFF=y
# CONFIG_POWEROFF is not set
# CONFIG_UTF8 is not set
CONFIG_CBPRINTF_COMPLETE=y
# CONFIG_CBPRINTF_NANO is not set
CONFIG_CBPRINTF_FULL_INTEGRAL=y
# CONFIG_CBPRINTF_REDUCED_INTEGRAL is not set
CONFIG_CBPRINTF_FP_SUPPORT=y
# CONFIG_CBPRINTF_FP_A_SUPPORT is not set
# CONFIG_CBPRINTF_FP_ALWAYS_A is not set
CONFIG_CBPRINTF_N_SPECIFIER=y
# CONFIG_CBPRINTF_LIBC_SUBSTS is not set
# CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_OFF is not set
# CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_ERR is not set
# CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_WRN is not set
# CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_INF is not set
# CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_DBG is not set
CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_DEFAULT=y
CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL=0
# CONFIG_CBPRINTF_PACKAGE_LONGDOUBLE is not set

#
# Heap and Memory Allocation
#
# CONFIG_SYS_HEAP_VALIDATE is not set
CONFIG_SYS_HEAP_ALLOC_LOOPS=3
# CONFIG_SYS_HEAP_RUNTIME_STATS is not set
# CONFIG_SYS_HEAP_LISTENER is not set
# CONFIG_SYS_HEAP_SMALL_ONLY is not set
# CONFIG_SYS_HEAP_BIG_ONLY is not set
CONFIG_SYS_HEAP_AUTO=y
# CONFIG_SYS_MEM_BLOCKS is not set
# end of Heap and Memory Allocation
# end of OS Support Library

# CONFIG_POSIX_API is not set
# CONFIG_POSIX_CLOCK is not set
# CONFIG_EVENTFD is not set
# CONFIG_FNMATCH is not set
# CONFIG_POSIX_MQUEUE is not set
CONFIG_SEM_VALUE_MAX=32767
# CONFIG_POSIX_SIGNAL is not set
CONFIG_TIMER=y
CONFIG_MAX_TIMER_COUNT=5
CONFIG_TIMER_CREATE_WAIT=100
CONFIG_TIMER_DELAYTIMER_MAX=20
# CONFIG_POSIX_UNAME is not set
# CONFIG_OPENAMP_RSC_TABLE is not set
# CONFIG_SMF is not set
CONFIG_ACPI_HID_LEN_MAX=12
CONFIG_LIBGCC_RTLIB=y
# end of Additional libraries

#
# Subsystems and OS Services
#
CONFIG_BT=y
CONFIG_BT_HCI=y
# CONFIG_BT_CUSTOM is not set
# CONFIG_BT_HCI_RAW is not set
# CONFIG_BT_HCI_RAW_H4 is not set
# CONFIG_BT_HCI_RAW_CMD_EXT is not set
CONFIG_BT_CONN_TX=y
# CONFIG_BT_SCA_UPDATE is not set
# CONFIG_BT_ISO_PERIPHERAL is not set
# CONFIG_BT_ISO_CENTRAL is not set
# CONFIG_BT_ISO_BROADCASTER is not set
# CONFIG_BT_ISO_SYNC_RECEIVER is not set

#
# Bluetooth buffer configuration
#
# end of Bluetooth buffer configuration

#
# Bluetooth Host
#

#
# L2CAP Options
#
# end of L2CAP Options

#
# ATT and GATT Options
#
# end of ATT and GATT Options

#
# GATT Services
#
# end of GATT Services
# end of Bluetooth Host

# CONFIG_BT_SHELL is not set
# CONFIG_BT_EAD is not set
CONFIG_BT_LOG=y

#
# Bluetooth logging
#
CONFIG_BT_LOG_LEGACY=y

#
# Bluetooth legacy logging options
#
# CONFIG_BT_DEBUG_HCI_DRIVER is not set
# CONFIG_BT_DEBUG_RPA is not set

#
# [DEPRECATED] Audio
#
# CONFIG_BT_DEBUG_AICS is not set
# CONFIG_BT_DEBUG_AICS_CLIENT is not set
# CONFIG_BT_DEBUG_CSIP_SET_MEMBER is not set
# CONFIG_BT_DEBUG_CSIP_SET_COORDINATOR is not set
# CONFIG_BT_DEBUG_HAS is not set
# CONFIG_BT_DEBUG_MCS is not set
# CONFIG_BT_DEBUG_MCC is not set
# CONFIG_MCTL_DEBUG is not set
# CONFIG_BT_DEBUG_MICP_MIC_DEV is not set
# CONFIG_BT_DEBUG_MICP_MIC_CTLR is not set
# CONFIG_BT_DEBUG_MPL is not set
# CONFIG_BT_DEBUG_TBS is not set
# CONFIG_BT_DEBUG_TBS_CLIENT is not set
# CONFIG_BT_DEBUG_VCP_VOL_REND is not set
# CONFIG_BT_DEBUG_VCP_VOL_CTLR is not set
# CONFIG_BT_DEBUG_VOCS is not set
# CONFIG_BT_DEBUG_VOCS_CLIENT is not set
# end of [DEPRECATED] Audio

#
# [DEPRECATED] Others
#
# CONFIG_BT_DEBUG_CRYPTO is not set
# CONFIG_BT_DEBUG_ATT is not set
# CONFIG_BT_DEBUG_GATT is not set
# CONFIG_BT_DEBUG_L2CAP is not set
# CONFIG_BT_DEBUG_DF is not set
# CONFIG_BT_DEBUG_SETTINGS is not set
# CONFIG_BT_DEBUG_HCI_CORE is not set
# CONFIG_BT_DEBUG_CONN is not set
# CONFIG_BT_DEBUG_ISO is not set
# CONFIG_BT_DEBUG_KEYS is not set
# CONFIG_BT_DEBUG_SMP is not set
# CONFIG_BT_DEBUG_SERVICE is not set
# end of [DEPRECATED] Others

#
# [DEPRECATED] BR/EDR
#
# end of [DEPRECATED] BR/EDR

#
# [DEPRECATED] Mesh
#
# CONFIG_BT_MESH_DEBUG is not set
# CONFIG_BT_MESH_DEBUG_NET is not set
# CONFIG_BT_MESH_DEBUG_RPL is not set
# CONFIG_BT_MESH_DEBUG_TRANS is not set
# CONFIG_BT_MESH_DEBUG_BEACON is not set
# CONFIG_BT_MESH_DEBUG_CRYPTO is not set
# CONFIG_BT_MESH_DEBUG_KEYS is not set
# CONFIG_BT_MESH_DEBUG_PROV is not set
# CONFIG_BT_MESH_DEBUG_PROVISIONER is not set
# CONFIG_BT_MESH_DEBUG_PROV_DEVICE is not set
# CONFIG_BT_MESH_DEBUG_ACCESS is not set
# CONFIG_BT_MESH_DEBUG_MODEL is not set
# CONFIG_BT_MESH_DEBUG_ADV is not set
# CONFIG_BT_MESH_DEBUG_LOW_POWER is not set
# CONFIG_BT_MESH_DEBUG_FRIEND is not set
# CONFIG_BT_MESH_DEBUG_SETTINGS is not set
# CONFIG_BT_MESH_DEBUG_CFG is not set
# end of [DEPRECATED] Mesh

#
# [DEPRECATED] Services
#
# CONFIG_BT_DEBUG_OTS_CLIENT is not set
# end of [DEPRECATED] Services
# end of Bluetooth legacy logging options

# CONFIG_BT_LOG_LEVEL_OFF is not set
# CONFIG_BT_LOG_LEVEL_ERR is not set
# CONFIG_BT_LOG_LEVEL_WRN is not set
# CONFIG_BT_LOG_LEVEL_INF is not set
# CONFIG_BT_LOG_LEVEL_DBG is not set
CONFIG_BT_LOG_LEVEL_DEFAULT=y
CONFIG_BT_LOG_LEVEL=0
CONFIG_BT_HCI_DRIVER_LOG_LEVEL=0
CONFIG_BT_HCI_DRIVER_LOG_LEVEL_INHERIT=y
# CONFIG_BT_HCI_DRIVER_LOG_LEVEL_OFF is not set
# CONFIG_BT_HCI_DRIVER_LOG_LEVEL_ERR is not set
# CONFIG_BT_HCI_DRIVER_LOG_LEVEL_WRN is not set
# CONFIG_BT_HCI_DRIVER_LOG_LEVEL_INF is not set
# CONFIG_BT_HCI_DRIVER_LOG_LEVEL_DBG is not set
# CONFIG_BT_HCI_DRIVER_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_RPA_LOG_LEVEL=0
CONFIG_BT_RPA_LOG_LEVEL_INHERIT=y
# CONFIG_BT_RPA_LOG_LEVEL_OFF is not set
# CONFIG_BT_RPA_LOG_LEVEL_ERR is not set
# CONFIG_BT_RPA_LOG_LEVEL_WRN is not set
# CONFIG_BT_RPA_LOG_LEVEL_INF is not set
# CONFIG_BT_RPA_LOG_LEVEL_DBG is not set
# CONFIG_BT_RPA_LOG_LEVEL_DEFAULT is not set

#
# Audio
#
CONFIG_BT_AICS_LOG_LEVEL=0
CONFIG_BT_AICS_LOG_LEVEL_INHERIT=y
# CONFIG_BT_AICS_LOG_LEVEL_OFF is not set
# CONFIG_BT_AICS_LOG_LEVEL_ERR is not set
# CONFIG_BT_AICS_LOG_LEVEL_WRN is not set
# CONFIG_BT_AICS_LOG_LEVEL_INF is not set
# CONFIG_BT_AICS_LOG_LEVEL_DBG is not set
# CONFIG_BT_AICS_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_AICS_CLIENT_LOG_LEVEL=0
CONFIG_BT_AICS_CLIENT_LOG_LEVEL_INHERIT=y
# CONFIG_BT_AICS_CLIENT_LOG_LEVEL_OFF is not set
# CONFIG_BT_AICS_CLIENT_LOG_LEVEL_ERR is not set
# CONFIG_BT_AICS_CLIENT_LOG_LEVEL_WRN is not set
# CONFIG_BT_AICS_CLIENT_LOG_LEVEL_INF is not set
# CONFIG_BT_AICS_CLIENT_LOG_LEVEL_DBG is not set
# CONFIG_BT_AICS_CLIENT_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_BAP_STREAM_LOG_LEVEL=0
CONFIG_BT_BAP_STREAM_LOG_LEVEL_INHERIT=y
# CONFIG_BT_BAP_STREAM_LOG_LEVEL_OFF is not set
# CONFIG_BT_BAP_STREAM_LOG_LEVEL_ERR is not set
# CONFIG_BT_BAP_STREAM_LOG_LEVEL_WRN is not set
# CONFIG_BT_BAP_STREAM_LOG_LEVEL_INF is not set
# CONFIG_BT_BAP_STREAM_LOG_LEVEL_DBG is not set
# CONFIG_BT_BAP_STREAM_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_AUDIO_CODEC_LOG_LEVEL_INHERIT=y
CONFIG_BT_AUDIO_CODEC_LOG_LEVEL=0
# CONFIG_BT_AUDIO_CODEC_LOG_LEVEL_OFF is not set
# CONFIG_BT_AUDIO_CODEC_LOG_LEVEL_ERR is not set
# CONFIG_BT_AUDIO_CODEC_LOG_LEVEL_WRN is not set
# CONFIG_BT_AUDIO_CODEC_LOG_LEVEL_INF is not set
# CONFIG_BT_AUDIO_CODEC_LOG_LEVEL_DBG is not set
# CONFIG_BT_AUDIO_CODEC_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_ASCS_LOG_LEVEL=0
CONFIG_BT_ASCS_LOG_LEVEL_INHERIT=y
# CONFIG_BT_ASCS_LOG_LEVEL_OFF is not set
# CONFIG_BT_ASCS_LOG_LEVEL_ERR is not set
# CONFIG_BT_ASCS_LOG_LEVEL_WRN is not set
# CONFIG_BT_ASCS_LOG_LEVEL_INF is not set
# CONFIG_BT_ASCS_LOG_LEVEL_DBG is not set
# CONFIG_BT_ASCS_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_BAP_UNICAST_SERVER_LOG_LEVEL=0
CONFIG_BT_BAP_UNICAST_SERVER_LOG_LEVEL_INHERIT=y
# CONFIG_BT_BAP_UNICAST_SERVER_LOG_LEVEL_OFF is not set
# CONFIG_BT_BAP_UNICAST_SERVER_LOG_LEVEL_ERR is not set
# CONFIG_BT_BAP_UNICAST_SERVER_LOG_LEVEL_WRN is not set
# CONFIG_BT_BAP_UNICAST_SERVER_LOG_LEVEL_INF is not set
# CONFIG_BT_BAP_UNICAST_SERVER_LOG_LEVEL_DBG is not set
# CONFIG_BT_BAP_UNICAST_SERVER_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_BAP_UNICAST_CLIENT_LOG_LEVEL=0
CONFIG_BT_BAP_UNICAST_CLIENT_LOG_LEVEL_INHERIT=y
# CONFIG_BT_BAP_UNICAST_CLIENT_LOG_LEVEL_OFF is not set
# CONFIG_BT_BAP_UNICAST_CLIENT_LOG_LEVEL_ERR is not set
# CONFIG_BT_BAP_UNICAST_CLIENT_LOG_LEVEL_WRN is not set
# CONFIG_BT_BAP_UNICAST_CLIENT_LOG_LEVEL_INF is not set
# CONFIG_BT_BAP_UNICAST_CLIENT_LOG_LEVEL_DBG is not set
# CONFIG_BT_BAP_UNICAST_CLIENT_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_BAP_BROADCAST_SOURCE_LOG_LEVEL=0
CONFIG_BT_BAP_BROADCAST_SOURCE_LOG_LEVEL_INHERIT=y
# CONFIG_BT_BAP_BROADCAST_SOURCE_LOG_LEVEL_OFF is not set
# CONFIG_BT_BAP_BROADCAST_SOURCE_LOG_LEVEL_ERR is not set
# CONFIG_BT_BAP_BROADCAST_SOURCE_LOG_LEVEL_WRN is not set
# CONFIG_BT_BAP_BROADCAST_SOURCE_LOG_LEVEL_INF is not set
# CONFIG_BT_BAP_BROADCAST_SOURCE_LOG_LEVEL_DBG is not set
# CONFIG_BT_BAP_BROADCAST_SOURCE_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_BAP_BROADCAST_SINK_LOG_LEVEL=0
CONFIG_BT_BAP_BROADCAST_SINK_LOG_LEVEL_INHERIT=y
# CONFIG_BT_BAP_BROADCAST_SINK_LOG_LEVEL_OFF is not set
# CONFIG_BT_BAP_BROADCAST_SINK_LOG_LEVEL_ERR is not set
# CONFIG_BT_BAP_BROADCAST_SINK_LOG_LEVEL_WRN is not set
# CONFIG_BT_BAP_BROADCAST_SINK_LOG_LEVEL_INF is not set
# CONFIG_BT_BAP_BROADCAST_SINK_LOG_LEVEL_DBG is not set
# CONFIG_BT_BAP_BROADCAST_SINK_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_BAP_SCAN_DELEGATOR_LOG_LEVEL=0
CONFIG_BT_BAP_SCAN_DELEGATOR_LOG_LEVEL_INHERIT=y
# CONFIG_BT_BAP_SCAN_DELEGATOR_LOG_LEVEL_OFF is not set
# CONFIG_BT_BAP_SCAN_DELEGATOR_LOG_LEVEL_ERR is not set
# CONFIG_BT_BAP_SCAN_DELEGATOR_LOG_LEVEL_WRN is not set
# CONFIG_BT_BAP_SCAN_DELEGATOR_LOG_LEVEL_INF is not set
# CONFIG_BT_BAP_SCAN_DELEGATOR_LOG_LEVEL_DBG is not set
# CONFIG_BT_BAP_SCAN_DELEGATOR_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_BAP_BROADCAST_ASSISTANT_LOG_LEVEL=0
CONFIG_BT_BAP_BROADCAST_ASSISTANT_LOG_LEVEL_INHERIT=y
# CONFIG_BT_BAP_BROADCAST_ASSISTANT_LOG_LEVEL_OFF is not set
# CONFIG_BT_BAP_BROADCAST_ASSISTANT_LOG_LEVEL_ERR is not set
# CONFIG_BT_BAP_BROADCAST_ASSISTANT_LOG_LEVEL_WRN is not set
# CONFIG_BT_BAP_BROADCAST_ASSISTANT_LOG_LEVEL_INF is not set
# CONFIG_BT_BAP_BROADCAST_ASSISTANT_LOG_LEVEL_DBG is not set
# CONFIG_BT_BAP_BROADCAST_ASSISTANT_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_BAP_ISO_LOG_LEVEL_INHERIT=y
CONFIG_BT_BAP_ISO_LOG_LEVEL=0
# CONFIG_BT_BAP_ISO_LOG_LEVEL_OFF is not set
# CONFIG_BT_BAP_ISO_LOG_LEVEL_ERR is not set
# CONFIG_BT_BAP_ISO_LOG_LEVEL_WRN is not set
# CONFIG_BT_BAP_ISO_LOG_LEVEL_INF is not set
# CONFIG_BT_BAP_ISO_LOG_LEVEL_DBG is not set
# CONFIG_BT_BAP_ISO_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_CAP_ACCEPTOR_LOG_LEVEL=0
CONFIG_BT_CAP_ACCEPTOR_LOG_LEVEL_INHERIT=y
# CONFIG_BT_CAP_ACCEPTOR_LOG_LEVEL_OFF is not set
# CONFIG_BT_CAP_ACCEPTOR_LOG_LEVEL_ERR is not set
# CONFIG_BT_CAP_ACCEPTOR_LOG_LEVEL_WRN is not set
# CONFIG_BT_CAP_ACCEPTOR_LOG_LEVEL_INF is not set
# CONFIG_BT_CAP_ACCEPTOR_LOG_LEVEL_DBG is not set
# CONFIG_BT_CAP_ACCEPTOR_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_CAP_INITIATOR_LOG_LEVEL=0
CONFIG_BT_CAP_INITIATOR_LOG_LEVEL_INHERIT=y
# CONFIG_BT_CAP_INITIATOR_LOG_LEVEL_OFF is not set
# CONFIG_BT_CAP_INITIATOR_LOG_LEVEL_ERR is not set
# CONFIG_BT_CAP_INITIATOR_LOG_LEVEL_WRN is not set
# CONFIG_BT_CAP_INITIATOR_LOG_LEVEL_INF is not set
# CONFIG_BT_CAP_INITIATOR_LOG_LEVEL_DBG is not set
# CONFIG_BT_CAP_INITIATOR_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_CAP_STREAM_LOG_LEVEL_INHERIT=y
CONFIG_BT_CAP_STREAM_LOG_LEVEL=0
# CONFIG_BT_CAP_STREAM_LOG_LEVEL_OFF is not set
# CONFIG_BT_CAP_STREAM_LOG_LEVEL_ERR is not set
# CONFIG_BT_CAP_STREAM_LOG_LEVEL_WRN is not set
# CONFIG_BT_CAP_STREAM_LOG_LEVEL_INF is not set
# CONFIG_BT_CAP_STREAM_LOG_LEVEL_DBG is not set
# CONFIG_BT_CAP_STREAM_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_CSIP_SET_MEMBER_LOG_LEVEL=0
CONFIG_BT_CSIP_SET_MEMBER_LOG_LEVEL_INHERIT=y
# CONFIG_BT_CSIP_SET_MEMBER_LOG_LEVEL_OFF is not set
# CONFIG_BT_CSIP_SET_MEMBER_LOG_LEVEL_ERR is not set
# CONFIG_BT_CSIP_SET_MEMBER_LOG_LEVEL_WRN is not set
# CONFIG_BT_CSIP_SET_MEMBER_LOG_LEVEL_INF is not set
# CONFIG_BT_CSIP_SET_MEMBER_LOG_LEVEL_DBG is not set
# CONFIG_BT_CSIP_SET_MEMBER_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_CSIP_SET_COORDINATOR_LOG_LEVEL=0
CONFIG_BT_CSIP_SET_COORDINATOR_LOG_LEVEL_INHERIT=y
# CONFIG_BT_CSIP_SET_COORDINATOR_LOG_LEVEL_OFF is not set
# CONFIG_BT_CSIP_SET_COORDINATOR_LOG_LEVEL_ERR is not set
# CONFIG_BT_CSIP_SET_COORDINATOR_LOG_LEVEL_WRN is not set
# CONFIG_BT_CSIP_SET_COORDINATOR_LOG_LEVEL_INF is not set
# CONFIG_BT_CSIP_SET_COORDINATOR_LOG_LEVEL_DBG is not set
# CONFIG_BT_CSIP_SET_COORDINATOR_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_CSIP_SET_MEMBER_CRYPTO_LOG_LEVEL=0
CONFIG_BT_CSIP_SET_MEMBER_CRYPTO_LOG_LEVEL_INHERIT=y
# CONFIG_BT_CSIP_SET_MEMBER_CRYPTO_LOG_LEVEL_OFF is not set
# CONFIG_BT_CSIP_SET_MEMBER_CRYPTO_LOG_LEVEL_ERR is not set
# CONFIG_BT_CSIP_SET_MEMBER_CRYPTO_LOG_LEVEL_WRN is not set
# CONFIG_BT_CSIP_SET_MEMBER_CRYPTO_LOG_LEVEL_INF is not set
# CONFIG_BT_CSIP_SET_MEMBER_CRYPTO_LOG_LEVEL_DBG is not set
# CONFIG_BT_CSIP_SET_MEMBER_CRYPTO_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_HAS_LOG_LEVEL=0
CONFIG_BT_HAS_LOG_LEVEL_INHERIT=y
# CONFIG_BT_HAS_LOG_LEVEL_OFF is not set
# CONFIG_BT_HAS_LOG_LEVEL_ERR is not set
# CONFIG_BT_HAS_LOG_LEVEL_WRN is not set
# CONFIG_BT_HAS_LOG_LEVEL_INF is not set
# CONFIG_BT_HAS_LOG_LEVEL_DBG is not set
# CONFIG_BT_HAS_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_HAS_CLIENT_LOG_LEVEL=0
CONFIG_BT_HAS_CLIENT_LOG_LEVEL_INHERIT=y
# CONFIG_BT_HAS_CLIENT_LOG_LEVEL_OFF is not set
# CONFIG_BT_HAS_CLIENT_LOG_LEVEL_ERR is not set
# CONFIG_BT_HAS_CLIENT_LOG_LEVEL_WRN is not set
# CONFIG_BT_HAS_CLIENT_LOG_LEVEL_INF is not set
# CONFIG_BT_HAS_CLIENT_LOG_LEVEL_DBG is not set
# CONFIG_BT_HAS_CLIENT_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MCS_LOG_LEVEL=0
CONFIG_BT_MCS_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MCS_LOG_LEVEL_OFF is not set
# CONFIG_BT_MCS_LOG_LEVEL_ERR is not set
# CONFIG_BT_MCS_LOG_LEVEL_WRN is not set
# CONFIG_BT_MCS_LOG_LEVEL_INF is not set
# CONFIG_BT_MCS_LOG_LEVEL_DBG is not set
# CONFIG_BT_MCS_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MCC_LOG_LEVEL=0
CONFIG_BT_MCC_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MCC_LOG_LEVEL_OFF is not set
# CONFIG_BT_MCC_LOG_LEVEL_ERR is not set
# CONFIG_BT_MCC_LOG_LEVEL_WRN is not set
# CONFIG_BT_MCC_LOG_LEVEL_INF is not set
# CONFIG_BT_MCC_LOG_LEVEL_DBG is not set
# CONFIG_BT_MCC_LOG_LEVEL_DEFAULT is not set
CONFIG_MCTL_LOG_LEVEL=0
CONFIG_MCTL_LOG_LEVEL_INHERIT=y
# CONFIG_MCTL_LOG_LEVEL_OFF is not set
# CONFIG_MCTL_LOG_LEVEL_ERR is not set
# CONFIG_MCTL_LOG_LEVEL_WRN is not set
# CONFIG_MCTL_LOG_LEVEL_INF is not set
# CONFIG_MCTL_LOG_LEVEL_DBG is not set
# CONFIG_MCTL_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MICP_MIC_DEV_LOG_LEVEL=0
CONFIG_BT_MICP_MIC_DEV_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MICP_MIC_DEV_LOG_LEVEL_OFF is not set
# CONFIG_BT_MICP_MIC_DEV_LOG_LEVEL_ERR is not set
# CONFIG_BT_MICP_MIC_DEV_LOG_LEVEL_WRN is not set
# CONFIG_BT_MICP_MIC_DEV_LOG_LEVEL_INF is not set
# CONFIG_BT_MICP_MIC_DEV_LOG_LEVEL_DBG is not set
# CONFIG_BT_MICP_MIC_DEV_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MICP_MIC_CTLR_LOG_LEVEL=0
CONFIG_BT_MICP_MIC_CTLR_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MICP_MIC_CTLR_LOG_LEVEL_OFF is not set
# CONFIG_BT_MICP_MIC_CTLR_LOG_LEVEL_ERR is not set
# CONFIG_BT_MICP_MIC_CTLR_LOG_LEVEL_WRN is not set
# CONFIG_BT_MICP_MIC_CTLR_LOG_LEVEL_INF is not set
# CONFIG_BT_MICP_MIC_CTLR_LOG_LEVEL_DBG is not set
# CONFIG_BT_MICP_MIC_CTLR_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MPL_LOG_LEVEL=0
CONFIG_BT_MPL_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MPL_LOG_LEVEL_OFF is not set
# CONFIG_BT_MPL_LOG_LEVEL_ERR is not set
# CONFIG_BT_MPL_LOG_LEVEL_WRN is not set
# CONFIG_BT_MPL_LOG_LEVEL_INF is not set
# CONFIG_BT_MPL_LOG_LEVEL_DBG is not set
# CONFIG_BT_MPL_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_PACS_LOG_LEVEL=0
CONFIG_BT_PACS_LOG_LEVEL_INHERIT=y
# CONFIG_BT_PACS_LOG_LEVEL_OFF is not set
# CONFIG_BT_PACS_LOG_LEVEL_ERR is not set
# CONFIG_BT_PACS_LOG_LEVEL_WRN is not set
# CONFIG_BT_PACS_LOG_LEVEL_INF is not set
# CONFIG_BT_PACS_LOG_LEVEL_DBG is not set
# CONFIG_BT_PACS_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_TBS_LOG_LEVEL=0
CONFIG_BT_TBS_LOG_LEVEL_INHERIT=y
# CONFIG_BT_TBS_LOG_LEVEL_OFF is not set
# CONFIG_BT_TBS_LOG_LEVEL_ERR is not set
# CONFIG_BT_TBS_LOG_LEVEL_WRN is not set
# CONFIG_BT_TBS_LOG_LEVEL_INF is not set
# CONFIG_BT_TBS_LOG_LEVEL_DBG is not set
# CONFIG_BT_TBS_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_TBS_CLIENT_LOG_LEVEL=0
CONFIG_BT_TBS_CLIENT_LOG_LEVEL_INHERIT=y
# CONFIG_BT_TBS_CLIENT_LOG_LEVEL_OFF is not set
# CONFIG_BT_TBS_CLIENT_LOG_LEVEL_ERR is not set
# CONFIG_BT_TBS_CLIENT_LOG_LEVEL_WRN is not set
# CONFIG_BT_TBS_CLIENT_LOG_LEVEL_INF is not set
# CONFIG_BT_TBS_CLIENT_LOG_LEVEL_DBG is not set
# CONFIG_BT_TBS_CLIENT_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_VCP_VOL_REND_LOG_LEVEL=0
CONFIG_BT_VCP_VOL_REND_LOG_LEVEL_INHERIT=y
# CONFIG_BT_VCP_VOL_REND_LOG_LEVEL_OFF is not set
# CONFIG_BT_VCP_VOL_REND_LOG_LEVEL_ERR is not set
# CONFIG_BT_VCP_VOL_REND_LOG_LEVEL_WRN is not set
# CONFIG_BT_VCP_VOL_REND_LOG_LEVEL_INF is not set
# CONFIG_BT_VCP_VOL_REND_LOG_LEVEL_DBG is not set
# CONFIG_BT_VCP_VOL_REND_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_VCP_VOL_CTLR_LOG_LEVEL=0
CONFIG_BT_VCP_VOL_CTLR_LOG_LEVEL_INHERIT=y
# CONFIG_BT_VCP_VOL_CTLR_LOG_LEVEL_OFF is not set
# CONFIG_BT_VCP_VOL_CTLR_LOG_LEVEL_ERR is not set
# CONFIG_BT_VCP_VOL_CTLR_LOG_LEVEL_WRN is not set
# CONFIG_BT_VCP_VOL_CTLR_LOG_LEVEL_INF is not set
# CONFIG_BT_VCP_VOL_CTLR_LOG_LEVEL_DBG is not set
# CONFIG_BT_VCP_VOL_CTLR_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_VOCS_LOG_LEVEL=0
CONFIG_BT_VOCS_LOG_LEVEL_INHERIT=y
# CONFIG_BT_VOCS_LOG_LEVEL_OFF is not set
# CONFIG_BT_VOCS_LOG_LEVEL_ERR is not set
# CONFIG_BT_VOCS_LOG_LEVEL_WRN is not set
# CONFIG_BT_VOCS_LOG_LEVEL_INF is not set
# CONFIG_BT_VOCS_LOG_LEVEL_DBG is not set
# CONFIG_BT_VOCS_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_VOCS_CLIENT_LOG_LEVEL=0
CONFIG_BT_VOCS_CLIENT_LOG_LEVEL_INHERIT=y
# CONFIG_BT_VOCS_CLIENT_LOG_LEVEL_OFF is not set
# CONFIG_BT_VOCS_CLIENT_LOG_LEVEL_ERR is not set
# CONFIG_BT_VOCS_CLIENT_LOG_LEVEL_WRN is not set
# CONFIG_BT_VOCS_CLIENT_LOG_LEVEL_INF is not set
# CONFIG_BT_VOCS_CLIENT_LOG_LEVEL_DBG is not set
# CONFIG_BT_VOCS_CLIENT_LOG_LEVEL_DEFAULT is not set
# end of Audio

#
# Others
#
CONFIG_BT_CRYPTO_LOG_LEVEL=0
CONFIG_BT_CRYPTO_LOG_LEVEL_INHERIT=y
# CONFIG_BT_CRYPTO_LOG_LEVEL_OFF is not set
# CONFIG_BT_CRYPTO_LOG_LEVEL_ERR is not set
# CONFIG_BT_CRYPTO_LOG_LEVEL_WRN is not set
# CONFIG_BT_CRYPTO_LOG_LEVEL_INF is not set
# CONFIG_BT_CRYPTO_LOG_LEVEL_DBG is not set
# CONFIG_BT_CRYPTO_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_ATT_LOG_LEVEL=0
CONFIG_BT_ATT_LOG_LEVEL_INHERIT=y
# CONFIG_BT_ATT_LOG_LEVEL_OFF is not set
# CONFIG_BT_ATT_LOG_LEVEL_ERR is not set
# CONFIG_BT_ATT_LOG_LEVEL_WRN is not set
# CONFIG_BT_ATT_LOG_LEVEL_INF is not set
# CONFIG_BT_ATT_LOG_LEVEL_DBG is not set
# CONFIG_BT_ATT_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_GATT_LOG_LEVEL=0
CONFIG_BT_GATT_LOG_LEVEL_INHERIT=y
# CONFIG_BT_GATT_LOG_LEVEL_OFF is not set
# CONFIG_BT_GATT_LOG_LEVEL_ERR is not set
# CONFIG_BT_GATT_LOG_LEVEL_WRN is not set
# CONFIG_BT_GATT_LOG_LEVEL_INF is not set
# CONFIG_BT_GATT_LOG_LEVEL_DBG is not set
# CONFIG_BT_GATT_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_L2CAP_LOG_LEVEL=0
CONFIG_BT_L2CAP_LOG_LEVEL_INHERIT=y
# CONFIG_BT_L2CAP_LOG_LEVEL_OFF is not set
# CONFIG_BT_L2CAP_LOG_LEVEL_ERR is not set
# CONFIG_BT_L2CAP_LOG_LEVEL_WRN is not set
# CONFIG_BT_L2CAP_LOG_LEVEL_INF is not set
# CONFIG_BT_L2CAP_LOG_LEVEL_DBG is not set
# CONFIG_BT_L2CAP_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_EAD_LOG_LEVEL_INHERIT=y
CONFIG_BT_EAD_LOG_LEVEL=0
# CONFIG_BT_EAD_LOG_LEVEL_OFF is not set
# CONFIG_BT_EAD_LOG_LEVEL_ERR is not set
# CONFIG_BT_EAD_LOG_LEVEL_WRN is not set
# CONFIG_BT_EAD_LOG_LEVEL_INF is not set
# CONFIG_BT_EAD_LOG_LEVEL_DBG is not set
# CONFIG_BT_EAD_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_DF_LOG_LEVEL=0
CONFIG_BT_DF_LOG_LEVEL_INHERIT=y
# CONFIG_BT_DF_LOG_LEVEL_OFF is not set
# CONFIG_BT_DF_LOG_LEVEL_ERR is not set
# CONFIG_BT_DF_LOG_LEVEL_WRN is not set
# CONFIG_BT_DF_LOG_LEVEL_INF is not set
# CONFIG_BT_DF_LOG_LEVEL_DBG is not set
# CONFIG_BT_DF_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_SETTINGS_LOG_LEVEL=0
CONFIG_BT_SETTINGS_LOG_LEVEL_INHERIT=y
# CONFIG_BT_SETTINGS_LOG_LEVEL_OFF is not set
# CONFIG_BT_SETTINGS_LOG_LEVEL_ERR is not set
# CONFIG_BT_SETTINGS_LOG_LEVEL_WRN is not set
# CONFIG_BT_SETTINGS_LOG_LEVEL_INF is not set
# CONFIG_BT_SETTINGS_LOG_LEVEL_DBG is not set
# CONFIG_BT_SETTINGS_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_HCI_CORE_LOG_LEVEL=0
CONFIG_BT_HCI_CORE_LOG_LEVEL_INHERIT=y
# CONFIG_BT_HCI_CORE_LOG_LEVEL_OFF is not set
# CONFIG_BT_HCI_CORE_LOG_LEVEL_ERR is not set
# CONFIG_BT_HCI_CORE_LOG_LEVEL_WRN is not set
# CONFIG_BT_HCI_CORE_LOG_LEVEL_INF is not set
# CONFIG_BT_HCI_CORE_LOG_LEVEL_DBG is not set
# CONFIG_BT_HCI_CORE_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_CONN_LOG_LEVEL=0
CONFIG_BT_CONN_LOG_LEVEL_INHERIT=y
# CONFIG_BT_CONN_LOG_LEVEL_OFF is not set
# CONFIG_BT_CONN_LOG_LEVEL_ERR is not set
# CONFIG_BT_CONN_LOG_LEVEL_WRN is not set
# CONFIG_BT_CONN_LOG_LEVEL_INF is not set
# CONFIG_BT_CONN_LOG_LEVEL_DBG is not set
# CONFIG_BT_CONN_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_ISO_LOG_LEVEL=0
CONFIG_BT_ISO_LOG_LEVEL_INHERIT=y
# CONFIG_BT_ISO_LOG_LEVEL_OFF is not set
# CONFIG_BT_ISO_LOG_LEVEL_ERR is not set
# CONFIG_BT_ISO_LOG_LEVEL_WRN is not set
# CONFIG_BT_ISO_LOG_LEVEL_INF is not set
# CONFIG_BT_ISO_LOG_LEVEL_DBG is not set
# CONFIG_BT_ISO_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_KEYS_LOG_LEVEL=0
CONFIG_BT_KEYS_LOG_LEVEL_INHERIT=y
# CONFIG_BT_KEYS_LOG_LEVEL_OFF is not set
# CONFIG_BT_KEYS_LOG_LEVEL_ERR is not set
# CONFIG_BT_KEYS_LOG_LEVEL_WRN is not set
# CONFIG_BT_KEYS_LOG_LEVEL_INF is not set
# CONFIG_BT_KEYS_LOG_LEVEL_DBG is not set
# CONFIG_BT_KEYS_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_SMP_LOG_LEVEL=0
CONFIG_BT_SMP_LOG_LEVEL_INHERIT=y
# CONFIG_BT_SMP_LOG_LEVEL_OFF is not set
# CONFIG_BT_SMP_LOG_LEVEL_ERR is not set
# CONFIG_BT_SMP_LOG_LEVEL_WRN is not set
# CONFIG_BT_SMP_LOG_LEVEL_INF is not set
# CONFIG_BT_SMP_LOG_LEVEL_DBG is not set
# CONFIG_BT_SMP_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_SERVICE_LOG_LEVEL=0
CONFIG_BT_SERVICE_LOG_LEVEL_INHERIT=y
# CONFIG_BT_SERVICE_LOG_LEVEL_OFF is not set
# CONFIG_BT_SERVICE_LOG_LEVEL_ERR is not set
# CONFIG_BT_SERVICE_LOG_LEVEL_WRN is not set
# CONFIG_BT_SERVICE_LOG_LEVEL_INF is not set
# CONFIG_BT_SERVICE_LOG_LEVEL_DBG is not set
# CONFIG_BT_SERVICE_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_CTLR_ISOAL_LOG_LEVEL=0
CONFIG_BT_CTLR_ISOAL_LOG_LEVEL_INHERIT=y
# CONFIG_BT_CTLR_ISOAL_LOG_LEVEL_OFF is not set
# CONFIG_BT_CTLR_ISOAL_LOG_LEVEL_ERR is not set
# CONFIG_BT_CTLR_ISOAL_LOG_LEVEL_WRN is not set
# CONFIG_BT_CTLR_ISOAL_LOG_LEVEL_INF is not set
# CONFIG_BT_CTLR_ISOAL_LOG_LEVEL_DBG is not set
# CONFIG_BT_CTLR_ISOAL_LOG_LEVEL_DEFAULT is not set
# end of Others

#
# BR/EDR
#
CONFIG_BT_RFCOMM_LOG_LEVEL=0
CONFIG_BT_RFCOMM_LOG_LEVEL_INHERIT=y
# CONFIG_BT_RFCOMM_LOG_LEVEL_OFF is not set
# CONFIG_BT_RFCOMM_LOG_LEVEL_ERR is not set
# CONFIG_BT_RFCOMM_LOG_LEVEL_WRN is not set
# CONFIG_BT_RFCOMM_LOG_LEVEL_INF is not set
# CONFIG_BT_RFCOMM_LOG_LEVEL_DBG is not set
# CONFIG_BT_RFCOMM_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_HFP_HF_LOG_LEVEL=0
CONFIG_BT_HFP_HF_LOG_LEVEL_INHERIT=y
# CONFIG_BT_HFP_HF_LOG_LEVEL_OFF is not set
# CONFIG_BT_HFP_HF_LOG_LEVEL_ERR is not set
# CONFIG_BT_HFP_HF_LOG_LEVEL_WRN is not set
# CONFIG_BT_HFP_HF_LOG_LEVEL_INF is not set
# CONFIG_BT_HFP_HF_LOG_LEVEL_DBG is not set
# CONFIG_BT_HFP_HF_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_AVDTP_LOG_LEVEL=0
CONFIG_BT_AVDTP_LOG_LEVEL_INHERIT=y
# CONFIG_BT_AVDTP_LOG_LEVEL_OFF is not set
# CONFIG_BT_AVDTP_LOG_LEVEL_ERR is not set
# CONFIG_BT_AVDTP_LOG_LEVEL_WRN is not set
# CONFIG_BT_AVDTP_LOG_LEVEL_INF is not set
# CONFIG_BT_AVDTP_LOG_LEVEL_DBG is not set
# CONFIG_BT_AVDTP_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_A2DP_LOG_LEVEL=0
CONFIG_BT_A2DP_LOG_LEVEL_INHERIT=y
# CONFIG_BT_A2DP_LOG_LEVEL_OFF is not set
# CONFIG_BT_A2DP_LOG_LEVEL_ERR is not set
# CONFIG_BT_A2DP_LOG_LEVEL_WRN is not set
# CONFIG_BT_A2DP_LOG_LEVEL_INF is not set
# CONFIG_BT_A2DP_LOG_LEVEL_DBG is not set
# CONFIG_BT_A2DP_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_SDP_LOG_LEVEL=0
CONFIG_BT_SDP_LOG_LEVEL_INHERIT=y
# CONFIG_BT_SDP_LOG_LEVEL_OFF is not set
# CONFIG_BT_SDP_LOG_LEVEL_ERR is not set
# CONFIG_BT_SDP_LOG_LEVEL_WRN is not set
# CONFIG_BT_SDP_LOG_LEVEL_INF is not set
# CONFIG_BT_SDP_LOG_LEVEL_DBG is not set
# CONFIG_BT_SDP_LOG_LEVEL_DEFAULT is not set
# end of BR/EDR

#
# Mesh
#
CONFIG_BT_MESH_LOG_LEVEL=0
CONFIG_BT_MESH_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_NET_LOG_LEVEL=0
CONFIG_BT_MESH_NET_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_NET_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_NET_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_NET_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_NET_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_NET_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_NET_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_RPL_LOG_LEVEL=0
CONFIG_BT_MESH_RPL_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_RPL_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_RPL_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_RPL_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_RPL_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_RPL_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_RPL_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_TRANS_LOG_LEVEL=0
CONFIG_BT_MESH_TRANS_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_TRANS_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_TRANS_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_TRANS_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_TRANS_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_TRANS_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_TRANS_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_BEACON_LOG_LEVEL=0
CONFIG_BT_MESH_BEACON_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_BEACON_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_BEACON_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_BEACON_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_BEACON_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_BEACON_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_BEACON_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_CRYPTO_LOG_LEVEL=0
CONFIG_BT_MESH_CRYPTO_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_CRYPTO_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_CRYPTO_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_CRYPTO_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_CRYPTO_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_CRYPTO_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_CRYPTO_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_KEYS_LOG_LEVEL=0
CONFIG_BT_MESH_KEYS_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_KEYS_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_KEYS_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_KEYS_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_KEYS_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_KEYS_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_KEYS_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_PROV_LOG_LEVEL=0
CONFIG_BT_MESH_PROV_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_PROV_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_PROV_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_PROV_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_PROV_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_PROV_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_PROV_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_PROVISIONER_LOG_LEVEL=0
CONFIG_BT_MESH_PROVISIONER_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_PROVISIONER_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_PROVISIONER_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_PROVISIONER_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_PROVISIONER_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_PROVISIONER_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_PROVISIONER_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_PROV_DEVICE_LOG_LEVEL=0
CONFIG_BT_MESH_PROV_DEVICE_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_PROV_DEVICE_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_PROV_DEVICE_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_PROV_DEVICE_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_PROV_DEVICE_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_PROV_DEVICE_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_PROV_DEVICE_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_ACCESS_LOG_LEVEL=0
CONFIG_BT_MESH_ACCESS_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_ACCESS_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_ACCESS_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_ACCESS_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_ACCESS_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_ACCESS_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_ACCESS_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_MODEL_LOG_LEVEL=0
CONFIG_BT_MESH_MODEL_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_MODEL_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_MODEL_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_MODEL_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_MODEL_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_MODEL_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_MODEL_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_DFU_LOG_LEVEL=0
CONFIG_BT_MESH_DFU_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_DFU_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_DFU_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_DFU_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_DFU_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_DFU_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_DFU_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_ADV_LOG_LEVEL=0
CONFIG_BT_MESH_ADV_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_ADV_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_ADV_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_ADV_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_ADV_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_ADV_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_ADV_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_LOW_POWER_LOG_LEVEL=0
CONFIG_BT_MESH_LOW_POWER_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_LOW_POWER_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_LOW_POWER_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_LOW_POWER_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_LOW_POWER_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_LOW_POWER_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_LOW_POWER_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_FRIEND_LOG_LEVEL=0
CONFIG_BT_MESH_FRIEND_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_FRIEND_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_FRIEND_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_FRIEND_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_FRIEND_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_FRIEND_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_FRIEND_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_PROXY_LOG_LEVEL=0
CONFIG_BT_MESH_PROXY_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_PROXY_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_PROXY_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_PROXY_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_PROXY_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_PROXY_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_PROXY_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_SETTINGS_LOG_LEVEL=0
CONFIG_BT_MESH_SETTINGS_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_SETTINGS_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_SETTINGS_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_SETTINGS_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_SETTINGS_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_SETTINGS_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_SETTINGS_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_CDB_LOG_LEVEL=0
CONFIG_BT_MESH_CDB_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_CDB_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_CDB_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_CDB_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_CDB_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_CDB_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_CDB_LOG_LEVEL_DEFAULT is not set
CONFIG_BT_MESH_CFG_LOG_LEVEL=0
CONFIG_BT_MESH_CFG_LOG_LEVEL_INHERIT=y
# CONFIG_BT_MESH_CFG_LOG_LEVEL_OFF is not set
# CONFIG_BT_MESH_CFG_LOG_LEVEL_ERR is not set
# CONFIG_BT_MESH_CFG_LOG_LEVEL_WRN is not set
# CONFIG_BT_MESH_CFG_LOG_LEVEL_INF is not set
# CONFIG_BT_MESH_CFG_LOG_LEVEL_DBG is not set
# CONFIG_BT_MESH_CFG_LOG_LEVEL_DEFAULT is not set
# end of Mesh

#
# Services
#
# CONFIG_BT_BAS_LOG_LEVEL_OFF is not set
# CONFIG_BT_BAS_LOG_LEVEL_ERR is not set
# CONFIG_BT_BAS_LOG_LEVEL_WRN is not set
# CONFIG_BT_BAS_LOG_LEVEL_INF is not set
# CONFIG_BT_BAS_LOG_LEVEL_DBG is not set
CONFIG_BT_BAS_LOG_LEVEL_DEFAULT=y
CONFIG_BT_BAS_LOG_LEVEL=0
# CONFIG_BT_HRS_LOG_LEVEL_OFF is not set
# CONFIG_BT_HRS_LOG_LEVEL_ERR is not set
# CONFIG_BT_HRS_LOG_LEVEL_WRN is not set
# CONFIG_BT_HRS_LOG_LEVEL_INF is not set
# CONFIG_BT_HRS_LOG_LEVEL_DBG is not set
CONFIG_BT_HRS_LOG_LEVEL_DEFAULT=y
CONFIG_BT_HRS_LOG_LEVEL=0
# CONFIG_BT_TPS_LOG_LEVEL_OFF is not set
# CONFIG_BT_TPS_LOG_LEVEL_ERR is not set
# CONFIG_BT_TPS_LOG_LEVEL_WRN is not set
# CONFIG_BT_TPS_LOG_LEVEL_INF is not set
# CONFIG_BT_TPS_LOG_LEVEL_DBG is not set
CONFIG_BT_TPS_LOG_LEVEL_DEFAULT=y
CONFIG_BT_TPS_LOG_LEVEL=0
CONFIG_BT_IAS_CLIENT_LOG_LEVEL=0
CONFIG_BT_IAS_CLIENT_LOG_LEVEL_INHERIT=y
# CONFIG_BT_IAS_CLIENT_LOG_LEVEL_OFF is not set
# CONFIG_BT_IAS_CLIENT_LOG_LEVEL_ERR is not set
# CONFIG_BT_IAS_CLIENT_LOG_LEVEL_WRN is not set
# CONFIG_BT_IAS_CLIENT_LOG_LEVEL_INF is not set
# CONFIG_BT_IAS_CLIENT_LOG_LEVEL_DBG is not set
# CONFIG_BT_IAS_CLIENT_LOG_LEVEL_DEFAULT is not set
# CONFIG_BT_IAS_LOG_LEVEL_OFF is not set
# CONFIG_BT_IAS_LOG_LEVEL_ERR is not set
# CONFIG_BT_IAS_LOG_LEVEL_WRN is not set
# CONFIG_BT_IAS_LOG_LEVEL_INF is not set
# CONFIG_BT_IAS_LOG_LEVEL_DBG is not set
CONFIG_BT_IAS_LOG_LEVEL_DEFAULT=y
CONFIG_BT_IAS_LOG_LEVEL=0
CONFIG_BT_OTS_CLIENT_LOG_LEVEL=0
CONFIG_BT_OTS_CLIENT_LOG_LEVEL_INHERIT=y
# CONFIG_BT_OTS_CLIENT_LOG_LEVEL_OFF is not set
# CONFIG_BT_OTS_CLIENT_LOG_LEVEL_ERR is not set
# CONFIG_BT_OTS_CLIENT_LOG_LEVEL_WRN is not set
# CONFIG_BT_OTS_CLIENT_LOG_LEVEL_INF is not set
# CONFIG_BT_OTS_CLIENT_LOG_LEVEL_DBG is not set
# CONFIG_BT_OTS_CLIENT_LOG_LEVEL_DEFAULT is not set
# CONFIG_BT_OTS_LOG_LEVEL_OFF is not set
# CONFIG_BT_OTS_LOG_LEVEL_ERR is not set
# CONFIG_BT_OTS_LOG_LEVEL_WRN is not set
# CONFIG_BT_OTS_LOG_LEVEL_INF is not set
# CONFIG_BT_OTS_LOG_LEVEL_DBG is not set
CONFIG_BT_OTS_LOG_LEVEL_DEFAULT=y
CONFIG_BT_OTS_LOG_LEVEL=0
# end of Services
# end of Bluetooth logging

CONFIG_BT_COMPANY_ID=0x05F1

#
# Controller Area Network (CAN) bus subsystem
#
# CONFIG_ISOTP is not set
# end of Controller Area Network (CAN) bus subsystem

# CONFIG_CONSOLE_SUBSYS is not set

#
# System Monitoring Options
#
# CONFIG_THREAD_ANALYZER is not set
# end of System Monitoring Options

#
# Debugging Options
#
# CONFIG_DEBUG is not set
# CONFIG_STACK_USAGE is not set
# CONFIG_STACK_SENTINEL is not set
CONFIG_PRINTK=y
CONFIG_EARLY_CONSOLE=y
# CONFIG_ASSERT is not set
# CONFIG_FORCE_NO_ASSERT is not set
CONFIG_ASSERT_VERBOSE=y
# CONFIG_ASSERT_NO_FILE_INFO is not set
# CONFIG_ASSERT_NO_COND_INFO is not set
# CONFIG_ASSERT_NO_MSG_INFO is not set
# CONFIG_ASSERT_TEST is not set
# CONFIG_OVERRIDE_FRAME_POINTER_DEFAULT is not set
# CONFIG_DEBUG_INFO is not set
# CONFIG_DEBUG_THREAD_INFO is not set
# CONFIG_DEBUG_COREDUMP is not set
# end of Debugging Options

# CONFIG_DISK_ACCESS is not set
# CONFIG_DSP is not set
# CONFIG_EMUL is not set
# CONFIG_CHARACTER_FRAMEBUFFER is not set

#
# File Systems
#
# CONFIG_FILE_SYSTEM is not set
# CONFIG_FCB is not set
CONFIG_NVS=y
# CONFIG_NVS_LOOKUP_CACHE is not set
# CONFIG_NVS_LOG_LEVEL_OFF is not set
# CONFIG_NVS_LOG_LEVEL_ERR is not set
# CONFIG_NVS_LOG_LEVEL_WRN is not set
# CONFIG_NVS_LOG_LEVEL_INF is not set
# CONFIG_NVS_LOG_LEVEL_DBG is not set
CONFIG_NVS_LOG_LEVEL_DEFAULT=y
CONFIG_NVS_LOG_LEVEL=0
# end of File Systems

#
# Inter Processor Communication
#
# CONFIG_RPMSG_SERVICE is not set
CONFIG_IPC_SERVICE=y
CONFIG_IPC_SERVICE_REG_BACKEND_PRIORITY=46
CONFIG_IPC_SERVICE_BACKEND_RPMSG=y
CONFIG_IPC_SERVICE_BACKEND_RPMSG_WQ_STACK_SIZE=1024
CONFIG_IPC_SERVICE_BACKEND_RPMSG_NUM_ENDPOINTS_PER_INSTANCE=2
CONFIG_IPC_SERVICE_RPMSG=y
CONFIG_IPC_SERVICE_STATIC_VRINGS=y
CONFIG_IPC_SERVICE_STATIC_VRINGS_MEM_ALIGNMENT=4
# CONFIG_IPC_SERVICE_ICMSG is not set
# CONFIG_IPC_SERVICE_ICMSG_ME is not set
# CONFIG_IPC_SERVICE_LOG_LEVEL_OFF is not set
# CONFIG_IPC_SERVICE_LOG_LEVEL_ERR is not set
# CONFIG_IPC_SERVICE_LOG_LEVEL_WRN is not set
# CONFIG_IPC_SERVICE_LOG_LEVEL_INF is not set
# CONFIG_IPC_SERVICE_LOG_LEVEL_DBG is not set
CONFIG_IPC_SERVICE_LOG_LEVEL_DEFAULT=y
CONFIG_IPC_SERVICE_LOG_LEVEL=0
# end of Inter Processor Communication

# CONFIG_JWT is not set

#
# Logging
#
CONFIG_LOG=y
CONFIG_LOG_CORE_INIT_PRIORITY=0
CONFIG_LOG_MODE_DEFERRED=y
# CONFIG_LOG_MODE_IMMEDIATE is not set
# CONFIG_LOG_MODE_MINIMAL is not set
# CONFIG_LOG_FRONTEND is not set
# CONFIG_LOG_CUSTOM_HEADER is not set
# CONFIG_LOG_MULTIDOMAIN is not set

#
# Logging levels filtering
#
# CONFIG_LOG_RUNTIME_FILTERING is not set
CONFIG_LOG_DEFAULT_LEVEL=0
CONFIG_LOG_OVERRIDE_LEVEL=0
CONFIG_LOG_MAX_LEVEL=4
# end of Logging levels filtering

#
# Processing
#
CONFIG_LOG_PRINTK=y
CONFIG_LOG_MODE_OVERFLOW=y
# CONFIG_LOG_BLOCK_IN_THREAD is not set
CONFIG_LOG_PROCESS_TRIGGER_THRESHOLD=10
CONFIG_LOG_PROCESS_THREAD=y
CONFIG_LOG_PROCESS_THREAD_STARTUP_DELAY_MS=0
CONFIG_LOG_PROCESS_THREAD_SLEEP_MS=1000
CONFIG_LOG_PROCESS_THREAD_STACK_SIZE=768
# CONFIG_LOG_PROCESS_THREAD_CUSTOM_PRIORITY is not set
CONFIG_LOG_TRACE_SHORT_TIMESTAMP=y
# CONFIG_LOG_TIMESTAMP_64BIT is not set
# CONFIG_LOG_SPEED is not set
# end of Processing

#
# Output Formatting
#

#
# Prepend non-hexdump log message with function name
#
# CONFIG_LOG_FUNC_NAME_PREFIX_ERR is not set
# CONFIG_LOG_FUNC_NAME_PREFIX_WRN is not set
# CONFIG_LOG_FUNC_NAME_PREFIX_INF is not set
CONFIG_LOG_FUNC_NAME_PREFIX_DBG=y
# end of Prepend non-hexdump log message with function name

# CONFIG_LOG_MIPI_SYST_ENABLE is not set
# CONFIG_LOG_THREAD_ID_PREFIX is not set
# CONFIG_LOG_CUSTOM_FORMAT_SUPPORT is not set
CONFIG_LOG_BACKEND_SHOW_COLOR=y
# CONFIG_LOG_INFO_COLOR_GREEN is not set
CONFIG_LOG_TAG_MAX_LEN=0
CONFIG_LOG_BACKEND_FORMAT_TIMESTAMP=y
# CONFIG_LOG_OUTPUT_FORMAT_LINUX_TIMESTAMP is not set
# CONFIG_LOG_OUTPUT_FORMAT_CUSTOM_TIMESTAMP is not set
# end of Output Formatting

#
# Backends
#
CONFIG_LOG_BACKEND_RTT=y
# CONFIG_LOG_BACKEND_RTT_MODE_DROP is not set
CONFIG_LOG_BACKEND_RTT_MODE_BLOCK=y
# CONFIG_LOG_BACKEND_RTT_MODE_OVERWRITE is not set
CONFIG_LOG_BACKEND_RTT_OUTPUT_TEXT=y
# CONFIG_LOG_BACKEND_RTT_OUTPUT_DICTIONARY is not set
# CONFIG_LOG_BACKEND_RTT_OUTPUT_CUSTOM is not set
CONFIG_LOG_BACKEND_RTT_OUTPUT_DEFAULT=0
CONFIG_LOG_BACKEND_RTT_OUTPUT_BUFFER_SIZE=16
CONFIG_LOG_BACKEND_RTT_RETRY_CNT=4
CONFIG_LOG_BACKEND_RTT_RETRY_DELAY_MS=5
CONFIG_LOG_BACKEND_RTT_BUFFER=0
CONFIG_LOG_BACKEND_RTT_FORCE_PRINTK=y
# CONFIG_LOG_BACKEND_SWO is not set
# CONFIG_LOG_BACKEND_IPC_SERVICE is not set
# end of Backends

#
# Misc
#
CONFIG_LOG_DOMAIN_ID=0
CONFIG_LOG_USE_VLA=y
# CONFIG_LOG_ALWAYS_RUNTIME is not set
# CONFIG_LOG_FMT_SECTION is not set
# CONFIG_LOG_MEM_UTILIZATION is not set
CONFIG_LOG_FAILURE_REPORT_PERIOD=1000
# end of Misc

CONFIG_LOG_OUTPUT=y
# end of Logging

#
# Device Management
#

#
# Host command handler subsystem
#
# CONFIG_EC_HOST_CMD is not set
# end of Host command handler subsystem

# CONFIG_OSDP is not set
# end of Device Management

# CONFIG_MODBUS is not set
# CONFIG_MODEM_MODULES is not set

#
# Networking
#
CONFIG_NET_BUF=y
# CONFIG_NET_BUF_LOG is not set
# CONFIG_NET_BUF_LOG_LEVEL_OFF is not set
# CONFIG_NET_BUF_LOG_LEVEL_ERR is not set
# CONFIG_NET_BUF_LOG_LEVEL_WRN is not set
# CONFIG_NET_BUF_LOG_LEVEL_INF is not set
# CONFIG_NET_BUF_LOG_LEVEL_DBG is not set
CONFIG_NET_BUF_LOG_LEVEL_DEFAULT=y
CONFIG_NET_BUF_LOG_LEVEL=0
# CONFIG_NET_BUF_POOL_USAGE is not set
# CONFIG_NETWORKING is not set
# end of Networking

#
# Power Management
#
# end of Power Management

#
# Portability
#
# end of Portability

#
# Random Number Generators
#
# CONFIG_TEST_RANDOM_GENERATOR is not set
# CONFIG_ENTROPY_DEVICE_RANDOM_GENERATOR is not set
# CONFIG_XOROSHIRO_RANDOM_GENERATOR is not set
CONFIG_XOSHIRO_RANDOM_GENERATOR=y
CONFIG_CSPRING_ENABLED=y
CONFIG_HARDWARE_DEVICE_CS_GENERATOR=y
# CONFIG_CTR_DRBG_CSPRNG_GENERATOR is not set
# end of Random Number Generators

# CONFIG_RTIO is not set

#
# SD
#
# CONFIG_MMC_STACK is not set
# CONFIG_SDMMC_STACK is not set
# CONFIG_SDIO_STACK is not set
# end of SD

CONFIG_SETTINGS=y
# CONFIG_SETTINGS_LOG_LEVEL_OFF is not set
# CONFIG_SETTINGS_LOG_LEVEL_ERR is not set
# CONFIG_SETTINGS_LOG_LEVEL_WRN is not set
# CONFIG_SETTINGS_LOG_LEVEL_INF is not set
# CONFIG_SETTINGS_LOG_LEVEL_DBG is not set
CONFIG_SETTINGS_LOG_LEVEL_DEFAULT=y
CONFIG_SETTINGS_LOG_LEVEL=0
# CONFIG_SETTINGS_RUNTIME is not set
CONFIG_SETTINGS_DYNAMIC_HANDLERS=y
CONFIG_SETTINGS_NVS=y
# CONFIG_SETTINGS_NVS_NAME_CACHE is not set
# CONFIG_SETTINGS_CUSTOM is not set
# CONFIG_SETTINGS_NONE is not set
CONFIG_SETTINGS_NVS_SECTOR_SIZE_MULT=1
CONFIG_SETTINGS_NVS_SECTOR_COUNT=8
# CONFIG_SHELL is not set
# CONFIG_STATS is not set

#
# Storage
#
CONFIG_FLASH_MAP=y
# CONFIG_FLASH_AREA_CHECK_INTEGRITY is not set
# CONFIG_FLASH_MAP_LABELS is not set
# CONFIG_STREAM_FLASH is not set
# end of Storage

# CONFIG_TASK_WDT is not set

#
# Testing
#
# CONFIG_ZTEST is not set
# CONFIG_ZTEST_MOCKING is not set
# CONFIG_ZTRESS is not set
# CONFIG_TEST is not set
CONFIG_COVERAGE_GCOV_HEAP_SIZE=16384
# CONFIG_TEST_USERSPACE is not set
# end of Testing

# CONFIG_TIMING_FUNCTIONS is not set
# CONFIG_TRACING is not set
# CONFIG_USB_DEVICE_STACK is not set
# CONFIG_USB_DEVICE_STACK_NEXT is not set
# CONFIG_USB_HOST_STACK is not set
# CONFIG_USBC_STACK is not set
# CONFIG_ZBUS is not set
# end of Subsystems and OS Services

CONFIG_TOOLCHAIN_ZEPHYR_0_16=y
CONFIG_TOOLCHAIN_ZEPHYR_SUPPORTS_THREAD_LOCAL_STORAGE=y

#
# Build and Link Features
#

#
# Linker Options
#
# CONFIG_LINKER_ORPHAN_SECTION_PLACE is not set
CONFIG_LINKER_ORPHAN_SECTION_WARN=y
# CONFIG_LINKER_ORPHAN_SECTION_ERROR is not set
CONFIG_HAS_FLASH_LOAD_OFFSET=y
CONFIG_LD_LINKER_SCRIPT_SUPPORTED=y
CONFIG_LD_LINKER_TEMPLATE=y
# CONFIG_CMAKE_LINKER_GENERATOR is not set
# CONFIG_HAVE_CUSTOM_LINKER_SCRIPT is not set
CONFIG_KERNEL_ENTRY="__start"
CONFIG_LINKER_SORT_BY_ALIGNMENT=y
CONFIG_SRAM_OFFSET=0

#
# Linker Sections
#
# CONFIG_LINKER_USE_BOOT_SECTION is not set
# CONFIG_LINKER_USE_PINNED_SECTION is not set
CONFIG_LINKER_GENERIC_SECTIONS_PRESENT_AT_BOOT=y
CONFIG_LINKER_LAST_SECTION_ID=y
CONFIG_LINKER_LAST_SECTION_ID_PATTERN=0xE015E015
CONFIG_LINKER_USE_RELAX=y
# end of Linker Sections
# end of Linker Options

#
# Compiler Options
#
# CONFIG_CODING_GUIDELINE_CHECK is not set
# CONFIG_COMPILER_FREESTANDING is not set
CONFIG_SIZE_OPTIMIZATIONS=y
# CONFIG_SPEED_OPTIMIZATIONS is not set
# CONFIG_DEBUG_OPTIMIZATIONS is not set
# CONFIG_NO_OPTIMIZATIONS is not set
# CONFIG_COMPILER_WARNINGS_AS_ERRORS is not set
# CONFIG_COMPILER_SAVE_TEMPS is not set
CONFIG_COMPILER_TRACK_MACRO_EXPANSION=y
CONFIG_COMPILER_COLOR_DIAGNOSTICS=y
# CONFIG_FORTIFY_SOURCE_NONE is not set
CONFIG_FORTIFY_SOURCE_COMPILE_TIME=y
# CONFIG_FORTIFY_SOURCE_RUN_TIME is not set
CONFIG_COMPILER_OPT=""
# CONFIG_MISRA_SANE is not set
# end of Compiler Options

# CONFIG_ASSERT_ON_ERRORS is not set
# CONFIG_NO_RUNTIME_CHECKS is not set
CONFIG_RUNTIME_ERROR_CHECKS=y

#
# Build Options
#
CONFIG_KERNEL_BIN_NAME="zephyr"
CONFIG_OUTPUT_STAT=y
# CONFIG_OUTPUT_SYMBOLS is not set
# CONFIG_OUTPUT_DISASSEMBLY is not set
CONFIG_OUTPUT_PRINT_MEMORY_USAGE=y
# CONFIG_CLEANUP_INTERMEDIATE_FILES is not set
# CONFIG_BUILD_NO_GAP_FILL is not set
# CONFIG_BUILD_OUTPUT_EXE is not set
# CONFIG_BUILD_OUTPUT_S19 is not set
# CONFIG_BUILD_OUTPUT_UF2 is not set
# CONFIG_BUILD_OUTPUT_STRIPPED is not set
# CONFIG_BUILD_ALIGN_LMA is not set
# CONFIG_APPLICATION_DEFINED_SYSCALL is not set
# CONFIG_MAKEFILE_EXPORTS is not set
# CONFIG_BUILD_OUTPUT_META is not set
CONFIG_BUILD_OUTPUT_STRIP_PATHS=y
CONFIG_CHECK_INIT_PRIORITIES=y
# CONFIG_CHECK_INIT_PRIORITIES_FAIL_ON_WARNING is not set
# CONFIG_EMIT_ALL_SYSCALLS is not set
# end of Build Options

CONFIG_WARN_DEPRECATED=y
CONFIG_EXPERIMENTAL=y
CONFIG_ENFORCE_ZEPHYR_STDINT=y
# end of Build and Link Features

#
# Boot Options
#
# CONFIG_IS_BOOTLOADER is not set
# CONFIG_BOOTLOADER_BOSSA is not set
# end of Boot Options

#
# Compatibility
#
CONFIG_COMPAT_INCLUDES=y
# end of Compatibility
