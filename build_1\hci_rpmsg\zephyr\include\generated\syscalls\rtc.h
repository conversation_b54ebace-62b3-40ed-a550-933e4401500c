/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_RTC_H
#define Z_INCLUDE_SYSCALLS_RTC_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_rtc_set_time(const struct device * dev, const struct rtc_time * timeptr);

__pinned_func
static inline int rtc_set_time(const struct device * dev, const struct rtc_time * timeptr)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const struct rtc_time * val; } parm1 = { .val = timeptr };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_RTC_SET_TIME);
	}
#endif
	compiler_barrier();
	return z_impl_rtc_set_time(dev, timeptr);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define rtc_set_time(dev, timeptr) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_RTC_SET_TIME, rtc_set_time, dev, timeptr); 	syscall__retval = rtc_set_time(dev, timeptr); 	sys_port_trace_syscall_exit(K_SYSCALL_RTC_SET_TIME, rtc_set_time, dev, timeptr, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_rtc_get_time(const struct device * dev, struct rtc_time * timeptr);

__pinned_func
static inline int rtc_get_time(const struct device * dev, struct rtc_time * timeptr)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct rtc_time * val; } parm1 = { .val = timeptr };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_RTC_GET_TIME);
	}
#endif
	compiler_barrier();
	return z_impl_rtc_get_time(dev, timeptr);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define rtc_get_time(dev, timeptr) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_RTC_GET_TIME, rtc_get_time, dev, timeptr); 	syscall__retval = rtc_get_time(dev, timeptr); 	sys_port_trace_syscall_exit(K_SYSCALL_RTC_GET_TIME, rtc_get_time, dev, timeptr, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_rtc_alarm_get_supported_fields(const struct device * dev, uint16_t id, uint16_t * mask);

__pinned_func
static inline int rtc_alarm_get_supported_fields(const struct device * dev, uint16_t id, uint16_t * mask)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint16_t val; } parm1 = { .val = id };
		union { uintptr_t x; uint16_t * val; } parm2 = { .val = mask };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_RTC_ALARM_GET_SUPPORTED_FIELDS);
	}
#endif
	compiler_barrier();
	return z_impl_rtc_alarm_get_supported_fields(dev, id, mask);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define rtc_alarm_get_supported_fields(dev, id, mask) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_RTC_ALARM_GET_SUPPORTED_FIELDS, rtc_alarm_get_supported_fields, dev, id, mask); 	syscall__retval = rtc_alarm_get_supported_fields(dev, id, mask); 	sys_port_trace_syscall_exit(K_SYSCALL_RTC_ALARM_GET_SUPPORTED_FIELDS, rtc_alarm_get_supported_fields, dev, id, mask, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_rtc_alarm_set_time(const struct device * dev, uint16_t id, uint16_t mask, const struct rtc_time * timeptr);

__pinned_func
static inline int rtc_alarm_set_time(const struct device * dev, uint16_t id, uint16_t mask, const struct rtc_time * timeptr)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint16_t val; } parm1 = { .val = id };
		union { uintptr_t x; uint16_t val; } parm2 = { .val = mask };
		union { uintptr_t x; const struct rtc_time * val; } parm3 = { .val = timeptr };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_RTC_ALARM_SET_TIME);
	}
#endif
	compiler_barrier();
	return z_impl_rtc_alarm_set_time(dev, id, mask, timeptr);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define rtc_alarm_set_time(dev, id, mask, timeptr) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_RTC_ALARM_SET_TIME, rtc_alarm_set_time, dev, id, mask, timeptr); 	syscall__retval = rtc_alarm_set_time(dev, id, mask, timeptr); 	sys_port_trace_syscall_exit(K_SYSCALL_RTC_ALARM_SET_TIME, rtc_alarm_set_time, dev, id, mask, timeptr, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_rtc_alarm_get_time(const struct device * dev, uint16_t id, uint16_t * mask, struct rtc_time * timeptr);

__pinned_func
static inline int rtc_alarm_get_time(const struct device * dev, uint16_t id, uint16_t * mask, struct rtc_time * timeptr)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint16_t val; } parm1 = { .val = id };
		union { uintptr_t x; uint16_t * val; } parm2 = { .val = mask };
		union { uintptr_t x; struct rtc_time * val; } parm3 = { .val = timeptr };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_RTC_ALARM_GET_TIME);
	}
#endif
	compiler_barrier();
	return z_impl_rtc_alarm_get_time(dev, id, mask, timeptr);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define rtc_alarm_get_time(dev, id, mask, timeptr) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_RTC_ALARM_GET_TIME, rtc_alarm_get_time, dev, id, mask, timeptr); 	syscall__retval = rtc_alarm_get_time(dev, id, mask, timeptr); 	sys_port_trace_syscall_exit(K_SYSCALL_RTC_ALARM_GET_TIME, rtc_alarm_get_time, dev, id, mask, timeptr, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_rtc_alarm_is_pending(const struct device * dev, uint16_t id);

__pinned_func
static inline int rtc_alarm_is_pending(const struct device * dev, uint16_t id)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint16_t val; } parm1 = { .val = id };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_RTC_ALARM_IS_PENDING);
	}
#endif
	compiler_barrier();
	return z_impl_rtc_alarm_is_pending(dev, id);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define rtc_alarm_is_pending(dev, id) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_RTC_ALARM_IS_PENDING, rtc_alarm_is_pending, dev, id); 	syscall__retval = rtc_alarm_is_pending(dev, id); 	sys_port_trace_syscall_exit(K_SYSCALL_RTC_ALARM_IS_PENDING, rtc_alarm_is_pending, dev, id, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_rtc_alarm_set_callback(const struct device * dev, uint16_t id, rtc_alarm_callback callback, void * user_data);

__pinned_func
static inline int rtc_alarm_set_callback(const struct device * dev, uint16_t id, rtc_alarm_callback callback, void * user_data)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint16_t val; } parm1 = { .val = id };
		union { uintptr_t x; rtc_alarm_callback val; } parm2 = { .val = callback };
		union { uintptr_t x; void * val; } parm3 = { .val = user_data };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_RTC_ALARM_SET_CALLBACK);
	}
#endif
	compiler_barrier();
	return z_impl_rtc_alarm_set_callback(dev, id, callback, user_data);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define rtc_alarm_set_callback(dev, id, callback, user_data) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_RTC_ALARM_SET_CALLBACK, rtc_alarm_set_callback, dev, id, callback, user_data); 	syscall__retval = rtc_alarm_set_callback(dev, id, callback, user_data); 	sys_port_trace_syscall_exit(K_SYSCALL_RTC_ALARM_SET_CALLBACK, rtc_alarm_set_callback, dev, id, callback, user_data, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_rtc_update_set_callback(const struct device * dev, rtc_update_callback callback, void * user_data);

__pinned_func
static inline int rtc_update_set_callback(const struct device * dev, rtc_update_callback callback, void * user_data)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; rtc_update_callback val; } parm1 = { .val = callback };
		union { uintptr_t x; void * val; } parm2 = { .val = user_data };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_RTC_UPDATE_SET_CALLBACK);
	}
#endif
	compiler_barrier();
	return z_impl_rtc_update_set_callback(dev, callback, user_data);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define rtc_update_set_callback(dev, callback, user_data) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_RTC_UPDATE_SET_CALLBACK, rtc_update_set_callback, dev, callback, user_data); 	syscall__retval = rtc_update_set_callback(dev, callback, user_data); 	sys_port_trace_syscall_exit(K_SYSCALL_RTC_UPDATE_SET_CALLBACK, rtc_update_set_callback, dev, callback, user_data, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_rtc_set_calibration(const struct device * dev, int32_t calibration);

__pinned_func
static inline int rtc_set_calibration(const struct device * dev, int32_t calibration)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; int32_t val; } parm1 = { .val = calibration };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_RTC_SET_CALIBRATION);
	}
#endif
	compiler_barrier();
	return z_impl_rtc_set_calibration(dev, calibration);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define rtc_set_calibration(dev, calibration) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_RTC_SET_CALIBRATION, rtc_set_calibration, dev, calibration); 	syscall__retval = rtc_set_calibration(dev, calibration); 	sys_port_trace_syscall_exit(K_SYSCALL_RTC_SET_CALIBRATION, rtc_set_calibration, dev, calibration, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_rtc_get_calibration(const struct device * dev, int32_t * calibration);

__pinned_func
static inline int rtc_get_calibration(const struct device * dev, int32_t * calibration)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; int32_t * val; } parm1 = { .val = calibration };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_RTC_GET_CALIBRATION);
	}
#endif
	compiler_barrier();
	return z_impl_rtc_get_calibration(dev, calibration);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define rtc_get_calibration(dev, calibration) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_RTC_GET_CALIBRATION, rtc_get_calibration, dev, calibration); 	syscall__retval = rtc_get_calibration(dev, calibration); 	sys_port_trace_syscall_exit(K_SYSCALL_RTC_GET_CALIBRATION, rtc_get_calibration, dev, calibration, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
