empty_file.o: C:/ncs/v2.5.99-dev1/zephyr/misc/empty_file.c \
 C:/ncs/v2.5.99-dev1/zephyr/boards/arm/nrf5340dk_nrf5340/nrf5340dk_nrf5340_cpuapp_ns.dts \
 C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf5340_cpuappns_qkaa.dtsi \
 C:/ncs/v2.5.99-dev1/zephyr/dts/common/mem.h \
 C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf5340_cpuappns.dtsi \
 C:/ncs/v2.5.99-dev1/zephyr/dts/arm/armv8-m.dtsi \
 C:/ncs/v2.5.99-dev1/zephyr/dts/common/skeleton.dtsi \
 C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf_common.dtsi \
 C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/adc/adc.h \
 C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/adc/nrf-adc.h \
 C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/dt-util.h \
 C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/sys/util_macro.h \
 C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/sys/util_internal.h \
 C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/sys/util_loops.h \
 C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/gpio/gpio.h \
 C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/i2c/i2c.h \
 C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/pinctrl/nrf-pinctrl.h \
 C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/pwm/pwm.h \
 C:/ncs/v2.5.99-dev1/zephyr/dts/common/freq.h \
 C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/override.dtsi \
 C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf5340_cpuapp_peripherals.dtsi \
 C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf5340_cpuapp_ipc.dtsi \
 C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf5340_cpuapp_peripherals_ns.dtsi \
 C:/ncs/v2.5.99-dev1/zephyr/boards/arm/nrf5340dk_nrf5340/nrf5340_cpuapp_common.dtsi \
 C:/ncs/v2.5.99-dev1/zephyr/boards/arm/nrf5340dk_nrf5340/nrf5340_cpuapp_common-pinctrl.dtsi \
 C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/input/input-event-codes.h \
 C:/ncs/v2.5.99-dev1/zephyr/boards/arm/nrf5340dk_nrf5340/nrf5340_cpuapp_partition_conf.dtsi \
 C:/ncs/v2.5.99-dev1/zephyr/boards/arm/nrf5340dk_nrf5340/nrf5340_shared_sram_planning_conf.dtsi \
 C:/Users/<USER>/Music/nordic-nRF-as7058/boards/nrf5340dk_nrf5340_cpuapp_ns.overlay
