/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_CAN_H
#define Z_INCLUDE_SYSCALLS_CAN_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_can_get_core_clock(const struct device * dev, uint32_t * rate);

__pinned_func
static inline int can_get_core_clock(const struct device * dev, uint32_t * rate)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t * val; } parm1 = { .val = rate };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_CAN_GET_CORE_CLOCK);
	}
#endif
	compiler_barrier();
	return z_impl_can_get_core_clock(dev, rate);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_get_core_clock(dev, rate) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_GET_CORE_CLOCK, can_get_core_clock, dev, rate); 	syscall__retval = can_get_core_clock(dev, rate); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_GET_CORE_CLOCK, can_get_core_clock, dev, rate, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_can_get_max_bitrate(const struct device * dev, uint32_t * max_bitrate);

__pinned_func
static inline int can_get_max_bitrate(const struct device * dev, uint32_t * max_bitrate)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t * val; } parm1 = { .val = max_bitrate };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_CAN_GET_MAX_BITRATE);
	}
#endif
	compiler_barrier();
	return z_impl_can_get_max_bitrate(dev, max_bitrate);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_get_max_bitrate(dev, max_bitrate) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_GET_MAX_BITRATE, can_get_max_bitrate, dev, max_bitrate); 	syscall__retval = can_get_max_bitrate(dev, max_bitrate); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_GET_MAX_BITRATE, can_get_max_bitrate, dev, max_bitrate, syscall__retval); 	syscall__retval; })
#endif
#endif


extern const struct can_timing * z_impl_can_get_timing_min(const struct device * dev);

__pinned_func
static inline const struct can_timing * can_get_timing_min(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (const struct can_timing *) arch_syscall_invoke1(parm0.x, K_SYSCALL_CAN_GET_TIMING_MIN);
	}
#endif
	compiler_barrier();
	return z_impl_can_get_timing_min(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_get_timing_min(dev) ({ 	const struct can_timing * syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_GET_TIMING_MIN, can_get_timing_min, dev); 	syscall__retval = can_get_timing_min(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_GET_TIMING_MIN, can_get_timing_min, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern const struct can_timing * z_impl_can_get_timing_max(const struct device * dev);

__pinned_func
static inline const struct can_timing * can_get_timing_max(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (const struct can_timing *) arch_syscall_invoke1(parm0.x, K_SYSCALL_CAN_GET_TIMING_MAX);
	}
#endif
	compiler_barrier();
	return z_impl_can_get_timing_max(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_get_timing_max(dev) ({ 	const struct can_timing * syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_GET_TIMING_MAX, can_get_timing_max, dev); 	syscall__retval = can_get_timing_max(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_GET_TIMING_MAX, can_get_timing_max, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_can_calc_timing(const struct device * dev, struct can_timing * res, uint32_t bitrate, uint16_t sample_pnt);

__pinned_func
static inline int can_calc_timing(const struct device * dev, struct can_timing * res, uint32_t bitrate, uint16_t sample_pnt)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct can_timing * val; } parm1 = { .val = res };
		union { uintptr_t x; uint32_t val; } parm2 = { .val = bitrate };
		union { uintptr_t x; uint16_t val; } parm3 = { .val = sample_pnt };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_CAN_CALC_TIMING);
	}
#endif
	compiler_barrier();
	return z_impl_can_calc_timing(dev, res, bitrate, sample_pnt);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_calc_timing(dev, res, bitrate, sample_pnt) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_CALC_TIMING, can_calc_timing, dev, res, bitrate, sample_pnt); 	syscall__retval = can_calc_timing(dev, res, bitrate, sample_pnt); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_CALC_TIMING, can_calc_timing, dev, res, bitrate, sample_pnt, syscall__retval); 	syscall__retval; })
#endif
#endif


extern const struct can_timing * z_impl_can_get_timing_data_min(const struct device * dev);

__pinned_func
static inline const struct can_timing * can_get_timing_data_min(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (const struct can_timing *) arch_syscall_invoke1(parm0.x, K_SYSCALL_CAN_GET_TIMING_DATA_MIN);
	}
#endif
	compiler_barrier();
	return z_impl_can_get_timing_data_min(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_get_timing_data_min(dev) ({ 	const struct can_timing * syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_GET_TIMING_DATA_MIN, can_get_timing_data_min, dev); 	syscall__retval = can_get_timing_data_min(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_GET_TIMING_DATA_MIN, can_get_timing_data_min, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern const struct can_timing * z_impl_can_get_timing_data_max(const struct device * dev);

__pinned_func
static inline const struct can_timing * can_get_timing_data_max(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (const struct can_timing *) arch_syscall_invoke1(parm0.x, K_SYSCALL_CAN_GET_TIMING_DATA_MAX);
	}
#endif
	compiler_barrier();
	return z_impl_can_get_timing_data_max(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_get_timing_data_max(dev) ({ 	const struct can_timing * syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_GET_TIMING_DATA_MAX, can_get_timing_data_max, dev); 	syscall__retval = can_get_timing_data_max(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_GET_TIMING_DATA_MAX, can_get_timing_data_max, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_can_calc_timing_data(const struct device * dev, struct can_timing * res, uint32_t bitrate, uint16_t sample_pnt);

__pinned_func
static inline int can_calc_timing_data(const struct device * dev, struct can_timing * res, uint32_t bitrate, uint16_t sample_pnt)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct can_timing * val; } parm1 = { .val = res };
		union { uintptr_t x; uint32_t val; } parm2 = { .val = bitrate };
		union { uintptr_t x; uint16_t val; } parm3 = { .val = sample_pnt };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_CAN_CALC_TIMING_DATA);
	}
#endif
	compiler_barrier();
	return z_impl_can_calc_timing_data(dev, res, bitrate, sample_pnt);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_calc_timing_data(dev, res, bitrate, sample_pnt) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_CALC_TIMING_DATA, can_calc_timing_data, dev, res, bitrate, sample_pnt); 	syscall__retval = can_calc_timing_data(dev, res, bitrate, sample_pnt); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_CALC_TIMING_DATA, can_calc_timing_data, dev, res, bitrate, sample_pnt, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_can_set_timing_data(const struct device * dev, const struct can_timing * timing_data);

__pinned_func
static inline int can_set_timing_data(const struct device * dev, const struct can_timing * timing_data)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const struct can_timing * val; } parm1 = { .val = timing_data };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_CAN_SET_TIMING_DATA);
	}
#endif
	compiler_barrier();
	return z_impl_can_set_timing_data(dev, timing_data);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_set_timing_data(dev, timing_data) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_SET_TIMING_DATA, can_set_timing_data, dev, timing_data); 	syscall__retval = can_set_timing_data(dev, timing_data); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_SET_TIMING_DATA, can_set_timing_data, dev, timing_data, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_can_set_bitrate_data(const struct device * dev, uint32_t bitrate_data);

__pinned_func
static inline int can_set_bitrate_data(const struct device * dev, uint32_t bitrate_data)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = bitrate_data };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_CAN_SET_BITRATE_DATA);
	}
#endif
	compiler_barrier();
	return z_impl_can_set_bitrate_data(dev, bitrate_data);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_set_bitrate_data(dev, bitrate_data) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_SET_BITRATE_DATA, can_set_bitrate_data, dev, bitrate_data); 	syscall__retval = can_set_bitrate_data(dev, bitrate_data); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_SET_BITRATE_DATA, can_set_bitrate_data, dev, bitrate_data, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_can_set_timing(const struct device * dev, const struct can_timing * timing);

__pinned_func
static inline int can_set_timing(const struct device * dev, const struct can_timing * timing)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const struct can_timing * val; } parm1 = { .val = timing };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_CAN_SET_TIMING);
	}
#endif
	compiler_barrier();
	return z_impl_can_set_timing(dev, timing);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_set_timing(dev, timing) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_SET_TIMING, can_set_timing, dev, timing); 	syscall__retval = can_set_timing(dev, timing); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_SET_TIMING, can_set_timing, dev, timing, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_can_get_capabilities(const struct device * dev, can_mode_t * cap);

__pinned_func
static inline int can_get_capabilities(const struct device * dev, can_mode_t * cap)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; can_mode_t * val; } parm1 = { .val = cap };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_CAN_GET_CAPABILITIES);
	}
#endif
	compiler_barrier();
	return z_impl_can_get_capabilities(dev, cap);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_get_capabilities(dev, cap) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_GET_CAPABILITIES, can_get_capabilities, dev, cap); 	syscall__retval = can_get_capabilities(dev, cap); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_GET_CAPABILITIES, can_get_capabilities, dev, cap, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_can_start(const struct device * dev);

__pinned_func
static inline int can_start(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_CAN_START);
	}
#endif
	compiler_barrier();
	return z_impl_can_start(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_start(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_START, can_start, dev); 	syscall__retval = can_start(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_START, can_start, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_can_stop(const struct device * dev);

__pinned_func
static inline int can_stop(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_CAN_STOP);
	}
#endif
	compiler_barrier();
	return z_impl_can_stop(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_stop(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_STOP, can_stop, dev); 	syscall__retval = can_stop(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_STOP, can_stop, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_can_set_mode(const struct device * dev, can_mode_t mode);

__pinned_func
static inline int can_set_mode(const struct device * dev, can_mode_t mode)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; can_mode_t val; } parm1 = { .val = mode };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_CAN_SET_MODE);
	}
#endif
	compiler_barrier();
	return z_impl_can_set_mode(dev, mode);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_set_mode(dev, mode) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_SET_MODE, can_set_mode, dev, mode); 	syscall__retval = can_set_mode(dev, mode); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_SET_MODE, can_set_mode, dev, mode, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_can_set_bitrate(const struct device * dev, uint32_t bitrate);

__pinned_func
static inline int can_set_bitrate(const struct device * dev, uint32_t bitrate)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = bitrate };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_CAN_SET_BITRATE);
	}
#endif
	compiler_barrier();
	return z_impl_can_set_bitrate(dev, bitrate);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_set_bitrate(dev, bitrate) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_SET_BITRATE, can_set_bitrate, dev, bitrate); 	syscall__retval = can_set_bitrate(dev, bitrate); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_SET_BITRATE, can_set_bitrate, dev, bitrate, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_can_send(const struct device * dev, const struct can_frame * frame, k_timeout_t timeout, can_tx_callback_t callback, void * user_data);

__pinned_func
static inline int can_send(const struct device * dev, const struct can_frame * frame, k_timeout_t timeout, can_tx_callback_t callback, void * user_data)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const struct can_frame * val; } parm1 = { .val = frame };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm2 = { .val = timeout };
		union { uintptr_t x; can_tx_callback_t val; } parm3 = { .val = callback };
		union { uintptr_t x; void * val; } parm4 = { .val = user_data };
		return (int) arch_syscall_invoke6(parm0.x, parm1.x, parm2.split.lo, parm2.split.hi, parm3.x, parm4.x, K_SYSCALL_CAN_SEND);
	}
#endif
	compiler_barrier();
	return z_impl_can_send(dev, frame, timeout, callback, user_data);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_send(dev, frame, timeout, callback, user_data) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_SEND, can_send, dev, frame, timeout, callback, user_data); 	syscall__retval = can_send(dev, frame, timeout, callback, user_data); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_SEND, can_send, dev, frame, timeout, callback, user_data, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_can_add_rx_filter_msgq(const struct device * dev, struct k_msgq * msgq, const struct can_filter * filter);

__pinned_func
static inline int can_add_rx_filter_msgq(const struct device * dev, struct k_msgq * msgq, const struct can_filter * filter)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct k_msgq * val; } parm1 = { .val = msgq };
		union { uintptr_t x; const struct can_filter * val; } parm2 = { .val = filter };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_CAN_ADD_RX_FILTER_MSGQ);
	}
#endif
	compiler_barrier();
	return z_impl_can_add_rx_filter_msgq(dev, msgq, filter);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_add_rx_filter_msgq(dev, msgq, filter) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_ADD_RX_FILTER_MSGQ, can_add_rx_filter_msgq, dev, msgq, filter); 	syscall__retval = can_add_rx_filter_msgq(dev, msgq, filter); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_ADD_RX_FILTER_MSGQ, can_add_rx_filter_msgq, dev, msgq, filter, syscall__retval); 	syscall__retval; })
#endif
#endif


extern void z_impl_can_remove_rx_filter(const struct device * dev, int filter_id);

__pinned_func
static inline void can_remove_rx_filter(const struct device * dev, int filter_id)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; int val; } parm1 = { .val = filter_id };
		(void) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_CAN_REMOVE_RX_FILTER);
		return;
	}
#endif
	compiler_barrier();
	z_impl_can_remove_rx_filter(dev, filter_id);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_remove_rx_filter(dev, filter_id) do { 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_REMOVE_RX_FILTER, can_remove_rx_filter, dev, filter_id); 	can_remove_rx_filter(dev, filter_id); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_REMOVE_RX_FILTER, can_remove_rx_filter, dev, filter_id); } while(false)
#endif
#endif


extern int z_impl_can_get_max_filters(const struct device * dev, bool ide);

__pinned_func
static inline int can_get_max_filters(const struct device * dev, bool ide)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; bool val; } parm1 = { .val = ide };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_CAN_GET_MAX_FILTERS);
	}
#endif
	compiler_barrier();
	return z_impl_can_get_max_filters(dev, ide);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_get_max_filters(dev, ide) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_GET_MAX_FILTERS, can_get_max_filters, dev, ide); 	syscall__retval = can_get_max_filters(dev, ide); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_GET_MAX_FILTERS, can_get_max_filters, dev, ide, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_can_get_state(const struct device * dev, enum can_state * state, struct can_bus_err_cnt * err_cnt);

__pinned_func
static inline int can_get_state(const struct device * dev, enum can_state * state, struct can_bus_err_cnt * err_cnt)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; enum can_state * val; } parm1 = { .val = state };
		union { uintptr_t x; struct can_bus_err_cnt * val; } parm2 = { .val = err_cnt };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_CAN_GET_STATE);
	}
#endif
	compiler_barrier();
	return z_impl_can_get_state(dev, state, err_cnt);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_get_state(dev, state, err_cnt) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_GET_STATE, can_get_state, dev, state, err_cnt); 	syscall__retval = can_get_state(dev, state, err_cnt); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_GET_STATE, can_get_state, dev, state, err_cnt, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_can_recover(const struct device * dev, k_timeout_t timeout);

__pinned_func
static inline int can_recover(const struct device * dev, k_timeout_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm1 = { .val = timeout };
		return (int) arch_syscall_invoke3(parm0.x, parm1.split.lo, parm1.split.hi, K_SYSCALL_CAN_RECOVER);
	}
#endif
	compiler_barrier();
	return z_impl_can_recover(dev, timeout);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define can_recover(dev, timeout) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_CAN_RECOVER, can_recover, dev, timeout); 	syscall__retval = can_recover(dev, timeout); 	sys_port_trace_syscall_exit(K_SYSCALL_CAN_RECOVER, can_recover, dev, timeout, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
