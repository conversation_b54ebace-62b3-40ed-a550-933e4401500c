/******************************************************************************
 * Copyright © 2022 ams-OSRAM AG                                              *
 * All rights are reserved.                                                   *
 *                                                                            *
 * FOR FULL LICENSE TEXT SEE LICENSE.TXT                                      *
 *                                                                            *
 ******************************************************************************/

/******************************************************************************
 *                                 INCLUDES                                   *
 ******************************************************************************/

#include <zephyr/as7058/as7058_chiplib.h>
#include <zephyr/as7058/agc.h>
#include <zephyr/as7058/as7058_bioz_measurement.h>
#include <zephyr/as7058/as7058_eda_scaling.h>
#include <zephyr/as7058/as7058_extract.h>
#include <zephyr/as7058/as7058_interface.h>
#include <zephyr/as7058/as7058_osal_chiplib.h>
#include <zephyr/as7058/as7058_pd_offset_calibration.h>
#include <zephyr/as7058/as7058_typedefs.h>
#include <zephyr/as7058/as7058_version.h>
#include <zephyr/as7058/error_codes.h>
#include <zephyr/as7058/std_inc.h>

/******************************************************************************
 *                                DEFINITIONS                                 *
 ******************************************************************************/
#define AS7058_HR_ENABLE 0
#define AS7058ENABLE 0

extern int HR_Enable_Flag;
extern int SPo2_Enable_Flag;


enum LIB_STATES {
    LIB_STATE_UNINITIALIZED = 0,
    LIB_STATE_CONFIGURATION = 1,
    LIB_STATE_MEASUREMENT = 2,
};

struct device_config {
    as7058_callback_t p_normal_callback;
    as7058_callback_special_measurement_t p_special_callback;
    const void *p_cb_param;
    volatile enum LIB_STATES lib_state;
    as7058_meas_config_t meas_config;
    volatile uint8_t is_meas_running;
    as7058_interrupt_t enabled_irqs;
    uint16_t fifo_threshold;
    as7058_meas_mode_t mode;
    uint8_t reg_val_bioz_cfg; /*!< Value of register BIOZ_CFG as passed to the Chip Library. The reset value of this
                                   register in the silicon is 0x00. */
};

/*! Index of register BIOZ_CFG in as7058_reg_group_ecg_t::reg_buffer. */
#define REG_INDEX__ECG__BIOZ_CFG 0

/*! Bitmask for field gsr_en of register BIOZ_CFG. */
#define REG_MASK__BIOZ_CFG__GSR_EN 0x02

/******************************************************************************
 *                                  GLOBALS                                   *
 ******************************************************************************/

#define RAWDATASIZE 2000

uint8_t ppg1raw[RAWDATASIZE]={0};
//  int ppg1rawbitsub[RAWDATASIZE]={0};
 uint32_t ppgsub1raw[RAWDATASIZE]={0};
 uint32_t ppg2raw[RAWDATASIZE]={0};
 uint32_t ppgsub2raw[RAWDATASIZE]={0};
 uint32_t ecgsub1raw[RAWDATASIZE]={0};
 uint32_t ecgsub2raw[RAWDATASIZE]={0};
 uint32_t ecgchannelraw[RAWDATASIZE]={0};
 uint32_t statusraw[RAWDATASIZE]={0};

uint32_t red_data,ir_data,amb_data,temp,gren_data,green_data;
#define SIZERAW 6000
uint32_t RED_data[SIZERAW];
uint32_t IR_data[SIZERAW];
uint32_t AMB_data[SIZERAW];
uint32_t GREEN_data[SIZERAW];

static struct device_config g_dev_config;
/******************************************************************************
 *                               LOCAL FUNCTIONS                              *
 ******************************************************************************/
uint8_t fifo_data[AS7058_FIFO_DATA_BUFFER_SIZE];
uint8_t RawData[36000] = {0};
int countraw = 0;
int Raw_Data_Counter = 0;
int Skipcount = 0;
// err_code_t interrupt_handler(void)
// {
//     err_code_t result;
//     as7058_interrupt_t irq_status;
 
//     agc_status_t agc_status[AGC_MAX_CHANNEL_CNT];
//     uint8_t agc_status_num = AGC_MAX_CHANNEL_CNT;
//     uint16_t fifo_level;
//     uint16_t fifo_data_size = 0;
//     uint32_t temp =0;
//     as7058_status_events_t status_events = {0, 0, 0, 0, 0, 0, 0, 0, 0};
//     as7058_interrupt_t combined = {0, 0, 0, 0, 0, 0, 0, 0};
//     const as7058_special_measurement_result_t *p_special_result;
//     uint16_t result_size;

//     if (!g_dev_config.is_meas_running) {
//         return ERR_PERMISSION;
//     }
//     result = as7058_ifce_get_interrupt_status(&irq_status);

//     if (ERR_SUCCESS == result) {
//         combined.asat = g_dev_config.enabled_irqs.asat & irq_status.asat;
//         combined.iir_overflow = g_dev_config.enabled_irqs.iir_overflow & irq_status.iir_overflow;
//         combined.leadoff = g_dev_config.enabled_irqs.leadoff & irq_status.leadoff;
//         combined.led_lowvds = g_dev_config.enabled_irqs.led_lowvds & irq_status.led_lowvds;
//         combined.sequencer = g_dev_config.enabled_irqs.sequencer & irq_status.sequencer;
//         combined.vcsel = g_dev_config.enabled_irqs.vcsel & irq_status.vcsel;
//         result = as7058_ifce_get_sub_status_registers(combined, &status_events);

//         /* Readout FIFO overflow status if enabled and triggered */
//         if (ERR_SUCCESS != result) {
//             /* Do nothing */
//         } else if (irq_status.fifo_overflow && g_dev_config.enabled_irqs.fifo_overflow) {
//             result = ERR_OVERFLOW;
//         } else if (irq_status.fifo_threshold && g_dev_config.enabled_irqs.fifo_threshold) {
//             /* Readout FIFO level if FIFO threshold IRQ is enabled and triggered */
//             result = as7058_ifce_get_fifo_level(&fifo_level);
//             if (ERR_SUCCESS == result) {

//                 /* Prevent division by zero */
//                 if (0 == g_dev_config.fifo_threshold) {
//                     result = ERR_CONFIG;
//                 } else {

//                     /* Calculate read length as multiple of FIFO threshold */
//                     fifo_level = (fifo_level / g_dev_config.fifo_threshold) * g_dev_config.fifo_threshold;

//                     /* Calculate the real data size */
//                     fifo_data_size = fifo_level * AS7058_FIFO_SAMPLE_SIZE;

//                     /* Check that we have no capacity issues */
//                     if (sizeof(fifo_data) < fifo_data_size) {
//                         fifo_data_size = 0;
//                         result = ERR_SIZE;
//                     } 
//                     else 
//                     {

//                         result = as7058_ifce_get_fifo_data(fifo_data, fifo_data_size);

//                         if(SPo2_Enable_Flag ==1)
//                         { 
//                           Skipcount++;
//                           if(Skipcount >= 300)
//                            {
// 	                        temp = (fifo_data[2]<<16 | fifo_data[1]<<8 | fifo_data[0]);
// 						    red_data = temp>>4;
// 						    RED_data[countraw]=red_data;

// 						    temp = (fifo_data[5]<<16 | fifo_data[4]<<8 | fifo_data[3]);
// 						    ir_data =  temp>>4;
// 						    IR_data[countraw]=ir_data;

// 						    temp = (fifo_data[8]<<16 | fifo_data[7]<<8 | fifo_data[6]);
// 						    amb_data = temp>>4;
// 						    AMB_data[countraw] = amb_data;
//                            // printk("red ir Amb %d  %d  %d\n",RED_data[countraw],IR_data[countraw],AMB_data[countraw]);
//                          }
//                         }
//                         if(HR_Enable_Flag == 2)
//                         {

// 						 memcpy(&RawData[Raw_Data_Counter] ,fifo_data, fifo_data_size);
// 						 Raw_Data_Counter = Raw_Data_Counter + fifo_data_size;

//                         }

//                          countraw++;
//                          printk("countraw =%d\n",countraw);

//                         if(countraw >= 2000) 
//                         {

//                            if(HR_Enable_Flag ==2)
//                            {
//                               HR_Enable_Flag =0;
//                               Fiforawdataexteract(&RawData,Raw_Data_Counter);
//                               countraw = NULL;
//                               Raw_Data_Counter=NULL;
//                               as7058_stop_measurement();
//                            }
//                             if(SPo2_Enable_Flag == 1)
//                             {
//                               SPo2_Enable_Flag = 0;
//                               as7058_stop_measurement();
//                               RawDataPassAlgo(&RED_data,&IR_data,&AMB_data);
//                               Fiforawdataexteract(&RawData,Raw_Data_Counter);//asha
//                             //   send_rawdata_over_BLE(RawData,Raw_Data_Counter);
//                               countraw = NULL;
//                               Raw_Data_Counter=NULL;                     
//                             }

//                         }
//                     }
//                 }
//             }

//             if (ERR_SUCCESS == result) {
//                 result = agc_get_status(agc_status, &agc_status_num);
//             }

//             if (ERR_SUCCESS == result && g_dev_config.p_normal_callback) {
//                 g_dev_config.p_normal_callback(result, fifo_data, fifo_data_size, agc_status, agc_status_num,
//                                                status_events, g_dev_config.p_cb_param);
//             }

//             if ((ERR_SUCCESS == result) && (AS7058_MEAS_MODE_NORMAL == g_dev_config.mode)) {
//                 result = agc_execute(fifo_data, fifo_data_size);
//             }

//             if ((ERR_SUCCESS == result) && (AS7058_MEAS_MODE_NORMAL != g_dev_config.mode)) {
//                 if (AS7058_MEAS_MODE_SPECIAL_SCALING_EDA == g_dev_config.mode) {
//                     result_size = sizeof(as7058_eda_scaling_result_t);
//                     result = as7058_eda_scaling_process(fifo_data, fifo_data_size,
//                                                         (const as7058_eda_scaling_result_t **)&p_special_result);
//                 } else if (AS7058_MEAS_MODE_SPECIAL_BIOZ == g_dev_config.mode) {
//                     result_size = sizeof(as7058_bioz_meas_result_t);
//                     result = as7058_bioz_process(fifo_data, fifo_data_size,
//                                                  (const as7058_bioz_meas_result_t **)&p_special_result);
//                 } else if (AS7058_MEAS_MODE_SPECIAL_PD_OFFSET_CALIBRATION == g_dev_config.mode) {
//                     result_size = sizeof(as7058_pd_offset_calibration_result_t);
//                     result = as7058_pd_offset_calibration_process(
//                         fifo_data, fifo_data_size, (const as7058_pd_offset_calibration_result_t **)&p_special_result);
//                 } else {
//                     return ERR_NOT_SUPPORTED;
//                 }
//                 if ((ERR_SUCCESS == result) && (g_dev_config.p_special_callback)) {
//                     g_dev_config.p_special_callback(g_dev_config.mode, p_special_result, result_size,
//                                                     g_dev_config.p_cb_param);
//                 } else if (ERR_NO_DATA == result) {
//                     result = ERR_SUCCESS;
//                 }
//             }
//         } else if (g_dev_config.p_normal_callback) {
//             g_dev_config.p_normal_callback(result, NULL, 0, NULL, 0, status_events, g_dev_config.p_cb_param);
//         }
//     }

//     if (ERR_SUCCESS != result && g_dev_config.p_normal_callback) {
//         g_dev_config.p_normal_callback(result, NULL, 0, NULL, 0, status_events, g_dev_config.p_cb_param);
//         as7058_stop_measurement();
//     }

//     return result;
// }
//////////////////asha
// err_code_t interrupt_handler(void)
// {
//     err_code_t result;
//     as7058_interrupt_t irq_status;
//     uint16_t fifo_level;
//     uint16_t fifo_data_size = 0;
//     uint32_t temp = 0;
//     uint16_t red_data = 0, ir_data = 0, green_data = 0, amb_data = 0;
//     uint8_t ble_data[8]; // 2 bytes each for RED, IR, GREEN, and AMBIENT
//     uint16_t countraw = 0;

//     if (!g_dev_config.is_meas_running) {
//         return ERR_PERMISSION;
//     }
//     result = as7058_ifce_get_interrupt_status(&irq_status);

//     if (ERR_SUCCESS == result) {
//         if (irq_status.fifo_threshold && g_dev_config.enabled_irqs.fifo_threshold) {
//             result = as7058_ifce_get_fifo_level(&fifo_level);

//             if (ERR_SUCCESS == result) {
//                 fifo_level = (fifo_level / g_dev_config.fifo_threshold) * g_dev_config.fifo_threshold;
//                 fifo_data_size = fifo_level * AS7058_FIFO_SAMPLE_SIZE;

//                 if (sizeof(fifo_data) >= fifo_data_size) {
//                     result = as7058_ifce_get_fifo_data(fifo_data, fifo_data_size);

//                     // Extract RED, IR, GREEN, and AMBIENT data
//                     temp = (fifo_data[2] << 16) | (fifo_data[1] << 8) | fifo_data[0];
//                     red_data = temp >> 4;

//                     temp = (fifo_data[5] << 16) | (fifo_data[4] << 8) | fifo_data[3];
//                     ir_data = temp >> 4;

//                     temp = (fifo_data[8] << 16) | (fifo_data[7] << 8) | fifo_data[6];
//                     green_data = temp >> 4;

//                     temp = (fifo_data[11] << 16) | (fifo_data[10] << 8) | fifo_data[9];
//                     amb_data = temp >> 4;

//                     // Prepare data for BLE transmission (convert to bytes)
//                     ble_data[0] = (red_data >> 8) & 0xFF;
//                     ble_data[1] = red_data & 0xFF;
//                     ble_data[2] = (ir_data >> 8) & 0xFF;
//                     ble_data[3] = ir_data & 0xFF;
//                     ble_data[4] = (green_data >> 8) & 0xFF;
//                     ble_data[5] = green_data & 0xFF;
//                     ble_data[6] = (amb_data >> 8) & 0xFF;
//                     ble_data[7] = amb_data & 0xFF;

//                     // Send the data over BLE
//                     send_rawdata_over_BLE(ble_data, sizeof(ble_data));

//                     countraw++;
//                     printk("Counter: %d, RED: %d, IR: %d, GREEN: %d, AMBIENT: %d\n", countraw, red_data, ir_data, green_data, amb_data);
//                 }
//             }
//         }
//     }
//     return result;
// }

// err_code_t interrupt_handler(void)
// {
//     err_code_t result;
//     as7058_interrupt_t irq_status;
//     uint16_t fifo_level;
//     uint16_t fifo_data_size = 0;
//     uint32_t temp = 0;
//     uint32_t red_data = 0, ir_data = 0, green_data = 0, amb_data = 0;
//     uint8_t ble_data[20]; // 3 bytes each for RED, IR, GREEN, and AMBIENT
//     uint16_t countraw = 0;

//     if (!g_dev_config.is_meas_running) {
//         return ERR_PERMISSION;
//     }
//     result = as7058_ifce_get_interrupt_status(&irq_status);

//     if (ERR_SUCCESS == result) {
//         if (irq_status.fifo_threshold && g_dev_config.enabled_irqs.fifo_threshold) {
//             result = as7058_ifce_get_fifo_level(&fifo_level);

//             if (ERR_SUCCESS == result) {
//                 fifo_level = (fifo_level / g_dev_config.fifo_threshold) * g_dev_config.fifo_threshold;
//                 fifo_data_size = fifo_level * AS7058_FIFO_SAMPLE_SIZE;

//                 if (sizeof(fifo_data) >= fifo_data_size) {
//                     result = as7058_ifce_get_fifo_data(fifo_data, fifo_data_size);

//                     // Extract RED, IR, GREEN, and AMBIENT data (3 bytes each)
//                     temp = (fifo_data[2] << 16) | (fifo_data[1] << 8) | fifo_data[0];
//                     red_data = temp >> 4;

//                     temp = (fifo_data[5] << 16) | (fifo_data[4] << 8) | fifo_data[3];
//                     ir_data = temp >> 4;

//                     temp = (fifo_data[8] << 16) | (fifo_data[7] << 8) | fifo_data[6];
//                     green_data = temp >> 4;

//                     temp = (fifo_data[11] << 16) | (fifo_data[10] << 8) | fifo_data[9];
//                     amb_data = temp >> 4;

//                     // Prepare 12-byte BLE data packet
//                     ble_data[0] = (red_data >> 16) & 0xFF;
//                     ble_data[1] = (red_data >> 8) & 0xFF;
//                     ble_data[2] = red_data & 0xFF;

//                     ble_data[3] = (ir_data >> 16) & 0xFF;
//                     ble_data[4] = (ir_data >> 8) & 0xFF;
//                     ble_data[5] = ir_data & 0xFF;

//                     ble_data[6] = (green_data >> 16) & 0xFF;
//                     ble_data[7] = (green_data >> 8) & 0xFF;
//                     ble_data[8] = green_data & 0xFF;

//                     ble_data[9] = (amb_data >> 16) & 0xFF;
//                     ble_data[10] = (amb_data >> 8) & 0xFF;
//                     ble_data[11] = amb_data & 0xFF;

//                     // Send the 12-byte packet over BLE immediately
//                     send_rawdata_over_BLE(ble_data, sizeof(ble_data));

//                     countraw++;
//                     printk("Counter: %d, RED: %d, IR: %d, GREEN: %d, AMBIENT: %d\n", countraw, red_data, ir_data, green_data, amb_data);
//                 }
//             }
//         }
//     }
//     return result;
// }


// static uint8_t ble_buff1[180];
// static uint8_t ble_buff2[180];//,fbfr=0xFF;;
// static uint8_t fbfr=0;
// err_code_t interrupt_handler(void)
// {
//  uint32_t red_data = 0, ir_data = 0, green_data = 0, amb_data = 0;
// //  intf=q1
//     err_code_t result;
//     as7058_interrupt_t irq_status;
//     uint16_t fifo_level;
//     uint16_t fifo_data_size = 0;
//     uint16_t countraw = 0;
//     static int iCnt = 4;
//     static int iflagb = 0; // Toggle between buffers
//     static int fbfr = 0;   // Indicates which buffer to transmit

//     if (!g_dev_config.is_meas_running) {
//         return ERR_PERMISSION;
//     }

//     result = as7058_ifce_get_interrupt_status(&irq_status);

//     if (ERR_SUCCESS == result) {
//         if (irq_status.fifo_threshold && g_dev_config.enabled_irqs.fifo_threshold) {
//             result = as7058_ifce_get_fifo_level(&fifo_level);

//             if (ERR_SUCCESS == result) {
//                 fifo_level = (fifo_level / g_dev_config.fifo_threshold) * g_dev_config.fifo_threshold;
//                 fifo_data_size = fifo_level * AS7058_FIFO_SAMPLE_SIZE;

//                 if (sizeof(fifo_data) >= fifo_data_size) {
//                     result = as7058_ifce_get_fifo_data(fifo_data, fifo_data_size);

//                     // Fill RED, IR, GREEN, and AMBIENT data fields with the counter value
//                     for (int j = 0; j < fifo_data_size; j += 12) {
//                         uint32_t counter_value = countraw;

//                         if (iflagb == 0) {
//                                  ble_buff1[iCnt++] = (red_data >> 16) & 0xFF;
//                             ble_buff1[iCnt++] = (red_data >> 8) & 0xFF;
//                             ble_buff1[iCnt++] = red_data & 0xFF;

//                             ble_buff1[iCnt++] = (ir_data >> 16) & 0xFF;
//                             ble_buff1[iCnt++] = (ir_data >> 8) & 0xFF;
//                             ble_buff1[iCnt++] = ir_data & 0xFF;

//                             ble_buff1[iCnt++] = (green_data >> 16) & 0xFF;
//                             ble_buff1[iCnt++] = (green_data >> 8) & 0xFF;
//                             ble_buff1[iCnt++] = green_data & 0xFF;

//                             ble_buff1[iCnt++] = (amb_data >> 16) & 0xFF;
//                             ble_buff1[iCnt++] = (amb_data >> 8) & 0xFF;
//                             ble_buff1[iCnt++] = amb_data & 0xFF;
//                             // ble_buff1[iCnt++] = counter_value++;//(counter_value >> 16) & 0xFF;
//                             // ble_buff1[iCnt++] = counter_value++;//(counter_value >> 8) & 0xFF;
//                             // ble_buff1[iCnt++] = counter_value++;//counter_value & 0xFF;

//                             // ble_buff1[iCnt++] = counter_value++;//(counter_value >> 16) & 0xFF;
//                             // ble_buff1[iCnt++] = counter_value++;//(counter_value >> 8) & 0xFF;
//                             // ble_buff1[iCnt++] = counter_value++;//counter_value & 0xFF;

//                             // ble_buff1[iCnt++] = counter_value++;//(counter_value >> 16) & 0xFF;
//                             // ble_buff1[iCnt++] = counter_value++;//(counter_value >> 8) & 0xFF;
//                             // ble_buff1[iCnt++] = counter_value++;//counter_value & 0xFF;

//                             // ble_buff1[iCnt++] = counter_value++;//(counter_value >> 16) & 0xFF;
//                             // ble_buff1[iCnt++] = counter_value++;//(counter_value >> 8) & 0xFF;
//                             // ble_buff1[iCnt++] = counter_value++;//counter_value & 0xFF;

//                             if (iCnt >= 180) {
//                                 iflagb = 1;   // Switch to buffer2
//                                 fbfr = 1;     // Mark buffer1 for BLE transmission
//                                 iCnt = 4;     // Reset index for buffer1
//                                 // send_rawdata_over_BLE(ble_buff1, sizeof(ble_buff1));
//                                 send_ble_buff1();
//                                  printk("Buffer1 -> Sample %d -> RED: %d, IR: %d, GREEN: %d, AMBIENT: %d\n", countraw, red_data, ir_data, green_data, amb_data);
//                             }
//                         } else {
//                                  ble_buff1[iCnt++] = (red_data >> 16) & 0xFF;
//                             ble_buff1[iCnt++] = (red_data >> 8) & 0xFF;
//                             ble_buff1[iCnt++] = red_data & 0xFF;

//                             ble_buff1[iCnt++] = (ir_data >> 16) & 0xFF;
//                             ble_buff1[iCnt++] = (ir_data >> 8) & 0xFF;
//                             ble_buff1[iCnt++] = ir_data & 0xFF;

//                             ble_buff1[iCnt++] = (green_data >> 16) & 0xFF;
//                             ble_buff1[iCnt++] = (green_data >> 8) & 0xFF;
//                             ble_buff1[iCnt++] = green_data & 0xFF;

//                             ble_buff1[iCnt++] = (amb_data >> 16) & 0xFF;
//                             ble_buff1[iCnt++] = (amb_data >> 8) & 0xFF;
//                             ble_buff1[iCnt++] = amb_data & 0xFF;

//                             // ble_buff2[iCnt++] = counter_value++;//(counter_value >> 16) & 0xFF;
//                             // ble_buff2[iCnt++] = counter_value++;//(counter_value >> 8) & 0xFF;
//                             // ble_buff2[iCnt++] = counter_value++;//counter_value & 0xFF;

//                             // ble_buff2[iCnt++] = counter_value++;//(counter_value >> 16) & 0xFF;
//                             // ble_buff2[iCnt++] = counter_value++;//(counter_value >> 8) & 0xFF;
//                             // ble_buff2[iCnt++] = counter_value++;//counter_value & 0xFF;

//                             // ble_buff2[iCnt++] = counter_value++;//(counter_value >> 16) & 0xFF;
//                             // ble_buff2[iCnt++] = counter_value++;//(counter_value >> 8) & 0xFF;
//                             // ble_buff2[iCnt++] = counter_value++;//counter_value & 0xFF;

//                             // ble_buff2[iCnt++] = counter_value++;//(counter_value >> 16) & 0xFF;
//                             // ble_buff2[iCnt++] = counter_value++;//(counter_value >> 8) & 0xFF;
//                             // ble_buff2[iCnt++] = counter_value++;//counter_value & 0xFF;

//                             if (iCnt >= 180) {
//                                 iflagb = 0;   // Switch back to buffer1
//                                 fbfr = 2;     // Mark buffer2 for BLE transmission
//                                 iCnt = 4;     // Reset index for buffer2
//                                 // send_rawdata_over_BLE(ble_buff2, sizeof(ble_buff2));
//                                 send_ble_buff2();
//                                  printk("Buffer2 -> Sample %d -> RED: %d, IR: %d, GREEN: %d, AMBIENT: %d\n", countraw, red_data, ir_data, green_data, amb_data);
//                             }
//                         }
//                     }

//                     countraw++;
//                     printk("Counter: %d\n", countraw);
//                 }
//             }
//         }
//     }
//     return result;
// }
static uint8_t ble_buff1[183];
static uint8_t ble_buff2[183];
void send_ble_buff1(void)
{
 
        ble_buff1[0] = 0x01;
        ble_buff1[1] = 0xFF;
        ble_buff1[2] = 0xFF;
        send_rawdata_over_BLE(ble_buff1, 180);
    
    // else if (fbfr == 2) {
    //     fbfr = 0xFF;
    //     ble_buff2[0] = 0x02;
    //     ble_buff2[2] = 0xFF;
    //     ble_buff2[3] = 0xFF;
    //     send_rawdata_over_BLE(ble_buff2, 20);
    // }
}
void send_ble_buff2(void)
{
    // if (fbfr == 1) {
    //     fbfr = 0xFF;
    //     ble_buff1[0] = 0x01;
    //     ble_buff1[2] = 0xFF;
    //     ble_buff1[3] = 0xFF;
    //     send_rawdata_over_BLE(ble_buff1, 20);
    // }
   
        ble_buff2[0] = 0x02;
        ble_buff2[1] = 0xFF;
        ble_buff2[2] = 0xFF;
        send_rawdata_over_BLE(ble_buff2, 180);
    
}
// void send_ble_buff(void){
// static int bf1cnt=0,bf2cnt=0;
//  if(fbfr == 1){
//   fbfr =0xFF;
//   ble_buff1[0]=0x01;
//  // ble_buff1[1]= 0xff;
//   ble_buff1[2]= 0xff;
//   ble_buff1[3]= 0xff;


//   //  for( int J=205; J <=243 ;J++){
//   //       ble_buff1[J]=J; //FILL REMAING BYTES WITH ZERO
//   //  }
     
//   // printk(" buff1\n");
//   send_rawdata_over_BLE(ble_buff1,180);
  
//   //iflagb=0;
//  }
//   else if(fbfr == 2 ){
//   //  for( int J=205; J <=243 ;J++){
//   //       ble_buff2[J]=J++; //FILL REMAING BYTES WITH ZERO
//   //  }
//     fbfr =0xFF;
//   ble_buff2[0]=0x02;
//  // ble_buff2[1]= 0xff;
//   ble_buff2[2]= 0xff;
//   ble_buff2[3]= 0xff;
//        //  printk(" buff2\n");
//   send_rawdata_over_BLE(ble_buff2,180);
//     //iflagb=0;
 
// //  bfr0_txnfg=1
//  }
// }
// void send_ble_buff(void)
// {
// if (fbfr == 1) {
//         printk("Attempting to send ble_buff1\n");
//         fbfr = 0xFF;
//         ble_buff1[0] = 0x01;
//         if (send_rawdata_over_BLE(ble_buff1, 20) != 0) {
//             printk("Failed to send ble_buff1\n");
//         }
//     }
//     else if (fbfr == 2) {
//         printk("Attempting to send ble_buff2\n");
//         fbfr = 0xFF;
//         ble_buff2[0] = 0x02;
//         if (send_rawdata_over_BLE(ble_buff2, 20) != 0) {
//             printk("Failed to send ble_buff2\n");
//         }
//     } else {
//         printk("No buffer ready for sending (fbfr = %d)\n", fbfr);
//     }   

// }
/////working 

// err_code_t interrupt_handler(void)
// {
   
//     err_code_t result;
//     as7058_interrupt_t irq_status;
//     uint16_t fifo_level;
//     uint16_t fifo_data_size = 0;
//     uint32_t temp = 0;
//     // uint32_t red_data = 0, ir_data = 0, green_data = 0, amb_data = 0;
//     // static uint8_t ble_buff1[20], ble_buff2[20];
//     static int iCnt = 4;
//     static int iflagb ; // Toggle between buffers
//     static int fbfr = 0;   // Indicates which buffer to transmit
//     uint16_t countraw = 0;

//     if (!g_dev_config.is_meas_running) {
//         return ERR_PERMISSION;
//     }
    
//     result = as7058_ifce_get_interrupt_status(&irq_status);

//     if (ERR_SUCCESS == result) {
//         if (irq_status.fifo_threshold && g_dev_config.enabled_irqs.fifo_threshold) {
//             result = as7058_ifce_get_fifo_level(&fifo_level);
//            // printk("FIFO Level: %d\n", fifo_level);

//             if (ERR_SUCCESS == result) {
//                 fifo_level = (fifo_level / g_dev_config.fifo_threshold) * g_dev_config.fifo_threshold;
//                 fifo_data_size = fifo_level * AS7058_FIFO_SAMPLE_SIZE;

//                 if (sizeof(fifo_data) >= fifo_data_size) {
//                     result = as7058_ifce_get_fifo_data(fifo_data, fifo_data_size);

//                     // Extract and pack data into the BLE buffers
//                     for (int j = 0; j < fifo_data_size; j += 12) {  // Each sample is 12 bytes (3 bytes each for RED, IR, GREEN, AMBIENT)
//                         // Extract RED, IR, GREEN, and AMBIENT data
//                         temp = (fifo_data[j + 2] << 16) | (fifo_data[j + 1] << 8) | fifo_data[j];
//                         red_data = temp >> 4;
//                         RED_data[countraw] = red_data;
//                         // prink("red %d\n",red_data);

//                         temp = (fifo_data[j + 5] << 16) | (fifo_data[j + 4] << 8) | fifo_data[j + 3];
//                         ir_data = temp >> 4;
//                         IR_data[countraw] = ir_data;

//                         temp = (fifo_data[j + 8] << 16) | (fifo_data[j + 7] << 8) | fifo_data[j + 6];
//                         green_data = temp >> 4;
//                         GREEN_data[countraw] = green_data; 

//                         temp = (fifo_data[j + 11] << 16) | (fifo_data[j + 10] << 8) | fifo_data[j + 9];
//                         amb_data = temp >> 4;
//                         AMB_data[countraw] = amb_data;

//                         // Pack data into the current buffer
//                         if (iflagb == 0) {

//                              ble_buff1[iCnt++] = (RED_data[j] & 0x30000)>>16;
//                              ble_buff1[iCnt++] = (RED_data[j] &0xff00)>>8;
//                              ble_buff1[iCnt++] = (RED_data[j] &0xff);

//                              ble_buff1[iCnt++] = (IR_data[j] & 0x30000)>>16;
//                              ble_buff1[iCnt++] = (IR_data[j] &0xff00)>>8;
//                              ble_buff1[iCnt++] = (IR_data[j] &0xff);

//                              ble_buff1[iCnt++] = (GREEN_data[j] & 0x30000)>>16;
//                              ble_buff1[iCnt++] = (GREEN_data[j] &0xff00)>>8;
//                              ble_buff1[iCnt++] = (GREEN_data[j] &0xff);

//                              ble_buff1[iCnt++] = (AMB_data[j] & 0x30000)>>16;
//                              ble_buff1[iCnt++] = (AMB_data[j] &0xff00)>>8;
//                              ble_buff1[iCnt++] = (AMB_data[j] &0xff);

//                             // ble_buff1[iCnt++] = (red_data >> 16) & 0xFF;
//                             // ble_buff1[iCnt++] = (red_data >> 8) & 0xFF;
//                             // ble_buff1[iCnt++] = red_data & 0xFF;

//                             // ble_buff1[iCnt++] = (ir_data >> 16) & 0xFF;
//                             // ble_buff1[iCnt++] = (ir_data >> 8) & 0xFF;
//                             // ble_buff1[iCnt++] = ir_data & 0xFF;

//                             // ble_buff1[iCnt++] = (green_data >> 16) & 0xFF;
//                             // ble_buff1[iCnt++] = (green_data >> 8) & 0xFF;
//                             // ble_buff1[iCnt++] = green_data & 0xFF;

//                             // ble_buff1[iCnt++] = (amb_data >> 16) & 0xFF;
//                             // ble_buff1[iCnt++] = (amb_data >> 8) & 0xFF;
//                             // ble_buff1[iCnt++] = amb_data & 0xFF;

//                             // Check if buffer1 is full
//                             if (iCnt >= 180) {
//                                 iflagb = 1;   // Switch to buffer2 on next callback
//                                 fbfr = 1;     // Mark buffer1 for BLE transmission
//                                 iCnt = 4;     // Reset index for buffer1
//                                 // send_rawdata_over_BLE(ble_buff1, sizeof(ble_buff1));
//                                 send_ble_buff1();
//                                 printk("Counter: %d, RED: %d, IR: %d, GREEN: %d, AMBIENT: %d\n", countraw, red_data, ir_data, green_data, amb_data);
//                             }
//                         } else {    
//                             if(iflagb ==1)
//                             {
//                              ble_buff2[iCnt++] = (RED_data[j] & 0x30000)>>16;
//                              ble_buff2[iCnt++] = (RED_data[j] &0xff00)>>8;
//                              ble_buff2[iCnt++] = (RED_data[j] &0xff);

//                              ble_buff2[iCnt++] = (IR_data[j] & 0x30000)>>16;
//                              ble_buff2[iCnt++] = (IR_data[j] &0xff00)>>8;
//                              ble_buff2[iCnt++] = (IR_data[j] &0xff);

//                              ble_buff2[iCnt++] = (GREEN_data[j]  & 0x30000)>>16;
//                              ble_buff2[iCnt++] = (GREEN_data[j]  &0xff00)>>8;
//                              ble_buff2[iCnt++] = (GREEN_data[j]  &0xff);

//                              ble_buff2[iCnt++] = (AMB_data[j] & 0x30000)>>16;
//                              ble_buff2[iCnt++] = (AMB_data[j] &0xff00)>>8;
//                              ble_buff2[iCnt++] = (AMB_data[j] &0xff);
//                             // ble_buff2[iCnt++] = (red_data >> 16) & 0xFF;
//                             // ble_buff2[iCnt++] = (red_data >> 8) & 0xFF;
//                             // ble_buff2[iCnt++] = red_data & 0xFF;

//                             // ble_buff2[iCnt++] = (ir_data >> 16) & 0xFF;
//                             // ble_buff2[iCnt++] = (ir_data >> 8) & 0xFF;
//                             // ble_buff2[iCnt++] = ir_data & 0xFF;

//                             // ble_buff2[iCnt++] = (green_data >> 16) & 0xFF;
//                             // ble_buff2[iCnt++] = (green_data >> 8) & 0xFF;
//                             // ble_buff2[iCnt++] = green_data & 0xFF;

//                             // ble_buff2[iCnt++] = (amb_data >> 16) & 0xFF;
//                             // ble_buff2[iCnt++] = (amb_data >> 8) & 0xFF;
//                             // ble_buff2[iCnt++] = amb_data & 0xFF;

//                             // Check if buffer2 is full
//                             if (iCnt >= 180) {
//                                 iflagb = 0;   // Switch back to buffer1 on next callback
//                                 fbfr = 2;     // Mark buffer2 for BLE transmission
//                                 iCnt = 4;     // Reset index for buffer2
//                                 // send_rawdata_over_BLE(ble_buff2, sizeof(ble_buff2));
//                                 send_ble_buff2();
//                                 //  printk("Counter: %d, RED: %d, IR: %d, GREEN: %d, AMBIENT: %d\n", countraw, RED_data, IR_data, GREEN_data, AMB_data);
//                             }
//                             }
//                         }
//                     }

//                     countraw++;
//                     // printk("Counter: %d, RED: %d, IR: %d, GREEN: %d, AMBIENT: %d\n", countraw, red_data, ir_data, green_data, amb_data);
//                 }
//             }
//         }
//     }
//     return result;
// }
// err_code_t result;
// uint16_t fifo_level;
// uint16_t fifo_data_size = 0;
// uint32_t temp = 0;
// static int iCnt = 4 ;
// static int iflagb; // Toggle between buffers
// static int fbfr = 0;   // Indicates which buffer to transmit
// err_code_t interrupt_handler(void)
// {
//   as7058_interrupt_t irq_status;
//     // uint16_t countraw = 0;

//     if (!g_dev_config.is_meas_running) {
//         return ERR_PERMISSION;
//     }

//     result = as7058_ifce_get_interrupt_status(&irq_status);

//     if (ERR_SUCCESS == result) {
//         if (irq_status.fifo_threshold && g_dev_config.enabled_irqs.fifo_threshold) {
//             result = as7058_ifce_get_fifo_level(&fifo_level);
//             // printk("FIFO Level: %d\n", fifo_level);

//             if (ERR_SUCCESS == result) {
//                 fifo_level = (fifo_level / g_dev_config.fifo_threshold) * g_dev_config.fifo_threshold;
//                 fifo_data_size = fifo_level * AS7058_FIFO_SAMPLE_SIZE;

//                 if (sizeof(fifo_data) >= fifo_data_size) {
//                     result = as7058_ifce_get_fifo_data(fifo_data, fifo_data_size);

//                     // Extract and pack data into the BLE buffers
//                      // Each sample is 12 bytes (3 bytes each for RED, IR, GREEN, AMBIENT)
//                         for (int j = 0; j < fifo_data_size; j += 12) {// Extract RED, IR, GREEN, and AMBIENT data
//                         // int j;
//                         temp = (fifo_data[j + 2] << 16) | (fifo_data[j + 1] << 8) | fifo_data[j];
//                         red_data = temp >> 4;
//                         RED_data[countraw] = red_data;

//                         temp = (fifo_data[j + 5] << 16) | (fifo_data[j + 4] << 8) | fifo_data[j + 3];
//                         ir_data = temp >> 4;
//                         IR_data[countraw] = ir_data;

//                         temp = (fifo_data[j + 8] << 16) | (fifo_data[j + 7] << 8) | fifo_data[j + 6];
//                         green_data = temp >> 4;
//                         GREEN_data[countraw] = green_data;

//                         temp = (fifo_data[j + 11] << 16) | (fifo_data[j + 10] << 8) | fifo_data[j + 9];
//                         amb_data = temp >> 4;
//                         AMB_data[countraw] = amb_data;
//                         // red_data = ((fifo_data[j + 2] << 16) | (fifo_data[j + 1] << 8) | fifo_data[j]) >> 4;
//                         // ir_data = ((fifo_data[j + 5] << 16) | (fifo_data[j + 4] << 8) | fifo_data[j + 3]) >> 4;
//                         // green_data = ((fifo_data[j + 8] << 16) | (fifo_data[j + 7] << 8) | fifo_data[j + 6]) >> 4;
//                         // amb_data = ((fifo_data[j + 11] << 16) | (fifo_data[j + 10] << 8) | fifo_data[j + 9]) >> 4;

//                         // Pack data into the current buffer
                        
                        
//                         if (iflagb == 0) {
//                             // Fill buffer1
//                             ble_buff1[iCnt++] = (red_data & 0x30000) >> 16;
//                             ble_buff1[iCnt++] = (red_data & 0xff00) >> 8;
//                             ble_buff1[iCnt++] = (red_data & 0xff);

//                             ble_buff1[iCnt++] = (ir_data & 0x30000) >> 16;
//                             ble_buff1[iCnt++] = (ir_data & 0xff00) >> 8;
//                             ble_buff1[iCnt++] = (ir_data & 0xff);

//                             ble_buff1[iCnt++] = (green_data & 0x30000) >> 16;
//                             ble_buff1[iCnt++] = (green_data & 0xff00) >> 8;
//                             ble_buff1[iCnt++] = (green_data & 0xff);

//                             ble_buff1[iCnt++] = (amb_data & 0x30000) >> 16;
//                             ble_buff1[iCnt++] = (amb_data & 0xff00) >> 8;
//                             ble_buff1[iCnt++] = (amb_data & 0xff);

//                             // Check if buffer1 is full
//                             if (iCnt >= 180) {
//                                 iflagb = 1;   // Switch to buffer2 on next callback
//                                 fbfr = 1;     // Mark buffer1 for BLE transmission
//                                 iCnt = 4;     // Reset index for buffer1
//                                 // send_ble_buff1(); // Send data in buffer1
//                                 printk("Buffer1 -> Sample %d -> RED: %d, IR: %d, GREEN: %d, AMBIENT: %d\n", countraw, red_data, ir_data, green_data, amb_data);
//                             }
//                         } else {
//                             // Fill buffer2
//                             ble_buff2[iCnt++] = (red_data & 0x30000) >> 16;
//                             ble_buff2[iCnt++] = (red_data & 0xff00) >> 8;
//                             ble_buff2[iCnt++] = (red_data & 0xff);

//                             ble_buff2[iCnt++] = (ir_data & 0x30000) >> 16;
//                             ble_buff2[iCnt++] = (ir_data & 0xff00) >> 8;
//                             ble_buff2[iCnt++] = (ir_data & 0xff);

//                             ble_buff2[iCnt++] = (green_data & 0x30000) >> 16;
//                             ble_buff2[iCnt++] = (green_data & 0xff00) >> 8;
//                             ble_buff2[iCnt++] = (green_data & 0xff);

//                             ble_buff2[iCnt++] = (amb_data & 0x30000) >> 16;
//                             ble_buff2[iCnt++] = (amb_data & 0xff00) >> 8;
//                             ble_buff2[iCnt++] = (amb_data & 0xff);

//                             // Check if buffer2 is full
//                             if (iCnt >= 180) {
//                                 iflagb = 0;   // Switch back to buffer1 on next callback
//                                 fbfr = 2;     // Mark buffer2 for BLE transmission
//                                 iCnt = 4;     // Reset index for buffer2
//                                 // send_ble_buff2(); // Send data in buffer2
//                                 printk("Buffer2 -> Sample %d -> RED: %d, IR: %d, GREEN: %d, AMBIENT: %d\n", countraw, red_data, ir_data, green_data, amb_data);
//                             }
//                         }
                        
//                     }

//                     countraw++;
//                 }
//             }
//         }
//     }

//     return result;
// }

// void send_ble_buff(void){
// static int bf1cnt=0,bf2cnt=0;
//  if(fbfr == 1){
//   fbfr =0xFF;
//   ble_buff1[0]=0x01;
//  // ble_buff1[1]= 0xff;
//   ble_buff1[2]= 0xff;
//   ble_buff1[3]= 0xff;


//   //  for( int J=205; J <=243 ;J++){
//   //       ble_buff1[J]=J; //FILL REMAING BYTES WITH ZERO
//   //  }
     
//   // printk(" buff1\n");
//   send_rawdata_over_BLE(ble_buff1,(180-1));
  
//   //iflagb=0;
//  }
//   else if(fbfr == 2 ){
//   //  for( int J=205; J <=243 ;J++){
//   //       ble_buff2[J]=J++; //FILL REMAING BYTES WITH ZERO
//   //  }
//     fbfr =0xFF;
//   ble_buff2[0]=0x02;
//  // ble_buff2[1]= 0xff;
//   ble_buff2[2]= 0xff;
//   ble_buff2[3]= 0xff;
//        //  printk(" buff2\n");
//   send_rawdata_over_BLE(ble_buff2,(180-1));
//     //iflagb=0;
 
// //  bfr0_txnfg=1
//  }
// }


// typedef int err_code_t;
err_code_t result;
#define ERR_SUCCESS 0
#define ERR_PERMISSION -1
#define AS7058_FIFO_SAMPLE_SIZE 4
#define BUFFER_SIZE 180

// Sensor Data
// uint8_t fifo_data[1024];  // Example size; adjust as needed
// uint8_t ble_buff1[BUFFER_SIZE];
// uint8_t ble_buff2[BUFFER_SIZE];
// uint16_t RED_data[100], IR_data[100], GREEN_data[100], AMB_data[100];

#include <zephyr/kernel.h>
#include <zephyr/sys/printk.h>
#include <zephyr/drivers/timer/nrf_rtc_timer.h>
#include <hal/nrf_rtc.h>

#define RTC NRF_RTC1 
// Variables for sensor data
uint32_t red_data, ir_data, green_data, amb_data;
uint16_t fifo_level=1;
uint16_t fifo_data_size = 0;
uint32_t temp = 0;
static int iCnt = 0;
static int iflagb = 0;   // Toggle between buffers
static int fbfr = 0;     // Indicates which buffer to transmit
static int counter_int=0;


   uint32_t current_time;
static uint8_t data_int = 0;  // Persist data_int across calls
static uint32_t last_interrupt_time = 0;  // Store last interrupt timestamp
static uint32_t interrupt_duration = 0;  // Store duration of interrupt handling
static uint32_t interrupt_count = 0;  // Total interrupt count
static uint32_t last_rtc_value = 0;   // Last RTC counter value
static uint32_t rtc_interrupt_interval = 0; 

static uint32_t Timer_counter(void) {
    return nrf_rtc_counter_get(RTC);
}

err_code_t interrupt_handler(void) {
    if (!g_dev_config.is_meas_running) {
        return;
    }

    
    // Increment the interrupt count
 

    // Call Data_rady function to handle the data processing
    data_int=1;
    Data_rady();

    // Print interrupt statistics


    return ERR_SUCCESS;
}

 void Data_rady(void) {
    as7058_interrupt_t irq_status;

    if (data_int == 0) {
        return;
    }

    data_int = 0;

    // Update RTC-based interrupt interval
    uint32_t current_rtc_value = Timer_counter();
    rtc_interrupt_interval = current_rtc_value - last_rtc_value;
    last_rtc_value = current_rtc_value;
    interrupt_count++;

    err_code_t result = as7058_ifce_get_interrupt_status(&irq_status);

    if (ERR_SUCCESS == result && irq_status.fifo_threshold && g_dev_config.enabled_irqs.fifo_threshold) {
        result = as7058_ifce_get_fifo_level(&fifo_level);
        fifo_level = (fifo_level / g_dev_config.fifo_threshold) * g_dev_config.fifo_threshold;

        fifo_data_size = fifo_level * AS7058_FIFO_SAMPLE_SIZE;

        if (sizeof(fifo_data) >= fifo_data_size) {
            result = as7058_ifce_get_fifo_data(fifo_data, fifo_data_size);

            for (int j = 0; j < fifo_data_size; j += AS7058_FIFO_SAMPLE_SIZE) {
                uint32_t temp;

                // Parse RED data
                temp = (fifo_data[j + 2] << 16) | (fifo_data[j + 1] << 8) | fifo_data[j];
                uint32_t red_data = temp >> 4;
                RED_data[countraw] = red_data;

                // Parse IR data
                temp = (fifo_data[j + 5] << 16) | (fifo_data[j + 4] << 8) | fifo_data[j + 3];
                uint32_t ir_data = temp >> 4;
                IR_data[countraw] = ir_data;

                // Parse GREEN data
                temp = (fifo_data[j + 8] << 16) | (fifo_data[j + 7] << 8) | fifo_data[j + 6];
                uint32_t green_data = temp >> 4;
                GREEN_data[countraw] = green_data;

                // Parse AMBIENT data (fix the index)
                temp = (fifo_data[j + 11] << 16) | (fifo_data[j + 10] << 8) | fifo_data[j + 9];
                uint32_t amb_data = temp >> 4;
                AMB_data[countraw] = amb_data;

                 printk("RED: %d, IR: %d, GREEN: %d\n ", RED_data[countraw], IR_data[countraw], GREEN_data[countraw]);

                // Pack data into the current buffer
                uint8_t *current_buffer = (iflagb == 0) ? (ble_buff1 + 3) : (ble_buff2 + 3);

                // Ensure proper packing order
                current_buffer[iCnt++] = (red_data & 0xFF0000) >> 16;
                current_buffer[iCnt++] = (red_data & 0x00FF00) >> 8;
                current_buffer[iCnt++] = (red_data & 0x0000FF);

                current_buffer[iCnt++] = (ir_data & 0xFF0000) >> 16;
                current_buffer[iCnt++] = (ir_data & 0x00FF00) >> 8;
                current_buffer[iCnt++] = (ir_data & 0x0000FF);

                current_buffer[iCnt++] = (green_data & 0xFF0000) >> 16;
                current_buffer[iCnt++] = (green_data & 0x00FF00) >> 8;
                current_buffer[iCnt++] = (green_data & 0x0000FF);

                current_buffer[iCnt++] = (amb_data & 0xFF0000) >> 16;
                current_buffer[iCnt++] = (amb_data & 0x00FF00) >> 8;
                current_buffer[iCnt++] = (amb_data & 0x0000FF);

                // Check if buffer is full before switching
                if (iCnt >= BUFFER_SIZE) {
                    fbfr = (iflagb == 0) ? 1 : 2;
                    iflagb = !iflagb;
                    iCnt = 0;

                    // Clear the newly active buffer to avoid leftover data
                    if (iflagb == 0) {
                        memset(ble_buff1, 0, BUFFER_SIZE);
                    } else {
                        memset(ble_buff2, 0, BUFFER_SIZE);
                    }

                   // printk("Buffer%d -> RED: %d, IR: %d, GREEN: %d, AMBIENT: %d\n", iflagb + 1, red_data, ir_data, green_data, amb_data);
                }
            }
        }
    }

    return;
}



// err_code_t interrupt_handler(void)
// {
//     uint8_t data_int;
//     if(data_int ==1)
//     {
//         Data_rady();
//         data_int++;
//     }
//     return;
//     data_int =0;
// }



// // Interrupt handler to manage data extraction and buffering
// // err_code_t interrupt_handler(void) {
// void Data_rady(void){
//     as7058_interrupt_t irq_status;

//     if (!g_dev_config.is_meas_running) {
//         return ERR_PERMISSION;
//     }

//     err_code_t result = as7058_ifce_get_interrupt_status(&irq_status);

//     if (ERR_SUCCESS == result && irq_status.fifo_threshold && g_dev_config.enabled_irqs.fifo_threshold) {
//         result = as7058_ifce_get_fifo_level(&fifo_level);
//         fifo_level = (fifo_level / g_dev_config.fifo_threshold) * g_dev_config.fifo_threshold;

//         // printk("fifo_level  %d \n",fifo_level);
//         fifo_data_size = fifo_level * AS7058_FIFO_SAMPLE_SIZE;

//         if (sizeof(fifo_data) >= fifo_data_size) {
//             result = as7058_ifce_get_fifo_data(fifo_data, fifo_data_size);

//            for (int j = 0; j < fifo_data_size; j += 12) {
//            //int j =0;

//                         temp = (fifo_data[j + 2] << 16) | (fifo_data[j + 1] << 8) | fifo_data[j];
//                         red_data = temp >> 4;
//                         RED_data[countraw] = red_data;
                        

//                         temp = (fifo_data[j + 5] << 16) | (fifo_data[j + 4] << 8) | fifo_data[j + 3];
//                         ir_data = temp >> 4;
//                         IR_data[countraw] = ir_data;
                        

//                        // temp = (fifo_data[j + 8] << 16) | (fifo_data[j + 7] << 8) | fifo_data[j + 6];
//                        green_data = 0x00;//temp >> 4;
//                         //GREEN_data[countraw] = green_data;

//                        temp = (fifo_data[j + 8] << 16) | (fifo_data[j + 7] << 8) | fifo_data[j + 6];
//                         amb_data = temp >> 4;
//                        AMB_data[countraw] = amb_data;
                       
                       
//                 // red_data = ((fifo_data[j + 2] << 16) | (fifo_data[j + 1] << 8) | fifo_data[j]) >> 4;
//                 // ir_data = ((fifo_data[j + 5] << 16) | (fifo_data[j + 4] << 8) | fifo_data[j + 3]) >> 4;
//                 // green_data = ((fifo_data[j + 8] << 16) | (fifo_data[j + 7] << 8) | fifo_data[j + 6]) >> 4;
//                 // amb_data = ((fifo_data[j + 11] << 16) | (fifo_data[j + 10] << 8) | fifo_data[j + 9]) >> 4;

//                 // Pack data into the current buffer
//                 uint8_t *current_buffer = iflagb == 0 ? ble_buff1+3 : ble_buff2+3;

//                 current_buffer[iCnt++] = (red_data & 0xFF0000) >> 16;// counter_int++;//(red_data & 0xFF0000) >> 16;
//                 current_buffer[iCnt++] = (red_data & 0x00FF00) >> 8;// counter_int++;//(red_data & 0x00FF00) >> 8;
//                 current_buffer[iCnt++] = (red_data & 0x0000FF);//counter_int++;//(red_data & 0x0000FF);

//                 current_buffer[iCnt++] = (ir_data & 0xFF0000) >> 16;//counter_int++;//(ir_data & 0xFF0000) >> 16;
//                 current_buffer[iCnt++] = (ir_data & 0x00FF00) >> 8;//counter_int++;//(ir_data & 0x00FF00) >> 8;
//                 current_buffer[iCnt++] = (ir_data & 0x0000FF);//counter_int++;//(ir_data & 0x0000FF);

//                 current_buffer[iCnt++] = (green_data & 0xFF0000) >> 16;//counter_int++;//(green_data & 0xFF0000) >> 16;
//                 current_buffer[iCnt++] = (green_data & 0x00FF00) >> 8;//counter_int++;//(green_data & 0x00FF00) >> 8;
//                 current_buffer[iCnt++] = (green_data & 0x0000FF);//counter_int++;//(green_data & 0x0000FF);

//                 current_buffer[iCnt++] = (amb_data & 0xFF0000) >> 16;//counter_int++;//(amb_data & 0xFF0000) >> 16;
//                 current_buffer[iCnt++] = (amb_data & 0x00FF00) >> 8;//counter_int++;//(amb_data & 0x00FF00) >> 8;
//                 current_buffer[iCnt++] = (amb_data & 0x0000FF);//counter_int++;//(amb_data & 0x0000FF);

//                 if (iCnt >= BUFFER_SIZE) {  // Buffer is full
//                     fbfr = iflagb == 0 ? 1 : 2;  // Set buffer flag for sending

//                     iflagb = !iflagb;           // Toggle buffer
//                     iCnt = 0;                   // Reset index for next buffer
//                     // printk("clr buffer %d\n",iflagb);
//                     counter_int =0;
//                     // printk("Buffer%d -> RED: %d, IR: %d, GREEN: %d, AMBIENT: %d\n", iflagb + 1, red_data, ir_data, green_data, amb_data);
//                 }
//             }
//         }
//     }
//     return result;
// }

// Function to send the appropriate BLE buffer
void send_ble_buff(void) {
    if (fbfr == 1) {  // Buffer 1 is ready to send
        fbfr = 0xFF;  // Reset to avoid repeat sends
        ble_buff1[0] = 0x01;
        ble_buff1[1] = 0xFF;
        ble_buff1[2] = 0xFF;
        send_rawdata_over_BLE(ble_buff1, (180+3));  // Send buffer1
    }
    else if (fbfr == 2) {  // Buffer 2 is ready to send
        fbfr = 0xFF;  // Reset to avoid repeat sends
        ble_buff2[0] = 0x02;
        ble_buff2[1] = 0xFF;
        ble_buff2[2] = 0xFF;
        send_rawdata_over_BLE(ble_buff2, (180+3));  // Send buffer2
    }
}



// static uint8_t fbfr=0xB4;
// void send_ble_buff(void){
// static int bf1cnt=0,bf2cnt=0;
//  if(fbfr == 1){
//   fbfr =0xFF;
//   ble_buff1[0]=0x01;
//  // ble_buff1[1]= 0xff;
//   ble_buff1[2]= 0xff;
//   ble_buff1[3]= 0xff;


//   //  for( int J=205; J <=243 ;J++){
//   //       ble_buff1[J]=J; //FILL REMAING BYTES WITH ZERO
//   //  }
     
//   // printk(" buff1\n");
//   send_rawdata_over_BLE(ble_buff1,(20));
  
//   //iflagb=0;
//  }
//   else if(fbfr == 2 ){
//   //  for( int J=205; J <=243 ;J++){
//   //       ble_buff2[J]=J++; //FILL REMAING BYTES WITH ZERO
//   //  }
//     fbfr =0xFF;
//   ble_buff2[0]=0x02;
//  // ble_buff2[1]= 0xff;
//   ble_buff2[2]= 0xff;
//   ble_buff2[3]= 0xff;
//        //  printk(" buff2\n");
//   send_rawdata_over_BLE(ble_buff2,(20));
//     //iflagb=0;
 
// //  bfr0_txnfg=1
//  }
// }

// err_code_t interrupt_handler(void)
// {
//     err_code_t result;
//     as7058_interrupt_t irq_status;
//     uint16_t fifo_level;
//     uint16_t fifo_data_size = 0;
//     uint32_t temp = 0;
//     uint16_t red_data = 0, ir_data = 0, green_data = 0, amb_data = 0;
//     uint8_t ble_data[240];  // 240 bytes for BLE packet
//     static uint16_t ble_index = 0;  // Tracks position in ble_data array
//     uint16_t countraw = 0;

//     if (!g_dev_config.is_meas_running) {
//         return ERR_PERMISSION;
//     }
//     result = as7058_ifce_get_interrupt_status(&irq_status);

//     if (ERR_SUCCESS == result) {
//         if (irq_status.fifo_threshold && g_dev_config.enabled_irqs.fifo_threshold) {
//             result = as7058_ifce_get_fifo_level(&fifo_level);

//             if (ERR_SUCCESS == result) {
//                 fifo_level = (fifo_level / g_dev_config.fifo_threshold) * g_dev_config.fifo_threshold;
//                 fifo_data_size = fifo_level * AS7058_FIFO_SAMPLE_SIZE;

//                 if (sizeof(fifo_data) >= fifo_data_size) {
//                     result = as7058_ifce_get_fifo_data(fifo_data, fifo_data_size);

//                     // Extract RED, IR, GREEN, and AMBIENT data
//                     temp = (fifo_data[2] << 16) | (fifo_data[1] << 8) | fifo_data[0];
//                     red_data = temp >> 4;

//                     temp = (fifo_data[5] << 16) | (fifo_data[4] << 8) | fifo_data[3];
//                     ir_data = temp >> 4;

//                     temp = (fifo_data[8] << 16) | (fifo_data[7] << 8) | fifo_data[6];
//                     green_data = temp >> 4;

//                     temp = (fifo_data[11] << 16) | (fifo_data[10] << 8) | fifo_data[9];
//                     amb_data = temp >> 4;

//                     // Store data in ble_data buffer
//                     ble_data[ble_index++] = (red_data >> 8) & 0xFF;
//                     ble_data[ble_index++] = red_data & 0xFF;
//                     ble_data[ble_index++] = (ir_data >> 8) & 0xFF;
//                     ble_data[ble_index++] = ir_data & 0xFF;
//                     ble_data[ble_index++] = (green_data >> 8) & 0xFF;
//                     ble_data[ble_index++] = green_data & 0xFF;
//                     ble_data[ble_index++] = (amb_data >> 8) & 0xFF;
//                     ble_data[ble_index++] = amb_data & 0xFF;

//                     countraw++;
//                     printk("Counter: %d, RED: %d, IR: %d, GREEN: %d, AMBIENT: %d\n", countraw, red_data, ir_data, green_data, amb_data);

//                     // Check if ble_data is filled to 240 bytes
//                     if (ble_index >= 240) {
//                         send_rawdata_over_BLE(ble_data, 240);  // Send 240 bytes over BLE
//                         ble_index = 0;  // Reset the index for next packet
//                     }
//                 }
//             }
//         }
//     }
//     return result;
// }

static err_code_t get_measurement_config_from_sensor(as7058_meas_config_t *p_meas_config,
                                                     uint32_t *p_agc_enabled_sub_sample_flags)
{
    err_code_t result;
    agc_configuration_t ags_configs[AGC_MAX_CHANNEL_CNT];
    uint8_t agc_configs_num = AGC_MAX_CHANNEL_CNT;
    uint8_t i;

    M_CHECK_NULL_POINTER(p_meas_config);
    M_CHECK_NULL_POINTER(p_agc_enabled_sub_sample_flags);

    memset(p_meas_config->reserved, 0, sizeof(p_meas_config->reserved));

    result =
        as7058_ifce_get_sample_periods(&p_meas_config->ppg_sample_period_us, &p_meas_config->ecg_seq1_sample_period_us,
                                       &p_meas_config->ecg_seq2_sample_period_us);

    if (ERR_SUCCESS == result) {
        result = as7058_ifce_get_fifo_mapping(&p_meas_config->fifo_map);
    }

    if (ERR_SUCCESS == result) {
        *p_agc_enabled_sub_sample_flags = AS7058_SUB_SAMPLE_FLAG_NONE;
        result = agc_get_configuration(ags_configs, &agc_configs_num);
    }
    if (ERR_SUCCESS == result) {
        for (i = 0; i < AGC_MAX_CHANNEL_CNT; i++) {
            if (i < agc_configs_num) {
                p_meas_config->agc_channels[i] = ags_configs[i].channel;
                if (AGC_MODE_DEFAULT == ags_configs[i].mode) {
                    *p_agc_enabled_sub_sample_flags |= M_AS7058_SUB_SAMPLE_ID_TO_FLAG(ags_configs[i].channel);
                }
            } else {
                p_meas_config->agc_channels[i] = AS7058_SUB_SAMPLE_ID_DISABLED;
            }
        }

        result = as7058_ifce_get_active_sar_sub_samples(&p_meas_config->sar_map);
    }

    if (ERR_SUCCESS == result) {
        result = as7058_ifce_get_sar_transfer_mode(&p_meas_config->sar_transfer_mode);
    }

    return result;
}

/******************************************************************************
 *                             GLOBAL FUNCTIONS                               *
 ******************************************************************************/
int COUNTDebug = 0;
err_code_t as7058_initialize(const as7058_callback_t p_normal_callback,
                             const as7058_callback_special_measurement_t p_special_callback, const void *p_cb_param,
                             const char *p_interface_descr)
{
    err_code_t result;
    uint8_t id;

    M_CHECK_NULL_POINTER(p_normal_callback);

    as7058_shutdown();

    result = as7058_osal_initialize(p_interface_descr);

    if (ERR_SUCCESS == result) 
    {
        result = as7058_ifce_get_silicon_id(&id);
    }
    if ((ERR_SUCCESS == result) && (AS7058_SILICON_ID != id)) 
    {
        COUNTDebug++;
        result = ERR_IDENTIFICATION;
    }

    if (ERR_SUCCESS == result) 
    {
        COUNTDebug++;
        result = as7058_ifce_reset_chip();
    }

    if (ERR_SUCCESS == result) 
    {
        COUNTDebug++;
        g_dev_config.p_normal_callback = p_normal_callback;
        g_dev_config.p_special_callback = p_special_callback;
        g_dev_config.p_cb_param = p_cb_param;

        result = as7058_osal_register_int_handler(interrupt_handler);
    }

    if (ERR_SUCCESS == result)
     {
        COUNTDebug++;
        result = agc_initialize();
    }

    if (ERR_SUCCESS == result) 
    {
        result = as7058_eda_scaling_initialize();
    }
    if (ERR_SUCCESS == result)
     {
        result = as7058_bioz_initialize();
    }

    if (ERR_SUCCESS == result) 
    {
        result = as7058_pd_offset_calibration_initialize();
    }

    if (ERR_SUCCESS != result) 
    {
        as7058_shutdown();
    } else 
    {
        g_dev_config.lib_state = LIB_STATE_CONFIGURATION;
        g_dev_config.is_meas_running = FALSE;
    }

    return result;
}

err_code_t as7058_shutdown(void)
{
    err_code_t result, result_osal, result_agc, result_eda_scaling, result_bioz, result_pd_offset_calibration;

    if (LIB_STATE_UNINITIALIZED != g_dev_config.lib_state) {

        result = as7058_stop_measurement();

        if (ERR_SUCCESS == result) {
            result = as7058_ifce_reset_chip();
        }
    } else {
        result = ERR_SUCCESS;
    }

    result_agc = agc_shutdown();
    if (ERR_SUCCESS == result) {
        result = result_agc;
    }

    result_eda_scaling = as7058_eda_scaling_shutdown();
    if (ERR_SUCCESS == result) {
        result = result_eda_scaling;
    }

    result_bioz = as7058_bioz_shutdown();
    if (ERR_SUCCESS == result) {
        result = result_bioz;
    }

    result_pd_offset_calibration = as7058_pd_offset_calibration_shutdown();
    if (ERR_SUCCESS == result) {
        result = result_pd_offset_calibration;
    }

    result_osal = as7058_osal_shutdown();
    if (ERR_SUCCESS == result) {
        result = result_osal;
    }

    memset(&g_dev_config, 0, sizeof(g_dev_config));

    return result;
}

err_code_t as7058_set_reg_group(as7058_reg_group_ids_t id, const uint8_t *p_data, uint8_t size)
{
    if (LIB_STATE_CONFIGURATION != g_dev_config.lib_state) {
        return ERR_PERMISSION;
    }

    if (AS7058_REG_GROUP_ID_ECG == id) {
        /* Store value of register BIOZ_CFG. */
        g_dev_config.reg_val_bioz_cfg = p_data[REG_INDEX__ECG__BIOZ_CFG];

        /* Copy register group buffer so that it is modifiable. */
        M_CHECK_SIZE(size, sizeof(as7058_reg_group_ecg_t));
        uint8_t modified_data[sizeof(as7058_reg_group_ecg_t)];
        memcpy(modified_data, p_data, sizeof(as7058_reg_group_ecg_t));

        /* To work around an issue with Revision A of the silicon, field gsr_en of register BIOZ_CFG must only be set
         * while a GSR measurement is in progress. Since it is not required to start a measurement immediately after
         * writing register values, the field is cleared in the register values buffer that gets written to the silicon.
         * If the field is set in the register values passed to the Chip Library, the Chip Library sets and clears the
         * register field in the silicon immediately before and after measurement, respectively. In the output of all
         * Chip Library functions to read register values, the register's value is always set to the value that has been
         * passed to the Chip Library, independent of the register's current value in the silicon. Note that this
         * workaround is only effective when the measurement is both started and stopped by calling the corresponding
         * Chip Library functions. */
        modified_data[REG_INDEX__ECG__BIOZ_CFG] &= ~REG_MASK__BIOZ_CFG__GSR_EN;

        return as7058_ifce_set_reg_group(id, modified_data, sizeof(as7058_reg_group_ecg_t));
    } else {
        return as7058_ifce_set_reg_group(id, p_data, size);
    }
}

err_code_t as7058_get_reg_group(as7058_reg_group_ids_t id, uint8_t *p_data, uint8_t *p_size)
{
    if (LIB_STATE_CONFIGURATION != g_dev_config.lib_state) {
        return ERR_PERMISSION;
    }

    M_CHECK_SUCCESS(as7058_ifce_get_reg_group(id, p_data, p_size));

    if (AS7058_REG_GROUP_ID_ECG == id) {
        /* Replace the value of register BIOZ_CFG with the value that has been passed to the Chip Library. See the
         * comments in as7058_set_reg_group and as7058_write_register for more information. */
        p_data[REG_INDEX__ECG__BIOZ_CFG] = g_dev_config.reg_val_bioz_cfg;
    }

    return ERR_SUCCESS;
}

err_code_t as7058_write_register(as7058_reg_addresses_t reg_addr, uint8_t reg_val)
{
    if (LIB_STATE_UNINITIALIZED == g_dev_config.lib_state) {
        return ERR_PERMISSION;
    }

    if (AS7058_REGADDR_BIOZ_CFG == reg_addr) {
        /* Store value of register BIOZ_CFG. */
        g_dev_config.reg_val_bioz_cfg = reg_val;

        /* To work around an issue with Revision A of the silicon, field gsr_en of register BIOZ_CFG must only be set
         * while a GSR measurement is in progress. Since it is not required to start a measurement immediately after
         * writing register values, the field is cleared in the register value that gets written to the silicon. If the
         * field is set in the register value passed to the Chip Library, the Chip Library sets and clears the register
         * field in the silicon immediately before and after measurement, respectively. In the output of all Chip
         * Library functions to read register values, the register's value is always set to the value that has been
         * passed to the Chip Library, independent of the register's current value in the silicon. Note that this
         * workaround is only effective when the measurement is both started and stopped by calling the corresponding
         * Chip Library functions. */
        reg_val &= ~REG_MASK__BIOZ_CFG__GSR_EN;
    }

    return as7058_ifce_write_register(reg_addr, reg_val);
}

err_code_t as7058_read_register(as7058_reg_addresses_t reg_addr, uint8_t *p_reg_val)
{
    if (LIB_STATE_UNINITIALIZED == g_dev_config.lib_state) {
        return ERR_PERMISSION;
    }

    M_CHECK_SUCCESS(as7058_ifce_read_register(reg_addr, p_reg_val));

    if (AS7058_REGADDR_BIOZ_CFG == reg_addr) {
        /* Replace the value of register BIOZ_CFG with the value that has been passed to the Chip Library. See the
         * comments in as7058_set_reg_group and as7058_write_register for more information. */
        *p_reg_val = g_dev_config.reg_val_bioz_cfg;
    }

    return ERR_SUCCESS;
}

err_code_t as7058_set_agc_config(const agc_configuration_t *p_agc_configs, uint8_t agc_config_num)
{
    if (LIB_STATE_CONFIGURATION != g_dev_config.lib_state) {
        return ERR_PERMISSION;
    }

    return agc_set_configuration(p_agc_configs, agc_config_num);
}

err_code_t as7058_get_agc_config(agc_configuration_t *p_agc_configs, uint8_t *p_agc_config_num)
{
    if (LIB_STATE_UNINITIALIZED == g_dev_config.lib_state) {
        return ERR_PERMISSION;
    }

    return agc_get_configuration(p_agc_configs, p_agc_config_num);
}

err_code_t as7058_get_measurement_config(as7058_meas_config_t *p_meas_config)
{
    err_code_t result;
    uint32_t agc_enabled_sub_sample_flags;
    M_CHECK_NULL_POINTER(p_meas_config);

    switch (g_dev_config.lib_state) {

    case LIB_STATE_CONFIGURATION:

        result = get_measurement_config_from_sensor(p_meas_config, &agc_enabled_sub_sample_flags);
        break;

    case LIB_STATE_MEASUREMENT:

        memcpy(p_meas_config, &g_dev_config.meas_config, sizeof(as7058_meas_config_t));
        result = ERR_SUCCESS;
        break;

    default:

        result = ERR_PERMISSION;
        break;
    }

    return result;
}

err_code_t as7058_start_measurement(as7058_meas_mode_t mode)
{
    err_code_t result;
    uint32_t active_agc_sub_samples;

    if (LIB_STATE_CONFIGURATION != g_dev_config.lib_state) {
        return ERR_PERMISSION;
    }

    M_CHECK_ARGUMENT_LOWER(mode, AS7058_MEAS_MODE_NUM);

    /* Update the measurement configuration */
    result = get_measurement_config_from_sensor(&g_dev_config.meas_config, &active_agc_sub_samples);

    /* Do not allow parallel AGC and SAR */
    if (ERR_SUCCESS == result) {

        /* Check whether any sub-samples are controlled by both AGC and SAR */
        if (active_agc_sub_samples & g_dev_config.meas_config.sar_map) {
            result = ERR_CONFIG;
        }
    }

    if (ERR_SUCCESS == result) {
        result = as7058_ifce_get_interrupt_enable(&g_dev_config.enabled_irqs);
    }

    if (ERR_SUCCESS == result) {
        result = as7058_ifce_get_fifo_threshold(&g_dev_config.fifo_threshold);
    }

    /* At least on sub sample should be enabled */
    if ((ERR_SUCCESS == result) && (0 == g_dev_config.meas_config.fifo_map)) {
        result = ERR_CONFIG;
    }

    /* Reset AGC first to set inital values for PD offset and LED current
       before measurement starts */
    if (ERR_SUCCESS == result) {
        result = agc_start_processing(&g_dev_config.meas_config, sizeof(g_dev_config.meas_config));
    }

    if ((ERR_SUCCESS == result) && (AS7058_MEAS_MODE_NORMAL != mode)) {
        /* Check that the special measurement callback was configured */
        if (NULL == g_dev_config.p_special_callback) {
            result = ERR_CONFIG;
        } else if (AS7058_MEAS_MODE_SPECIAL_SCALING_EDA == mode) {
            result = as7058_eda_scaling_start(g_dev_config.meas_config);
        } else if (AS7058_MEAS_MODE_SPECIAL_BIOZ == mode) {
            result = as7058_bioz_start(g_dev_config.meas_config);
        } else if (AS7058_MEAS_MODE_SPECIAL_PD_OFFSET_CALIBRATION == mode) {
            result = as7058_pd_offset_calibration_start(g_dev_config.meas_config);
        }
    }

    /* If the gsr_en field was set in the BIOZ_CFG register value passed to the Chip Library, set register BIOZ_CFG to
     * the stored register value. See the comments in as7058_set_reg_group and as7058_write_register for more
     * information. */
    if ((ERR_SUCCESS == result) && (g_dev_config.reg_val_bioz_cfg & REG_MASK__BIOZ_CFG__GSR_EN)) {
        result = as7058_ifce_write_register(AS7058_REGADDR_BIOZ_CFG, g_dev_config.reg_val_bioz_cfg);
    }

    if (ERR_SUCCESS == result) {
        g_dev_config.is_meas_running = TRUE;
        result = as7058_ifce_start_measurement();
    }

    if (ERR_SUCCESS == result) {
        g_dev_config.lib_state = LIB_STATE_MEASUREMENT;
        g_dev_config.mode = mode;
    } else {
        as7058_stop_measurement();
    }

    return result;
}

err_code_t as7058_stop_measurement(void)
{
    err_code_t result = ERR_DATA_TRANSFER;

    if (LIB_STATE_UNINITIALIZED == g_dev_config.lib_state) {
        return ERR_PERMISSION;
    }

    g_dev_config.is_meas_running = FALSE;

    result = as7058_ifce_stop_measurement();

    /* If starting the measurement did set the gsr_en field in register BIOZ_CFG, clear it now. See the comments in
     * as7058_set_reg_group and as7058_write_register for more information. */
    if ((ERR_SUCCESS == result) && (g_dev_config.reg_val_bioz_cfg & REG_MASK__BIOZ_CFG__GSR_EN)) {
        result = as7058_ifce_write_register(AS7058_REGADDR_BIOZ_CFG,
                                            (g_dev_config.reg_val_bioz_cfg & (~REG_MASK__BIOZ_CFG__GSR_EN)));
    }

    if (ERR_SUCCESS == result) {
        result = agc_stop_processing();
    }

    if (ERR_SUCCESS == result) {
        if (AS7058_MEAS_MODE_SPECIAL_SCALING_EDA == g_dev_config.mode) {
            result = as7058_eda_scaling_stop();
        } else if (AS7058_MEAS_MODE_SPECIAL_BIOZ == g_dev_config.mode) {
            result = as7058_bioz_stop();
        } else if (AS7058_MEAS_MODE_SPECIAL_PD_OFFSET_CALIBRATION == g_dev_config.mode) {
            result = as7058_pd_offset_calibration_stop();
        }
    }

    if (ERR_SUCCESS == result) {
        g_dev_config.lib_state = LIB_STATE_CONFIGURATION;
    }

    return result;
}

const char *as7058_get_version(void)
{
    return AS7058_CHIPLIB_VERSION;
}

err_code_t as7058_set_special_measurement_config(as7058_meas_mode_t mode, const void *p_config, uint16_t size)
{
    err_code_t result;

    if (LIB_STATE_CONFIGURATION != g_dev_config.lib_state) {
        return ERR_PERMISSION;
    }

    if (NULL == g_dev_config.p_special_callback) {
        return ERR_CONFIG;
    }

    M_CHECK_NULL_POINTER(p_config);

    if (AS7058_MEAS_MODE_SPECIAL_SCALING_EDA == mode) {
        if (sizeof(as7058_eda_scaling_config_t) == size) {
            result = as7058_eda_scaling_configure((const as7058_eda_scaling_config_t *)(p_config));
        } else {
            result = ERR_SIZE;
        }
    } else if (AS7058_MEAS_MODE_SPECIAL_BIOZ == mode) {
        if (sizeof(as7058_bioz_meas_config_t) == size) {
            result = as7058_bioz_configure((const as7058_bioz_meas_config_t *)(p_config));
        } else {
            result = ERR_SIZE;
        }
    } else if (AS7058_MEAS_MODE_SPECIAL_PD_OFFSET_CALIBRATION == mode) {
        if (sizeof(as7058_pd_offset_calibration_config_t) == size) {
            result = as7058_pd_offset_calibration_configure((const as7058_pd_offset_calibration_config_t *)(p_config));
        } else {
            result = ERR_SIZE;
        }
    } else {
        result = ERR_ARGUMENT;
    }

    return result;
}


/***********************RAW data extract*******************************************/
enum statemarker{
    ppg1,
    ppgsub1,
    ppg2,
    ppgsub2,
    ecgsub1,
    ecgsub2,
    ecg2channel
};



// int ppg1StatusFlash = 0;
/************************Raw initilization****************************/

void Fiforawdataexteract(uint8_t *fifo_data, int datasize)
{
    uint32_t ppg1c,ppg1cbitc,ppgs1c,ppg2c,ppgs2c,ecgsc1,ecgsc2,ecgcc,stsc = 0;
    uint32_t temp = 0;
    
    for(int i=0;i<datasize;)
    {
        temp = (fifo_data[i+2]<<16 | fifo_data[i+1]<<8 | fifo_data[i]);  
        //printk("temp =%d\n",temp);
        switch(temp&7)
        {
         case ppg1:
             ppg1raw[ppg1c] = temp>>4;
             ppg1c++;    //pp1 data TODO
            // printk("ppg1raw =%d %d\n",ppg1c,ppg1raw[ppg1c]);
             //printk("ppg1raw =%d %d %d\n",ppg1c,ppg1raw[ppg1c],temp);
           break;
         case ppgsub1:
             ppgsub1raw[ppgs1c] = temp>>4;
             ppgs1c++;  //pp1 sub data TODO
             printk("ppgs1c =%d\n",ppgs1c);
            //printk("ppgsub1raw =%d %d %d\n",ppgs1c,ppgsub1raw[ppgs1c],temp);      
           break;
         case ppg2:
             ppg2raw[ppg2c] = temp>>4;
             ppg2c++;   //pp2 data TODO
             printk("ppg2c =%d\n",ppg2c);
           break;
         case ppgsub2:
             ppgsub2raw[ppgs2c] = temp>>4;
             ppgs2c++;   //pp2  sub data TODO
             printk("ppgs2c =%d\n",ppgs2c);
           break;
         case ecgsub1:
            ecgsub1raw[ecgsc1] = temp>>4;
            ecgsc1++;    //ecg sub1 TODO
       
           break;
         case ecgsub2:
            ecgsub2raw[ecgsc2] = temp>>4;
            ecgsc2++;       //ecg sub2 TODO
       
           break;
         case ecg2channel:
            ecgchannelraw[ecgcc] = temp>>4;
            ecgcc++;       // ecg 2 channel TODO
           break;
         default:
            statusraw[stsc] = temp>>4;
            stsc++;        //status TODO
          break;
        }
        i=i+3;
    }
    HrAlgorthim(&ppg1raw);
}

void FifoReset(void)
{
    Raw_Data_Counter = 0;
    Skipcount = 0;
    countraw = 0;
    
    memset(RED_data,0,sizeof(RED_data));
    memset(IR_data,0,sizeof(IR_data));
    memset(AMB_data,0,sizeof(AMB_data));
    memset(GREEN_data,0,sizeof(GREEN_data));
    memset(ppg1raw,0,sizeof(ppg1raw));
    memset(ppgsub1raw,0,sizeof(ppgsub1raw));
    memset(ppg2raw,0,sizeof(ppg2raw));
    memset(ppgsub2raw,0,sizeof(ppgsub2raw));
    memset(RawData,0,sizeof(RawData));

}