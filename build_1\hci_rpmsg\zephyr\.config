# CONFIG_INPUT is not set
# CONFIG_WIFI is not set
# CONFIG_LV_COLOR_16_SWAP is not set
CONFIG_LV_DPI_DEF=130
# CONFIG_MIPI_DSI is not set
# CONFIG_MODEM is not set
# CONFIG_UART_INTERRUPT_DRIVEN is not set
# CONFIG_SPI is not set
CONFIG_BT_HCI_ACL_FLOW_CONTROL=y
CONFIG_BT_HCI_VS_EXT=y
CONFIG_BOARD="nrf5340dk_nrf5340_cpunet"
CONFIG_FLASH_LOAD_SIZE=0
CONFIG_SRAM_SIZE=64
CONFIG_FLASH_LOAD_OFFSET=0
CONFIG_MBOX_NRFX_IPC=y
CONFIG_HEAP_MEM_POOL_SIZE=8192
CONFIG_BT_CTLR=y
CONFIG_NUM_IRQS=30
CONFIG_SOC_SERIES="nrf53"
CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC=32768
CONFIG_SOC="nRF5340_CPUNET_QKAA"
CONFIG_CLOCK_CONTROL_INIT_PRIORITY=30
CONFIG_FLASH_SIZE=256
CONFIG_FLASH_BASE_ADDRESS=0x1000000
CONFIG_ICACHE_LINE_SIZE=32
CONFIG_DCACHE_LINE_SIZE=32
CONFIG_ROM_START_OFFSET=0
CONFIG_PINCTRL=y
CONFIG_CLOCK_CONTROL=y
# CONFIG_WATCHDOG is not set
# CONFIG_RESET is not set
CONFIG_GPIO=y
CONFIG_SOC_HAS_TIMING_FUNCTIONS=y
CONFIG_ARCH_HAS_CUSTOM_BUSY_WAIT=y
# CONFIG_PM_DEVICE is not set
# CONFIG_IPC_SERVICE_BACKEND_RPMSG_SHMEM_RESET is not set
CONFIG_LOG_DOMAIN_NAME="net"
CONFIG_NRF_RTC_TIMER=y
CONFIG_SYS_CLOCK_TICKS_PER_SEC=32768
CONFIG_BUILD_OUTPUT_HEX=y
CONFIG_SERIAL_INIT_PRIORITY=55
# CONFIG_MBEDTLS is not set
# CONFIG_MEMC is not set
# CONFIG_CODE_DATA_RELOCATION is not set
# CONFIG_CPU_HAS_CUSTOM_FIXED_SOC_MPU_REGIONS is not set
CONFIG_TINYCRYPT=y
CONFIG_SERIAL=y
CONFIG_MAIN_STACK_SIZE=512
# CONFIG_SRAM_VECTOR_TABLE is not set
# CONFIG_USE_DT_CODE_PARTITION is not set
CONFIG_PLATFORM_SPECIFIC_INIT=y
CONFIG_IDLE_STACK_SIZE=256
CONFIG_BUILD_OUTPUT_BIN=y
CONFIG_MP_MAX_NUM_CPUS=1
CONFIG_HAS_DTS=y

#
# Devicetree Info
#
CONFIG_DT_HAS_ARDUINO_HEADER_R3_ENABLED=y
CONFIG_DT_HAS_ARM_ARMV8M_MPU_ENABLED=y
CONFIG_DT_HAS_ARM_CORTEX_M33_ENABLED=y
CONFIG_DT_HAS_ARM_V8M_NVIC_ENABLED=y
CONFIG_DT_HAS_FIXED_PARTITIONS_ENABLED=y
CONFIG_DT_HAS_GPIO_KEYS_ENABLED=y
CONFIG_DT_HAS_GPIO_LEDS_ENABLED=y
CONFIG_DT_HAS_MMIO_SRAM_ENABLED=y
CONFIG_DT_HAS_NORDIC_MBOX_NRF_IPC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_ACL_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_CCM_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_CLOCK_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_DPPIC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_ECB_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_EGU_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_FICR_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_GPIO_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_GPIOTE_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_GPREGRET_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_IEEE802154_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_IPC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_PINCTRL_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_POWER_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_RADIO_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_RNG_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_SWI_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_TEMP_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_UARTE_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_UICR_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_VMC_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF_WDT_ENABLED=y
CONFIG_DT_HAS_NORDIC_NRF53_FLASH_CONTROLLER_ENABLED=y
CONFIG_DT_HAS_SOC_NV_FLASH_ENABLED=y
CONFIG_DT_HAS_ZEPHYR_BT_HCI_ENTROPY_ENABLED=y
CONFIG_DT_HAS_ZEPHYR_IPC_OPENAMP_STATIC_VRINGS_ENABLED=y
CONFIG_DT_HAS_ZEPHYR_MEMORY_REGION_ENABLED=y
# end of Devicetree Info

#
# Modules
#

#
# Available modules.
#

#
# nrf (C:/ncs/v2.5.99-dev1/nrf)
#
CONFIG_NUM_METAIRQ_PRIORITIES=0
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=512
# CONFIG_INIT_STACKS is not set

#
# Nordic nRF Connect
#
CONFIG_BOOT_BANNER_STRING="Booting nRF Connect SDK"
CONFIG_WARN_EXPERIMENTAL=y
CONFIG_PRIVILEGED_STACK_SIZE=1024
CONFIG_BT_BUF_CMD_TX_COUNT=10
CONFIG_ENTROPY_GENERATOR=y
CONFIG_INIT_ARCH_HW_AT_BOOT=y
CONFIG_NORDIC_QSPI_NOR_FLASH_LAYOUT_PAGE_SIZE=2048
# CONFIG_GETOPT is not set
# CONFIG_NCS_SAMPLES_DEFAULTS is not set
# CONFIG_NCS_SAMPLE_EMPTY_APP_CORE_CHILD_IMAGE is not set
# CONFIG_NCS_SAMPLE_DTM_REMOTE_HCI_CHILD_IMAGE is not set

#
# Image build variants
#
# CONFIG_NCS_MCUBOOT_IN_BUILD is not set
# end of Image build variants

# CONFIG_NCS_SAMPLE_MCUMGR_BT_OTA_DFU_SPEEDUP is not set
CONFIG_BT_BUF_ACL_TX_SIZE=27
CONFIG_BT_BUF_ACL_RX_SIZE=27
CONFIG_BT_CTLR_DATA_LENGTH_MAX=27

#
# Subsystems
#

#
# Bootloader
#
# CONFIG_BUILD_S1_VARIANT is not set
# CONFIG_SECURE_BOOT is not set
CONFIG_PM_PARTITION_SIZE_PROVISION=0x280
# CONFIG_B0_MIN_PARTITION_SIZE is not set
CONFIG_PM_PARTITION_SIZE_B0_IMAGE=0x7800
# CONFIG_SECURE_BOOT_CRYPTO is not set

#
# Secure Boot firmware validation
#
CONFIG_SB_VALIDATION_INFO_MAGIC=0x86518483
CONFIG_SB_VALIDATION_POINTER_MAGIC=0x6919b47e
CONFIG_SB_VALIDATION_INFO_CRYPTO_ID=1
CONFIG_SB_VALIDATION_INFO_VERSION=2
CONFIG_SB_VALIDATION_METADATA_OFFSET=0
CONFIG_SB_VALIDATE_FW_HASH=y
# end of Secure Boot firmware validation

# CONFIG_SECURE_BOOT_STORAGE is not set
# end of Bootloader

#
# Bluetooth Low Energy
#
CONFIG_BT_MAX_CONN=16
# CONFIG_BT_LL_SOFTDEVICE is not set
CONFIG_BT_CTLR_DF_SUPPORT=y
CONFIG_BT_HCI_TX_STACK_SIZE=1024
CONFIG_BT_RX_STACK_SIZE=768
CONFIG_BT_CTLR_DF=y
CONFIG_BT_CTLR_DF_CONN_CTE_RSP=y
CONFIG_BT_CTLR_FAL_SIZE=8

#
# BLE Libraries
#
# CONFIG_BT_GATT_POOL is not set
# CONFIG_BT_GATT_DM is not set
# CONFIG_BT_ADV_PROV is not set
# CONFIG_BT_SCAN is not set
# CONFIG_BT_CONN_CTX is not set

#
# Bluetooth Services
#
# CONFIG_BT_AMS_CLIENT is not set
# CONFIG_BT_ANCS_CLIENT is not set
# CONFIG_BT_BAS_CLIENT is not set
# CONFIG_BT_BMS is not set
# CONFIG_BT_CTS_CLIENT is not set
# CONFIG_BT_DFU_SMP is not set
# CONFIG_BT_GATTP is not set
# CONFIG_BT_HIDS is not set
# CONFIG_BT_HOGP is not set
# CONFIG_BT_LBS is not set
# CONFIG_BT_NSMS is not set
# CONFIG_BT_NUS is not set
# CONFIG_BT_NUS_CLIENT is not set
# CONFIG_BT_RSCS is not set
# CONFIG_BT_THROUGHPUT is not set
# CONFIG_BT_LATENCY is not set
# CONFIG_BT_LATENCY_CLIENT is not set
# CONFIG_BT_HRS_CLIENT is not set
# CONFIG_BT_DDFS is not set
# CONFIG_BT_MDS is not set
# CONFIG_BT_CGMS is not set
# CONFIG_BT_FAST_PAIR is not set
# end of Bluetooth Services

#
# BLE over nRF RPC
#
# CONFIG_BT_RPC_STACK is not set
CONFIG_BT_CENTRAL=y
CONFIG_BT_PERIPHERAL=y
CONFIG_BT_OBSERVER=y
CONFIG_BT_BROADCASTER=y
CONFIG_BT_CONN=y
CONFIG_BT_REMOTE_VERSION=y
CONFIG_BT_PHY_UPDATE=y
CONFIG_BT_DATA_LEN_UPDATE=y
# CONFIG_BT_EXT_ADV is not set
CONFIG_BT_BUF_ACL_TX_COUNT=7
CONFIG_BT_BUF_ACL_RX_COUNT=3
CONFIG_BT_BUF_EVT_RX_SIZE=68
CONFIG_BT_BUF_EVT_RX_COUNT=3
CONFIG_BT_BUF_EVT_DISCARDABLE_SIZE=43
CONFIG_BT_BUF_CMD_TX_SIZE=65
CONFIG_BT_HAS_HCI_VS=y
CONFIG_BT_HCI_VS=y
# CONFIG_BT_HCI_VS_EVT is not set
# CONFIG_BT_HCI_VS_FATAL_ERROR is not set
# CONFIG_BT_WAIT_NOP is not set
CONFIG_BT_RPA=y
CONFIG_BT_ASSERT=y
CONFIG_BT_ASSERT_VERBOSE=y
# CONFIG_BT_ASSERT_PANIC is not set
CONFIG_BT_DEBUG_NONE=y
# CONFIG_BT_DEBUG_LOG is not set
# CONFIG_BT_DEBUG_MONITOR_UART is not set
# CONFIG_BT_LONG_WQ is not set
# CONFIG_BT_HCI_TX_STACK_SIZE_WITH_PROMPT is not set
CONFIG_BT_HCI_TX_PRIO=7
CONFIG_BT_HCI_RESERVE=0
CONFIG_BT_RECV_BLOCKING=y
# CONFIG_BT_RECV_WORKQ_SYS is not set
# CONFIG_BT_RECV_WORKQ_BT is not set
CONFIG_BT_RX_PRIO=8
CONFIG_BT_DRIVER_RX_HIGH_PRIO=6
# CONFIG_BT_ECC is not set
# CONFIG_BT_HOST_CCM is not set
# CONFIG_BT_LOG_SNIFFER_INFO is not set
# CONFIG_BT_TESTING is not set
# CONFIG_BT_HCI_VS_EVT_USER is not set
# end of BLE over nRF RPC
# end of Bluetooth Low Energy

#
# DFU
#
# CONFIG_DFU_MULTI_IMAGE is not set
# CONFIG_DFU_TARGET is not set
# end of DFU

# CONFIG_ESB is not set

#
# Peripheral CPU DFU (PCD)
#
# CONFIG_PCD is not set
# CONFIG_PCD_APP is not set
CONFIG_PCD_VERSION_PAGE_BUF_SIZE=2046
# CONFIG_PCD_NET is not set
# end of Peripheral CPU DFU (PCD)

#
# Networking
#

#
# Application protocols
#

#
# nRF Cloud
#

#
# Client ID (nRF Cloud Device ID)
#
CONFIG_NRF_CLOUD_CLIENT_ID_SRC_COMPILE_TIME=y
CONFIG_NRF_CLOUD_CLIENT_ID="my-client-id"
# end of Client ID (nRF Cloud Device ID)

# CONFIG_NRF_CLOUD_MQTT is not set
# CONFIG_NRF_CLOUD_FOTA is not set
# CONFIG_NRF_CLOUD_FOTA_FULL_MODEM_UPDATE is not set
# CONFIG_NRF_CLOUD_REST is not set
# CONFIG_NRF_CLOUD_ALERT is not set
CONFIG_NRF_CLOUD_LOG_OUTPUT_LEVEL=1
# CONFIG_NRF_CLOUD_LOG_INCLUDE_LEVEL_0 is not set
CONFIG_NRF_CLOUD_LOG_BUF_SIZE=256
# CONFIG_NRF_CLOUD_COAP is not set
# CONFIG_NRF_CLOUD_GATEWAY is not set
# end of nRF Cloud

# CONFIG_REST_CLIENT is not set
# CONFIG_DOWNLOAD_CLIENT is not set
# CONFIG_AWS_IOT is not set
# CONFIG_AWS_JOBS is not set
# CONFIG_AZURE_IOT_HUB is not set

#
# Self-Registration (Zi ZHu Ce)
#
# end of Self-Registration (Zi ZHu Ce)

# CONFIG_ICAL_PARSER is not set
# CONFIG_FTP_CLIENT is not set
# CONFIG_LWM2M_CLIENT_UTILS is not set
# CONFIG_WIFI_CREDENTIALS is not set
# CONFIG_WIFI_CREDENTIALS_STATIC is not set
# CONFIG_MQTT_HELPER is not set
# CONFIG_NRF_MCUMGR_SMP_CLIENT is not set
# end of Application protocols
# end of Networking

#
# NFC
#
# CONFIG_NFC_NDEF is not set
# CONFIG_NFC_NDEF_PARSER is not set
# CONFIG_NFC_NDEF_PAYLOAD_TYPE_COMMON is not set
# CONFIG_NFC_T2T_PARSER is not set
# CONFIG_NFC_T4T_NDEF_FILE is not set
# CONFIG_NFC_T4T_ISODEP is not set
# CONFIG_NFC_T4T_APDU is not set
# CONFIG_NFC_T4T_CC_FILE is not set
# CONFIG_NFC_T4T_HL_PROCEDURE is not set
# CONFIG_NFC_PLATFORM is not set
# CONFIG_NFC_TNEP_TAG is not set
# CONFIG_NFC_TNEP_POLLER is not set
# CONFIG_NFC_TNEP_CH is not set
# end of NFC

# CONFIG_APP_EVENT_MANAGER is not set
# CONFIG_NRF_PROFILER is not set
# CONFIG_FW_INFO is not set

#
# Debug
#
# CONFIG_CPU_LOAD is not set
# CONFIG_PPI_TRACE is not set
# end of Debug

# CONFIG_SHELL_BT_NUS is not set
# CONFIG_SHELL_IPC is not set

#
# Multiprotocol service layer (MPSL)
#
# CONFIG_MPSL_FEM_ONLY is not set
# CONFIG_MPSL_FEM_DEVICE_CONFIG_254 is not set
CONFIG_MPSL_THREAD_COOP_PRIO=8
CONFIG_MPSL_WORK_STACK_SIZE=1024
CONFIG_MPSL_TIMESLOT_SESSION_COUNT=0
# CONFIG_MPSL_ASSERT_HANDLER is not set
# CONFIG_MPSL_TRIGGER_IPC_TASK_ON_RTC_START is not set
# end of Multiprotocol service layer (MPSL)

#
# Partition Manager
#
CONFIG_PARTITION_MANAGER_ENABLED=y
CONFIG_FLASH_MAP_CUSTOM=y
CONFIG_SRAM_BASE_ADDRESS=0x21000000

#
# Zephyr subsystem configurations
#
# end of Zephyr subsystem configurations

#
# NCS subsystem configurations
#
# CONFIG_PM_SINGLE_IMAGE is not set
CONFIG_PM_EXTERNAL_FLASH_BASE=0
CONFIG_PM_EXTERNAL_FLASH_PATH=""
CONFIG_PM_EXTERNAL_FLASH_SIZE_BITS=0
# CONFIG_PM_OVERRIDE_EXTERNAL_DRIVER_CHECK is not set
CONFIG_PM_SRAM_BASE=0x21000000
CONFIG_PM_SRAM_SIZE=0x10000
# end of Partition Manager

#
# nRF RPC (Remote Procedure Call) library
#
# end of nRF RPC (Remote Procedure Call) library

#
# Full Modem Firmware Update Management(FMFU)
#
# end of Full Modem Firmware Update Management(FMFU)

# CONFIG_CAF is not set

#
# Nordic IEEE 802.15.4
#
# end of Nordic IEEE 802.15.4

# CONFIG_DM_MODULE is not set

#
# nRF Security
#
# CONFIG_NORDIC_SECURITY_BACKEND is not set
# CONFIG_NRF_SECURITY is not set
# CONFIG_PSA_WANT_GENERATE_RANDOM is not set
# CONFIG_PSA_WANT_ALG_CTR_DRBG is not set
# CONFIG_PSA_WANT_ALG_HMAC_DRBG is not set
# CONFIG_PSA_WANT_KEY_TYPE_DERIVE is not set
# CONFIG_PSA_WANT_KEY_TYPE_RAW_DATA is not set
# CONFIG_PSA_WANT_KEY_TYPE_HMAC is not set
# CONFIG_PSA_WANT_KEY_TYPE_PASSWORD is not set
# CONFIG_PSA_WANT_KEY_TYPE_PASSWORD_HASH is not set
# CONFIG_PSA_WANT_KEY_TYPE_PEPPER is not set
# CONFIG_PSA_WANT_KEY_TYPE_AES is not set
# CONFIG_PSA_WANT_KEY_TYPE_ARIA is not set
# CONFIG_PSA_WANT_KEY_TYPE_DES is not set
# CONFIG_PSA_WANT_KEY_TYPE_CAMELLIA is not set
# CONFIG_PSA_WANT_KEY_TYPE_SM4 is not set
# CONFIG_PSA_WANT_KEY_TYPE_ARC4 is not set
# CONFIG_PSA_WANT_KEY_TYPE_ECC_KEY_PAIR is not set
# CONFIG_PSA_WANT_KEY_TYPE_ECC_PUBLIC_KEY is not set
# CONFIG_PSA_WANT_KEY_TYPE_RSA_KEY_PAIR is not set
# CONFIG_PSA_WANT_KEY_TYPE_RSA_PUBLIC_KEY is not set
# CONFIG_PSA_WANT_KEY_TYPE_DH_KEY_PAIR is not set
# CONFIG_PSA_WANT_KEY_TYPE_DH_PUBLIC_KEY is not set
# CONFIG_PSA_WANT_ALG_CCM is not set
# CONFIG_PSA_WANT_ALG_GCM is not set
# CONFIG_PSA_WANT_ALG_CHACHA20_POLY1305 is not set
# CONFIG_PSA_WANT_ALG_CBC_MAC is not set
# CONFIG_PSA_WANT_ALG_CMAC is not set
# CONFIG_PSA_WANT_ALG_HMAC is not set
# CONFIG_PSA_WANT_ALG_SHA_1 is not set
# CONFIG_PSA_WANT_ALG_SHA_224 is not set
# CONFIG_PSA_WANT_ALG_SHA_256 is not set
# CONFIG_PSA_WANT_ALG_SHA_384 is not set
# CONFIG_PSA_WANT_ALG_SHA_512 is not set
# CONFIG_PSA_WANT_ALG_SHA_512_224 is not set
# CONFIG_PSA_WANT_ALG_SHA_512_256 is not set
# CONFIG_PSA_WANT_ALG_SHA3_224 is not set
# CONFIG_PSA_WANT_ALG_SHA3_256 is not set
# CONFIG_PSA_WANT_ALG_SHA3_384 is not set
# CONFIG_PSA_WANT_ALG_SHA3_512 is not set
# CONFIG_PSA_WANT_ALG_SM3 is not set
# CONFIG_PSA_WANT_ALG_SHAKE256_512 is not set
# CONFIG_PSA_WANT_ALG_RIPEMD160 is not set
# CONFIG_PSA_WANT_ALG_MD2 is not set
# CONFIG_PSA_WANT_ALG_MD4 is not set
# CONFIG_PSA_WANT_ALG_MD5 is not set
# CONFIG_PSA_WANT_ALG_ECB_NO_PADDING is not set
# CONFIG_PSA_WANT_ALG_CBC_NO_PADDING is not set
# CONFIG_PSA_WANT_ALG_CBC_PKCS7 is not set
# CONFIG_PSA_WANT_ALG_CFB is not set
# CONFIG_PSA_WANT_ALG_CTR is not set
# CONFIG_PSA_WANT_ALG_OFB is not set
# CONFIG_PSA_WANT_ALG_XTS is not set
# CONFIG_PSA_WANT_ALG_CCM_STAR_NO_TAG is not set
# CONFIG_PSA_WANT_ALG_STREAM_CIPHER is not set
# CONFIG_PSA_WANT_ALG_ECDH is not set
# CONFIG_PSA_WANT_ALG_FFDH is not set
# CONFIG_PSA_WANT_ALG_HKDF_EXTRACT is not set
# CONFIG_PSA_WANT_ALG_HKDF_EXPAND is not set
# CONFIG_PSA_WANT_ALG_TLS12_PRF is not set
# CONFIG_PSA_WANT_ALG_TLS12_PSK_TO_MS is not set
# CONFIG_PSA_WANT_ALG_TLS12_ECJPAKE_TO_PMS is not set
# CONFIG_PSA_WANT_ALG_ECDSA is not set
# CONFIG_PSA_WANT_ALG_ECDSA_ANY is not set
# CONFIG_PSA_WANT_ALG_DETERMINISTIC_ECDSA is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_160 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_192 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_224 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_256 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_320 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_384 is not set
# CONFIG_PSA_WANT_ECC_BRAINPOOL_P_R1_512 is not set
# CONFIG_PSA_WANT_ECC_MONTGOMERY_255 is not set
# CONFIG_PSA_WANT_ECC_MONTGOMERY_448 is not set
# CONFIG_PSA_WANT_ECC_TWISTED_EDWARDS_255 is not set
# CONFIG_PSA_WANT_ECC_TWISTED_EDWARDS_448 is not set
# CONFIG_PSA_WANT_ECC_SECP_K1_192 is not set
# CONFIG_PSA_WANT_ECC_SECP_K1_224 is not set
# CONFIG_PSA_WANT_ECC_SECP_K1_256 is not set
# CONFIG_PSA_WANT_ECC_SECP_R1_192 is not set
# CONFIG_PSA_WANT_ECC_SECP_R1_224 is not set
# CONFIG_PSA_WANT_ECC_SECP_R1_256 is not set
# CONFIG_PSA_WANT_ECC_SECP_R1_384 is not set
# CONFIG_PSA_WANT_ECC_SECP_R1_521 is not set
# CONFIG_PSA_WANT_ECC_SECP_R2_160 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_163 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_233 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_239 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_283 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_409 is not set
# CONFIG_PSA_WANT_ECC_SECT_K1_571 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_163 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_233 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_283 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_409 is not set
# CONFIG_PSA_WANT_ECC_SECT_R1_571 is not set
# CONFIG_PSA_WANT_ECC_SECT_R2_163 is not set
# CONFIG_PSA_WANT_ECC_FRP_V1_256 is not set
# CONFIG_PSA_WANT_ALG_RSA_OAEP is not set
# CONFIG_PSA_WANT_ALG_RSA_PKCS1V15_CRYPT is not set
# CONFIG_PSA_WANT_ALG_RSA_PKCS1V15_SIGN is not set
# CONFIG_PSA_WANT_ALG_RSA_PKCS1V15_SIGN_RAW is not set
# CONFIG_PSA_WANT_ALG_RSA_PSS is not set
# CONFIG_PSA_WANT_ALG_RSA_PSS_ANY_SALT is not set
# CONFIG_PSA_WANT_ALG_JPAKE is not set
# CONFIG_PSA_WANT_ALG_SPAKE2P is not set
# CONFIG_PSA_WANT_ALG_SRP_6 is not set
# CONFIG_PSA_WANT_ALG_PURE_EDDSA is not set
# CONFIG_PSA_WANT_ALG_ED25519PH is not set
# CONFIG_PSA_WANT_ALG_ED448PH is not set
# end of nRF Security

# CONFIG_NET_CORE_MONITOR is not set

#
# Audio Module
#
# CONFIG_AUDIO_MODULE_TEST is not set
CONFIG_AUDIO_MODULE_NAME_SIZE=20
# end of Audio Module
# end of Subsystems

#
# TF-M
#

#
# TF-M SPM component configs
#
CONFIG_TFM_CONN_HANDLE_MAX_NUM=8
# end of TF-M SPM component configs
# end of TF-M

# CONFIG_WPA_SUPP is not set
CONFIG_POSIX_MAX_FDS=4

#
# Libraries
#

#
# Binary libraries
#
# CONFIG_BT_LL_ACS_NRF53 is not set

#
# Log levels
#
# end of Log levels
# end of Binary libraries

# CONFIG_ADP536X is not set
# CONFIG_AT_MONITOR is not set
# CONFIG_LTE_LINK_CONTROL is not set
CONFIG_NRF_ACL_FLASH_REGION_SIZE=0x800
CONFIG_FPROTECT_BLOCK_SIZE=0x800
# CONFIG_FPROTECT is not set
# CONFIG_AT_CMD_CUSTOM is not set
# CONFIG_DK_LIBRARY is not set
# CONFIG_AT_CMD_PARSER is not set
# CONFIG_MODEM_INFO is not set
CONFIG_RESET_ON_FATAL_ERROR=y
# CONFIG_SMS is not set
# CONFIG_SUPL_CLIENT_LIB is not set
# CONFIG_DATE_TIME is not set
# CONFIG_HW_ID_LIBRARY is not set
# CONFIG_WAVE_GEN_LIB is not set
CONFIG_HW_UNIQUE_KEY_PARTITION_SIZE=0x800
# CONFIG_MODEM_JWT is not set
# CONFIG_LOCATION is not set
# CONFIG_QOS is not set
# CONFIG_SFLOAT is not set
# CONFIG_CONTIN_ARRAY is not set
# CONFIG_PCM_MIX is not set
# CONFIG_TONE is not set
# CONFIG_PSCM is not set
# CONFIG_DATA_FIFO is not set
# CONFIG_FEM_AL_LIB is not set
# end of Libraries

#
# Device Drivers
#
# CONFIG_BT_DRIVER_QUIRK_NO_AUTO_DLE is not set
# CONFIG_ETH_RTT is not set
# CONFIG_SENSOR is not set
# CONFIG_NRF_SW_LPUART is not set
CONFIG_NRFX_GPIOTE_NUM_OF_EVT_HANDLERS=1
# CONFIG_IPC_UART is not set
# end of Device Drivers

#
# External libraries
#
# end of External libraries

#
# Test
#
CONFIG_ZTEST_MULTICORE_DEFAULT_SETTINGS=y
# CONFIG_UNITY is not set

#
# Mocks
#
# CONFIG_MOCK_NRF_MODEM_AT is not set
# end of Mocks
# end of Test
# end of Nordic nRF Connect

CONFIG_ZEPHYR_NRF_MODULE=y
# end of nrf (C:/ncs/v2.5.99-dev1/nrf)

#
# wfa-qt-control-app (C:/ncs/v2.5.99-dev1/modules/lib/wfa-qt-control-app)
#
# CONFIG_WFA_QT_CONTROL_APP is not set
CONFIG_WFA_QT_THREAD_STACK_SIZE=4096
CONFIG_ZEPHYR_WFA_QT_CONTROL_APP_MODULE=y
# end of wfa-qt-control-app (C:/ncs/v2.5.99-dev1/modules/lib/wfa-qt-control-app)

#
# mcuboot (C:/ncs/v2.5.99-dev1/bootloader/mcuboot)
#

#
# MCUboot
#
CONFIG_BOOT_SIGNATURE_KEY_FILE=""
CONFIG_DT_FLASH_WRITE_BLOCK_SIZE=4
# end of MCUboot

CONFIG_ZEPHYR_MCUBOOT_MODULE=y
# end of mcuboot (C:/ncs/v2.5.99-dev1/bootloader/mcuboot)

#
# mbedtls (C:/ncs/v2.5.99-dev1/modules/crypto/mbedtls)
#
CONFIG_ZEPHYR_MBEDTLS_MODULE=y

#
# PSA RNG support
#
# end of PSA RNG support

#
# PSA key type support
#
# end of PSA key type support

#
# PSA AEAD support
#
# end of PSA AEAD support

#
# PSA MAC support
#
# end of PSA MAC support

#
# PSA Hash support
#
# end of PSA Hash support

#
# PSA Cipher support
#
# end of PSA Cipher support

#
# PSA Key agreement support
#
# end of PSA Key agreement support

#
# PSA Key derivation support
#
# end of PSA Key derivation support

#
# PSA Asymmetric support
#

#
# Elliptic Curve type support
#
# end of Elliptic Curve type support
# end of PSA Asymmetric support
# end of mbedtls (C:/ncs/v2.5.99-dev1/modules/crypto/mbedtls)

#
# trusted-firmware-m (C:/ncs/v2.5.99-dev1/modules/tee/tf-m/trusted-firmware-m)
#
# CONFIG_BOOTLOADER_MCUBOOT is not set
CONFIG_ZEPHYR_TRUSTED_FIRMWARE_M_MODULE=y
# end of trusted-firmware-m (C:/ncs/v2.5.99-dev1/modules/tee/tf-m/trusted-firmware-m)

#
# cjson (C:/ncs/v2.5.99-dev1/modules/lib/cjson)
#
# CONFIG_CJSON_LIB is not set
CONFIG_ZEPHYR_CJSON_MODULE=y
# end of cjson (C:/ncs/v2.5.99-dev1/modules/lib/cjson)

#
# azure-sdk-for-c (C:/ncs/v2.5.99-dev1/modules/lib/azure-sdk-for-c)
#
# CONFIG_AZURE_SDK is not set
CONFIG_ZEPHYR_AZURE_SDK_FOR_C_MODULE=y
# end of azure-sdk-for-c (C:/ncs/v2.5.99-dev1/modules/lib/azure-sdk-for-c)

#
# cirrus-logic (C:/ncs/v2.5.99-dev1/modules/hal/cirrus-logic)
#
# CONFIG_HW_CODEC_CIRRUS_LOGIC is not set
CONFIG_ZEPHYR_CIRRUS_LOGIC_MODULE=y
# end of cirrus-logic (C:/ncs/v2.5.99-dev1/modules/hal/cirrus-logic)

#
# openthread (C:/ncs/v2.5.99-dev1/modules/lib/openthread)
#
# CONFIG_OPENTHREAD is not set
CONFIG_ZEPHYR_OPENTHREAD_MODULE=y
# end of openthread (C:/ncs/v2.5.99-dev1/modules/lib/openthread)

#
# memfault-firmware-sdk (C:/ncs/v2.5.99-dev1/modules/lib/memfault-firmware-sdk)
#
# CONFIG_MEMFAULT is not set
CONFIG_ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE=y
# end of memfault-firmware-sdk (C:/ncs/v2.5.99-dev1/modules/lib/memfault-firmware-sdk)

#
# canopennode (C:/ncs/v2.5.99-dev1/modules/lib/canopennode)
#
CONFIG_ZEPHYR_CANOPENNODE_MODULE=y
# end of canopennode (C:/ncs/v2.5.99-dev1/modules/lib/canopennode)

#
# chre (C:/ncs/v2.5.99-dev1/modules/lib/chre)
#
CONFIG_ZEPHYR_CHRE_MODULE=y
# CONFIG_CHRE is not set
# end of chre (C:/ncs/v2.5.99-dev1/modules/lib/chre)

#
# cmsis (C:/ncs/v2.5.99-dev1/modules/hal/cmsis)
#
CONFIG_HAS_CMSIS_CORE=y
CONFIG_HAS_CMSIS_CORE_M=y
# CONFIG_CMSIS_DSP is not set
# CONFIG_CMSIS_NN is not set
CONFIG_ZEPHYR_CMSIS_MODULE=y
# end of cmsis (C:/ncs/v2.5.99-dev1/modules/hal/cmsis)

#
# fatfs (C:/ncs/v2.5.99-dev1/modules/fs/fatfs)
#
CONFIG_ZEPHYR_FATFS_MODULE=y
# end of fatfs (C:/ncs/v2.5.99-dev1/modules/fs/fatfs)

#
# hal_nordic (C:/ncs/v2.5.99-dev1/modules/hal/nordic)
#
CONFIG_ZEPHYR_HAL_NORDIC_MODULE=y
CONFIG_HAS_NORDIC_DRIVERS=y

#
# Nordic drivers
#
# CONFIG_NRF_802154_SOURCE_HAL_NORDIC is not set
# CONFIG_NRF_802154_RADIO_DRIVER is not set
# CONFIG_NRF_802154_SER_RADIO is not set
# end of Nordic drivers

CONFIG_HAS_NRFX=y

#
# nrfx drivers
#
CONFIG_NRFX_CLOCK=y
CONFIG_NRFX_CLOCK_LFXO_TWO_STAGE_ENABLED=y
CONFIG_NRFX_DPPI=y
# CONFIG_NRFX_EGU0 is not set
CONFIG_NRFX_GPIOTE=y
CONFIG_NRFX_IPC=y
# CONFIG_NRFX_NVMC is not set
# CONFIG_NRFX_POWER is not set
# CONFIG_NRFX_RNG is not set
# CONFIG_NRFX_RTC0 is not set
# CONFIG_NRFX_RTC1 is not set
# CONFIG_NRFX_SPIM0 is not set
# CONFIG_NRFX_SYSTICK is not set
# CONFIG_NRFX_TEMP is not set
# CONFIG_NRFX_TIMER0 is not set
# CONFIG_NRFX_TIMER1 is not set
# CONFIG_NRFX_TIMER2 is not set
# CONFIG_NRFX_TWIM0 is not set
# CONFIG_NRFX_UARTE0 is not set
# CONFIG_NRFX_WDT0 is not set

#
# Peripheral Resource Sharing module
#
# CONFIG_NRFX_PRS_BOX_0 is not set
# CONFIG_NRFX_PRS_BOX_1 is not set
# CONFIG_NRFX_PRS_BOX_2 is not set
# CONFIG_NRFX_PRS_BOX_3 is not set
# CONFIG_NRFX_PRS_BOX_4 is not set
# end of Peripheral Resource Sharing module
# end of nrfx drivers
# end of hal_nordic (C:/ncs/v2.5.99-dev1/modules/hal/nordic)

#
# liblc3 (C:/ncs/v2.5.99-dev1/modules/lib/liblc3)
#
CONFIG_ZEPHYR_LIBLC3_MODULE=y
# end of liblc3 (C:/ncs/v2.5.99-dev1/modules/lib/liblc3)

#
# littlefs (C:/ncs/v2.5.99-dev1/modules/fs/littlefs)
#
CONFIG_ZEPHYR_LITTLEFS_MODULE=y
# end of littlefs (C:/ncs/v2.5.99-dev1/modules/fs/littlefs)

#
# loramac-node (C:/ncs/v2.5.99-dev1/modules/lib/loramac-node)
#
CONFIG_ZEPHYR_LORAMAC_NODE_MODULE=y
# CONFIG_HAS_SEMTECH_RADIO_DRIVERS is not set
# end of loramac-node (C:/ncs/v2.5.99-dev1/modules/lib/loramac-node)

#
# lvgl (C:/ncs/v2.5.99-dev1/modules/lib/gui/lvgl)
#

#
# LVGL configuration
#
# CONFIG_LV_CONF_SKIP is not set
# CONFIG_LV_CONF_MINIMAL is not set

#
# Color settings
#
# CONFIG_LV_COLOR_DEPTH_32 is not set
CONFIG_LV_COLOR_DEPTH_16=y
# CONFIG_LV_COLOR_DEPTH_8 is not set
# CONFIG_LV_COLOR_DEPTH_1 is not set
CONFIG_LV_COLOR_DEPTH=16
# CONFIG_LV_COLOR_SCREEN_TRANSP is not set
CONFIG_LV_COLOR_MIX_ROUND_OFS=128
CONFIG_LV_COLOR_CHROMA_KEY_HEX=0x00FF00
# end of Color settings

#
# Memory settings
#
# CONFIG_LV_MEM_CUSTOM is not set
CONFIG_LV_MEM_SIZE_KILOBYTES=32
CONFIG_LV_MEM_ADDR=0x0
CONFIG_LV_MEM_BUF_MAX_NUM=16
# CONFIG_LV_MEMCPY_MEMSET_STD is not set
# end of Memory settings

#
# HAL Settings
#
CONFIG_LV_DISP_DEF_REFR_PERIOD=30
CONFIG_LV_INDEV_DEF_READ_PERIOD=30
# CONFIG_LV_TICK_CUSTOM is not set
# end of HAL Settings

#
# Feature configuration
#

#
# Drawing
#
CONFIG_LV_DRAW_COMPLEX=y
CONFIG_LV_SHADOW_CACHE_SIZE=0
CONFIG_LV_CIRCLE_CACHE_SIZE=4
CONFIG_LV_LAYER_SIMPLE_BUF_SIZE=24576
CONFIG_LV_IMG_CACHE_DEF_SIZE=0
CONFIG_LV_GRADIENT_MAX_STOPS=2
CONFIG_LV_GRAD_CACHE_DEF_SIZE=0
# CONFIG_LV_DITHER_GRADIENT is not set
CONFIG_LV_DISP_ROT_MAX_BUF=10240
# end of Drawing

#
# GPU
#
# CONFIG_LV_USE_GPU_ARM2D is not set
# CONFIG_LV_USE_GPU_STM32_DMA2D is not set
# CONFIG_LV_USE_GPU_SWM341_DMA2D is not set
# CONFIG_LV_USE_GPU_NXP_PXP is not set
# CONFIG_LV_USE_GPU_NXP_VG_LITE is not set
# CONFIG_LV_USE_GPU_SDL is not set
# end of GPU

#
# Logging
#
# CONFIG_LV_USE_LOG is not set
# end of Logging

#
# Asserts
#
CONFIG_LV_USE_ASSERT_NULL=y
CONFIG_LV_USE_ASSERT_MALLOC=y
# CONFIG_LV_USE_ASSERT_STYLE is not set
# CONFIG_LV_USE_ASSERT_MEM_INTEGRITY is not set
# CONFIG_LV_USE_ASSERT_OBJ is not set
CONFIG_LV_ASSERT_HANDLER_INCLUDE="assert.h"
# end of Asserts

#
# Others
#
# CONFIG_LV_USE_PERF_MONITOR is not set
# CONFIG_LV_USE_MEM_MONITOR is not set
# CONFIG_LV_USE_REFR_DEBUG is not set
# CONFIG_LV_SPRINTF_CUSTOM is not set
# CONFIG_LV_SPRINTF_USE_FLOAT is not set
CONFIG_LV_USE_USER_DATA=y
# CONFIG_LV_ENABLE_GC is not set
# end of Others

#
# Compiler settings
#
# CONFIG_LV_BIG_ENDIAN_SYSTEM is not set
CONFIG_LV_ATTRIBUTE_MEM_ALIGN_SIZE=1
# CONFIG_LV_ATTRIBUTE_FAST_MEM_USE_IRAM is not set
# CONFIG_LV_USE_LARGE_COORD is not set
# end of Compiler settings
# end of Feature configuration

#
# Font usage
#

#
# Enable built-in fonts
#
# CONFIG_LV_FONT_MONTSERRAT_8 is not set
# CONFIG_LV_FONT_MONTSERRAT_10 is not set
# CONFIG_LV_FONT_MONTSERRAT_12 is not set
CONFIG_LV_FONT_MONTSERRAT_14=y
# CONFIG_LV_FONT_MONTSERRAT_16 is not set
# CONFIG_LV_FONT_MONTSERRAT_18 is not set
# CONFIG_LV_FONT_MONTSERRAT_20 is not set
# CONFIG_LV_FONT_MONTSERRAT_22 is not set
# CONFIG_LV_FONT_MONTSERRAT_24 is not set
# CONFIG_LV_FONT_MONTSERRAT_26 is not set
# CONFIG_LV_FONT_MONTSERRAT_28 is not set
# CONFIG_LV_FONT_MONTSERRAT_30 is not set
# CONFIG_LV_FONT_MONTSERRAT_32 is not set
# CONFIG_LV_FONT_MONTSERRAT_34 is not set
# CONFIG_LV_FONT_MONTSERRAT_36 is not set
# CONFIG_LV_FONT_MONTSERRAT_38 is not set
# CONFIG_LV_FONT_MONTSERRAT_40 is not set
# CONFIG_LV_FONT_MONTSERRAT_42 is not set
# CONFIG_LV_FONT_MONTSERRAT_44 is not set
# CONFIG_LV_FONT_MONTSERRAT_46 is not set
# CONFIG_LV_FONT_MONTSERRAT_48 is not set
# CONFIG_LV_FONT_MONTSERRAT_12_SUBPX is not set
# CONFIG_LV_FONT_MONTSERRAT_28_COMPRESSED is not set
# CONFIG_LV_FONT_DEJAVU_16_PERSIAN_HEBREW is not set
# CONFIG_LV_FONT_SIMSUN_16_CJK is not set
# CONFIG_LV_FONT_UNSCII_8 is not set
# CONFIG_LV_FONT_UNSCII_16 is not set
# CONFIG_LV_FONT_CUSTOM is not set
# end of Enable built-in fonts

# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_8 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_12 is not set
CONFIG_LV_FONT_DEFAULT_MONTSERRAT_14=y
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_16 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_18 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_20 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_22 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_24 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_26 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_28 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_30 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_32 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_34 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_36 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_38 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_40 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_42 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_44 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_46 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_48 is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_12_SUBPX is not set
# CONFIG_LV_FONT_DEFAULT_MONTSERRAT_28_COMPRESSED is not set
# CONFIG_LV_FONT_DEFAULT_DEJAVU_16_PERSIAN_HEBREW is not set
# CONFIG_LV_FONT_DEFAULT_SIMSUN_16_CJK is not set
# CONFIG_LV_FONT_DEFAULT_UNSCII_8 is not set
# CONFIG_LV_FONT_DEFAULT_UNSCII_16 is not set
# CONFIG_LV_FONT_FMT_TXT_LARGE is not set
# CONFIG_LV_USE_FONT_COMPRESSED is not set
# CONFIG_LV_USE_FONT_SUBPX is not set
CONFIG_LV_USE_FONT_PLACEHOLDER=y
# end of Font usage

#
# Text Settings
#
CONFIG_LV_TXT_ENC_UTF8=y
# CONFIG_LV_TXT_ENC_ASCII is not set
CONFIG_LV_TXT_BREAK_CHARS=" ,.;:-_"
CONFIG_LV_TXT_LINE_BREAK_LONG_LEN=0
CONFIG_LV_TXT_COLOR_CMD="#"
# CONFIG_LV_USE_BIDI is not set
# CONFIG_LV_USE_ARABIC_PERSIAN_CHARS is not set
# end of Text Settings

#
# Widget usage
#
CONFIG_LV_USE_ARC=y
CONFIG_LV_USE_BAR=y
CONFIG_LV_USE_BTN=y
CONFIG_LV_USE_BTNMATRIX=y
CONFIG_LV_USE_CANVAS=y
CONFIG_LV_USE_CHECKBOX=y
CONFIG_LV_USE_DROPDOWN=y
CONFIG_LV_USE_IMG=y
CONFIG_LV_USE_LABEL=y
CONFIG_LV_LABEL_TEXT_SELECTION=y
CONFIG_LV_LABEL_LONG_TXT_HINT=y
CONFIG_LV_USE_LINE=y
CONFIG_LV_USE_ROLLER=y
CONFIG_LV_ROLLER_INF_PAGES=7
CONFIG_LV_USE_SLIDER=y
CONFIG_LV_USE_SWITCH=y
CONFIG_LV_USE_TEXTAREA=y
CONFIG_LV_TEXTAREA_DEF_PWD_SHOW_TIME=1500
CONFIG_LV_USE_TABLE=y
# end of Widget usage

#
# Extra Widgets
#
CONFIG_LV_USE_ANIMIMG=y
CONFIG_LV_USE_CALENDAR=y
# CONFIG_LV_CALENDAR_WEEK_STARTS_MONDAY is not set
CONFIG_LV_USE_CALENDAR_HEADER_ARROW=y
CONFIG_LV_USE_CALENDAR_HEADER_DROPDOWN=y
CONFIG_LV_USE_CHART=y
CONFIG_LV_USE_COLORWHEEL=y
CONFIG_LV_USE_IMGBTN=y
CONFIG_LV_USE_KEYBOARD=y
CONFIG_LV_USE_LED=y
CONFIG_LV_USE_LIST=y
CONFIG_LV_USE_MENU=y
CONFIG_LV_USE_METER=y
CONFIG_LV_USE_MSGBOX=y
CONFIG_LV_USE_SPAN=y
CONFIG_LV_SPAN_SNIPPET_STACK_SIZE=64
CONFIG_LV_USE_SPINBOX=y
CONFIG_LV_USE_SPINNER=y
CONFIG_LV_USE_TABVIEW=y
CONFIG_LV_USE_TILEVIEW=y
CONFIG_LV_USE_WIN=y
# end of Extra Widgets

#
# Themes
#
CONFIG_LV_USE_THEME_DEFAULT=y
# CONFIG_LV_THEME_DEFAULT_DARK is not set
CONFIG_LV_THEME_DEFAULT_GROW=y
CONFIG_LV_THEME_DEFAULT_TRANSITION_TIME=80
CONFIG_LV_USE_THEME_BASIC=y
# CONFIG_LV_USE_THEME_MONO is not set
# end of Themes

#
# Layouts
#
CONFIG_LV_USE_FLEX=y
CONFIG_LV_USE_GRID=y
# end of Layouts

#
# 3rd Party Libraries
#
# CONFIG_LV_USE_FS_STDIO is not set
# CONFIG_LV_USE_FS_POSIX is not set
# CONFIG_LV_USE_FS_WIN32 is not set
# CONFIG_LV_USE_FS_FATFS is not set
# CONFIG_LV_USE_PNG is not set
# CONFIG_LV_USE_BMP is not set
# CONFIG_LV_USE_SJPG is not set
# CONFIG_LV_USE_GIF is not set
# CONFIG_LV_USE_QRCODE is not set
# CONFIG_LV_USE_FREETYPE is not set
# CONFIG_LV_USE_RLOTTIE is not set
# CONFIG_LV_USE_FFMPEG is not set
# end of 3rd Party Libraries

#
# Others
#
CONFIG_LV_USE_SNAPSHOT=y
# CONFIG_LV_USE_MONKEY is not set
# CONFIG_LV_USE_GRIDNAV is not set
# CONFIG_LV_USE_FRAGMENT is not set
# CONFIG_LV_USE_IMGFONT is not set
# CONFIG_LV_USE_MSG is not set
# CONFIG_LV_USE_IME_PINYIN is not set
# end of Others

#
# Examples
#
CONFIG_LV_BUILD_EXAMPLES=y
# end of Examples

#
# Demos
#
# CONFIG_LV_USE_DEMO_WIDGETS is not set
# CONFIG_LV_USE_DEMO_KEYPAD_AND_ENCODER is not set
# CONFIG_LV_USE_DEMO_BENCHMARK is not set
# CONFIG_LV_USE_DEMO_STRESS is not set
# CONFIG_LV_USE_DEMO_MUSIC is not set
# end of Demos
# end of LVGL configuration

CONFIG_ZEPHYR_LVGL_MODULE=y
# end of lvgl (C:/ncs/v2.5.99-dev1/modules/lib/gui/lvgl)

#
# lz4 (C:/ncs/v2.5.99-dev1/modules/lib/lz4)
#
CONFIG_ZEPHYR_LZ4_MODULE=y
# CONFIG_LZ4 is not set
# end of lz4 (C:/ncs/v2.5.99-dev1/modules/lib/lz4)

#
# nanopb (C:/ncs/v2.5.99-dev1/modules/lib/nanopb)
#
CONFIG_ZEPHYR_NANOPB_MODULE=y
# CONFIG_NANOPB is not set
# end of nanopb (C:/ncs/v2.5.99-dev1/modules/lib/nanopb)

#
# picolibc (C:/ncs/v2.5.99-dev1/modules/lib/picolibc)
#
# CONFIG_PICOLIBC_MODULE is not set
CONFIG_ZEPHYR_PICOLIBC_MODULE=y
# end of picolibc (C:/ncs/v2.5.99-dev1/modules/lib/picolibc)

#
# segger (C:/ncs/v2.5.99-dev1/modules/debug/segger)
#
CONFIG_ZEPHYR_SEGGER_MODULE=y
CONFIG_HAS_SEGGER_RTT=y
# CONFIG_USE_SEGGER_RTT is not set
# end of segger (C:/ncs/v2.5.99-dev1/modules/debug/segger)

#
# TraceRecorder (C:/ncs/v2.5.99-dev1/modules/debug/TraceRecorder)
#
CONFIG_ZEPHYR_TRACERECORDER_MODULE=y
# end of TraceRecorder (C:/ncs/v2.5.99-dev1/modules/debug/TraceRecorder)

#
# uoscore-uedhoc (C:/ncs/v2.5.99-dev1/modules/lib/uoscore-uedhoc)
#
CONFIG_ZEPHYR_UOSCORE_UEDHOC_MODULE=y
# end of uoscore-uedhoc (C:/ncs/v2.5.99-dev1/modules/lib/uoscore-uedhoc)

#
# zcbor (C:/ncs/v2.5.99-dev1/modules/lib/zcbor)
#
CONFIG_ZEPHYR_ZCBOR_MODULE=y
# CONFIG_ZCBOR is not set
# end of zcbor (C:/ncs/v2.5.99-dev1/modules/lib/zcbor)

#
# zscilib (C:/ncs/v2.5.99-dev1/modules/lib/zscilib)
#
# CONFIG_ZSL is not set
CONFIG_ZEPHYR_ZSCILIB_MODULE=y
# end of zscilib (C:/ncs/v2.5.99-dev1/modules/lib/zscilib)

#
# nrfxlib (C:/ncs/v2.5.99-dev1/nrfxlib)
#

#
# Nordic nrfxlib
#

#
# nrf_modem (Modem library)
#
# end of nrf_modem (Modem library)

CONFIG_NRF_MODEM_SHMEM_CTRL_SIZE=0x4e8
# CONFIG_NFC_T2T_NRFXLIB is not set
# CONFIG_NFC_T4T_NRFXLIB is not set
# CONFIG_MPSL is not set

#
# Crypto libraries for nRF5x SOCs.
#
# CONFIG_NRF_OBERON is not set
# CONFIG_NRF_CC310_BL is not set
# end of Crypto libraries for nRF5x SOCs.

# CONFIG_NRF_RPC is not set
CONFIG_NRF_802154_SOURCE_NRFXLIB=y
# CONFIG_GZLL is not set
# CONFIG_NRF_DM is not set
# CONFIG_LC3_PLC_DISABLED is not set
CONFIG_LC3_ENC_CHAN_MAX=1
CONFIG_LC3_DEC_CHAN_MAX=1

#
# Encoder sample rates
#
CONFIG_LC3_ENC_SAMPLE_RATE_8KHZ_SUPPORT=y
CONFIG_LC3_ENC_SAMPLE_RATE_16KHZ_SUPPORT=y
CONFIG_LC3_ENC_SAMPLE_RATE_24KHZ_SUPPORT=y
CONFIG_LC3_ENC_SAMPLE_RATE_32KHZ_SUPPORT=y
CONFIG_LC3_ENC_SAMPLE_RATE_441KHZ_SUPPORT=y
CONFIG_LC3_ENC_SAMPLE_RATE_48KHZ_SUPPORT=y
# end of Encoder sample rates

#
# Decoder sample rates
#
CONFIG_LC3_DEC_SAMPLE_RATE_8KHZ_SUPPORT=y
CONFIG_LC3_DEC_SAMPLE_RATE_16KHZ_SUPPORT=y
CONFIG_LC3_DEC_SAMPLE_RATE_24KHZ_SUPPORT=y
CONFIG_LC3_DEC_SAMPLE_RATE_32KHZ_SUPPORT=y
CONFIG_LC3_DEC_SAMPLE_RATE_441KHZ_SUPPORT=y
CONFIG_LC3_DEC_SAMPLE_RATE_48KHZ_SUPPORT=y
# end of Decoder sample rates

# CONFIG_NRF_FUEL_GAUGE is not set
# end of Nordic nrfxlib

CONFIG_ZEPHYR_NRFXLIB_MODULE=y
# end of nrfxlib (C:/ncs/v2.5.99-dev1/nrfxlib)

#
# connectedhomeip (C:/ncs/v2.5.99-dev1/modules/lib/matter)
#
# CONFIG_CHIP is not set
CONFIG_ZEPHYR_CONNECTEDHOMEIP_MODULE=y
# end of connectedhomeip (C:/ncs/v2.5.99-dev1/modules/lib/matter)

#
# Optional modules. Make sure they're installed, via the project manifest.
#
CONFIG_LIBMETAL=y
CONFIG_LIBMETAL_SRC_PATH="libmetal"
# CONFIG_LVGL is not set
# CONFIG_HAS_MEC_HAL is not set
# CONFIG_HAS_MPFS_HAL is not set
CONFIG_OPENAMP=y
CONFIG_OPENAMP_SRC_PATH="open-amp"
CONFIG_OPENAMP_MASTER=y
CONFIG_OPENAMP_SLAVE=y
# CONFIG_SOF is not set
# CONFIG_MIPI_SYST_LIB is not set
# CONFIG_HAS_TELINK_DRIVERS is not set
# CONFIG_TINYCRYPT_CTR_PRNG is not set
# CONFIG_TINYCRYPT_SHA256 is not set
# CONFIG_TINYCRYPT_ECC_DH is not set
# CONFIG_TINYCRYPT_ECC_DSA is not set
CONFIG_TINYCRYPT_AES=y
# CONFIG_TINYCRYPT_AES_CBC is not set
# CONFIG_TINYCRYPT_AES_CTR is not set
# CONFIG_TINYCRYPT_AES_CCM is not set
# CONFIG_TINYCRYPT_AES_CMAC is not set
# CONFIG_MCUBOOT_BOOTUTIL_LIB is not set

#
# Unavailable modules, please install those via the project manifest.
#

#
# hal_gigadevice module not available.
#

#
# Trusted-firmware-a module not available.
#

#
# THRIFT module not available.
#
# CONFIG_ACPI is not set
# end of Modules

CONFIG_BOARD_REVISION="$BOARD_REVISION"
# CONFIG_NET_DRIVERS is not set
CONFIG_BOARD_NRF5340DK_NRF5340_CPUNET=y

#
# Board Options
#
CONFIG_DOMAIN_CPUAPP_BOARD="nrf5340dk_nrf5340_cpuapp"
# end of Board Options

# CONFIG_SOC_SERIES_APOLLO4X is not set
# CONFIG_SOC_SERIES_BEETLE is not set
# CONFIG_SOC_SERIES_ARM_DESIGNSTART is not set
# CONFIG_SOC_SERIES_FVP_AEMV8R_AARCH32 is not set
# CONFIG_SOC_SERIES_MPS2 is not set
# CONFIG_SOC_SERIES_MPS3 is not set
# CONFIG_SOC_SERIES_MUSCA_B1 is not set
# CONFIG_SOC_SERIES_MUSCA_S1 is not set
# CONFIG_SOC_SERIES_AST10X0 is not set
# CONFIG_SOC_SERIES_SAMC20 is not set
# CONFIG_SOC_SERIES_SAMC21 is not set
# CONFIG_SOC_SERIES_SAMD20 is not set
# CONFIG_SOC_SERIES_SAMD21 is not set
# CONFIG_SOC_SERIES_SAMD51 is not set
# CONFIG_SOC_SERIES_SAME51 is not set
# CONFIG_SOC_SERIES_SAME53 is not set
# CONFIG_SOC_SERIES_SAME54 is not set
# CONFIG_SOC_SERIES_SAML21 is not set
# CONFIG_SOC_SERIES_SAMR21 is not set
# CONFIG_SOC_SERIES_SAMR34 is not set
# CONFIG_SOC_SERIES_SAMR35 is not set
# CONFIG_SOC_SERIES_SAM3X is not set
# CONFIG_SOC_SERIES_SAM4E is not set
# CONFIG_SOC_SERIES_SAM4L is not set
# CONFIG_SOC_SERIES_SAM4S is not set
# CONFIG_SOC_SERIES_SAME70 is not set
# CONFIG_SOC_SERIES_SAMV71 is not set
# CONFIG_SOC_SERIES_VALKYRIE is not set
# CONFIG_SOC_SERIES_VIPER is not set
# CONFIG_SOC_SERIES_PSOC62 is not set
# CONFIG_SOC_SERIES_PSOC63 is not set
# CONFIG_SOC_SERIES_GD32A50X is not set
# CONFIG_SOC_SERIES_GD32E10X is not set
# CONFIG_SOC_SERIES_GD32E50X is not set
# CONFIG_SOC_SERIES_GD32F3X0 is not set
# CONFIG_SOC_SERIES_GD32F403 is not set
# CONFIG_SOC_SERIES_GD32F4XX is not set
# CONFIG_SOC_SERIES_GD32L23X is not set
# CONFIG_SOC_SERIES_PSOC_60 is not set
# CONFIG_SOC_SERIES_PSOC_61 is not set
# CONFIG_SOC_SERIES_PSOC_62 is not set
# CONFIG_SOC_SERIES_PSOC_63 is not set
# CONFIG_SOC_SERIES_PSOC_64 is not set
# CONFIG_SOC_SERIES_XMC_4XXX is not set
# CONFIG_SOC_SERIES_CYCLONE5 is not set
# CONFIG_SOC_SERIES_MEC1501X is not set
# CONFIG_SOC_SERIES_MEC172X is not set
# CONFIG_SOC_SERIES_NRF51X is not set
# CONFIG_SOC_SERIES_NRF52X is not set
CONFIG_SOC_SERIES_NRF53X=y
# CONFIG_SOC_SERIES_NRF91X is not set
# CONFIG_SOC_SERIES_NPCX4 is not set
# CONFIG_SOC_SERIES_NPCX7 is not set
# CONFIG_SOC_SERIES_NPCX9 is not set
# CONFIG_SOC_SERIES_M46X is not set
# CONFIG_SOC_SERIES_M48X is not set
# CONFIG_SOC_SERIES_IMX_6X_M4 is not set
# CONFIG_SOC_SERIES_IMX7_M4 is not set
# CONFIG_SOC_SERIES_IMX8ML_M7 is not set
# CONFIG_SOC_SERIES_IMX8MM_M4 is not set
# CONFIG_SOC_SERIES_IMX8MQ_M4 is not set
# CONFIG_SOC_SERIES_IMX_RT5XX is not set
# CONFIG_SOC_SERIES_IMX_RT6XX is not set
# CONFIG_SOC_SERIES_IMX_RT is not set
# CONFIG_SOC_SERIES_KINETIS_K2X is not set
# CONFIG_SOC_SERIES_KINETIS_K6X is not set
# CONFIG_SOC_SERIES_KINETIS_K8X is not set
# CONFIG_SOC_SERIES_KINETIS_KE1XF is not set
# CONFIG_SOC_SERIES_KINETIS_KL2X is not set
# CONFIG_SOC_SERIES_KINETIS_KV5X is not set
# CONFIG_SOC_SERIES_KINETIS_KWX is not set
# CONFIG_SOC_SERIES_LPC11U6X is not set
# CONFIG_SOC_SERIES_LPC51U68 is not set
# CONFIG_SOC_SERIES_LPC54XXX is not set
# CONFIG_SOC_SERIES_LPC55XXX is not set
# CONFIG_SOC_SERIES_S32K3_M7 is not set
# CONFIG_SOC_SERIES_S32ZE_R52 is not set
# CONFIG_SOC_EOS_S3 is not set
# CONFIG_SOC_SERIES_RCAR_GEN3 is not set
# CONFIG_SOC_SERIES_DA1469X is not set
# CONFIG_SOC_SERIES_RP2XXX is not set
# CONFIG_SOC_SERIES_EFM32GG11B is not set
# CONFIG_SOC_SERIES_EFM32GG12B is not set
# CONFIG_SOC_SERIES_EFM32HG is not set
# CONFIG_SOC_SERIES_EFM32JG12B is not set
# CONFIG_SOC_SERIES_EFM32PG12B is not set
# CONFIG_SOC_SERIES_EFM32PG1B is not set
# CONFIG_SOC_SERIES_EFM32WG is not set
# CONFIG_SOC_SERIES_EFR32BG13P is not set
# CONFIG_SOC_SERIES_EFR32BG22 is not set
# CONFIG_SOC_SERIES_EFR32BG27 is not set
# CONFIG_SOC_SERIES_EFR32FG13P is not set
# CONFIG_SOC_SERIES_EFR32FG1P is not set
# CONFIG_SOC_SERIES_EFR32MG12P is not set
# CONFIG_SOC_SERIES_EFR32MG21 is not set
# CONFIG_SOC_SERIES_EFR32MG24 is not set
# CONFIG_SOC_SERIES_STM32C0X is not set
# CONFIG_SOC_SERIES_STM32F0X is not set
# CONFIG_SOC_SERIES_STM32F1X is not set
# CONFIG_SOC_SERIES_STM32F2X is not set
# CONFIG_SOC_SERIES_STM32F3X is not set
# CONFIG_SOC_SERIES_STM32F4X is not set
# CONFIG_SOC_SERIES_STM32F7X is not set
# CONFIG_SOC_SERIES_STM32G0X is not set
# CONFIG_SOC_SERIES_STM32G4X is not set
# CONFIG_SOC_SERIES_STM32H5X is not set
# CONFIG_SOC_SERIES_STM32H7X is not set
# CONFIG_SOC_SERIES_STM32L0X is not set
# CONFIG_SOC_SERIES_STM32L1X is not set
# CONFIG_SOC_SERIES_STM32L4X is not set
# CONFIG_SOC_SERIES_STM32L5X is not set
# CONFIG_SOC_SERIES_STM32MP1X is not set
# CONFIG_SOC_SERIES_STM32U5X is not set
# CONFIG_SOC_SERIES_STM32WBX is not set
# CONFIG_SOC_SERIES_STM32WBAX is not set
# CONFIG_SOC_SERIES_STM32WLX is not set
# CONFIG_SOC_SERIES_AM62X_M4 is not set
# CONFIG_SOC_TI_LM3S6965 is not set
# CONFIG_SOC_SERIES_CC13X2_CC26X2 is not set
# CONFIG_SOC_SERIES_CC13X2X7_CC26X2X7 is not set
# CONFIG_SOC_SERIES_CC32XX is not set
# CONFIG_SOC_SERIES_MSP432P4XX is not set
# CONFIG_SOC_SERIES_XILINX_XC7ZXXX is not set
# CONFIG_SOC_SERIES_XILINX_XC7ZXXXS is not set
# CONFIG_SOC_XILINX_ZYNQMP_RPU is not set

#
# Hardware Configuration
#
CONFIG_CPU_HAS_ARM_MPU=y
CONFIG_HAS_SWO=y
CONFIG_SOC_FAMILY="nordic_nrf"
CONFIG_GPIO_INIT_PRIORITY=40
CONFIG_SOC_FAMILY_NRF=y
CONFIG_HAS_HW_NRF_ACL=y
CONFIG_HAS_HW_NRF_CCM=y
CONFIG_HAS_HW_NRF_CCM_LFLEN_8BIT=y
CONFIG_HAS_HW_NRF_CCM_HEADERMASK=y
CONFIG_HAS_HW_NRF_CLOCK=y
CONFIG_HAS_HW_NRF_DPPIC=y
CONFIG_HAS_HW_NRF_ECB=y
CONFIG_HAS_HW_NRF_EGU0=y
CONFIG_HAS_HW_NRF_GPIO0=y
CONFIG_HAS_HW_NRF_GPIO1=y
CONFIG_HAS_HW_NRF_GPIOTE=y
CONFIG_HAS_HW_NRF_IPC=y
CONFIG_HAS_HW_NRF_NVMC_PE=y
CONFIG_HAS_HW_NRF_POWER=y
CONFIG_HAS_HW_NRF_RADIO_BLE_2M=y
CONFIG_HAS_HW_NRF_RADIO_BLE_CODED=y
CONFIG_HAS_HW_NRF_RADIO_DFE=y
CONFIG_HAS_HW_NRF_RADIO_IEEE802154=y
CONFIG_HAS_HW_NRF_RNG=y
CONFIG_HAS_HW_NRF_SWI0=y
CONFIG_HAS_HW_NRF_SWI1=y
CONFIG_HAS_HW_NRF_SWI2=y
CONFIG_HAS_HW_NRF_SWI3=y
CONFIG_HAS_HW_NRF_TEMP=y
CONFIG_HAS_HW_NRF_UARTE0=y
CONFIG_HAS_HW_NRF_VMC=y
CONFIG_HAS_HW_NRF_WDT0=y
CONFIG_SOC_NRF5340_CPUNET=y
# CONFIG_SOC_NRF5340_CPUAPP_QKAA is not set
CONFIG_SOC_NRF5340_CPUNET_QKAA=y
CONFIG_SOC_NRF53_ANOMALY_160_WORKAROUND_NEEDED=y
CONFIG_SOC_NRF53_ANOMALY_160_WORKAROUND=y
CONFIG_SOC_NRF53_RTC_PRETICK=y
CONFIG_SOC_NRF53_RTC_PRETICK_IPC_CH_FROM_NET=10
CONFIG_SOC_NRF53_RTC_PRETICK_IPC_CH_TO_NET=11
CONFIG_NRF_ENABLE_CACHE=y
# CONFIG_NRF53_SYNC_RTC is not set
CONFIG_NRF_RTC_TIMER_USER_CHAN_COUNT=0
CONFIG_NRF_SOC_SECURE_SUPPORTED=y
CONFIG_NRF_APPROTECT_USE_UICR=y
# CONFIG_NRF_APPROTECT_LOCK is not set
# CONFIG_NRF_APPROTECT_USER_HANDLING is not set
# CONFIG_NRF_TRACE_PORT is not set
# CONFIG_BUILD_OUTPUT_INFO_HEADER is not set
# end of Hardware Configuration

CONFIG_SOC_COMPATIBLE_NRF=y
CONFIG_SOC_COMPATIBLE_NRF53X=y
CONFIG_SOC_COMPATIBLE_NRF5340_CPUNET=y

#
# ARM Options
#
CONFIG_ARCH="arm"
CONFIG_CPU_CORTEX=y
# CONFIG_CODE_DATA_RELOCATION_SRAM is not set
CONFIG_ARM_ON_ENTER_CPU_IDLE_HOOK=y
CONFIG_ARM_ON_ENTER_CPU_IDLE_PREPARE_HOOK=y
CONFIG_ARM_ON_EXIT_CPU_IDLE=y
CONFIG_CPU_CORTEX_M=y
# CONFIG_ARM_ZIMAGE_HEADER is not set
CONFIG_ISA_THUMB2=y
CONFIG_ASSEMBLER_ISA_THUMB2=y
CONFIG_COMPILER_ISA_THUMB2=y
CONFIG_STACK_ALIGN_DOUBLE_WORD=y
# CONFIG_RUNTIME_NMI is not set
CONFIG_FAULT_DUMP=2
CONFIG_BUILTIN_STACK_GUARD=y
CONFIG_ARM_STACK_PROTECTION=y
CONFIG_FP16=y
CONFIG_FP16_IEEE=y
# CONFIG_FP16_ALT is not set
CONFIG_CPU_CORTEX_M33=y
CONFIG_CPU_CORTEX_M_HAS_SYSTICK=y
CONFIG_CPU_CORTEX_M_HAS_DWT=y
CONFIG_CPU_CORTEX_M_HAS_BASEPRI=y
CONFIG_CPU_CORTEX_M_HAS_VTOR=y
CONFIG_CPU_CORTEX_M_HAS_SPLIM=y
CONFIG_CPU_CORTEX_M_HAS_PROGRAMMABLE_FAULT_PRIOS=y
CONFIG_CPU_CORTEX_M_HAS_CMSE=y
CONFIG_ARMV7_M_ARMV8_M_MAINLINE=y
CONFIG_ARMV8_M_MAINLINE=y

#
# ARM Cortex-M0/M0+/M1/M3/M4/M7/M23/M33 options
#
CONFIG_GEN_ISR_TABLES=y
# CONFIG_ZERO_LATENCY_IRQS is not set
# CONFIG_SW_VECTOR_RELAY is not set
# CONFIG_CORTEX_M_DWT is not set
# CONFIG_CORTEX_M_DEBUG_MONITOR_HOOK is not set
# CONFIG_TRAP_UNALIGNED_ACCESS is not set
# end of ARM Cortex-M0/M0+/M1/M3/M4/M7/M23/M33 options

CONFIG_NULL_POINTER_EXCEPTION_DETECTION_NONE=y
# CONFIG_NULL_POINTER_EXCEPTION_DETECTION_DWT is not set
# CONFIG_NULL_POINTER_EXCEPTION_DETECTION_MPU is not set
CONFIG_GEN_IRQ_VECTOR_TABLE=y
CONFIG_ARM_MPU=y
CONFIG_ARM_MPU_REGION_MIN_ALIGN_AND_SIZE=32
# CONFIG_MPU_STACK_GUARD is not set
# CONFIG_MPU_ALLOW_FLASH_WRITE is not set
# CONFIG_MPU_DISABLE_BACKGROUND_MAP is not set
# CONFIG_CUSTOM_SECTION_ALIGN is not set
CONFIG_CUSTOM_SECTION_MIN_ALIGN_SIZE=32
# end of ARM Options

CONFIG_ARM=y
CONFIG_ARCH_IS_SET=y

#
# General Architecture Options
#
# CONFIG_SEMIHOST is not set
CONFIG_LITTLE_ENDIAN=y
CONFIG_HW_STACK_PROTECTION=y
# CONFIG_USERSPACE is not set
CONFIG_KOBJECT_TEXT_AREA=256
CONFIG_KOBJECT_DATA_AREA_RESERVE_EXTRA_PERCENT=100
CONFIG_KOBJECT_RODATA_AREA_EXTRA_BYTES=16
CONFIG_GEN_PRIV_STACKS=y
# CONFIG_STACK_GROWS_UP is not set

#
# Interrupt Configuration
#
# CONFIG_DYNAMIC_INTERRUPTS is not set
CONFIG_ARCH_IRQ_VECTOR_TABLE_ALIGN=4
CONFIG_IRQ_VECTOR_TABLE_JUMP_BY_ADDRESS=y
# CONFIG_IRQ_VECTOR_TABLE_JUMP_BY_CODE is not set
CONFIG_GEN_SW_ISR_TABLE=y
CONFIG_ARCH_SW_ISR_TABLE_ALIGN=4
CONFIG_GEN_IRQ_START_VECTOR=0
# CONFIG_EXTRA_EXCEPTION_INFO is not set
# CONFIG_SIMPLIFIED_EXCEPTION_CODES is not set
# end of Interrupt Configuration
# end of General Architecture Options

CONFIG_ARCH_HAS_SINGLE_THREAD_SUPPORT=y
CONFIG_ARCH_HAS_TIMING_FUNCTIONS=y
CONFIG_ARCH_HAS_STACK_PROTECTION=y
CONFIG_ARCH_HAS_USERSPACE=y
CONFIG_ARCH_HAS_EXECUTABLE_PAGE_BIT=y
CONFIG_ARCH_HAS_RAMFUNC_SUPPORT=y
CONFIG_ARCH_HAS_NESTED_EXCEPTION_DETECTION=y
CONFIG_ARCH_SUPPORTS_COREDUMP=y
CONFIG_ARCH_SUPPORTS_ARCH_HW_INIT=y
CONFIG_ARCH_HAS_EXTRA_EXCEPTION_INFO=y
CONFIG_ARCH_HAS_THREAD_LOCAL_STORAGE=y
CONFIG_ARCH_HAS_SUSPEND_TO_RAM=y
CONFIG_ARCH_HAS_THREAD_ABORT=y
CONFIG_ARCH_HAS_CODE_DATA_RELOCATION=y
CONFIG_CPU_HAS_MPU=y
CONFIG_MPU=y
CONFIG_MPU_REQUIRES_NON_OVERLAPPING_REGIONS=y
CONFIG_MPU_GAP_FILLING=y
CONFIG_SRAM_REGION_PERMISSIONS=y

#
# Floating Point Options
#
# end of Floating Point Options

#
# Cache Options
#
# end of Cache Options

CONFIG_TOOLCHAIN_HAS_BUILTIN_FFS=y

#
# General Kernel Options
#
CONFIG_MULTITHREADING=y
CONFIG_NUM_COOP_PRIORITIES=16
CONFIG_NUM_PREEMPT_PRIORITIES=15
CONFIG_MAIN_THREAD_PRIORITY=0
CONFIG_COOP_ENABLED=y
CONFIG_PREEMPT_ENABLED=y
CONFIG_PRIORITY_CEILING=-127
# CONFIG_SCHED_DEADLINE is not set
# CONFIG_SCHED_CPU_MASK is not set
CONFIG_ISR_STACK_SIZE=2048
CONFIG_THREAD_STACK_INFO=y
# CONFIG_THREAD_CUSTOM_DATA is not set
# CONFIG_DYNAMIC_THREAD is not set
CONFIG_LIBC_ERRNO=y
CONFIG_ERRNO=y
CONFIG_SCHED_DUMB=y
# CONFIG_SCHED_SCALABLE is not set
# CONFIG_SCHED_MULTIQ is not set
# CONFIG_WAITQ_SCALABLE is not set
CONFIG_WAITQ_DUMB=y

#
# Kernel Debugging and Metrics
#
CONFIG_BOOT_BANNER=y
CONFIG_BOOT_DELAY=0
# CONFIG_THREAD_MONITOR is not set
# CONFIG_THREAD_NAME is not set
# CONFIG_THREAD_RUNTIME_STATS is not set
# end of Kernel Debugging and Metrics

#
# Work Queue Options
#
CONFIG_SYSTEM_WORKQUEUE_PRIORITY=-1
# CONFIG_SYSTEM_WORKQUEUE_NO_YIELD is not set
# end of Work Queue Options

#
# Barrier Operations
#
CONFIG_BARRIER_OPERATIONS_ARCH=y
# end of Barrier Operations

#
# Atomic Operations
#
CONFIG_ATOMIC_OPERATIONS_BUILTIN=y
# end of Atomic Operations

#
# Timer API Options
#
CONFIG_TIMESLICING=y
CONFIG_TIMESLICE_SIZE=0
CONFIG_TIMESLICE_PRIORITY=0
# CONFIG_TIMESLICE_PER_THREAD is not set
CONFIG_POLL=y
# end of Timer API Options

#
# Other Kernel Object Options
#
# CONFIG_MEM_SLAB_TRACE_MAX_UTILIZATION is not set
CONFIG_NUM_MBOX_ASYNC_MSGS=10
# CONFIG_EVENTS is not set
# CONFIG_PIPES is not set
CONFIG_KERNEL_MEM_POOL=y
# end of Other Kernel Object Options

CONFIG_ARCH_HAS_CUSTOM_SWAP_TO_MAIN=y
CONFIG_SWAP_NONATOMIC=y
CONFIG_SYS_CLOCK_EXISTS=y
CONFIG_TIMEOUT_64BIT=y
CONFIG_SYS_CLOCK_MAX_TIMEOUT_DAYS=365
CONFIG_XIP=y

#
# Initialization Priorities
#
CONFIG_KERNEL_INIT_PRIORITY_OBJECTS=30
CONFIG_KERNEL_INIT_PRIORITY_DEFAULT=40
CONFIG_KERNEL_INIT_PRIORITY_DEVICE=50
CONFIG_APPLICATION_INIT_PRIORITY=90
# end of Initialization Priorities

#
# Security Options
#
# CONFIG_STACK_CANARIES is not set
CONFIG_STACK_POINTER_RANDOM=0
# end of Security Options

#
# SMP Options
#
CONFIG_MP_NUM_CPUS=1
# end of SMP Options

CONFIG_TICKLESS_KERNEL=y
CONFIG_TOOLCHAIN_SUPPORTS_THREAD_LOCAL_STORAGE=y
CONFIG_THREAD_LOCAL_STORAGE=y
# end of General Kernel Options

#
# Device Options
#
# CONFIG_DEVICE_DEPS is not set
# end of Device Options

#
# Virtual Memory Support
#
# end of Virtual Memory Support

#
# Device Drivers
#
# CONFIG_ADC is not set
# CONFIG_AUXDISPLAY is not set
# CONFIG_AUDIO is not set
# CONFIG_BBRAM is not set
# CONFIG_CACHE is not set
# CONFIG_CAN is not set
CONFIG_CLOCK_CONTROL_NRF=y
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_RC is not set
CONFIG_CLOCK_CONTROL_NRF_K32SRC_XTAL=y
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_SYNTH is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_500PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_250PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_150PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_100PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_75PPM is not set
CONFIG_CLOCK_CONTROL_NRF_K32SRC_50PPM=y
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_30PPM is not set
# CONFIG_CLOCK_CONTROL_NRF_K32SRC_20PPM is not set
CONFIG_CLOCK_CONTROL_NRF_ACCURACY=50
# CONFIG_CLOCK_CONTROL_FIXED_RATE_CLOCK is not set
CONFIG_CONSOLE=y
CONFIG_CONSOLE_INPUT_MAX_LINE_LEN=128
CONFIG_CONSOLE_HAS_DRIVER=y
# CONFIG_CONSOLE_HANDLER is not set
CONFIG_CONSOLE_INIT_PRIORITY=60
CONFIG_UART_CONSOLE=y
# CONFIG_UART_CONSOLE_DEBUG_SERVER_HOOKS is not set
# CONFIG_UART_CONSOLE_MCUMGR is not set
# CONFIG_RAM_CONSOLE is not set
# CONFIG_IPM_CONSOLE_SENDER is not set
# CONFIG_IPM_CONSOLE_RECEIVER is not set
# CONFIG_UART_MCUMGR is not set
# CONFIG_GSM_MUX is not set
# CONFIG_EFI_CONSOLE is not set
# CONFIG_COREDUMP_DEVICE is not set
# CONFIG_COUNTER is not set
# CONFIG_CRYPTO is not set
# CONFIG_DAC is not set
# CONFIG_DAI is not set
# CONFIG_DISK_DRIVERS is not set
# CONFIG_DISPLAY is not set
# CONFIG_DMA is not set
# CONFIG_EDAC is not set
# CONFIG_EEPROM is not set
CONFIG_ENTROPY_INIT_PRIORITY=50
CONFIG_ENTROPY_NRF5_RNG=y
CONFIG_ENTROPY_NRF5_BIAS_CORRECTION=y
CONFIG_ENTROPY_NRF5_THR_POOL_SIZE=8
CONFIG_ENTROPY_NRF5_THR_THRESHOLD=4
CONFIG_ENTROPY_NRF5_ISR_POOL_SIZE=16
CONFIG_ENTROPY_NRF5_ISR_THRESHOLD=12
CONFIG_ENTROPY_HAS_DRIVER=y
# CONFIG_ESPI is not set
# CONFIG_FLASH is not set
# CONFIG_FPGA is not set
# CONFIG_FUEL_GAUGE is not set
# CONFIG_GPIO_GET_DIRECTION is not set
# CONFIG_GPIO_GET_CONFIG is not set
# CONFIG_GPIO_HOGS is not set
# CONFIG_GPIO_ENABLE_DISABLE_INTERRUPT is not set
CONFIG_GPIO_NRFX=y
CONFIG_GPIO_NRFX_INTERRUPT=y
# CONFIG_HWINFO is not set
# CONFIG_I2C is not set
# CONFIG_I2S is not set
# CONFIG_I3C is not set
# CONFIG_SMBUS is not set

#
# Interrupt controller drivers
#
CONFIG_INTC_INIT_PRIORITY=40
# CONFIG_MULTI_LEVEL_INTERRUPTS is not set
CONFIG_1ST_LEVEL_INTERRUPT_BITS=8
CONFIG_2ND_LEVEL_INTERRUPT_BITS=8
CONFIG_3RD_LEVEL_INTERRUPT_BITS=8
# CONFIG_INTC_ESP32 is not set
# CONFIG_PLIC_SUPPORTS_EDGE_IRQ is not set
# end of Interrupt controller drivers

# CONFIG_IPM is not set
# CONFIG_KSCAN is not set
# CONFIG_LED is not set
# CONFIG_LED_STRIP is not set
# CONFIG_LORA is not set
CONFIG_MBOX=y
CONFIG_MBOX_INIT_PRIORITY=40
# CONFIG_MDIO is not set
# CONFIG_MFD is not set

#
# Miscellaneous Drivers
#
# CONFIG_GROVE_LCD_RGB is not set
# end of Miscellaneous Drivers

# CONFIG_MM_DRV is not set
# CONFIG_NEURAL_NET_ACCEL is not set
# CONFIG_PCIE is not set
# CONFIG_PCIE_ENDPOINT is not set
# CONFIG_PECI is not set
CONFIG_PINCTRL_STORE_REG=y
# CONFIG_PINCTRL_DYNAMIC is not set
CONFIG_PINCTRL_NRF=y
# CONFIG_PM_CPU_OPS is not set
# CONFIG_POWER_DOMAIN is not set
# CONFIG_PS2 is not set
# CONFIG_PTP_CLOCK is not set
# CONFIG_PWM is not set
# CONFIG_REGULATOR is not set
# CONFIG_RETAINED_MEM is not set
# CONFIG_RTC is not set
# CONFIG_SDHC is not set

#
# Capabilities
#
CONFIG_SERIAL_HAS_DRIVER=y
CONFIG_SERIAL_SUPPORT_ASYNC=y
CONFIG_SERIAL_SUPPORT_INTERRUPT=y
CONFIG_UART_USE_RUNTIME_CONFIGURE=y
# CONFIG_UART_ASYNC_API is not set
# CONFIG_UART_LINE_CTRL is not set
# CONFIG_UART_DRV_CMD is not set
# CONFIG_UART_WIDE_DATA is not set
# CONFIG_UART_PIPE is not set

#
# Serial Drivers
#
CONFIG_UART_NRFX=y
CONFIG_UART_NRFX_UARTE=y
CONFIG_UART_0_ENHANCED_POLL_OUT=y
# CONFIG_UART_0_NRF_PARITY_BIT is not set
CONFIG_UART_0_NRF_TX_BUFFER_SIZE=32
CONFIG_UART_ENHANCED_POLL_OUT=y
# CONFIG_SYSCON is not set

#
# Timer drivers
#
# CONFIG_TIMER_READS_ITS_FREQUENCY_AT_RUNTIME is not set
# CONFIG_SYSTEM_CLOCK_SLOPPY_IDLE is not set
CONFIG_SYSTEM_CLOCK_INIT_PRIORITY=0
CONFIG_TICKLESS_CAPABLE=y
CONFIG_SYSTEM_TIMER_HAS_DISABLE_SUPPORT=y
# CONFIG_NRF_RTC_TIMER_TRIGGER_OVERFLOW is not set
# CONFIG_SYSTEM_CLOCK_NO_WAIT is not set
# CONFIG_SYSTEM_CLOCK_WAIT_FOR_AVAILABILITY is not set
CONFIG_SYSTEM_CLOCK_WAIT_FOR_STABILITY=y
# end of Timer drivers

# CONFIG_USB_BC12 is not set
# CONFIG_UDC_DRIVER is not set
# CONFIG_UHC_DRIVER is not set
# CONFIG_UVB is not set
# CONFIG_USB_DEVICE_DRIVER is not set
# CONFIG_USBC_TCPC_DRIVER is not set
# CONFIG_USBC_VBUS_DRIVER is not set
# CONFIG_VIDEO is not set
# CONFIG_VIRTUALIZATION is not set
# CONFIG_W1 is not set
# CONFIG_HWSPINLOCK is not set
# end of Device Drivers

# CONFIG_REQUIRES_FULL_LIBC is not set
CONFIG_FULL_LIBC_SUPPORTED=y
CONFIG_MINIMAL_LIBC_SUPPORTED=y
CONFIG_NEWLIB_LIBC_SUPPORTED=y
CONFIG_PICOLIBC_SUPPORTED=y

#
# C Library
#
# CONFIG_MINIMAL_LIBC is not set
CONFIG_PICOLIBC=y
# CONFIG_NEWLIB_LIBC is not set
# CONFIG_EXTERNAL_LIBC is not set
CONFIG_HAS_NEWLIB_LIBC_NANO=y
CONFIG_COMMON_LIBC_ABORT=y
CONFIG_COMMON_LIBC_MALLOC=y
CONFIG_COMMON_LIBC_MALLOC_ARENA_SIZE=-1
CONFIG_COMMON_LIBC_CALLOC=y
CONFIG_COMMON_LIBC_REALLOCARRAY=y
# CONFIG_PICOLIBC_USE_MODULE is not set
CONFIG_PICOLIBC_HEAP_SIZE=-2
CONFIG_PICOLIBC_IO_LONG_LONG=y
# CONFIG_PICOLIBC_IO_FLOAT is not set
CONFIG_STDOUT_CONSOLE=y
# end of C Library

#
# C++ Language Support
#
# CONFIG_CPP is not set

#
# Deprecated
#
# CONFIG_CPLUSPLUS is not set
# CONFIG_LIB_CPLUSPLUS is not set
# end of Deprecated
# end of C++ Language Support

# CONFIG_CRC is not set

#
# Additional libraries
#

#
# Hash Function Support
#
# CONFIG_SYS_HASH_FUNC32 is not set
# end of Hash Function Support

#
# Hashmap (Hash Table) Support
#
# CONFIG_SYS_HASH_MAP is not set
# end of Hashmap (Hash Table) Support

#
# OS Support Library
#
# CONFIG_JSON_LIBRARY is not set
# CONFIG_RING_BUFFER is not set
CONFIG_NOTIFY=y
# CONFIG_BASE64 is not set
# CONFIG_PRINTK_SYNC is not set
# CONFIG_MPSC_PBUF is not set
CONFIG_ONOFF=y
# CONFIG_SPSC_PBUF is not set
# CONFIG_SHARED_MULTI_HEAP is not set
# CONFIG_WINSTREAM is not set
CONFIG_REBOOT=y
# CONFIG_UTF8 is not set
CONFIG_CBPRINTF_COMPLETE=y
# CONFIG_CBPRINTF_NANO is not set
CONFIG_CBPRINTF_FULL_INTEGRAL=y
# CONFIG_CBPRINTF_REDUCED_INTEGRAL is not set
# CONFIG_CBPRINTF_FP_SUPPORT is not set
# CONFIG_CBPRINTF_FP_A_SUPPORT is not set
# CONFIG_CBPRINTF_FP_ALWAYS_A is not set
CONFIG_CBPRINTF_N_SPECIFIER=y
# CONFIG_CBPRINTF_LIBC_SUBSTS is not set
# CONFIG_CBPRINTF_PACKAGE_LONGDOUBLE is not set
# CONFIG_CBPRINTF_STATIC_PACKAGE_CHECK_ALIGNMENT is not set

#
# Heap and Memory Allocation
#
# CONFIG_SYS_HEAP_VALIDATE is not set
CONFIG_SYS_HEAP_ALLOC_LOOPS=3
# CONFIG_SYS_HEAP_RUNTIME_STATS is not set
# CONFIG_SYS_HEAP_LISTENER is not set
# CONFIG_SYS_HEAP_SMALL_ONLY is not set
# CONFIG_SYS_HEAP_BIG_ONLY is not set
CONFIG_SYS_HEAP_AUTO=y
# CONFIG_SYS_MEM_BLOCKS is not set
# end of Heap and Memory Allocation
# end of OS Support Library

# CONFIG_POSIX_API is not set
# CONFIG_POSIX_CLOCK is not set
# CONFIG_EVENTFD is not set
# CONFIG_FNMATCH is not set
# CONFIG_POSIX_MQUEUE is not set
CONFIG_SEM_VALUE_MAX=32767
# CONFIG_POSIX_SIGNAL is not set
# CONFIG_TIMER is not set
CONFIG_MAX_TIMER_COUNT=5
CONFIG_TIMER_CREATE_WAIT=100
CONFIG_TIMER_DELAYTIMER_MAX=20
# CONFIG_POSIX_UNAME is not set
# CONFIG_OPENAMP_RSC_TABLE is not set
# CONFIG_SMF is not set
CONFIG_ACPI_HID_LEN_MAX=12
CONFIG_LIBGCC_RTLIB=y
# end of Additional libraries

#
# Subsystems and OS Services
#
CONFIG_BT=y
CONFIG_BT_HCI=y
# CONFIG_BT_CUSTOM is not set
CONFIG_BT_HCI_RAW=y
# CONFIG_BT_HCI_RAW_H4 is not set
CONFIG_BT_HCI_RAW_RESERVE=1
# CONFIG_BT_HCI_RAW_CMD_EXT is not set
CONFIG_BT_CONN_TX=y
# CONFIG_BT_SCA_UPDATE is not set
# CONFIG_BT_ISO_PERIPHERAL is not set
# CONFIG_BT_ISO_CENTRAL is not set
# CONFIG_BT_ISO_BROADCASTER is not set
# CONFIG_BT_ISO_SYNC_RECEIVER is not set

#
# Bluetooth buffer configuration
#
# end of Bluetooth buffer configuration

#
# Bluetooth Host
#
# end of Bluetooth Host

CONFIG_BT_CTLR_LE_ENC_SUPPORT=y
CONFIG_BT_CTLR_CONN_PARAM_REQ_SUPPORT=y
CONFIG_BT_CTLR_EXT_REJ_IND_SUPPORT=y
CONFIG_BT_CTLR_PER_INIT_FEAT_XCHG_SUPPORT=y
CONFIG_BT_CTLR_DATA_LEN_UPDATE_SUPPORT=y
CONFIG_BT_CTLR_PRIVACY_SUPPORT=y
CONFIG_BT_CTLR_EXT_SCAN_FP_SUPPORT=y
CONFIG_BT_CTLR_PHY_UPDATE_SUPPORT=y
CONFIG_BT_CTLR_PHY_2M_SUPPORT=y
CONFIG_BT_CTLR_PHY_CODED_SUPPORT=y
CONFIG_BT_CTLR_ADV_EXT_SUPPORT=y
CONFIG_BT_CTLR_ADV_PERIODIC_SUPPORT=y
CONFIG_BT_CTLR_SYNC_PERIODIC_SUPPORT=y
CONFIG_BT_CTLR_ADV_ISO_SUPPORT=y
CONFIG_BT_CTLR_SYNC_ISO_SUPPORT=y
CONFIG_BT_CTLR_CENTRAL_ISO_SUPPORT=y
CONFIG_BT_CTLR_PERIPHERAL_ISO_SUPPORT=y
CONFIG_BT_CTLR_CHAN_SEL_2_SUPPORT=y
CONFIG_BT_CTLR_MIN_USED_CHAN_SUPPORT=y
CONFIG_BT_CTLR_SCA_UPDATE_SUPPORT=y
CONFIG_BT_CTLR_CONN_RSSI_SUPPORT=y
CONFIG_BT_LL_SW_SPLIT=y

#
# BLE Controller configuration
#
CONFIG_BT_CTLR_CRYPTO=y
CONFIG_BT_CTLR_HCI_VS_BUILD_INFO=""
CONFIG_BT_CTLR_DUP_FILTER_LEN=16
CONFIG_BT_CTLR_RX_BUFFERS=6
# CONFIG_BT_CTLR_TX_PWR_PLUS_3 is not set
# CONFIG_BT_CTLR_TX_PWR_PLUS_2 is not set
# CONFIG_BT_CTLR_TX_PWR_PLUS_1 is not set
CONFIG_BT_CTLR_TX_PWR_0=y
# CONFIG_BT_CTLR_TX_PWR_MINUS_1 is not set
# CONFIG_BT_CTLR_TX_PWR_MINUS_2 is not set
# CONFIG_BT_CTLR_TX_PWR_MINUS_3 is not set
# CONFIG_BT_CTLR_TX_PWR_MINUS_4 is not set
# CONFIG_BT_CTLR_TX_PWR_MINUS_5 is not set
# CONFIG_BT_CTLR_TX_PWR_MINUS_6 is not set
# CONFIG_BT_CTLR_TX_PWR_MINUS_7 is not set
# CONFIG_BT_CTLR_TX_PWR_MINUS_8 is not set
# CONFIG_BT_CTLR_TX_PWR_MINUS_12 is not set
# CONFIG_BT_CTLR_TX_PWR_MINUS_16 is not set
# CONFIG_BT_CTLR_TX_PWR_MINUS_20 is not set
# CONFIG_BT_CTLR_TX_PWR_MINUS_40 is not set
CONFIG_BT_CTLR_TX_PWR_DBM=0
# CONFIG_BT_CTLR_TX_PWR_DYNAMIC_CONTROL is not set

#
# BLE Controller features
#
CONFIG_BT_CTLR_LE_ENC=y
CONFIG_BT_CTLR_CONN_PARAM_REQ=y
CONFIG_BT_CTLR_EXT_REJ_IND=y
CONFIG_BT_CTLR_PER_INIT_FEAT_XCHG=y
CONFIG_BT_CTLR_LE_PING=y
CONFIG_BT_CTLR_DATA_LENGTH=y
CONFIG_BT_CTLR_PHY=y
CONFIG_BT_CTLR_MIN_USED_CHAN=y
CONFIG_BT_CTLR_CONN_RSSI=y
CONFIG_BT_CTLR_FILTER_ACCEPT_LIST=y
CONFIG_BT_CTLR_PRIVACY=y
CONFIG_BT_CTLR_RL_SIZE=8
CONFIG_BT_CTLR_EXT_SCAN_FP=y
CONFIG_BT_CTLR_PHY_2M=y
CONFIG_BT_CTLR_PHY_CODED=y
CONFIG_BT_CTLR_CHAN_SEL_2=y
# CONFIG_BT_CTLR_ADV_EXT is not set
# CONFIG_BT_CTLR_SET_HOST_FEATURE is not set
# CONFIG_BT_CTLR_CENTRAL_ISO is not set
# CONFIG_BT_CTLR_PERIPHERAL_ISO is not set
# CONFIG_BT_CTLR_HCI_CODEC_AND_DELAY_INFO is not set
CONFIG_BT_CTLR_DF_CTE_TX_SUPPORT=y
CONFIG_BT_CTLR_DF_CTE_RX_SUPPORT=y
CONFIG_BT_CTLR_DF_CTE_RX_SAMPLE_1US_SUPPORT=y
CONFIG_BT_CTLR_DF_ANT_SWITCH_2US_SUPPORT=y
CONFIG_BT_CTLR_DF_ANT_SWITCH_1US_SUPPORT=y
CONFIG_BT_CTLR_CTEINLINE_SUPPORT=y
CONFIG_BT_CTLR_DF_CTE_TX=y
CONFIG_BT_CTLR_DF_CTE_RX_SAMPLE_1US=y
CONFIG_BT_CTLR_DF_ANT_SWITCH_1US=y
CONFIG_BT_CTLR_DF_ANT_SWITCH_TX=y
CONFIG_BT_CTLR_DF_ANT_SWITCH_RX=y
CONFIG_BT_CTLR_DF_CTE_RX=y
# CONFIG_BT_CTLR_DF_CONN_CTE_REQ is not set
CONFIG_BT_CTLR_DF_CONN_CTE_TX=y
CONFIG_BT_CTLR_DF_CONN_CTE_RX=y
CONFIG_BT_CTLR_DF_MAX_ANT_SW_PATTERN_LEN=12
CONFIG_BT_CTLR_DF_INIT_ANT_SEL_GPIOS=y
CONFIG_BT_CTLR_DF_SWITCH_OFFSET=0
CONFIG_BT_CTLR_DF_SAMPLE_OFFSET_PHY_1M_SAMPLING_1US=1
CONFIG_BT_CTLR_DF_SAMPLE_OFFSET_PHY_2M_SAMPLING_1US=15
CONFIG_BT_CTLR_DF_SAMPLE_OFFSET_PHY_1M_SAMPLING_2US=6
CONFIG_BT_CTLR_DF_SAMPLE_OFFSET_PHY_2M_SAMPLING_2US=20
# CONFIG_BT_CTLR_DF_DEBUG_ENABLE is not set
CONFIG_BT_CTLR_DF_PHYEND_OFFSET_COMPENSATION_ENABLE=y
# CONFIG_BT_CTLR_DF_IQ_SAMPLES_CONVERT_4_BITS_SHIFT is not set
# CONFIG_BT_CTLR_DF_IQ_SAMPLES_CONVERT_2_BITS_SHIFT is not set
CONFIG_BT_CTLR_DF_IQ_SAMPLES_CONVERT_USE_8_LSB=y
# CONFIG_BT_CTLR_DF_VS_CONN_IQ_REPORT_16_BITS_IQ_SAMPLES is not set
CONFIG_BT_LLL_VENDOR_NORDIC=y
CONFIG_BT_CTLR_XTAL_ADVANCED_SUPPORT=y
CONFIG_BT_CTLR_SCHED_ADVANCED_SUPPORT=y
CONFIG_BT_CTLR_TIFS_HW_SUPPORT=y
CONFIG_BT_CTLR_ULL_LLL_PRIO_SUPPORT=y
CONFIG_BT_CTLR_RX_PRIO_STACK_SIZE=448
CONFIG_BT_CTLR_COMPANY_ID=0x05F1
CONFIG_BT_CTLR_SUBVERSION_NUMBER=0xFFFF
CONFIG_BT_CTLR_AD_DATA_BACKUP=y
CONFIG_BT_CTLR_CHECK_SAME_PEER_CONN=y
CONFIG_BT_CTLR_ADVANCED_FEATURES=y
# CONFIG_BT_CTLR_TEST is not set

#
# Advanced features
#
# CONFIG_BT_CTLR_SW_DEFERRED_PRIVACY is not set
# CONFIG_BT_CTLR_ADV_DATA_CHAIN is not set
CONFIG_BT_CTLR_ADV_DATA_BUF_MAX=1
# CONFIG_BT_CTLR_ADV_ENABLE_STRICT is not set
# CONFIG_BT_CTLR_SCAN_ENABLE_STRICT is not set
CONFIG_BT_CTLR_OPTIMIZE_FOR_SPEED=y
CONFIG_BT_CTLR_XTAL_ADVANCED=y
CONFIG_BT_CTLR_XTAL_THRESHOLD=1500
CONFIG_BT_CTLR_SCHED_ADVANCED=y
CONFIG_BT_CTLR_ASSERT_OVERHEAD_START=y
CONFIG_BT_CTLR_CENTRAL_SPACING=0
CONFIG_BT_CTLR_CENTRAL_RESERVE_MAX=y
CONFIG_BT_CTLR_EVENT_OVERHEAD_RESERVE_MAX=y
CONFIG_BT_CTLR_SLOT_RESERVATION_UPDATE=y
CONFIG_BT_CTLR_LLL_PRIO=0
CONFIG_BT_CTLR_ULL_HIGH_PRIO=1
CONFIG_BT_CTLR_ULL_LOW_PRIO=1
# CONFIG_BT_CTLR_LOW_LAT is not set
# CONFIG_BT_CTLR_LOW_LAT_ULL_DONE is not set
# CONFIG_BT_CTLR_CONN_META is not set
# CONFIG_BT_CTLR_RX_PDU_META is not set
CONFIG_BT_CTLR_RADIO_ENABLE_FAST=y
# CONFIG_BT_CTLR_SW_SWITCH_SINGLE_TIMER is not set
CONFIG_BT_CTLR_PARAM_CHECK=y
CONFIG_BT_CTLR_LLCP_CONN=16
CONFIG_BT_CTLR_LLCP_TX_PER_CONN_TX_CTRL_BUF_NUM_MAX=4
CONFIG_BT_CTLR_LLCP_PER_CONN_TX_CTRL_BUF_NUM=4
CONFIG_BT_CTLR_LLCP_COMMON_TX_CTRL_BUF_NUM=0
CONFIG_BT_CTLR_LLCP_LOCAL_PROC_CTX_BUF_NUM=6
CONFIG_BT_CTLR_LLCP_REMOTE_PROC_CTX_BUF_NUM=16
CONFIG_BT_CTLR_LLID_DATA_START_EMPTY=y
# CONFIG_BT_CTLR_TX_RETRY_DISABLE is not set
CONFIG_BT_CTLR_THROUGHPUT=y
CONFIG_BT_CTLR_FORCE_MD_COUNT=1
CONFIG_BT_CTLR_FORCE_MD_AUTO=y
CONFIG_BT_CTLR_CONN_RANDOM_FORCE=y
# CONFIG_BT_CTLR_CONN_RSSI_EVENT is not set
# CONFIG_BT_CTLR_ALLOW_SAME_PEER_CONN is not set
# CONFIG_BT_CTLR_ADV_INDICATION is not set
# CONFIG_BT_CTLR_SCAN_REQ_NOTIFY is not set
# CONFIG_BT_CTLR_SCAN_INDICATION is not set
# CONFIG_BT_CTLR_SCAN_UNRESERVED is not set
CONFIG_BT_MAYFLY_YIELD_AFTER_CALL=y
# CONFIG_BT_TICKER_LOW_LAT is not set
CONFIG_BT_TICKER_UPDATE=y
# CONFIG_BT_TICKER_REMAINDER is not set
# CONFIG_BT_TICKER_JOB_IDLE_GET is not set
CONFIG_BT_TICKER_NEXT_SLOT_GET=y
CONFIG_BT_TICKER_REMAINDER_GET=y
# CONFIG_BT_TICKER_LAZY_GET is not set
CONFIG_BT_TICKER_NEXT_SLOT_GET_MATCH=y
CONFIG_BT_TICKER_EXT=y
# CONFIG_BT_TICKER_EXT_SLOT_WINDOW_YIELD is not set
# CONFIG_BT_TICKER_EXT_EXPIRE_INFO is not set
# CONFIG_BT_TICKER_PRIORITY_SET is not set
# CONFIG_BT_TICKER_SLOT_AGNOSTIC is not set
# CONFIG_BT_TICKER_PREFER_START_BEFORE_STOP is not set
# CONFIG_BT_CTLR_JIT_SCHEDULING is not set
# CONFIG_BT_CTLR_USER_EXT is not set
# end of Advanced features

#
# BLE Controller debug configuration
#
# CONFIG_BT_CTLR_PROFILE_ISR is not set
# CONFIG_BT_CTLR_DEBUG_PINS is not set
CONFIG_BT_CTLR_DTM_HCI_SUPPORT=y
# CONFIG_BT_CTLR_DTM_HCI is not set
# CONFIG_BT_CTLR_ASSERT_HANDLER is not set
# CONFIG_BT_SHELL is not set
# CONFIG_BT_EAD is not set
CONFIG_BT_COMPANY_ID=0x05F1

#
# Controller Area Network (CAN) bus subsystem
#
# CONFIG_ISOTP is not set
# end of Controller Area Network (CAN) bus subsystem

# CONFIG_CONSOLE_SUBSYS is not set

#
# System Monitoring Options
#
# CONFIG_THREAD_ANALYZER is not set
# end of System Monitoring Options

#
# Debugging Options
#
# CONFIG_DEBUG is not set
# CONFIG_STACK_USAGE is not set
# CONFIG_STACK_SENTINEL is not set
CONFIG_PRINTK=y
CONFIG_EARLY_CONSOLE=y
# CONFIG_ASSERT is not set
# CONFIG_FORCE_NO_ASSERT is not set
CONFIG_ASSERT_VERBOSE=y
# CONFIG_ASSERT_NO_FILE_INFO is not set
# CONFIG_ASSERT_NO_COND_INFO is not set
# CONFIG_ASSERT_NO_MSG_INFO is not set
# CONFIG_ASSERT_TEST is not set
# CONFIG_OVERRIDE_FRAME_POINTER_DEFAULT is not set
# CONFIG_DEBUG_INFO is not set
# CONFIG_DEBUG_THREAD_INFO is not set
# CONFIG_DEBUG_COREDUMP is not set
# end of Debugging Options

# CONFIG_DISK_ACCESS is not set
# CONFIG_DSP is not set
# CONFIG_EMUL is not set
# CONFIG_CHARACTER_FRAMEBUFFER is not set

#
# File Systems
#
# CONFIG_FILE_SYSTEM is not set
# CONFIG_NVS is not set
# end of File Systems

#
# Inter Processor Communication
#
# CONFIG_RPMSG_SERVICE is not set
CONFIG_IPC_SERVICE=y
CONFIG_IPC_SERVICE_REG_BACKEND_PRIORITY=46
CONFIG_IPC_SERVICE_BACKEND_RPMSG=y
CONFIG_IPC_SERVICE_BACKEND_RPMSG_WQ_STACK_SIZE=1024
CONFIG_IPC_SERVICE_BACKEND_RPMSG_NUM_ENDPOINTS_PER_INSTANCE=2
CONFIG_IPC_SERVICE_RPMSG=y
CONFIG_IPC_SERVICE_STATIC_VRINGS=y
CONFIG_IPC_SERVICE_STATIC_VRINGS_MEM_ALIGNMENT=4
# CONFIG_IPC_SERVICE_ICMSG is not set
# CONFIG_IPC_SERVICE_ICMSG_ME is not set
# end of Inter Processor Communication

# CONFIG_JWT is not set

#
# Logging
#
# CONFIG_LOG is not set
# CONFIG_LOG_OUTPUT is not set
# end of Logging

#
# Device Management
#

#
# Host command handler subsystem
#
# CONFIG_EC_HOST_CMD is not set
# end of Host command handler subsystem

# CONFIG_OSDP is not set
# end of Device Management

# CONFIG_MODBUS is not set
# CONFIG_MODEM_MODULES is not set

#
# Networking
#
CONFIG_NET_BUF=y
# CONFIG_NET_BUF_LOG is not set
# CONFIG_NET_BUF_POOL_USAGE is not set
# CONFIG_NETWORKING is not set
# end of Networking

#
# Power Management
#
# end of Power Management

#
# Portability
#
# end of Portability

#
# Random Number Generators
#
# CONFIG_TEST_RANDOM_GENERATOR is not set
CONFIG_ENTROPY_DEVICE_RANDOM_GENERATOR=y
# CONFIG_XOROSHIRO_RANDOM_GENERATOR is not set
# CONFIG_XOSHIRO_RANDOM_GENERATOR is not set
CONFIG_CSPRING_ENABLED=y
CONFIG_HARDWARE_DEVICE_CS_GENERATOR=y
# CONFIG_CTR_DRBG_CSPRNG_GENERATOR is not set
# end of Random Number Generators

# CONFIG_RTIO is not set

#
# SD
#
# CONFIG_MMC_STACK is not set
# CONFIG_SDMMC_STACK is not set
# CONFIG_SDIO_STACK is not set
# end of SD

# CONFIG_SETTINGS is not set
# CONFIG_SHELL is not set
# CONFIG_STATS is not set

#
# Storage
#
# CONFIG_STREAM_FLASH is not set
# end of Storage

# CONFIG_TASK_WDT is not set

#
# Testing
#
# CONFIG_ZTEST is not set
# CONFIG_ZTEST_MOCKING is not set
# CONFIG_ZTRESS is not set
# CONFIG_TEST is not set
CONFIG_COVERAGE_GCOV_HEAP_SIZE=16384
# CONFIG_TEST_USERSPACE is not set
# end of Testing

# CONFIG_TIMING_FUNCTIONS is not set
# CONFIG_TRACING is not set
# CONFIG_USB_DEVICE_STACK is not set
# CONFIG_USB_DEVICE_STACK_NEXT is not set
# CONFIG_USB_HOST_STACK is not set
# CONFIG_USBC_STACK is not set
# CONFIG_ZBUS is not set
# end of Subsystems and OS Services

CONFIG_TOOLCHAIN_ZEPHYR_0_16=y
CONFIG_TOOLCHAIN_ZEPHYR_SUPPORTS_THREAD_LOCAL_STORAGE=y

#
# Build and Link Features
#

#
# Linker Options
#
# CONFIG_LINKER_ORPHAN_SECTION_PLACE is not set
CONFIG_LINKER_ORPHAN_SECTION_WARN=y
# CONFIG_LINKER_ORPHAN_SECTION_ERROR is not set
CONFIG_HAS_FLASH_LOAD_OFFSET=y
CONFIG_LD_LINKER_SCRIPT_SUPPORTED=y
CONFIG_LD_LINKER_TEMPLATE=y
# CONFIG_CMAKE_LINKER_GENERATOR is not set
# CONFIG_HAVE_CUSTOM_LINKER_SCRIPT is not set
CONFIG_KERNEL_ENTRY="__start"
CONFIG_LINKER_SORT_BY_ALIGNMENT=y
CONFIG_SRAM_OFFSET=0

#
# Linker Sections
#
# CONFIG_LINKER_USE_BOOT_SECTION is not set
# CONFIG_LINKER_USE_PINNED_SECTION is not set
CONFIG_LINKER_GENERIC_SECTIONS_PRESENT_AT_BOOT=y
CONFIG_LINKER_LAST_SECTION_ID=y
CONFIG_LINKER_LAST_SECTION_ID_PATTERN=0xE015E015
CONFIG_LINKER_USE_RELAX=y
# end of Linker Sections
# end of Linker Options

#
# Compiler Options
#
# CONFIG_CODING_GUIDELINE_CHECK is not set
# CONFIG_COMPILER_FREESTANDING is not set
CONFIG_SIZE_OPTIMIZATIONS=y
# CONFIG_SPEED_OPTIMIZATIONS is not set
# CONFIG_DEBUG_OPTIMIZATIONS is not set
# CONFIG_NO_OPTIMIZATIONS is not set
# CONFIG_COMPILER_WARNINGS_AS_ERRORS is not set
# CONFIG_COMPILER_SAVE_TEMPS is not set
CONFIG_COMPILER_TRACK_MACRO_EXPANSION=y
CONFIG_COMPILER_COLOR_DIAGNOSTICS=y
# CONFIG_FORTIFY_SOURCE_NONE is not set
CONFIG_FORTIFY_SOURCE_COMPILE_TIME=y
# CONFIG_FORTIFY_SOURCE_RUN_TIME is not set
CONFIG_COMPILER_OPT=""
# CONFIG_MISRA_SANE is not set
# end of Compiler Options

# CONFIG_ASSERT_ON_ERRORS is not set
# CONFIG_NO_RUNTIME_CHECKS is not set
CONFIG_RUNTIME_ERROR_CHECKS=y

#
# Build Options
#
CONFIG_KERNEL_BIN_NAME="zephyr"
CONFIG_OUTPUT_STAT=y
# CONFIG_OUTPUT_SYMBOLS is not set
# CONFIG_OUTPUT_DISASSEMBLY is not set
CONFIG_OUTPUT_PRINT_MEMORY_USAGE=y
# CONFIG_CLEANUP_INTERMEDIATE_FILES is not set
# CONFIG_BUILD_NO_GAP_FILL is not set
# CONFIG_BUILD_OUTPUT_EXE is not set
# CONFIG_BUILD_OUTPUT_S19 is not set
# CONFIG_BUILD_OUTPUT_UF2 is not set
# CONFIG_BUILD_OUTPUT_STRIPPED is not set
# CONFIG_BUILD_ALIGN_LMA is not set
# CONFIG_APPLICATION_DEFINED_SYSCALL is not set
# CONFIG_MAKEFILE_EXPORTS is not set
# CONFIG_BUILD_OUTPUT_META is not set
CONFIG_BUILD_OUTPUT_STRIP_PATHS=y
CONFIG_CHECK_INIT_PRIORITIES=y
# CONFIG_CHECK_INIT_PRIORITIES_FAIL_ON_WARNING is not set
# CONFIG_EMIT_ALL_SYSCALLS is not set
# end of Build Options

CONFIG_WARN_DEPRECATED=y
CONFIG_EXPERIMENTAL=y
CONFIG_ENFORCE_ZEPHYR_STDINT=y
# end of Build and Link Features

#
# Boot Options
#
# CONFIG_IS_BOOTLOADER is not set
# CONFIG_BOOTLOADER_BOSSA is not set
# end of Boot Options

#
# Compatibility
#
CONFIG_COMPAT_INCLUDES=y
# end of Compatibility
