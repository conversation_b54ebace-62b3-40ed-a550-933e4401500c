# ninja log v5
101	793	7766778601370515	zephyr/include/generated/ncs_version.h	c6e7626b1f1e14c9
101	793	7766778601370515	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/include/generated/ncs_version.h	c6e7626b1f1e14c9
117	1888	7766778613815228	modules/nrf/samples/hci_rpmsg_subimage-prefix/src/hci_rpmsg_subimage-stamp/hci_rpmsg_subimage-mkdir	2c806db01679bf3f
117	1888	7766778613815228	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/nrf/samples/hci_rpmsg_subimage-prefix/src/hci_rpmsg_subimage-stamp/hci_rpmsg_subimage-mkdir	2c806db01679bf3f
1889	2147	7766778616482338	modules/nrf/samples/hci_rpmsg_subimage-prefix/src/hci_rpmsg_subimage-stamp/hci_rpmsg_subimage-download	53693aa31d793cf0
1889	2147	7766778616482338	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/nrf/samples/hci_rpmsg_subimage-prefix/src/hci_rpmsg_subimage-stamp/hci_rpmsg_subimage-download	53693aa31d793cf0
2148	2745	7766778621921355	modules/nrf/samples/hci_rpmsg_subimage-prefix/src/hci_rpmsg_subimage-stamp/hci_rpmsg_subimage-update	190cd603eac6b71b
2148	2745	7766778621921355	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/nrf/samples/hci_rpmsg_subimage-prefix/src/hci_rpmsg_subimage-stamp/hci_rpmsg_subimage-update	190cd603eac6b71b
794	3455	7766778629256871	zephyr/include/generated/version.h	d767e5505717c091
794	3455	7766778629256871	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/include/generated/version.h	d767e5505717c091
2746	3644	7766778631533761	modules/nrf/samples/hci_rpmsg_subimage-prefix/src/hci_rpmsg_subimage-stamp/hci_rpmsg_subimage-patch	82cc5ac10d49c3f1
2746	3644	7766778631533761	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/nrf/samples/hci_rpmsg_subimage-prefix/src/hci_rpmsg_subimage-stamp/hci_rpmsg_subimage-patch	82cc5ac10d49c3f1
3644	3974	7766778634950599	modules/nrf/samples/hci_rpmsg_subimage-prefix/src/hci_rpmsg_subimage-stamp/hci_rpmsg_subimage-configure	436f654fb72eff48
3644	3974	7766778634950599	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/nrf/samples/hci_rpmsg_subimage-prefix/src/hci_rpmsg_subimage-stamp/hci_rpmsg_subimage-configure	436f654fb72eff48
134	12378	7766778707592245	tfm/CMakeCache.txt	984727f97c1bbf6c
134	12378	7766778707592245	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/CMakeCache.txt	984727f97c1bbf6c
12378	12817	7766778723519782	modules/trusted-firmware-m/tfm-prefix/src/tfm-stamp/tfm-mkdir	ecfba916e0ae1aa1
12378	12817	7766778723519782	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/trusted-firmware-m/tfm-prefix/src/tfm-stamp/tfm-mkdir	ecfba916e0ae1aa1
12817	12928	7766778724639030	modules/trusted-firmware-m/tfm-prefix/src/tfm-stamp/tfm-download	23232b6cc06dfcb4
12817	12928	7766778724639030	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/trusted-firmware-m/tfm-prefix/src/tfm-stamp/tfm-download	23232b6cc06dfcb4
12928	13028	7766778725648529	modules/trusted-firmware-m/tfm-prefix/src/tfm-stamp/tfm-update	13b5c095af210a13
12928	13028	7766778725648529	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/trusted-firmware-m/tfm-prefix/src/tfm-stamp/tfm-update	13b5c095af210a13
13028	13127	7766778726620206	modules/trusted-firmware-m/tfm-prefix/src/tfm-stamp/tfm-patch	a7cb207900598614
13028	13127	7766778726620206	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/trusted-firmware-m/tfm-prefix/src/tfm-stamp/tfm-patch	a7cb207900598614
13128	13370	7766778728985192	modules/trusted-firmware-m/tfm-prefix/src/tfm-stamp/tfm-configure	1e701482174e919b
13128	13370	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/trusted-firmware-m/tfm-prefix/src/tfm-stamp/tfm-configure	1e701482174e919b
72	37787	7766778972108417	zephyr/misc/generated/syscalls.json	123b2ce720bc8080
72	37787	7766778972108417	zephyr/misc/generated/struct_tags.json	123b2ce720bc8080
72	37787	7766778972108417	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/misc/generated/syscalls.json	123b2ce720bc8080
72	37787	7766778972108417	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/misc/generated/struct_tags.json	123b2ce720bc8080
37787	38318	7766778977187733	zephyr/include/generated/syscall_dispatch.c	dbc3f2178411e6fb
37787	38318	7766778977187733	zephyr/include/generated/syscall_list.h	dbc3f2178411e6fb
37787	38318	7766778977187733	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/include/generated/syscall_dispatch.c	dbc3f2178411e6fb
37787	38318	7766778977187733	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/include/generated/syscall_list.h	dbc3f2178411e6fb
37800	38467	7766778979794777	zephyr/include/generated/driver-validation.h	5a2c772ca286e03a
37800	38467	7766778979794777	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/include/generated/driver-validation.h	5a2c772ca286e03a
37813	38474	7766778979904820	zephyr/include/generated/kobj-types-enum.h	64547d94704ad957
37813	38474	7766778979904820	zephyr/include/generated/otype-to-str.h	64547d94704ad957
37813	38474	7766778979904820	zephyr/include/generated/otype-to-size.h	64547d94704ad957
37813	38474	7766778979904820	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/include/generated/kobj-types-enum.h	64547d94704ad957
37813	38474	7766778979904820	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/include/generated/otype-to-str.h	64547d94704ad957
37813	38474	7766778979904820	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/include/generated/otype-to-size.h	64547d94704ad957
3975	80932	7766778634950599	modules/nrf/samples/hci_rpmsg_subimage-prefix/src/hci_rpmsg_subimage-stamp/hci_rpmsg_subimage-build	f3d192657f11cafa
3975	80932	7766778634950599	hci_rpmsg/zephyr/zephyr.hex	f3d192657f11cafa
3975	80932	7766778634950599	hci_rpmsg/zephyr/zephyr.elf	f3d192657f11cafa
3975	80932	7766778634950599	hci_rpmsg/zephyr/merged_CPUNET.hex	f3d192657f11cafa
3975	80932	7766778634950599	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/nrf/samples/hci_rpmsg_subimage-prefix/src/hci_rpmsg_subimage-stamp/hci_rpmsg_subimage-build	f3d192657f11cafa
3975	80932	7766778634950599	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr.hex	f3d192657f11cafa
3975	80932	7766778634950599	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr.elf	f3d192657f11cafa
3975	80932	7766778634950599	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/merged_CPUNET.hex	f3d192657f11cafa
80933	81042	7766779405750089	modules/nrf/samples/hci_rpmsg_subimage-prefix/src/hci_rpmsg_subimage-stamp/hci_rpmsg_subimage-install	d789059e3d91b836
80933	81042	7766779405750089	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/nrf/samples/hci_rpmsg_subimage-prefix/src/hci_rpmsg_subimage-stamp/hci_rpmsg_subimage-install	d789059e3d91b836
81043	81168	7766779407043900	modules/nrf/samples/CMakeFiles/hci_rpmsg_subimage-complete	30444a705ab6998e
81043	81168	7766779407043900	modules/nrf/samples/hci_rpmsg_subimage-prefix/src/hci_rpmsg_subimage-stamp/hci_rpmsg_subimage-done	30444a705ab6998e
81043	81168	7766779407043900	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/nrf/samples/CMakeFiles/hci_rpmsg_subimage-complete	30444a705ab6998e
81043	81168	7766779407043900	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/nrf/samples/hci_rpmsg_subimage-prefix/src/hci_rpmsg_subimage-stamp/hci_rpmsg_subimage-done	30444a705ab6998e
80945	121169	7766778728985192	modules/trusted-firmware-m/tfm-prefix/src/tfm-stamp/tfm-build	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/secure_fw/s_veneers.o	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/tf-m-tests/app/libtfm_api_ns.a	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/generated/interface/include/psa_manifest/sid.h	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/platform/ns/libplatform_ns.a	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/bin/tfm_s.elf	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/bin/tfm_s.bin	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/bin/tfm_s.hex	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/bin/tfm_ns.bin	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/bin/tfm_ns.hex	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/bin/tfm_s_signed.bin	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/bin/tfm_ns_signed.bin	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/bin/tfm_s_ns_signed.bin	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/install/interface/src/tfm_attest_api.c	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/install/interface/src/tfm_crypto_api.c	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/install/interface/src/tfm_fwu_api.c	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/install/interface/src/tfm_its_api.c	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/install/interface/src/tfm_platform_api.c	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/install/interface/src/tfm_ps_api.c	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/install/interface/src/tfm_psa_ns_api.c	2159fecf5ee0bf24
80945	121169	7766778728985192	tfm/install/interface/src/tfm_ioctl_core_ns_api.c	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/trusted-firmware-m/tfm-prefix/src/tfm-stamp/tfm-build	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/secure_fw/s_veneers.o	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/tf-m-tests/app/libtfm_api_ns.a	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/generated/interface/include/psa_manifest/sid.h	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/platform/ns/libplatform_ns.a	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/bin/tfm_s.elf	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/bin/tfm_s.bin	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/bin/tfm_s.hex	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/bin/tfm_ns.bin	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/bin/tfm_ns.hex	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/bin/tfm_s_signed.bin	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/bin/tfm_ns_signed.bin	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/bin/tfm_s_ns_signed.bin	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/install/interface/src/tfm_attest_api.c	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/install/interface/src/tfm_crypto_api.c	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/install/interface/src/tfm_fwu_api.c	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/install/interface/src/tfm_its_api.c	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/install/interface/src/tfm_platform_api.c	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/install/interface/src/tfm_ps_api.c	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/install/interface/src/tfm_psa_ns_api.c	2159fecf5ee0bf24
80945	121169	7766778728985192	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/tfm/install/interface/src/tfm_ioctl_core_ns_api.c	2159fecf5ee0bf24
121171	121388	7766779809211343	zephyr/tfm_secure.hex	10e95c01b60367b7
121171	121388	7766779809211343	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/tfm_secure.hex	10e95c01b60367b7
121181	122009	7766779815490589	modules/trusted-firmware-m/tfm-prefix/src/tfm-stamp/tfm-install	2f048895969bda48
121181	122009	7766779815490589	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/trusted-firmware-m/tfm-prefix/src/tfm-stamp/tfm-install	2f048895969bda48
122009	122109	7766779816466257	modules/trusted-firmware-m/CMakeFiles/tfm-complete	3c41b1394fd0f372
122009	122109	7766779816466257	modules/trusted-firmware-m/tfm-prefix/src/tfm-stamp/tfm-done	3c41b1394fd0f372
122009	122109	7766779816466257	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/trusted-firmware-m/CMakeFiles/tfm-complete	3c41b1394fd0f372
122009	122109	7766779816466257	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/modules/trusted-firmware-m/tfm-prefix/src/tfm-stamp/tfm-done	3c41b1394fd0f372
122115	122625	7766779821126075	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/pkcs12.c.obj	21d6e0ef25521742
122120	122693	7766779821680934	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/pkparse.c.obj	4b7c182160fc2e35
122144	122785	7766779822695898	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/nrf/ext/oberon/psa/core/library/platform_util.c.obj	980cab6f95ebb2c7
122139	122886	7766779823370814	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/pkwrite.c.obj	c18a625112ffd215
122134	122895	7766779823490795	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/dhm.c.obj	1d48032b3db2cb50
122153	123049	7766779825316156	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/cipher.c.obj	7ff73574d9ddad20
122129	123184	7766779826552122	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/pkcs5.c.obj	58542a5f7d0747e
122125	123191	7766779826602115	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/pem.c.obj	6a14d3bb1f219fab
122109	123198	7766779826884800	zephyr/CMakeFiles/offsets.dir/arch/arm/core/offsets/offsets.c.obj	89b2c240ba46a1eb
122148	123266	7766779827286031	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/nrf/ext/oberon/psa/core/library/platform.c.obj	5a57823a2573fa77
122887	123529	7766779830352586	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/pk_wrap.c.obj	f5351c91cafca0b2
122694	123647	7766779830856205	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/hash_info.c.obj	836c04fdd52c447
122627	123654	7766779831239266	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/sha1.c.obj	696c34abacaca8cf
123050	123661	7766779831179268	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/pk.c.obj	fe2e74f49546aeec
122895	123668	7766779831542371	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/cipher_wrap.c.obj	32ddb53f057d69f9
122786	123953	7766779834302365	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/md.c.obj	422a9608631dfffe
123200	123959	7766779834038704	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/md5.c.obj	a606093f73793e6b
123185	124133	7766779836242470	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/sha256.c.obj	e8a144e740a268d8
123662	124168	7766779836162469	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/aria.c.obj	9bdd1f3b659e7da0
123530	124316	7766779838051232	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/aesni.c.obj	312e8e10c8ad5ad
123669	124323	7766779838021277	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/camellia.c.obj	12205097f9570cd5
123266	124459	7766779838933382	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/ripemd160.c.obj	bc27a334968f3a2e
123648	124545	7766779840043381	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/des.c.obj	407f9d42bd8725d0
123655	124644	7766779841544836	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/cmac.c.obj	3e4b64fc95476f11
123953	124736	7766779842071177	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/ccm.c.obj	31b4123808171bbc
123960	124831	7766779842998827	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/ecdsa.c.obj	c11652da6db779f8
123191	124925	7766779843360635	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/sha512.c.obj	c5683db7be9a62cc
124168	124976	7766779844444745	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/ecdh.c.obj	78f2d8c1284ee952
124317	125039	7766779845268391	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/gcm.c.obj	8cf099eaf283ed24
124134	125107	7766779845548411	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/chachapoly.c.obj	456a4f9d70327c3e
124545	125181	7766779845841896	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/ecp.c.obj	715ba6d8cdb928b4
124324	125320	7766779847895408	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/ecp_curves.c.obj	71eb7cb3431ce76e
124831	125339	7766779848163897	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/rsa_alt_helpers.c.obj	4b3ea22111e2f8d0
124737	125414	7766779848678005	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/ctr_drbg.c.obj	ae11b0f770067f17
124460	125505	7766779848949776	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/hmac_drbg.c.obj	be97dc36c3d356bf
124925	125512	7766779848929794	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/entropy.c.obj	e59a102f4d0b4007
124644	125604	7766779850440370	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/hkdf.c.obj	1a64651e419d7990
125320	125911	7766779853668030	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/legacy/aes_oberon.c.obj	358da0f31a3b1105
125039	125925	7766779853849653	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/rsa.c.obj	766609e25396889f
125339	125981	7766779854663111	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/legacy/ecjpake_oberon.c.obj	ac84cc8579616dd1
124976	126021	7766779854943116	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto.dir/legacy/aes.c.obj	25b5703e157e7ce3
125107	126111	7766779855630217	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto_base.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/asn1parse.c.obj	6285b0551dad163e
125513	126212	7766779856593902	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto_base.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/nist_kw.c.obj	224e3c5408e4cc71
125415	126410	7766779858943054	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto_base.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/base64.c.obj	bb94cfa93e7e658a
125182	126413	7766779859033049	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto_base.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/asn1write.c.obj	371a327d718bbf9b
125605	126657	7766779861399153	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto_base.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/oid.c.obj	d5a269c0bfbfafb
125926	126763	7766779862486574	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto_base.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/version.c.obj	1b01e0124a3823b7
126021	126877	7766779863802530	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto_base.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/padlock.c.obj	6ae969507d8a378d
125981	127007	7766779865395229	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto_base.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/constant_time.c.obj	71600576d0ba443
125911	127126	7766779866360722	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto_base.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/bignum_core.c.obj	77d06278be35f523
125506	127563	7766779871003324	modules/nrf/subsys/nrf_security/src/CMakeFiles/mbedcrypto_base.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/bignum.c.obj	79b98ea6645d574a
127564	127735	7766779872737977	modules/nrf/subsys/nrf_security/src/libmbedcrypto_base.a	937c78e675f6d126
127735	127960	7766779874989570	modules/nrf/subsys/nrf_security/src/libmbedcrypto.a	54ed5a023a892021
127960	128245	7766779877789836	zephyr/include/generated/offsets.h	62c4108493b0d380
127960	128245	7766779877789836	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/include/generated/offsets.h	62c4108493b0d380
128272	128817	7766779883062621	zephyr/CMakeFiles/zephyr.dir/lib/crc/crc32_sw.c.obj	6140986e56db982a
128267	128824	7766779883062621	zephyr/CMakeFiles/zephyr.dir/lib/crc/crc7_sw.c.obj	ef2f9b97fa5eff4e
128246	128843	7766779882942662	zephyr/CMakeFiles/zephyr.dir/lib/crc/crc32c_sw.c.obj	d5bdb5a0fcb6493a
128287	128850	7766779883657798	zephyr/CMakeFiles/zephyr.dir/lib/os/multi_heap.c.obj	c7eb98cb64a4c5c5
128256	128940	7766779884279376	zephyr/CMakeFiles/zephyr.dir/lib/crc/crc8_sw.c.obj	abf6654d9302f6e7
128282	129074	7766779884868345	zephyr/CMakeFiles/zephyr.dir/lib/os/dec.c.obj	abcccef748a6a49
128251	129166	7766779884888435	zephyr/CMakeFiles/zephyr.dir/lib/crc/crc16_sw.c.obj	cc42e0df5a8979bb
128292	129441	7766779889569533	zephyr/CMakeFiles/zephyr.dir/lib/os/bitarray.c.obj	a64e826402d975c5
128277	129589	7766779891086264	zephyr/CMakeFiles/zephyr.dir/lib/os/fdtable.c.obj	ffeaabf1d02ccf2e
128261	129942	7766779894056171	zephyr/CMakeFiles/zephyr.dir/lib/os/cbprintf_packaged.c.obj	1c04e9cc279a10f4
128843	130096	7766779895627210	zephyr/CMakeFiles/zephyr.dir/lib/os/notify.c.obj	f27ac90de45a8071
129074	130133	7766779895807215	zephyr/CMakeFiles/zephyr.dir/lib/os/sem.c.obj	9e386fb2ec4feb91
128850	130318	7766779897904493	zephyr/CMakeFiles/zephyr.dir/lib/os/onoff.c.obj	f47d923708c11bf9
128817	130327	7766779897914500	zephyr/CMakeFiles/zephyr.dir/lib/os/heap.c.obj	63a522868276041d
129590	130464	7766779899355382	zephyr/CMakeFiles/zephyr.dir/lib/os/hex.c.obj	c2c2dc86be2e09ec
128940	130544	7766779899865409	zephyr/CMakeFiles/zephyr.dir/lib/os/rb.c.obj	6fff78ce2a052fd4
129441	130568	7766779900879902	zephyr/CMakeFiles/zephyr.dir/lib/os/thread_entry.c.obj	e34b57a9b723966f
129166	130685	7766779901542900	zephyr/CMakeFiles/zephyr.dir/lib/os/printk.c.obj	84139ee76e764061
128824	130691	7766779901882899	zephyr/CMakeFiles/zephyr.dir/lib/os/heap-validate.c.obj	6d7d80879d9dcbf0
129942	130810	7766779902985720	zephyr/CMakeFiles/zephyr.dir/lib/os/timeutil.c.obj	ec6bc32b83ffa368
130133	130855	7766779903025728	zephyr/CMakeFiles/zephyr.dir/misc/generated/configs.c.obj	46c54975498e780e
130319	131487	7766779909796887	zephyr/CMakeFiles/zephyr.dir/lib/os/reboot.c.obj	aee87c950530ad06
130464	131632	7766779911200722	zephyr/CMakeFiles/zephyr.dir/lib/os/assert.c.obj	fc06f4fa19f77398
130692	131696	7766779911774815	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/validate_base_addresses.c.obj	49ccef5e160a97c3
130811	131717	7766779912069204	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/common/soc_nrf_common.S.obj	25d40f8497a27ba
130097	131838	7766779913380791	zephyr/CMakeFiles/zephyr.dir/lib/os/cbprintf_complete.c.obj	aa6ad8e4f6076e2c
130327	131895	7766779913712870	zephyr/CMakeFiles/zephyr.dir/lib/os/mpsc_pbuf.c.obj	c546456b9cc99062
130544	132048	7766779915292816	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/nrf53/sync_rtc.c.obj	5c51703009b5b775
131697	132864	7766779923420386	zephyr/CMakeFiles/zephyr.dir/subsys/logging/log_cache.c.obj	4f4468a30edf5db4
130685	132901	7766779924007969	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/common/soc_secure.c.obj	331e7bc05fde7ef9
131717	132961	7766779924352561	zephyr/CMakeFiles/zephyr.dir/subsys/storage/flash_map/flash_map.c.obj	ca945ec05ddf8b42
131488	133067	7766779925106557	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/validate_enabled_instances.c.obj	d5b67395fa1627f1
131633	133248	7766779927034418	zephyr/CMakeFiles/zephyr.dir/subsys/logging/backends/log_backend_rtt.c.obj	a5658a80f0b1899d
130855	133553	7766779930340222	zephyr/CMakeFiles/zephyr.dir/subsys/fs/nvs/nvs.c.obj	e9e5c93af96d4a58
131896	133669	7766779931584280	zephyr/CMakeFiles/zephyr.dir/subsys/logging/log_output.c.obj	1692b8ca58968127
130568	134006	7766779934940707	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/nrf53/soc.c.obj	b2795108404266f9
131839	134067	7766779935450736	zephyr/CMakeFiles/zephyr.dir/subsys/logging/log_msg.c.obj	b0ca7df1606d409c
132902	134223	7766779936901400	zephyr/CMakeFiles/zephyr.dir/subsys/ipc/ipc_service/lib/ipc_static_vrings.c.obj	ff1b12c571be4196
132049	134246	7766779937113289	zephyr/CMakeFiles/zephyr.dir/subsys/ipc/ipc_service/ipc_service.c.obj	eb2901b067382a62
133553	134259	7766779937429507	zephyr/CMakeFiles/zephyr.dir/subsys/tracing/tracing_none.c.obj	3cdee35156d3c2d1
132961	134334	7766779937828132	zephyr/CMakeFiles/zephyr.dir/subsys/ipc/ipc_service/lib/ipc_rpmsg.c.obj	e2619e1f5a4727d4
133068	134907	7766779943973093	zephyr/CMakeFiles/zephyr.dir/subsys/logging/log_core.c.obj	4dc075e7102ca610
134224	134936	7766779944334594	zephyr/CMakeFiles/zephyr.dir/subsys/net/lib/utils/addr_utils.c.obj	30b7e849e57ab54d
133249	134989	7766779944864596	zephyr/CMakeFiles/zephyr.dir/subsys/logging/log_mgmt.c.obj	7b9950f8765f48ff
132864	135197	7766779946719053	zephyr/CMakeFiles/zephyr.dir/subsys/ipc/ipc_service/backends/ipc_rpmsg_static_vrings.c.obj	acc94c2527f45d78
133669	135253	7766779947101871	zephyr/CMakeFiles/zephyr.dir/subsys/bluetooth/services/dis.c.obj	a3eaee08af38e923
134092	135260	7766779947286946	zephyr/CMakeFiles/zephyr.dir/subsys/storage/flash_map/flash_map_layout.c.obj	b7a073e162b2f40
134260	135329	7766779947611414	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/nrf/subsys/partition_manager/flash_map_partition_manager.c.obj	681e3198f6b6809d
134007	135443	7766779949077700	zephyr/CMakeFiles/zephyr.dir/subsys/settings/src/settings_store.c.obj	7351bb2bd78f3bc4
134907	135722	7766779952364767	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/tinycrypt/lib/source/utils.c.obj	161c096e73f78015
134334	135929	7766779954107453	zephyr/CMakeFiles/zephyr.dir/subsys/settings/src/settings_line.c.obj	6d2c811e19ef8c7b
134246	135994	7766779955214634	zephyr/CMakeFiles/zephyr.dir/subsys/settings/src/settings.c.obj	6724bad33c8f341a
135261	136019	7766779955234583	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/tinycrypt/lib/source/hmac.c.obj	1110caaeb924c312
135197	136066	7766779955528850	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/tinycrypt/lib/source/aes_decrypt.c.obj	48089c89744b8c6
135253	136073	7766779955548819	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/tinycrypt/lib/source/cmac_mode.c.obj	ab222a2004299c0e
135329	136123	7766779955689217	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/tinycrypt/lib/source/aes_encrypt.c.obj	a765f9ab754daf75
134937	136157	7766779956407824	zephyr/CMakeFiles/zephyr.dir/subsys/settings/src/settings_init.c.obj	a54780370b2e43a4
134989	136175	7766779956557244	zephyr/CMakeFiles/zephyr.dir/subsys/settings/src/settings_nvs.c.obj	f086a8068347ccfb
136074	136474	7766779959607202	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/vector_table.S.obj	cf8974cdc36399a0
135723	136523	7766779959647257	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/tinycrypt/lib/source/hmac_prng.c.obj	698ed635af088a47
135444	136550	7766779960622261	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/tinycrypt/lib/source/sha256.c.obj	39a2cf8030d737d0
136020	136715	7766779961473173	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/__aeabi_read_tp.S.obj	7acedc032707044
136123	136763	7766779961463172	zephyr/subsys/bluetooth/common/CMakeFiles/subsys__bluetooth__common.dir/addr.c.obj	f24e208f5d493c4
135929	137060	7766779965301424	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/scb.c.obj	a659a2ae435a02e1
136067	137179	7766779966700403	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/thread_abort.c.obj	78ef5c3145b2805b
135995	137521	7766779964779576	zephyr/linker_zephyr_pre0.cmd	295efb6df19a1536
135995	137521	7766779964779576	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/linker_zephyr_pre0.cmd	295efb6df19a1536
136213	137555	7766779969940664	zephyr/subsys/bluetooth/common/CMakeFiles/subsys__bluetooth__common.dir/dummy.c.obj	1b54c4b5a615939e
136157	137590	7766779970120674	zephyr/subsys/bluetooth/common/CMakeFiles/subsys__bluetooth__common.dir/bt_str.c.obj	20b06180df40784c
136475	137810	7766779972870320	zephyr/subsys/bluetooth/common/CMakeFiles/subsys__bluetooth__common.dir/rpa.c.obj	d294209bacad0f04
136764	137854	7766779973040317	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/buf.c.obj	82c621b4f20689e9
137061	137894	7766779973400321	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/uuid.c.obj	5c1a44b57bc5aefa
136715	137900	7766779973420322	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/long_wq.c.obj	1cc97e66d0239ffe
137180	138103	7766779976115014	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/addr.c.obj	90ef454987a9ee8a
136523	138342	7766779978297879	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/settings.c.obj	407477959dced5ba
137591	138528	7766779980210785	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/hci_common.c.obj	77773654b9af0e31
137555	138943	7766779984069723	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/data.c.obj	9bbade3d31234ba0
137810	139062	7766779983579688	zephyr/subsys/bluetooth/common/libsubsys__bluetooth__common.a	d955646107234d60
136550	139140	7766779984575741	zephyr/libzephyr.a	4e7693a7f34a763f
137894	139433	7766779988575535	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/crypto.c.obj	45cf35b12a8e5e09
137900	139744	7766779991864032	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/ecc.c.obj	9e2513069e593967
138104	141899	7766780012825263	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/l2cap.c.obj	22289fdb4354d15c
138528	142328	7766780017684202	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/adv.c.obj	f9b8107bb08a6823
139063	142894	7766780022932692	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/keys.c.obj	aa3eea1384a0a834
137854	142974	7766780024502719	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/id.c.obj	a071f5c44fefea44
139745	143186	7766780026638750	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/direction.c.obj	87ed936d8533c74c
138343	143921	7766780033765052	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/conn.c.obj	258b6e7240fd78c1
143922	144873	7766780043736028	CMakeFiles/app.dir/src/accelerometer/vital_signs_acc_osal_template.c.obj	aa58c27e31d6b43
142329	144961	7766780043975985	zephyr/subsys/net/CMakeFiles/subsys__net.dir/buf_simple.c.obj	12b2b24b4aca541c
143187	145223	7766780047060780	CMakeFiles/app.dir/src/accelerometer/vital_signs_accelerometer.c.obj	65856b46a688d3f5
137522	145371	7766780048494581	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/hci_core.c.obj	6d4450a26828704c
142895	145423	7766780049064608	zephyr/subsys/net/CMakeFiles/subsys__net.dir/buf.c.obj	1595ac7272a31486
141900	146155	7766780055879963	zephyr/subsys/bluetooth/crypto/CMakeFiles/subsys__bluetooth__crypto.dir/bt_crypto.c.obj	4854109f20210626
144961	146197	7766780056500325	CMakeFiles/app.dir/src/chiplib/as7058_bioz_measurement.c.obj	392651507900d8c3
144874	146278	7766780057571094	CMakeFiles/app.dir/src/chiplib/as7058_agc_hal.c.obj	6e58fb1b795c6162
139141	146564	7766780059851412	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/gatt.c.obj	6ad0c8d69d6435e7
145372	146611	7766780061262429	CMakeFiles/app.dir/src/chiplib/as7058_extract2.c.obj	5e4a3563c7249170
146197	147307	7766780068050720	CMakeFiles/app.dir/src/chiplib/as7058_eda_scaling.c.obj	ef45052bdb378698
145223	147371	7766780068693533	CMakeFiles/app.dir/src/chiplib/agc.c.obj	c1212a403aff5452
146611	147441	7766780069252716	CMakeFiles/app.dir/src/chiplib/as7058_extract.c.obj	eb4cefdeb33c4ab5
146565	147902	7766780074032689	CMakeFiles/app.dir/src/chiplib/as7058_interface.c.obj	2464f37955c9d895
145424	147939	7766780072892698	zephyr/subsys/net/libsubsys__net.a	f93de3af22efd25f
146156	147977	7766780073732687	zephyr/subsys/bluetooth/crypto/libsubsys__bluetooth__crypto.a	fca2a53814d4346a
147372	147984	7766780074829517	CMakeFiles/app.dir/src/sample_code/as7058a_sample_code_spo2.c.obj	4c2dc750efb9e05c
146278	148113	7766780076065284	CMakeFiles/app.dir/src/chiplib/as7058_chiplib.c.obj	92dfe01c9de15fd
147307	148347	7766780078234430	CMakeFiles/app.dir/src/chiplib/as7058_osal_chiplib_template.c.obj	939d49bf384591c
147441	148391	7766780078774445	CMakeFiles/app.dir/src/chiplib/as7058_pd_offset_calibration.c.obj	a67f901e1dbfd5e1
147940	148399	7766780078564420	CMakeFiles/app.dir/src/sample_code/as7058a_sample_code_hrm.c.obj	af78e73b4e10a94b
142975	148406	7766780078214418	CMakeFiles/app.dir/src/main.c.obj	101f4f505f456c29
147977	148433	7766780079004429	CMakeFiles/app.dir/src/sample_code/as7058a_sample_code_rrm.c.obj	16065ff35b56114
138944	148622	7766780080732575	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/smp.c.obj	df0bce151ceae751
148113	148981	7766780084427315	CMakeFiles/app.dir/src/sp02/AWT_AS7058_SPO2.c.obj	ee77be3147dea1e0
147903	149065	7766780085294899	CMakeFiles/app.dir/src/chiplib/lis2dh12.c.obj	f569c1e9940e2df1
139434	149309	7766780087868938	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/att.c.obj	18560a086815e4f0
148623	149379	7766780088549344	zephyr/CMakeFiles/zephyr_pre0.dir/misc/empty_file.c.obj	14f32ab2c5b5f03e
148434	149623	7766780091021042	CMakeFiles/app.dir/src/HR/Awt_Hr_Algo.c.obj	ef627ed220dd774b
147984	149707	7766780091291108	CMakeFiles/app.dir/src/chiplib/lis2dh12_reg.c.obj	716728a8a2e63f44
148391	149792	7766780092931767	CMakeFiles/app.dir/src/vitalsigns_algorithms/spo2/src/adcData.c.obj	9b843358c9c3048e
148348	149841	7766780093221766	CMakeFiles/app.dir/src/BLE/Awt_Ble_service.c.obj	135549ee05b01acd
149065	149966	7766780094473502	zephyr/drivers/adc/CMakeFiles/drivers__adc.dir/adc_common.c.obj	3298badbb90c2bcf
148407	149994	7766780094693503	CMakeFiles/app.dir/src/vitalsigns_algorithms/spo2/src/spo2.c.obj	f3d67c24fb9f6e3b
148399	150300	7766780097541441	CMakeFiles/app.dir/src/HR/Heart_rate.c.obj	593c1a34a239076e
149841	150757	7766780102132555	zephyr/drivers/entropy/CMakeFiles/drivers__entropy.dir/entropy_psa_crypto.c.obj	c5a64dc56f9764b6
149793	150943	7766780103986233	zephyr/drivers/console/CMakeFiles/drivers__console.dir/rtt_console.c.obj	55dcc944f535cefd
148982	150984	7766780104267222	zephyr/drivers/serial/CMakeFiles/drivers__serial.dir/uart_nrfx_uarte.c.obj	99402ccf078b60c5
149707	151118	7766780105273894	zephyr/drivers/clock_control/CMakeFiles/drivers__clock_control.dir/clock_control_nrf.c.obj	ecb7616225dcceb1
149966	151320	7766780108310802	zephyr/drivers/entropy/CMakeFiles/drivers__entropy.dir/entropy_bt_hci.c.obj	6c305f57f8eb18bd
149309	151369	7766780107584613	zephyr/subsys/bluetooth/host/libsubsys__bluetooth__host.a	c8e4f6380fceefbc
149380	151377	7766780108489604	zephyr/drivers/adc/CMakeFiles/drivers__adc.dir/adc_nrfx_saadc.c.obj	469c2ca316fe880c
149995	151424	7766780109222707	zephyr/drivers/flash/CMakeFiles/drivers__flash.dir/jesd216.c.obj	7c6c48a2f7fee9d6
150301	151672	7766780110994678	app/libapp.a	b10be07f0ebc08db
149623	151966	7766780114354601	zephyr/drivers/bluetooth/CMakeFiles/drivers__bluetooth.dir/hci/rpmsg.c.obj	fd5f8123ea1ba1a3
151424	152007	7766780114920149	zephyr/drivers/flash/CMakeFiles/drivers__flash.dir/flash_page_layout.c.obj	608eff13bf75e856
151119	152176	7766780114900153	zephyr/drivers/clock_control/libdrivers__clock_control.a	c9d276d007ad2eb7
150944	152282	7766780115950948	zephyr/drivers/console/libdrivers__console.a	163f4a07cbd87468
150985	152338	7766780116436413	zephyr/drivers/serial/libdrivers__serial.a	2a412403eeb555f8
151377	152523	7766780117718233	zephyr/drivers/adc/libdrivers__adc.a	f98db410e6028b51
151320	152724	7766780119343709	zephyr/drivers/entropy/libdrivers__entropy.a	b6f73f287c3dfaaa
151369	153697	7766780130687040	zephyr/drivers/flash/CMakeFiles/drivers__flash.dir/soc_flash_nrf.c.obj	b99d81144e91d425
152338	153705	7766780130637044	zephyr/drivers/pinctrl/CMakeFiles/drivers__pinctrl.dir/common.c.obj	ab097193937a4905
150758	154013	7766780134945638	zephyr/drivers/flash/CMakeFiles/drivers__flash.dir/nrf_qspi_nor.c.obj	c7c171e48bb4dcb3
152007	154077	7766780134640569	zephyr/drivers/i2c/CMakeFiles/drivers__i2c.dir/i2c_common.c.obj	bfae80df4ae49658
151966	154343	7766780133830043	zephyr/drivers/bluetooth/libdrivers__bluetooth.a	621cc0332f6056db
152283	154388	7766780138790496	zephyr/drivers/mbox/CMakeFiles/drivers__mbox.dir/mbox_nrfx_ipc.c.obj	ec20eb20a04282cd
152724	154583	7766780140805583	zephyr/drivers/timer/CMakeFiles/drivers__timer.dir/nrf_rtc_timer.c.obj	12664a53c7e74727
152177	154887	7766780143153199	zephyr/drivers/i2c/CMakeFiles/drivers__i2c.dir/i2c_nrfx_twim.c.obj	6fb645eab6fd2697
152523	155123	7766780145539914	zephyr/drivers/pinctrl/CMakeFiles/drivers__pinctrl.dir/pinctrl_nrf.c.obj	74ef785d9eb802da
154343	155162	7766780146820667	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/fault_s.S.obj	8ded366ea1f48616
153705	155256	7766780147348889	modules/nrf/lib/fatal_error/CMakeFiles/..__nrf__lib__fatal_error.dir/fatal_error.c.obj	6d467e6068abd5d6
154077	155285	7766780147662996	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/reset.S.obj	8ea2f701101d7f02
153697	155291	7766780147510468	zephyr/drivers/timer/CMakeFiles/drivers__timer.dir/sys_clock_init.c.obj	4dd01b7ada390ae5
154014	155430	7766780148237199	zephyr/drivers/flash/libdrivers__flash.a	8d495f29fe08117a
154583	155575	7766780150504166	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/irq_init.c.obj	1ac03a4858029ed9
151672	155625	7766780151055479	zephyr/drivers/gpio/CMakeFiles/drivers__gpio.dir/gpio_nrfx.c.obj	96a1cd94c67aad52
155163	155999	7766780154968157	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/fpu.c.obj	6b54fdcc3e0ac476
155256	156041	7766780154568161	modules/nrf/lib/fatal_error/lib..__nrf__lib__fatal_error.a	dd301dbcf112783f
154389	156047	7766780154488155	zephyr/drivers/mbox/libdrivers__mbox.a	8e662a5bba5dda6b
155285	156067	7766780155602988	zephyr/arch/common/CMakeFiles/isr_tables.dir/isr_tables.c.obj	4561424f2c75c1bf
155292	156080	7766780154678187	zephyr/drivers/timer/libdrivers__timer.a	a343f6265a2ad3d9
154887	156086	7766780154608166	zephyr/drivers/i2c/libdrivers__i2c.a	a59ba94988dea2d0
155430	156137	7766780156386663	zephyr/arch/common/CMakeFiles/arch__common.dir/sw_isr_common.c.obj	3b01c3bfc0258795
155124	156156	7766780155652989	zephyr/drivers/pinctrl/libdrivers__pinctrl.a	6b5d0066f76ba9a8
155999	156353	7766780158573218	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/cpu_idle.S.obj	a8c69851b0660b8a
156048	156610	7766780160980400	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/nmi_on_reset.S.obj	91063ad8e3206
156156	156623	7766780160990451	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/swap_helper.S.obj	f8e1a2c7a8ddf680
155625	156776	7766780161000421	zephyr/drivers/gpio/libdrivers__gpio.a	c0896c6c826c5fd2
155576	157106	7766780166128327	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/fatal.c.obj	5eae13de60c12a0e
156081	157142	7766780166292742	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/swap.c.obj	88acc0412ee23762
156138	157192	7766780166855697	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/nmi.c.obj	f8dcb4b429b69604
156086	157279	7766780166865715	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/prep_c.c.obj	c351d2c11cec1413
156068	157286	7766780166302743	zephyr/arch/common/libisr_tables.a	888c6013ec37b842
156042	157344	7766780168352869	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/irq_manage.c.obj	a8a1d5d06da8c749
156610	157476	7766780169561853	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/isr_wrapper.S.obj	b71233b541ac48dd
156353	157535	7766780169611950	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/thread.c.obj	e710a5fc3dceb52a
156776	157600	7766780170695512	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/exc_exit.S.obj	27151666231e841d
156624	157814	7766780173226000	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/C_/ncs/v2.5.99-dev1/zephyr/arch/arm/core/common/tls.c.obj	4355768b8713b512
157193	158513	7766780179852029	zephyr/arch/arch/arm/core/aarch32/mpu/CMakeFiles/arch__arm__core__aarch32__mpu.dir/arm_core_mpu.c.obj	1d1c7d1fba3e783
157142	158522	7766780179852029	zephyr/arch/arch/arm/core/aarch32/cortex_m/cmse/CMakeFiles/arch__arm__core__aarch32__cortex_m__cmse.dir/arm_core_cmse.c.obj	e76ad89c686ccbfb
157345	158556	7766780180392551	zephyr/lib/libc/picolibc/CMakeFiles/lib__libc__picolibc.dir/libc-hooks.c.obj	8319ae2624524eaa
157477	158675	7766780181449851	zephyr/lib/libc/common/CMakeFiles/lib__libc__common.dir/source/stdlib/abort.c.obj	b696828ae1ddf8dd
157536	158857	7766780183557007	zephyr/lib/libc/common/CMakeFiles/lib__libc__common.dir/source/stdlib/malloc.c.obj	2f00465a1fc476c7
157286	159040	7766780183946998	zephyr/arch/common/libarch__common.a	d9e4650e9fc87651
157106	159125	7766780185750567	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/fault.c.obj	bd67bccba05ed752
157280	159140	7766780185863295	zephyr/arch/arch/arm/core/aarch32/mpu/CMakeFiles/arch__arm__core__aarch32__mpu.dir/arm_mpu.c.obj	414ae84beb724e9d
157600	159229	7766780187464653	zephyr/boards/arm/nrf5340dk_nrf5340/CMakeFiles/boards__arm__nrf5340dk_nrf5340.dir/nrf5340_cpunet_reset.c.obj	8c6609ddae42047d
158514	159404	7766780189134250	zephyr/soc/soc/arm/common/cortex_m/CMakeFiles/soc__arm__common__cortex_m.dir/arm_mpu_regions.c.obj	90f737a460bbe63e
158556	159501	7766780189177977	zephyr/lib/libc/picolibc/liblib__libc__picolibc.a	3d7568bb104c6805
158522	159527	7766780189918705	zephyr/arch/arch/arm/core/aarch32/cortex_m/cmse/libarch__arm__core__aarch32__cortex_m__cmse.a	1a6a9b25f733497f
157814	159590	7766780190436944	zephyr/arch/arch/arm/core/aarch32/libarch__arm__core__aarch32.a	ed12d847a534d955
158858	159707	7766780190696936	zephyr/lib/libc/common/liblib__libc__common.a	3f72ef3f796a90f1
159040	159795	7766780193058483	zephyr/subsys/random/CMakeFiles/subsys__random.dir/rand32_entropy_device.c.obj	494f31485a2a2aff
158676	159850	7766780192718485	zephyr/subsys/random/CMakeFiles/subsys__random.dir/rand32_xoshiro128.c.obj	1986364716c29ce7
159229	159991	7766780193268484	zephyr/boards/arm/nrf5340dk_nrf5340/libboards__arm__nrf5340dk_nrf5340.a	d36fc51ceab0dc33
159140	159996	7766780193458484	zephyr/arch/arch/arm/core/aarch32/mpu/libarch__arm__core__aarch32__mpu.a	f740e9bed1b5a594
159502	160027	7766780195052900	modules/nrf/subsys/nrf_security/src/zephyr/CMakeFiles/mbedtls_zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/mbedtls/library/platform.c.obj	944bfac1be70a9cb
159126	160055	7766780194844093	zephyr/arch/arch/arm/core/aarch32/cortex_m/libarch__arm__core__aarch32__cortex_m.a	3acc7edeef0316b2
159707	160113	7766780195625170	modules/trusted-firmware-m/CMakeFiles/tfm_api.dir/src/zephyr_tfm_log.c.obj	2c4c4261d2410dd3
159404	160351	7766780195835168	zephyr/soc/soc/arm/common/cortex_m/libsoc__arm__common__cortex_m.a	612c3647cc6b280f
159796	160486	7766780199673420	modules/trusted-firmware-m/CMakeFiles/tfm_api.dir/__/__/tfm/install/interface/src/tfm_platform_api.c.obj	f003fa8f528ad239
159590	160636	7766780200519971	modules/trusted-firmware-m/CMakeFiles/tfm_api.dir/interface/interface.c.obj	9d1c44a6b3994b80
159991	160681	7766780201436136	modules/trusted-firmware-m/CMakeFiles/tfm_api.dir/__/__/tfm/install/interface/src/tfm_psa_ns_api.c.obj	87adafa142849044
160056	160887	7766780203334689	modules/trusted-firmware-m/CMakeFiles/tfm_api.dir/__/__/tfm/install/interface/src/tfm_ioctl_core_ns_api.c.obj	a2705a75ab4a26f9
159851	160992	7766780203364608	zephyr/subsys/random/libsubsys__random.a	3a975e5901c1c0df
160114	161173	7766780206393414	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/nrfx_glue.c.obj	1ec28026ae9dbdd0
160486	161267	7766780206563446	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/mdk/system_nrf5340_application.c.obj	4abfc0e06e3affdb
160352	161435	7766780209074256	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/helpers/nrfx_flag32_allocator.c.obj	e04ed4aea4b88904
159997	161628	7766780211245520	modules/trusted-firmware-m/CMakeFiles/tfm_api.dir/__/__/tfm/install/interface/src/tfm_crypto_api.c.obj	7e44fcaac4ce23cc
159527	161703	7766780211625506	modules/nrf/modules/tfm/zephyr/CMakeFiles/tfm_api_nrf.dir/C_/ncs/v2.5.99-dev1/nrf/modules/tfm/tfm/boards/src/tfm_ioctl_ns_api.c.obj	936ab0769319edd6
160028	161758	7766780210873010	modules/nrf/subsys/nrf_security/src/zephyr/libmbedtls_zephyr.a	d77630dfdaecf8af
160682	162151	7766780215736337	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/helpers/nrfx_gppi_dppi.c.obj	215ffe53da8c3b09
160636	162217	7766780216602513	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_clock.c.obj	557d3128d87c69c2
161268	162422	7766780219271207	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_nvmc.c.obj	6f3316984218017c
160888	162486	7766780219621234	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_ipc.c.obj	88b5aacc3fc03fb6
161173	163522	7766780229665935	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_dppi.c.obj	46e559f40a3aa06b
162487	163661	7766780231105088	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/init.c.obj	70de0090e2ca1fa6
161628	163975	7766780232938735	modules/trusted-firmware-m/libtfm_api.a	80467b3f1acba86a
161703	164112	7766780233413520	modules/nrf/modules/tfm/zephyr/libtfm_api_nrf.a	dff614fdefcb6e45
162423	164195	7766780236533336	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/dma.c.obj	192a2d9d959d6a16
162218	164226	7766780236493479	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/device.c.obj	d9b72d900e1b8de0
161436	164357	7766780238308668	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_qspi.c.obj	1686ec16e87a8ab2
163522	164572	7766780240579126	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/log.c.obj	da68097e412f237e
162151	164806	7766780242645600	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_twi_twim.c.obj	a9e71709f0fced84
163661	165170	7766780246343347	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/shmem.c.obj	9189a803036489a
164226	165208	7766780246855394	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/version.c.obj	4ca86991500ba569
164112	165341	7766780248299408	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/irq.c.obj	62743fc2d4a7db4b
164196	165487	7766780249455435	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/alloc.c.obj	13d35edb408b7775
163975	165877	7766780253566944	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/io.c.obj	c39c832aa7f28cb2
164358	165885	7766780253636900	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/condition.c.obj	5d6f5a57c81c5b77
164573	166339	7766780258121603	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/device.c.obj	99745a81f3b81532
164807	166425	7766780258758443	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/softirq.c.obj	425282b8f6c27a31
165171	166608	7766780260451879	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/irq.c.obj	9c680057fadb6485
165209	166776	7766780262560649	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/time.c.obj	8dbae61c7410e416
165341	166852	7766780263500652	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/log.c.obj	665115d18c87dce2
165488	167050	7766780265514970	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/shmem.c.obj	f112b08acaf51596
166340	167166	7766780266562212	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/version.c.obj	24e5d248ada1880e
160992	167229	7766780267474904	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_gpiote.c.obj	5affb7f81f2fbd33
165878	167243	7766780267634931	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/sys.c.obj	f3d0eb215dcbffa1
165885	167418	7766780269346007	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/init.c.obj	88d1cfee9c1c1010
161759	167554	7766780270300774	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_twim.c.obj	188dff6ab0ad2270
166608	168042	7766780275359843	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/rpmsg/rpmsg.c.obj	7d2b39bc4008e9ca
166777	168134	7766780276154246	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/virtio/virtio.c.obj	b8e71757c69b733c
166426	168186	7766780276514351	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/virtio/virtqueue.c.obj	5f05ac957f59350f
167167	168295	7766780277885601	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/remoteproc/rsc_table_parser.c.obj	9752ccdeecd46247
167050	168779	7766780282811130	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/remoteproc/remoteproc_virtio.c.obj	bb64ca840b5c0a4b
167243	168808	7766780282911126	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/remoteproc/elf_loader.c.obj	57b6839fc5eec5a5
166853	168816	7766780283041126	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/remoteproc/remoteproc.c.obj	4a9288294d1079ce
167229	168887	7766780283719699	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/rpmsg/rpmsg_virtio.c.obj	6c11efc0aa524a1e
167555	169041	7766780284314798	modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a	667084c5b7e64699
168186	169047	7766780285351500	zephyr/kernel/CMakeFiles/kernel.dir/errno.c.obj	7c76229ba27107c6
167419	169072	7766780284848013	modules/libmetal/libmetal/lib/libmetal.a	7e07eec9788c44cf
168043	169108	7766780286038277	modules/segger/CMakeFiles/modules__segger.dir/SEGGER_RTT_zephyr.c.obj	7dc04529b2fc5990
168134	169281	7766780287939195	modules/segger/CMakeFiles/modules__segger.dir/C_/ncs/v2.5.99-dev1/modules/debug/segger/SEGGER/SEGGER_RTT.c.obj	8bb8e91a27b9af63
168296	169466	7766780289284256	zephyr/kernel/CMakeFiles/kernel.dir/main_weak.c.obj	e2779364c3e27b8c
168816	169611	7766780291150558	zephyr/kernel/CMakeFiles/kernel.dir/device.c.obj	169a05f26c9c98c9
168809	169645	7766780290945563	zephyr/kernel/CMakeFiles/kernel.dir/busy_wait.c.obj	6e654d940a6d840e
168780	169898	7766780293661976	zephyr/kernel/CMakeFiles/kernel.dir/banner.c.obj	e8200ccb550082fb
168888	170072	7766780295127593	modules/open-amp/open-amp/lib/libopen_amp.a	52d4b55891f3721c
169282	170136	7766780295827049	modules/segger/libmodules__segger.a	4873e1573426f3bc
169108	170143	7766780296318011	zephyr/kernel/CMakeFiles/kernel.dir/thread.c.obj	abac7b83da2b74c1
169047	170158	7766780296648007	zephyr/kernel/CMakeFiles/kernel.dir/fatal.c.obj	d03ee1d1b0ecd323
169041	170244	7766780296418009	zephyr/kernel/CMakeFiles/kernel.dir/mem_slab.c.obj	608eaa9b16763c18
169073	170453	7766780299262514	zephyr/kernel/CMakeFiles/kernel.dir/kheap.c.obj	c777b479da38068f
169466	170604	7766780300727967	zephyr/kernel/CMakeFiles/kernel.dir/init.c.obj	463deeb4ff2d240d
170072	170728	7766780302147967	zephyr/kernel/CMakeFiles/kernel.dir/version.c.obj	2e6ff128ca96c12b
169611	170943	7766780304008980	zephyr/kernel/CMakeFiles/kernel.dir/idle.c.obj	d6bf57c51c2cd445
169646	170994	7766780304753645	zephyr/kernel/CMakeFiles/kernel.dir/msg_q.c.obj	f61c153283fdaf4c
170245	171487	7766780309539679	zephyr/kernel/CMakeFiles/kernel.dir/sem.c.obj	b1c4b4ec5ef25c6a
170143	171494	7766780309661840	zephyr/kernel/CMakeFiles/kernel.dir/queue.c.obj	5da389cdf2edc19f
170453	171528	7766780309951838	zephyr/kernel/CMakeFiles/kernel.dir/system_work_q.c.obj	5a9a8491a7bf5339
170137	171536	7766780309871858	zephyr/kernel/CMakeFiles/kernel.dir/mailbox.c.obj	578a2d6f6938b1ed
170728	171915	7766780313988210	zephyr/kernel/CMakeFiles/kernel.dir/stack.c.obj	9651d2bfc4c5b979
169899	171923	7766780314058723	zephyr/kernel/CMakeFiles/kernel.dir/mutex.c.obj	efecb714a9c7b782
170995	172094	7766780315645840	zephyr/kernel/CMakeFiles/kernel.dir/condvar.c.obj	7c64b5e336b2fa73
170943	172106	7766780315835841	zephyr/kernel/CMakeFiles/kernel.dir/timer.c.obj	9c9c181a5f723f6d
170159	172116	7766780315795845	zephyr/kernel/CMakeFiles/kernel.dir/sched.c.obj	d8d55f3061d3b246
170605	172452	7766780319487682	zephyr/kernel/CMakeFiles/kernel.dir/work.c.obj	815dff8d3b630e83
171487	172489	7766780319997682	zephyr/kernel/CMakeFiles/kernel.dir/xip.c.obj	f2aebcfcf948231f
171537	172548	7766780320412623	zephyr/kernel/CMakeFiles/kernel.dir/dynamic_disabled.c.obj	68bd8a1a239f7bee
171916	172646	7766780321587492	zephyr/kernel/CMakeFiles/kernel.dir/mempool.c.obj	91184bb17c63d61c
171529	172740	7766780322123633	zephyr/kernel/CMakeFiles/kernel.dir/timeout.c.obj	c54fc4f0ca67a344
171494	172770	7766780322923184	zephyr/kernel/CMakeFiles/kernel.dir/poll.c.obj	88bd6390a3eeb3e0
172770	173024	7766780325417344	zephyr/kernel/libkernel.a	391089fa16c95bf8
173024	173627	7766780330536736	zephyr/zephyr_pre0.elf	e39251384e0867c1
173024	173627	7766780330536736	zephyr/zephyr_pre0.map	e39251384e0867c1
173024	173627	7766780330536736	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/zephyr_pre0.map	e39251384e0867c1
173627	173812	7766780332791788	zephyr/linker.cmd	2420559867596173
173627	173812	7766780332791788	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/linker.cmd	2420559867596173
173812	174569	7766780340989360	zephyr/isr_tables.c	99bcdce3e6ab504a
173812	174569	7766780340989360	zephyr/isrList.bin	99bcdce3e6ab504a
173812	174569	7766780340989360	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/isr_tables.c	99bcdce3e6ab504a
173812	174569	7766780340989360	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/isrList.bin	99bcdce3e6ab504a
174570	174678	7766780342150280	zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj	d5e39c9b2cc1043f
174575	174880	7766780344153013	zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj	92e62ae649b4690c
174881	179063	7766780385931699	zephyr/zephyr.elf	ce66647dac91e6b5
174881	179063	7766780385931699	zephyr/zephyr.map	ce66647dac91e6b5
174881	179063	7766780385931699	zephyr/zephyr.hex	ce66647dac91e6b5
174881	179063	7766780385931699	zephyr/zephyr.bin	ce66647dac91e6b5
174881	179063	7766780385931699	zephyr/zephyr.stat	ce66647dac91e6b5
174881	179063	7766780385931699	zephyr/tfm_merged.hex	ce66647dac91e6b5
174881	179063	7766780385931699	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/zephyr.map	ce66647dac91e6b5
174881	179063	7766780385931699	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/zephyr.hex	ce66647dac91e6b5
174881	179063	7766780385931699	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/zephyr.bin	ce66647dac91e6b5
174881	179063	7766780385931699	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/zephyr.stat	ce66647dac91e6b5
174881	179063	7766780385931699	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/tfm_merged.hex	ce66647dac91e6b5
179064	179375	7766780389103457	zephyr/tfm_nonsecure.hex	531ae52b6714d29b
179064	179375	7766780389103457	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/tfm_nonsecure.hex	531ae52b6714d29b
179375	179860	7766780393966037	zephyr/merged.hex	67e1d8f55e96c9ee
179375	179860	7766780393966037	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/merged.hex	67e1d8f55e96c9ee
179860	180472	7766780400042293	zephyr/merged_domains.hex	5bf26a7a041419b
179860	180472	7766780400042293	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/zephyr/merged_domains.hex	5bf26a7a041419b
