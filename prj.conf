CONFIG_I2C=y
CONFIG_GPIO=y

# Increased stack due to settings API usage
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=2048

CONFIG_BT=y
CONFIG_LOG=y
CONFIG_BT_SMP=y
CONFIG_BT_SIGNING=y
CONFIG_BT_PERIPHERAL=y
CONFIG_BT_DIS=y
CONFIG_BT_ATT_PREPARE_COUNT=1
CONFIG_BT_PRIVACY=y
CONFIG_BT_DEVICE_NAME="BASE"
CONFIG_BT_DEVICE_APPEARANCE=833
CONFIG_BT_BUF_ACL_RX_SIZE=251
CONFIG_BT_BUF_ACL_TX_SIZE=251
CONFIG_BT_L2CAP_TX_MTU=247


CONFIG_BT_KEYS_OVERWRITE_OLDEST=y
CONFIG_BT_SETTINGS=y
CONFIG_FLASH=y
CONFIG_FLASH_PAGE_LAYOUT=y
CONFIG_FLASH_MAP=y
CONFIG_NVS=y
CONFIG_SETTINGS=y
CONFIG_REBOOT=y


# Enable Direction Finding Feature RX in connected mode
CONFIG_BT_DF=y
CONFIG_BT_DF_CONNECTION_CTE_TX=y
CONFIG_BT_DF_CONNECTION_CTE_RSP=y


CONFIG_TIMER=y
CONFIG_TIMER_READS_ITS_FREQUENCY_AT_RUNTIME=y

CONFIG_SENSOR=y
CONFIG_ADC=y
CONFIG_PRINTK=n
CONFIG_CBPRINTF_FP_SUPPORT=y

CONFIG_ADC_NRFX_SAADC=y

CONFIG_ADC_ASYNC=y

# choose RTT console
CONFIG_UART_CONSOLE=n
CONFIG_USE_SEGGER_RTT=y
CONFIG_RTT_CONSOLE=y

# General config
CONFIG_LOG=y
CONFIG_LOG_DEFAULT_LEVEL=0