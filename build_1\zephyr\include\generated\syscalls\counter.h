/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_COUNTER_H
#define Z_INCLUDE_SYSCALLS_COUNTER_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern bool z_impl_counter_is_counting_up(const struct device * dev);

__pinned_func
static inline bool counter_is_counting_up(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (bool) arch_syscall_invoke1(parm0.x, K_SYSCALL_COUNTER_IS_COUNTING_UP);
	}
#endif
	compiler_barrier();
	return z_impl_counter_is_counting_up(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_is_counting_up(dev) ({ 	bool syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_IS_COUNTING_UP, counter_is_counting_up, dev); 	syscall__retval = counter_is_counting_up(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_IS_COUNTING_UP, counter_is_counting_up, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern uint8_t z_impl_counter_get_num_of_channels(const struct device * dev);

__pinned_func
static inline uint8_t counter_get_num_of_channels(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (uint8_t) arch_syscall_invoke1(parm0.x, K_SYSCALL_COUNTER_GET_NUM_OF_CHANNELS);
	}
#endif
	compiler_barrier();
	return z_impl_counter_get_num_of_channels(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_get_num_of_channels(dev) ({ 	uint8_t syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_GET_NUM_OF_CHANNELS, counter_get_num_of_channels, dev); 	syscall__retval = counter_get_num_of_channels(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_GET_NUM_OF_CHANNELS, counter_get_num_of_channels, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern uint32_t z_impl_counter_get_frequency(const struct device * dev);

__pinned_func
static inline uint32_t counter_get_frequency(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (uint32_t) arch_syscall_invoke1(parm0.x, K_SYSCALL_COUNTER_GET_FREQUENCY);
	}
#endif
	compiler_barrier();
	return z_impl_counter_get_frequency(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_get_frequency(dev) ({ 	uint32_t syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_GET_FREQUENCY, counter_get_frequency, dev); 	syscall__retval = counter_get_frequency(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_GET_FREQUENCY, counter_get_frequency, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern uint32_t z_impl_counter_us_to_ticks(const struct device * dev, uint64_t us);

__pinned_func
static inline uint32_t counter_us_to_ticks(const struct device * dev, uint64_t us)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { struct { uintptr_t lo, hi; } split; uint64_t val; } parm1 = { .val = us };
		return (uint32_t) arch_syscall_invoke3(parm0.x, parm1.split.lo, parm1.split.hi, K_SYSCALL_COUNTER_US_TO_TICKS);
	}
#endif
	compiler_barrier();
	return z_impl_counter_us_to_ticks(dev, us);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_us_to_ticks(dev, us) ({ 	uint32_t syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_US_TO_TICKS, counter_us_to_ticks, dev, us); 	syscall__retval = counter_us_to_ticks(dev, us); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_US_TO_TICKS, counter_us_to_ticks, dev, us, syscall__retval); 	syscall__retval; })
#endif
#endif


extern uint64_t z_impl_counter_ticks_to_us(const struct device * dev, uint32_t ticks);

__pinned_func
static inline uint64_t counter_ticks_to_us(const struct device * dev, uint32_t ticks)
{
#ifdef CONFIG_USERSPACE
	uint64_t ret64;
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = ticks };
		(void) arch_syscall_invoke3(parm0.x, parm1.x, (uintptr_t)&ret64, K_SYSCALL_COUNTER_TICKS_TO_US);
		return (uint64_t) ret64;
	}
#endif
	compiler_barrier();
	return z_impl_counter_ticks_to_us(dev, ticks);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_ticks_to_us(dev, ticks) ({ 	uint64_t syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_TICKS_TO_US, counter_ticks_to_us, dev, ticks); 	syscall__retval = counter_ticks_to_us(dev, ticks); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_TICKS_TO_US, counter_ticks_to_us, dev, ticks, syscall__retval); 	syscall__retval; })
#endif
#endif


extern uint32_t z_impl_counter_get_max_top_value(const struct device * dev);

__pinned_func
static inline uint32_t counter_get_max_top_value(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (uint32_t) arch_syscall_invoke1(parm0.x, K_SYSCALL_COUNTER_GET_MAX_TOP_VALUE);
	}
#endif
	compiler_barrier();
	return z_impl_counter_get_max_top_value(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_get_max_top_value(dev) ({ 	uint32_t syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_GET_MAX_TOP_VALUE, counter_get_max_top_value, dev); 	syscall__retval = counter_get_max_top_value(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_GET_MAX_TOP_VALUE, counter_get_max_top_value, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_counter_start(const struct device * dev);

__pinned_func
static inline int counter_start(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_COUNTER_START);
	}
#endif
	compiler_barrier();
	return z_impl_counter_start(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_start(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_START, counter_start, dev); 	syscall__retval = counter_start(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_START, counter_start, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_counter_stop(const struct device * dev);

__pinned_func
static inline int counter_stop(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_COUNTER_STOP);
	}
#endif
	compiler_barrier();
	return z_impl_counter_stop(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_stop(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_STOP, counter_stop, dev); 	syscall__retval = counter_stop(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_STOP, counter_stop, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_counter_get_value(const struct device * dev, uint32_t * ticks);

__pinned_func
static inline int counter_get_value(const struct device * dev, uint32_t * ticks)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t * val; } parm1 = { .val = ticks };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_COUNTER_GET_VALUE);
	}
#endif
	compiler_barrier();
	return z_impl_counter_get_value(dev, ticks);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_get_value(dev, ticks) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_GET_VALUE, counter_get_value, dev, ticks); 	syscall__retval = counter_get_value(dev, ticks); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_GET_VALUE, counter_get_value, dev, ticks, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_counter_get_value_64(const struct device * dev, uint64_t * ticks);

__pinned_func
static inline int counter_get_value_64(const struct device * dev, uint64_t * ticks)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint64_t * val; } parm1 = { .val = ticks };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_COUNTER_GET_VALUE_64);
	}
#endif
	compiler_barrier();
	return z_impl_counter_get_value_64(dev, ticks);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_get_value_64(dev, ticks) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_GET_VALUE_64, counter_get_value_64, dev, ticks); 	syscall__retval = counter_get_value_64(dev, ticks); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_GET_VALUE_64, counter_get_value_64, dev, ticks, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_counter_set_channel_alarm(const struct device * dev, uint8_t chan_id, const struct counter_alarm_cfg * alarm_cfg);

__pinned_func
static inline int counter_set_channel_alarm(const struct device * dev, uint8_t chan_id, const struct counter_alarm_cfg * alarm_cfg)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t val; } parm1 = { .val = chan_id };
		union { uintptr_t x; const struct counter_alarm_cfg * val; } parm2 = { .val = alarm_cfg };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_COUNTER_SET_CHANNEL_ALARM);
	}
#endif
	compiler_barrier();
	return z_impl_counter_set_channel_alarm(dev, chan_id, alarm_cfg);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_set_channel_alarm(dev, chan_id, alarm_cfg) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_SET_CHANNEL_ALARM, counter_set_channel_alarm, dev, chan_id, alarm_cfg); 	syscall__retval = counter_set_channel_alarm(dev, chan_id, alarm_cfg); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_SET_CHANNEL_ALARM, counter_set_channel_alarm, dev, chan_id, alarm_cfg, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_counter_cancel_channel_alarm(const struct device * dev, uint8_t chan_id);

__pinned_func
static inline int counter_cancel_channel_alarm(const struct device * dev, uint8_t chan_id)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t val; } parm1 = { .val = chan_id };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_COUNTER_CANCEL_CHANNEL_ALARM);
	}
#endif
	compiler_barrier();
	return z_impl_counter_cancel_channel_alarm(dev, chan_id);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_cancel_channel_alarm(dev, chan_id) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_CANCEL_CHANNEL_ALARM, counter_cancel_channel_alarm, dev, chan_id); 	syscall__retval = counter_cancel_channel_alarm(dev, chan_id); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_CANCEL_CHANNEL_ALARM, counter_cancel_channel_alarm, dev, chan_id, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_counter_set_top_value(const struct device * dev, const struct counter_top_cfg * cfg);

__pinned_func
static inline int counter_set_top_value(const struct device * dev, const struct counter_top_cfg * cfg)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const struct counter_top_cfg * val; } parm1 = { .val = cfg };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_COUNTER_SET_TOP_VALUE);
	}
#endif
	compiler_barrier();
	return z_impl_counter_set_top_value(dev, cfg);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_set_top_value(dev, cfg) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_SET_TOP_VALUE, counter_set_top_value, dev, cfg); 	syscall__retval = counter_set_top_value(dev, cfg); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_SET_TOP_VALUE, counter_set_top_value, dev, cfg, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_counter_get_pending_int(const struct device * dev);

__pinned_func
static inline int counter_get_pending_int(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_COUNTER_GET_PENDING_INT);
	}
#endif
	compiler_barrier();
	return z_impl_counter_get_pending_int(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_get_pending_int(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_GET_PENDING_INT, counter_get_pending_int, dev); 	syscall__retval = counter_get_pending_int(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_GET_PENDING_INT, counter_get_pending_int, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern uint32_t z_impl_counter_get_top_value(const struct device * dev);

__pinned_func
static inline uint32_t counter_get_top_value(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (uint32_t) arch_syscall_invoke1(parm0.x, K_SYSCALL_COUNTER_GET_TOP_VALUE);
	}
#endif
	compiler_barrier();
	return z_impl_counter_get_top_value(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_get_top_value(dev) ({ 	uint32_t syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_GET_TOP_VALUE, counter_get_top_value, dev); 	syscall__retval = counter_get_top_value(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_GET_TOP_VALUE, counter_get_top_value, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_counter_set_guard_period(const struct device * dev, uint32_t ticks, uint32_t flags);

__pinned_func
static inline int counter_set_guard_period(const struct device * dev, uint32_t ticks, uint32_t flags)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = ticks };
		union { uintptr_t x; uint32_t val; } parm2 = { .val = flags };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_COUNTER_SET_GUARD_PERIOD);
	}
#endif
	compiler_barrier();
	return z_impl_counter_set_guard_period(dev, ticks, flags);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_set_guard_period(dev, ticks, flags) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_SET_GUARD_PERIOD, counter_set_guard_period, dev, ticks, flags); 	syscall__retval = counter_set_guard_period(dev, ticks, flags); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_SET_GUARD_PERIOD, counter_set_guard_period, dev, ticks, flags, syscall__retval); 	syscall__retval; })
#endif
#endif


extern uint32_t z_impl_counter_get_guard_period(const struct device * dev, uint32_t flags);

__pinned_func
static inline uint32_t counter_get_guard_period(const struct device * dev, uint32_t flags)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = flags };
		return (uint32_t) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_COUNTER_GET_GUARD_PERIOD);
	}
#endif
	compiler_barrier();
	return z_impl_counter_get_guard_period(dev, flags);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define counter_get_guard_period(dev, flags) ({ 	uint32_t syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_COUNTER_GET_GUARD_PERIOD, counter_get_guard_period, dev, flags); 	syscall__retval = counter_get_guard_period(dev, flags); 	sys_port_trace_syscall_exit(K_SYSCALL_COUNTER_GET_GUARD_PERIOD, counter_get_guard_period, dev, flags, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
