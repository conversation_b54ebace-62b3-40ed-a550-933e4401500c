# Available runners configured by board.cmake.
runners:
- nrfjprog
- nrfutil
- jlink

# Default flash runner if --runner is not given.
flash-runner: nrfjprog

# Default debug runner if --runner is not given.
debug-runner: jlink

# Common runner configuration values.
config:
  board_dir: C:/ncs/v2.5.99-dev1/zephyr/boards/arm/nrf5340dk_nrf5340
  # Build outputs:
  elf_file: zephyr.elf
  hex_file: merged_domains.hex
  bin_file: zephyr.bin
  # Host tools:
  gdb: C:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gdb.exe

# Runner specific arguments
args:
  nrfjprog:
    []

  nrfutil:
    []

  jlink:
    - --dt-flash=y
    - --device=nrf5340_xxaa_app
    - --speed=4000
