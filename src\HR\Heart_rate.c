/******************************************************************************
 * Copyright © 2023 ams-OSRAM AG                                              *
 * All rights are reserved.                                                   *
 *                                                                            *
 * FOR FULL LICENSE TEXT SEE LICENSE.TXT                                      *
 *                                                                            *
 ******************************************************************************/

/* This sample code shows how to acquire live data using the AS7058 Chip Library and the Accelerometer driver, and how
 * to process this data using the HRM library of the AS7058A Vital Signs Algorithms. The sample code is intended to be
 * executed on an x86-64 Windows computer that has an AS7058A EVK connected via USB. The sample code is linked against
 * as7058_osal_core_fw.dll, which implements an OSAL for the AS7058 Chip Library and the Accelerometer driver. */

/******************************************************************************
 *                                  INCLUDES                                  *
 ******************************************************************************/

#include <signal.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <zephyr/drivers/i2c.h>
#include <zephyr/as7058/error_codes.h>

#include <zephyr/as7058/as7058_chiplib.h>
#include <zephyr/as7058/as7058a_hrm_a0.h>


#if !UMESH
/******************************************************************************
 *                                 DEFINITIONS                                *
 ******************************************************************************/

/* The accelerometer of the AS7058A EVK supports the following sample periods: 20000 µs, 40000 µs, 100000 µs, and
 * 1000000 µs. We recommend using 100000 µs for HRM. */
#define ACC_SAMPLE_PERIOD_US 100000

/******************************************************************************
 *                                   GLOBALS                                  *
 ******************************************************************************/

/* Stores whether the HRM library is ready for execution. Note that this variable is accessed from multiple threads and
 * that this information should be signaled in a safer way in production code. */
static volatile uint8_t g_ready_for_execution = FALSE;

/* Stores whether the sample code should continue running or whether a stop was requested. Note that this variable is
 * accessed from multiple threads and that this information should be signaled in a safer way in production code. */
static volatile uint8_t g_keep_running = TRUE;

/******************************************************************************
 *                               LOCAL FUNCTIONS                              *
 ******************************************************************************/

/* Callback function called by the AS7058 Chip Library when measurement data is available. Data generated by the AS7058A
 * AFE or by the AS7058 Chip Library is provided via function arguments and doesn't need to be polled explicitly. The
 * function additionally polls samples from the accelerometer and forwards all data to the HRM library. */
static void as7058_callback(err_code_t error, const uint8_t *p_fifo_data, uint16_t fifo_data_size,
                            const agc_status_t *p_agc_statuses, uint8_t agc_statuses_num,
                            as7058_status_events_t sensor_events, const void *p_cb_param)
{

    /* Unused parameter, silence potentially enabled compiler warning. */
    M_UNUSED_PARAM(p_cb_param);

    err_code_t result = error;
    if (result != ERR_SUCCESS) {
        //printf("as7058_callback called with error %d.\n", result);
        return;
    }

    /* Poll data from the accelerometer. The required length of the accelerometer data buffer depends on the number of
     * samples generated by the accelerometer since the last invocation of the callback function. In practice, this
     * typically means that the required buffer length is dependent on the configured accelerometer sample period, PPG
     * sample period, and AS7058A FIFO threshold values. */
    vs_acc_data_t acc_data[20];
    uint8_t num_acc_data = sizeof(acc_data) / sizeof(acc_data[0]);
    //umesh result = vs_acc_get_data(acc_data, &num_acc_data);
    // if (result != ERR_SUCCESS) {
    //    // printf("vs_acc_get_data returned error %d.\n", result);
    //     return;
    // }
    /* Pass data to the HRM library. */
    uint8_t ready_for_execution = 0;
    #if UMESH
    result = as7058a_hrm_a0_set_input(p_fifo_data, fifo_data_size, sensor_events, p_agc_statuses, agc_statuses_num,
                                      acc_data, num_acc_data, &ready_for_execution);
    if (result != ERR_SUCCESS) {
       // printf("as7058a_hrm_a0_set_input returned error %d.\n", result);
        return;
    }
#endif
    if (ready_for_execution) {
        /* Store the information that the HRM algorithm is ready for execution. The main thread will execute the
         * algorithm depending on this information. */
        g_ready_for_execution = ready_for_execution;
    }
}

/******************************************************************************
 *                              GLOBAL FUNCTIONS                              *
 ******************************************************************************/

_Bool HeartRateInit(void)
{
    FifoReset();
    /* Get interface string from command line arguments. */
    char *p_interface_str = NULL;

    /**************************************************************************
     *                             INITIALIZATION                             *
     **************************************************************************/

    /* Initialize the AS7058 Chip Library. For this sample code, only the first and the fourth argument of this function
     * are of relevance. The other arguments can be set to NULL. The first argument points to a callback function that
     * is called when new measurement data of the regular measurement mode is available. The fourth argument is an
     * OSAL-specific interface string. For the Windows OSAL that is used by this sample code, this string needs to
     * contain the serial port the AS7058A EVK is connected to, prefixed by "COM:". Assuming that the AS7058A EVK is
     * connected to serial port COM7, the interface string needs to be "COM:COM7". */
    err_code_t result = as7058_initialize(as7058_callback, NULL, NULL, p_interface_str);
    if (result != ERR_SUCCESS) {
       // printf("as7058_initialize returned error %d.\n", result);
        goto ERROR;
    }

    /* Initialize the Accelerometer driver. For this sample code, the argument of this function is not of relevance and
     * can be set to NULL. */
   //umesh result = vs_acc_initialize(NULL);
    if (result != ERR_SUCCESS) {
       // printf("vs_acc_initialize returned error %d.\n", result);
        goto ERROR;
    }

    /* Initialize the HRM library. */
    result = as7058a_hrm_a0_initialize();
    if (result != ERR_SUCCESS) {
       // printf("as7058a_hrm_a0_initialize returned error %d.\n", result);
        goto ERROR;
    }

    /**************************************************************************
     *                              CONFIGURATION                             *
     **************************************************************************/

    /* Apply a register configuration to the AS7058A AFE using the AS7058 Chip Library. This configuration is suitable
     * for HRM measurements using the AS7058A EVK. For more information regarding the register values, please refer to
     * the datasheet. */

    /* Configure register group POWER of the AS7058A AFE. */
    const as7058_reg_group_power_t power_config = {{
        .pwr_on = 7,
        .pwr_iso = 0,
        .clk_cfg = 7,
        .ref_cfg1 = 63,
        .ref_cfg2 = 0,
        .ref_cfg3 = 0,
        .standby_on1 = 0,
        .standby_on2 = 0,
        .standby_en1 = 4,
        .standby_en2 = 2,
        .standby_en3 = 4,
        .standby_en4 = 0,
        .standby_en5 = 3,
        .standby_en6 = 16,
        .standby_en7 = 16,
        .standby_en8 = 4,
        .standby_en9 = 0,
        .standby_en10 = 3,
        .standby_en11 = 16,
        .standby_en12 = 16,
        .standby_en13 = 16,
        .standby_en14 = 16,
    }};
    result = as7058_set_reg_group(AS7058_REG_GROUP_ID_PWR, power_config.reg_buffer, sizeof(as7058_reg_group_power_t));
    if (result != ERR_SUCCESS) {
       // printf("Writing register group AS7058_REG_GROUP_ID_PWR returned error %d.\n", result);
        goto ERROR;
    }

    /* Configure register group CONTROL of the AS7058A AFE. */
    const as7058_reg_group_control_t control_config = {{
        .i2c_mode = 0,
        .int_cfg = 0,
        .if_cfg = 72,
        .gpio_cfg1 = 0,
        .gpio_cfg2 = 0,
        .io_cfg = 0,
    }};
    result =
        as7058_set_reg_group(AS7058_REG_GROUP_ID_CTRL, control_config.reg_buffer, sizeof(as7058_reg_group_control_t));
    if (result != ERR_SUCCESS) {
       // printf("Writing register group AS7058_REG_GROUP_ID_CTRL returned error %d.\n", result);
        goto ERROR;
    }

    /* Configure register group LED of the AS7058A AFE. */
    const as7058_reg_group_led_t led_config = {{
        .vcsel_password = 87,
        .vcsel_cfg = 0,
        .vcsel_mode = 0,
        .led_cfg = 0,
        .led_drv1 = 0,
        .led_drv2 = 0,
        .led1_ictrl =25, //9->25
        .led2_ictrl = 0,
        .led3_ictrl = 0,
        .led4_ictrl = 0,
        .led5_ictrl = 0,
        .led6_ictrl = 0,
        .led7_ictrl = 0,
        .led8_ictrl = 0,
        .led_irng1 = 23, //21->23
        .led_irng2 = 0,
        .led_sub1 = 3, //1->2
        .led_sub2 = 0,
        .led_sub3 = 0,
        .led_sub4 = 0,
        .led_sub5 = 0,
        .led_sub6 = 0,
        .led_sub7 = 0,
        .led_sub8 = 0,
        .lowvds_wait = 0,
    }};
    result = as7058_set_reg_group(AS7058_REG_GROUP_ID_LED, led_config.reg_buffer, sizeof(as7058_reg_group_led_t));
    if (result != ERR_SUCCESS) {
       // printf("Writing register group AS7058_REG_GROUP_ID_LED returned error %d.\n", result);
        goto ERROR;
    }

    /* Configure register group PD of the AS7058A AFE. */
    const as7058_reg_group_pd_t pd_config = {{
        .pdsel_cfg = 0,
        .ppg1_pdsel1 = 2,
        .ppg1_pdsel2 = 0,
        .ppg1_pdsel3 = 0,
        .ppg1_pdsel4 = 0,
        .ppg1_pdsel5 = 0,
        .ppg1_pdsel6 = 0,
        .ppg1_pdsel7 = 0,
        .ppg1_pdsel8 = 0,
        .ppg2_pdsel1 = 0,
        .ppg2_pdsel2 = 0,
        .ppg2_pdsel3 = 0,
        .ppg2_pdsel4 = 0,
        .ppg2_pdsel5 = 0,
        .ppg2_pdsel6 = 0,
        .ppg2_pdsel7 = 0,
        .ppg2_pdsel8 = 0,
        .ppg2_afesel1 = 0,
        .ppg2_afesel2 = 0,
        .ppg2_afesel3 = 0,
        .ppg2_afesel4 = 0,
        .ppg2_afeen = 0,
    }};
    result = as7058_set_reg_group(AS7058_REG_GROUP_ID_PD, pd_config.reg_buffer, sizeof(as7058_reg_group_pd_t));
    if (result != ERR_SUCCESS) {
       // printf("Writing register group AS7058_REG_GROUP_ID_PD returned error %d.\n", result);
        goto ERROR;
    }

    /* Configure register group IOS of the AS7058A AFE. */
    const as7058_reg_group_ios_t ios_config = {{
        .ios_ppg1_sub1 = 0,
        .ios_ppg1_sub2 = 0,
        .ios_ppg1_sub3 = 0,
        .ios_ppg1_sub4 = 0,
        .ios_ppg1_sub5 = 0,
        .ios_ppg1_sub6 = 0,
        .ios_ppg1_sub7 = 0,
        .ios_ppg1_sub8 = 0,
        .ios_ppg2_sub1 = 0,
        .ios_ppg2_sub2 = 0,
        .ios_ppg2_sub3 = 0,
        .ios_ppg2_sub4 = 0,
        .ios_ppg2_sub5 = 0,
        .ios_ppg2_sub6 = 0,
        .ios_ppg2_sub7 = 0,
        .ios_ppg2_sub8 = 0,
        .ios_ledoff = 0,
        .ios_cfg = 0,
        .aoc_sar_thres = 0,
        .aoc_sar_range = 0,
        .aoc_sar_ppg1 = 0,
        .aoc_sar_ppg2 = 0,
    }};
    result = as7058_set_reg_group(AS7058_REG_GROUP_ID_IOS, ios_config.reg_buffer, sizeof(as7058_reg_group_ios_t));
    if (result != ERR_SUCCESS) {
       // printf("Writing register group AS7058_REG_GROUP_ID_IOS returned error %d.\n", result);
        goto ERROR;
    }

    /* Configure register group PPG of the AS7058A AFE. */
    const as7058_reg_group_ppg_t ppg_config = {{
        .ppgmod_cfg1 = 0,
        .ppgmod_cfg2 = 0,
        .ppgmod_cfg3 = 0,
        .ppgmod1_cfg1 = 167,
        .ppgmod1_cfg2 = 100,
        .ppgmod1_cfg3 = 3,
        .ppgmod2_cfg1 = 7,
        .ppgmod2_cfg2 = 87,
        .ppgmod2_cfg3 = 7,
    }};
    result = as7058_set_reg_group(AS7058_REG_GROUP_ID_PPG, ppg_config.reg_buffer, sizeof(as7058_reg_group_ppg_t));
    if (result != ERR_SUCCESS) {
       // printf("Writing register group AS7058_REG_GROUP_ID_PPG returned error %d.\n", result);
        goto ERROR;
    }

    /* Configure register group ECG of the AS7058A AFE. */
    const as7058_reg_group_ecg_t ecg_config = {{
        .bioz_cfg = 0,
        .bioz_excit = 0,
        .bioz_mixer = 0,
        .bioz_select = 0,
        .bioz_gain = 0,
        .ecgmod_cfg1 = 0,
        .ecgmod_cfg2 = 0,
        .ecgimux_cfg1 = 0,
        .ecgimux_cfg2 = 0,
        .ecgimux_cfg3 = 0,
        .ecgamp_cfg1 = 0,
        .ecgamp_cfg2 = 0,
        .ecgamp_cfg3 = 0,
        .ecgamp_cfg4 = 0,
        .ecgamp_cfg5 = 0,
        .ecgamp_cfg6 = 0,
        .ecgamp_cfg7 = 0,
        .ecg_bioz = 0,
        .leadoff_cfg = 0,
        .leadoff_thresl = 0,
        .leadoff_thresh = 0,
    }};
    result = as7058_set_reg_group(AS7058_REG_GROUP_ID_ECG, ecg_config.reg_buffer, sizeof(as7058_reg_group_ecg_t));
    if (result != ERR_SUCCESS) {
       // printf("Writing register group AS7058_REG_GROUP_ID_ECG returned error %d.\n", result);
        goto ERROR;
    }

    /* Configure register group SINC of the AS7058A AFE. */
    const as7058_reg_group_sinc_t sinc_config = {{
        .ppg_sinc_cfga = 3,
        .ppg_sinc_cfgb = 3,
        .ppg_sinc_cfgc = 0,
        .ppg_sinc_cfgd = 0,
        .ecg1_sinc_cfga = 0,
        .ecg1_sinc_cfgb = 0,
        .ecg1_sinc_cfgc = 0,
        .ecg2_sinc_cfga = 0,
        .ecg2_sinc_cfgb = 0,
        .ecg2_sinc_cfgc = 0,
        .ecg_sinc_cfg = 0,
    }};
    result = as7058_set_reg_group(AS7058_REG_GROUP_ID_SINC, sinc_config.reg_buffer, sizeof(as7058_reg_group_sinc_t));
    if (result != ERR_SUCCESS) {
       // printf("Writing register group AS7058_REG_GROUP_ID_SINC returned error %d.\n", result);
        goto ERROR;
    }

    /* Configure register group SEQ of the AS7058A AFE. */
    const as7058_reg_group_seq_t seq_config = {{
        .irq_enable = 6, //7->6
        .ppg_sub_wait = 0,
        .ppg_sar_wait = 0,
        .ppg_led_init = 10,
        .ppg_freql = 159,
        .ppg_freqh = 0,
        .ppg1_sub_en = 1,
        .ppg2_sub_en = 0,
        .ppg_mode_1 = 0,
        .ppg_mode_2 = 0,
        .ppg_mode_3 = 0,
        .ppg_mode_4 = 0,
        .ppg_mode_5 = 0,
        .ppg_mode_6 = 0,
        .ppg_mode_7 = 0,
        .ppg_mode_8 = 0,
        .ppg_cfg = 0,
        .ecg_freql = 79,
        .ecg_freqh = 0,
        .ecg1_freqdivl = 0,
        .ecg1_freqdivh = 0,
        .ecg2_freqdivl = 0,
        .ecg2_freqdivh = 0,
        .ecg_subs = 0,
        .leadoff_initl = 0,
        .leadoff_inith = 0,
        .ecg_initl = 1,
        .ecg_inith = 0,
        .sample_num = 0,
    }};
    result = as7058_set_reg_group(AS7058_REG_GROUP_ID_SEQ, seq_config.reg_buffer, sizeof(as7058_reg_group_seq_t));
    if (result != ERR_SUCCESS) {
       // printf("Writing register group AS7058_REG_GROUP_ID_SEQ returned error %d.\n", result);
        goto ERROR;
    }

    /* Configure register group PP of the AS7058A AFE. */
    const as7058_reg_group_pp_t pp_config = {{
        .pp_cfg = 0,
        .ppg1_pp1 = 0,
        .ppg1_pp2 = 0,
        .ppg2_pp1 = 0,
        .ppg2_pp2 = 0,
    }};
    result = as7058_set_reg_group(AS7058_REG_GROUP_ID_PP, pp_config.reg_buffer, sizeof(as7058_reg_group_pp_t));
    if (result != ERR_SUCCESS) {
       // printf("Writing register group AS7058_REG_GROUP_ID_PP returned error %d.\n", result);
        goto ERROR;
    }

    /* Configure register group FIFO of the AS7058A AFE. */
    const as7058_reg_group_fifo_t fifo_config = {{
        .fifo_threshold = 0,
        .fifo_ctrl = 0, //umesh 0->2
    }};
    result = as7058_set_reg_group(AS7058_REG_GROUP_ID_FIFO, fifo_config.reg_buffer, sizeof(as7058_reg_group_fifo_t));
    if (result != ERR_SUCCESS) {
       // printf("Writing register group AS7058_REG_GROUP_ID_FIFO returned error %d.\n", result);
        goto ERROR;
    }

    /* Configure the Automatic Gain Control (AGC) algorithm using a single-channel configuration that is suitable for
     * HRM measurements using the AS7058A EVK. */
    const agc_configuration_t agc_config = {
        .mode = AGC_MODE_DEFAULT,
        .led_control_mode = AGC_AMPL_CNTL_MODE_AUTO,
        .channel = AS7058_SUB_SAMPLE_FLAG_PPG1_SUB1,
        .led_current_min = 5,
        .led_current_max = 30,
        .rel_amplitude_min_x100 = 5,
        .rel_amplitude_max_x100 = 25,
        .rel_amplitude_motion_x100 = 50,
        .num_led_steps = 6,
        .threshold_min = 250000,
        .threshold_max = 770000,
    };
    result = as7058_set_agc_config(&agc_config, 1);
    if (result != ERR_SUCCESS) {
      //  printf("as7058_set_agc_config returned error %d.\n", result);
        goto ERROR;
    }

    /* Configure the sample period of the accelerometer. */
   //umesh result = vs_acc_set_sample_period(ACC_SAMPLE_PERIOD_US);
    if (result != ERR_SUCCESS) {
       // printf("vs_acc_set_sample_period returned error %d.\n", result);
        goto ERROR;
    }

    /* Indicate to the HRM library that the AS7058A AFE was configured to use sub-sample 1 of the PPG modulator 1 to
     * acquire PPG samples. */
    result = as7058a_hrm_a0_set_signal_routing(AS7058_SUB_SAMPLE_ID_PPG1_SUB1);
    if (result != ERR_SUCCESS) {
       // printf("as7058a_hrm_a0_set_signal_routing returned error %d.\n", result);
        goto ERROR;
    }

    /**************************************************************************
     *                            START MEASUREMENT                           *
     **************************************************************************/

    /* Get the current measurement configuration from the AS7058 Chip Library. This information is necessary to start a
     * processing session in the HRM library. Do not change the configuration of the AS7058A AFE or the AGC algorithm
     * after calling this function. */
    as7058_meas_config_t meas_config;
    result = as7058_get_measurement_config(&meas_config);
    if (result != ERR_SUCCESS) {
        //printf("as7058_get_measurement_config returned error %d.\n", result);
        goto ERROR;
    }

    /* Start a processing session in the HRM library. To start a processing session, the current measurement
     * configuration from the AS7058 Chip Library and the accelerometer sample period need to be passed. The HRM library
     * only accepts measurement data while it is in a processing session. When the measurement is interrupted, it is
     * important to stop the current processing session by calling as7058a_hrm_a0_stop_processing and to start a new
     * processing session before resuming the measurement. */
    result = as7058a_hrm_a0_start_processing(meas_config, ACC_SAMPLE_PERIOD_US);
    if (result != ERR_SUCCESS) {
       // printf("as7058a_hrm_a0_start_processing returned error %d.\n", result);
        goto ERROR;
    }

    /* Start data acquisition in the Accelerometer driver. */
   //umesh result = vs_acc_start();
    if (result != ERR_SUCCESS) {
      //  printf("vs_acc_start returned error %d.\n", result);
        goto ERROR;
    }

    /* Start data acquisition in the AS7058 Chip Library. For heart rate monitoring, PPG measurements are required, i.e.
     * AS7058_MEAS_MODE_NORMAL is to be used. */
    result = as7058_start_measurement(AS7058_MEAS_MODE_NORMAL);
    if (result != ERR_SUCCESS) {
        //printf("as7058_start_measurement returned error %d.\n", result);
        goto ERROR;
    }

 return result;

 #if UMESHDEBUG

    /**************************************************************************
     *                           DURING MEASUREMENT                           *
     **************************************************************************/

    printf("\nPress CTRL+C to stop the measurement.\n\n");

    uint32_t output_counter = 0;
    while (g_keep_running) 
    {
        /* Check whether the HRM library indicated that it is ready for execution.  */
        if (g_ready_for_execution) {
            /* Execute the HRM algorithm. */
            result = as7058a_hrm_a0_execute();
            if (ERR_SUCCESS == result) {
                /* Output is available from the HRM library. */

                /* Get output from the HRM library. */
                bio_hrm_a0_output_t hrm_output;
                result = as7058a_hrm_a0_get_output(&hrm_output);
                if (result != ERR_SUCCESS) {
                    printf("as7058a_hrm_a0_get_output returned error %d.\n", result);
                    goto ERROR;
                }

                /* Print HRM library output. */
                printf("[%u] Heart Rate: %d bpm, Quality: %d\n", output_counter, hrm_output.heart_rate / 10,
                       hrm_output.quality);
                output_counter++;
            } else if (ERR_NO_DATA == result) {
                /* No output is available from the HRM library. */
            } else {
                printf("as7058a_hrm_a0_execute returned error %d.\n", result);
                goto ERROR;
            }

            /* Clear information that the HRM library is ready for execution. */
            g_ready_for_execution = FALSE;
        } else {
            /* Sleep before re-checking whether the HRM library is ready for execution */
            usleep(100000);
        }
    }
    /**************************************************************************
     *                            STOP MEASUREMENT                            *
     **************************************************************************/

    /* Stop data acquisition in the AS7058 Chip Library. */
    result = as7058_stop_measurement();
    if (result != ERR_SUCCESS) {
        printf("as7058_stop_measurement returned error %d.\n", result);
        goto ERROR;
    }

    /* Stop data acquisition in the Accelerometer driver. */
    result = vs_acc_stop();
    if (result != ERR_SUCCESS) {
        printf("vs_acc_stop returned error %d.\n", result);
        goto ERROR;
    }

    /* Stop the current processing session in the HRM library. */
    result = as7058a_hrm_a0_stop_processing();
    if (result != ERR_SUCCESS) {
        printf("as7058a_hrm_a0_stop_processing returned error %d.\n", result);
        goto ERROR;
    }
 #endif

ERROR:

    /**************************************************************************
     *                            DE-INITIALIZATION                           *
     **************************************************************************/

    /* De-initialize the AS7058 Chip Library. */
    result = as7058_shutdown();
    if (result != ERR_SUCCESS) {
       // printf("as7058_shutdown returned error %d.\n", result);
    }

    /* De-initialize the Accelerometer driver. */
   //umesh result = vs_acc_shutdown();
    // if (result != ERR_SUCCESS) {
    //   //  printf("vs_acc_shutdown returned error %d.\n", result);
    // }

    /* De-initialize the HRM library. */
    // result = as7058a_hrm_a0_shutdown();
    // if (result != ERR_SUCCESS) {
    //    // printf("as7058a_hrm_a0_shutdown returned error %d.\n", result);
    // }

}

#endif