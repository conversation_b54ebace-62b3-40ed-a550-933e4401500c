#include <zephyr/devicetree.h>
#include <zephyr/kernel.h>
#include <zephyr/drivers/i2c.h>
#include <nrfx_twim.h>
#include <nrfx_twis.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/device.h>
#include <zephyr/as7058/as7058a_spo2_a0.h>
#include <nrfx_gpiote.h>

#include<zephyr/max30102/driver_max30102.h>
#include <zephyr/max30102/driver_max30102_fifo.h>
#include <stdio.h>
#include <zephyr/drivers/i2c.h>
#include <zephyr/as7058/vitalsigns_algorithms/spo2/inc/spo2_private.h>

#include <zephyr/as7058/vitalsigns_algorithms/spo2/inc/adcData.h>
#include <zephyr/as7058/vitalsigns_algorithms/spo2/inc/spo2.h>
#include <zephyr/as7058/vitalsigns_algorithms/spo2/inc/spo2_private.h>

#include <zephyr/as7058/as7058a_hrm_a0.h>
#include <hal/nrf_saadc.h>
#include <zephyr/drivers/adc.h>

#include <hal/nrf_rtc.h>
#include <zephyr/drivers/clock_control.h>
#include <zephyr/drivers/clock_control/nrf_clock_control.h>

#define SLEEP_TIME_MS   500

#define INTTERRUPT_ENABLE 1
#define MAX102ENABLE 0
#define AS7058ENABLE 1
#define BLE_ENABLE 1
#define AS7058_HR_ENABLE 0

#define SENSOR_NODE          DT_ALIAS(sensorpower)
#define INTR_NODE            DT_NODELABEL(sensor_intr)
#define I2C2_NODE            DT_NODELABEL(bio_sen)
#define I2C1_LIS33DH_NODE    DT_NODELABEL(accelerometer)
#define GREEN_LED_NODE       DT_ALIAS(bleled)

#if INTTERRUPT_ENABLE

static int External_interrupt_As7058 =0;
int HR_Enable_Flag =0;
int SPo2_Enable_Flag =0;
int Charging_Flag = 0;
int temperture_Flag =0;
extern int Ble_Notify_Flag;



/*Algo added*/

#define DATASAMPLES 1000 //24000->1000

typedef struct {
int red_avg;
int ir_avg;
int amb_avg;
}SPO2config;

#if !TESTRAW
uint32_t RED[1000] = {0}; //24000->1000
uint32_t IR[1000] = {0};
uint32_t AMB[1000] = {0};

#else
uint32_t RED[1001] = {166950,166976,167001,167024,167045,167064,167081,167095,167106,167113,167118,167121,167120,167118,167115,167110,167105,167099,167093,167087,167081,167076,167070,167065,167061,167056,167053,167049,167047,167045,167043,167042,167042,167042,167043,167044,167045,167046,167047,167047,167048,167049,167051,167053,167055,167059,167064,167070,167078,167087,167098,167109,167121,167135,167149,167163,167179,167195,167211,167228,167245,167261,167278,167294,167310,167326,167341,167355,167369,167383,167395,167405,167414,167421,167425,167426,167425,167420,167413,167403,167391,167377,167363,167348,167333,167319,167307,167296,167287,167279,167274,167269,167266,167263,167261,167259,167257,167256,167255,167254,167254,167256,167258,167262,167268,167275,167284,167295,167308,167321,167335,167349,167363,167376,167388,167399,167409,167416,167422,167426,167429,167429,167429,167427,167424,167419,167414,167408,167401,167394,167387,167381,167375,167370,167365,167362,167359,167357,167356,167355,167355,167355,167355,167356,167357,167358,167360,167362,167365,167369,167373,167378,167383,167389,167394,167400,167405,167410,167414,167418,167422,167425,167429,167432,167437,167441,167447,167453,167459,167465,167472,167478,167482,167486,167488,167487,167485,167479,167471,167461,167447,167431,167413,167393,167371,167348,167323,167298,167273,167247,167221,167195,167170,167145,167121,167098,167075,167054,167033,167014,166995,166978,166962,166948,166935,166924,166915,166908,166902,166898,166895,166894,166893,166893,166893,166894,166894,166895,166896,166897,166897,166897,166896,166895,166893,166890,166886,166882,166877,166872,166867,166861,166855,166850,166845,166840,166835,166831,166828,166824,166822,166820,166818,166818,166817,166818,166819,166820,166822,166824,166828,166832,166837,166844,166851,166860,166869,166880,166891,166903,166916,166929,166943,166958,166972,166986,167000,167012,167024,167035,167045,167055,167064,167072,167081,167088,167096,167104,167111,167118,167124,167129,167133,167137,167138,167139,167138,167136,167132,167126,167119,167111,167101,167089,167077,167062,167047,167030,167013,166995,166977,166959,166942,166925,166908,166893,166879,166865,166854,166843,166834,166827,166821,166817,166814,166813,166814,166816,166820,166824,166829,166835,166840,166846,166851,166856,166860,166864,166868,166872,166876,166880,166883,166886,166888,166890,166892,166892,166892,166891,166890,166888,166885,166883,166880,166877,166875,166873,166871,166871,166871,166872,166875,166879,166884,166891,166900,166910,166922,166935,166948,166962,166977,166991,167006,167020,167034,167048,167061,167075,167089,167102,167116,167129,167143,167156,167170,167183,167197,167210,167222,167235,167247,167258,167269,167280,167292,167303,167314,167326,167337,167349,167361,167372,167382,167390,167396,167400,167400,167397,167391,167381,167367,167350,167328,167303,167275,167243,167209,167172,167134,167095,167056,167017,166980,166944,166911,166880,166852,166826,166804,166784,166768,166755,166745,166738,166734,166732,166733,166736,166740,166745,166750,166756,166761,166766,166771,166774,166777,166780,166781,166782,166783,166782,166781,166778,166775,166771,166767,166762,166756,166750,166745,166740,166735,166732,166729,166728,166727,166728,166729,166730,166733,166735,166738,166741,166744,166748,166752,166758,166763,166769,166776,166783,166790,166797,166805,166812,166819,166826,166833,166840,166847,166855,166863,166872,166882,166893,166905,166918,166932,166948,166964,166982,167001,167021,167041,167062,167083,167104,167125,167145,167164,167182,167198,167212,167223,167232,167237,167238,167236,167229,167220,167206,167190,167170,167147,167122,167096,167069,167041,167012,166985,166958,166933,166909,166888,166869,166854,166842,166834,166829,166828,166832,166839,166851,166866,166883,166904,166926,166950,166975,167000,167024,167047,167068,167088,167105,167120,167133,167143,167152,167159,167165,167170,167174,167179,167183,167186,167190,167194,167197,167201,167204,167208,167212,167216,167220,167224,167229,167234,167239,167244,167249,167254,167259,167265,167271,167278,167286,167295,167305,167315,167325,167337,167349,167361,167373,167386,167398,167411,167423,167436,167448,167460,167471,167483,167494,167504,167515,167525,167535,167545,167555,167565,167575,167585,167594,167603,167609,167614,167617,167616,167613,167607,167597,167584,167569,167551,167531,167509,167485,167461,167436,167411,167386,167361,167337,167314,167292,167271,167251,167233,167215,167199,167183,167169,167156,167144,167135,167127,167122,167119,167118,167119,167123,167127,167133,167139,167145,167150,167155,167159,167162,167163,167164,167164,167163,167161,167159,167156,167153,167149,167145,167140,167135,167129,167123,167117,167111,167105,167099,167094,167090,167087,167085,167084,167085,167088,167091,167097,167103,167111,167120,167130,167141,167152,167163,167174,167184,167194,167203,167210,167217,167221,167225,167228,167229,167230,167231,167231,167232,167232,167233,167234,167237,167240,167244,167249,167255,167261,167268,167275,167283,167290,167296,167301,167304,167306,167305,167302,167297,167289,167279,167267,167253,167237,167218,167198,167177,167154,167130,167106,167082,167058,167035,167013,166992,166972,166954,166937,166920,166905,166891,166878,166866,166856,166846,166838,166831,166824,166819,166816,166813,166811,166810,166810,166811,166811,166813,166814,166815,166815,166815,166815,166814,166813,166812,166811,166811,166811,166812,166813,166816,166820,166824,166831,166838,166847,166856,166866,166877,166888,166900,166911,166923,166934,166946,166957,166969,166981,166992,167004,167015,167026,167036,167046,167054,167061,167067,167072,167076,167078,167080,167081,167083,167085,167088,167092,167097,167103,167110,167117,167125,167134,167142,167151,167160,167168,167176,167184,167190,167194,167196,167194,167189,167180,167168,167151,167131,167108,167081,167052,167020,166987,166953,166919,166885,166852,166821,166792,166765,166741,166718,166699,166681,166666,166653,166643,166636,166631,166630,166631,166636,166643,166652,166664,166677,166691,166705,166720,166734,166748,166762,166775,166788,166800,166810,166821,166830,166838,166846,166854,166861,166867,166874,166881,166889,166896,166905,166913,166922,166932,166941,166950,166960,166969,166980,166990,167001,167012,167024,167036,167048,167061,167075,167089,167104,167120,167136,167152,167168,167183,167198,167213,167226,167238,167249,167259,167269,167277,167285,167293,167301,167309,167317,167326,167334,167343,167352,167361,167371,167380,167390,167399,167407,167414,167419,167423,167424,167423,167419,167412,167403,167391,167376,167359,167340,167320,167298,167275,167251,167227,167204,167181,167159,167138,167118,167100,167084,167069,167055,167043,167032,167023,167015,167009,167004,166999,166996,166993,166991,166990,166989,166988,166988,166987,166987,166987,166986,166985,166985,166984,166982,166981,166980,166978,166976,166973,166970,166967,166963,166959,166955,166951,166948,166945,166942,166941,166940,166939,166940,166940,166942,166944,166947,166951,166956,166962,166968};

uint32_t IR[1001] = {258376,258368,258359,258350,258339,258328,258315,258301,258287,258272,258257,258243,258230,258218,258208,258199,258191,258183,258177,258171,258166,258163,258161,258160,258162,258165,258171,258180,258191,258205,258222,258242,258263,258287,258311,258338,258365,258395,258426,258459,258493,258528,258565,258601,258638,258673,258707,258740,258770,258798,258825,258850,258874,258896,258918,258940,258961,258983,259006,259030,259055,259082,259109,259137,259164,259190,259213,259234,259252,259264,259271,259271,259265,259250,259228,259200,259166,259127,259084,259039,258993,258948,258905,258865,258830,258800,258778,258762,258753,258750,258752,258758,258766,258774,258782,258789,258794,258797,258801,258805,258810,258818,258828,258842,258859,258879,258903,258930,258959,258990,259023,259055,259088,259118,259146,259170,259190,259207,259219,259228,259235,259239,259243,259245,259248,259251,259254,259258,259263,259269,259276,259284,259293,259302,259312,259324,259336,259349,259362,259376,259391,259407,259424,259441,259458,259473,259487,259499,259510,259518,259524,259530,259535,259540,259546,259553,259559,259566,259572,259578,259583,259587,259591,259595,259597,259599,259599,259598,259594,259587,259578,259567,259552,259535,259514,259488,259459,259425,259387,259347,259304,259260,259216,259171,259126,259081,259036,258992,258949,258909,258872,258839,258810,258785,258765,258748,258735,258725,258716,258709,258703,258698,258694,258690,258685,258679,258671,258660,258646,258629,258610,258589,258567,258543,258518,258492,258464,258436,258406,258376,258347,258319,258292,258267,258244,258222,258202,258184,258168,258155,258146,258140,258138,258139,258142,258148,258155,258164,258174,258186,258198,258212,258227,258242,258258,258273,258287,258300,258312,258323,258334,258346,258358,258373,258391,258412,258437,258466,258500,258539,258583,258630,258679,258730,258780,258830,258876,258917,258954,258985,259010,259030,259043,259050,259052,259047,259037,259022,259001,258976,258947,258914,258879,258842,258804,258766,258727,258688,258649,258609,258568,258528,258488,258450,258414,258381,258349,258319,258290,258262,258235,258207,258178,258148,258118,258086,258055,258023,257992,257962,257933,257907,257885,257866,257852,257842,257837,257836,257840,257847,257857,257869,257883,257897,257911,257923,257935,257946,257955,257963,257972,257980,257990,258000,258011,258021,258031,258040,258048,258055,258062,258070,258080,258092,258107,258126,258149,258176,258206,258239,258275,258314,258355,258399,258445,258494,258544,258595,258648,258701,258753,258805,258856,258907,258956,259006,259053,259100,259145,259187,259228,259266,259302,259335,259366,259395,259422,259446,259467,259485,259500,259511,259518,259521,259521,259518,259512,259504,259494,259482,259469,259456,259440,259424,259405,259385,259364,259339,259313,259283,259251,259214,259174,259129,259080,259025,258965,258900,258829,258754,258676,258595,258514,258434,258356,258283,258215,258153,258098,258050,258010,257978,257954,257938,257929,257928,257933,257944,257960,257981,258004,258029,258054,258077,258098,258115,258127,258135,258138,258138,258133,258126,258117,258105,258093,258079,258065,258050,258036,258022,258010,257999,257990,257984,257981,257979,257981,257985,257991,257999,258009,258021,258033,258046,258060,258075,258091,258109,258128,258148,258169,258191,258214,258237,258260,258284,258309,258336,258365,258397,258431,258469,258509,258550,258593,258636,258680,258724,258769,258812,258855,258896,258936,258974,259010,259045,259076,259105,259131,259155,259175,259192,259206,259215,259219,259217,259210,259196,259176,259151,259120,259083,259042,258996,258946,258892,258835,258775,258714,258652,258589,258527,258466,258406,258349,258295,258246,258203,258168,258142,258127,258122,258128,258143,258167,258198,258235,258278,258325,258374,258425,258476,258527,258576,258622,258664,258703,258736,258765,258790,258811,258829,258844,258857,258869,258879,258888,258897,258905,258913,258920,258927,258933,258938,258942,258945,258947,258949,258952,258956,258962,258970,258980,258992,259006,259020,259036,259052,259069,259086,259104,259123,259144,259167,259192,259218,259246,259276,259308,259341,259376,259412,259449,259486,259523,259560,259596,259630,259664,259696,259726,259754,259780,259802,259820,259834,259844,259848,259847,259839,259825,259805,259778,259744,259703,259656,259604,259547,259487,259425,259363,259300,259238,259177,259118,259061,259007,258956,258909,258866,258827,258793,258764,258739,258718,258701,258687,258675,258665,258657,258652,258648,258646,258646,258648,258652,258657,258663,258671,258679,258687,258697,258706,258715,258724,258733,258742,258751,258760,258768,258777,258785,258793,258802,258811,258821,258831,258840,258849,258856,258861,258864,258865,258863,258858,258850,258840,258826,258810,258794,258778,258763,258751,258743,258739,258740,258746,258757,258774,258795,258822,258854,258891,258931,258975,259022,259071,259120,259169,259216,259260,259301,259337,259369,259396,259418,259434,259444,259448,259447,259442,259431,259417,259399,259377,259352,259322,259288,259250,259208,259163,259115,259064,259012,258956,258899,258840,258779,258716,258652,258586,258520,258455,258392,258330,258272,258217,258165,258117,258074,258036,258003,257975,257953,257936,257925,257917,257914,257913,257916,257920,257926,257933,257942,257952,257963,257974,257985,257996,258005,258013,258018,258022,258025,258026,258026,258024,258022,258020,258020,258020,258023,258028,258036,258047,258060,258077,258097,258118,258142,258167,258194,258222,258253,258286,258322,258362,258406,258455,258508,258566,258627,258692,258757,258824,258890,258955,259019,259079,259136,259190,259239,259283,259322,259356,259385,259409,259427,259441,259450,259454,259452,259446,259436,259421,259403,259382,259359,259335,259308,259280,259248,259212,259172,259125,259071,259010,258942,258867,258787,258704,258618,258532,258447,258364,258285,258210,258140,258077,258020,257970,257927,257890,257859,257833,257811,257792,257777,257766,257758,257755,257755,257760,257770,257785,257803,257825,257849,257874,257900,257925,257951,257977,258002,258026,258049,258070,258089,258107,258123,258139,258154,258169,258184,258199,258216,258232,258250,258269,258289,258312,258336,258363,258392,258423,258455,258489,258525,258562,258599,258638,258677,258717,258757,258798,258839,258881,258924,258968,259011,259055,259098,259141,259183,259223,259262,259299,259335,259368,259399,259427,259454,259479,259503,259525,259545,259565,259582,259598,259611,259622,259630,259636,259638,259635,259629,259617,259600,259577,259548,259514,259474,259430,259383,259333,259281,259229,259176,259122,259069,259015,258962,258908,258855,258802,258751,258702,258655,258611,258569,258531,258497,258467,258439,258415,258393,258373,258355,258340,258327,258317,258309,258303,258299,258297,258295,258295,258295,258295,258295,258294,258294,258294,258293,258292,258290,258287,258285,258282,258279,258276,258274,258274,258277,258283,258292,258305,258322,258342,258366,258391,258419,258447,258475,258503,258531,258559,258586,258612,258637};

uint32_t AMB[1001] = {161204,161200,161206,161205,161201,161210,161220,161216,161210,161218,161224,161209,161220,161228,161223,161219,161217,161227,161221,161221,161220,161236,161217,161213,161218,161233,161221,161216,161220,161231,161235,161220,161223,161220,161227,161237,161225,161223,161224,161215,161217,161223,161231,161222,161218,161229,161230,161229,161227,161229,161234,161226,161230,161226,161234,161224,161237,161234,161230,161227,161235,161239,161233,161229,161232,161246,161233,161234,161236,161229,161231,161227,161228,161238,161231,161227,161237,161230,161232,161233,161227,161229,161246,161235,161249,161250,161254,161250,161248,161248,161243,161242,161241,161240,161243,161249,161252,161258,161253,161249,161255,161257,161249,161255,161253,161247,161253,161244,161250,161250,161251,161247,161257,161265,161254,161257,161254,161259,161259,161259,161240,161247,161248,161238,161246,161261,161258,161252,161251,161259,161253,161246,161265,161252,161244,161249,161260,161275,161246,161249,161247,161250,161268,161245,161255,161262,161262,161250,161256,161266,161266,161252,161258,161258,161269,161250,161253,161255,161252,161245,161259,161260,161265,161261,161262,161264,161258,161252,161262,161272,161270,161256,161261,161256,161272,161260,161253,161267,161263,161261,161259,161267,161263,161260,161262,161260,161261,161246,161259,161257,161256,161250,161252,161253,161249,161255,161255,161264,161265,161257,161251,161242,161248,161251,161247,161248,161256,161265,161251,161262,161263,161258,161262,161262,161263,161257,161254,161263,161259,161261,161257,161261,161263,161262,161266,161264,161258,161253,161263,161261,161270,161256,161258,161255,161267,161260,161262,161266,161269,161258,161254,161266,161265,161266,161268,161261,161275,161258,161268,161270,161271,161262,161267,161256,161266,161267,161265,161262,161251,161246,161250,161262,161263,161259,161257,161275,161260,161264,161261,161263,161264,161258,161267,161263,161277,161267,161259,161265,161278,161268,161261,161267,161274,161267,161267,161261,161263,161266,161261,161260,161267,161270,161267,161260,161273,161269,161264,161269,161272,161267,161267,161262,161266,161262,161257,161262,161269,161270,161262,161260,161270,161260,161267,161276,161270,161279,161263,161261,161267,161262,161257,161265,161267,161271,161267,161262,161268,161268,161262,161262,161259,161277,161279,161262,161272,161269,161274,161268,161271,161270,161259,161253,161265,161268,161263,161265,161274,161275,161263,161265,161267,161272,161256,161257,161259,161270,161264,161261,161268,161268,161257,161282,161265,161264,161264,161262,161273,161274,161267,161273,161284,161266,161269,161272,161281,161276,161273,161274,161275,161282,161260,161270,161279,161279,161275,161264,161286,161282,161274,161273,161274,161273,161273,161269,161280,161274,161256,161268,161277,161271,161265,161274,161278,161274,161285,161266,161280,161265,161270,161269,161269,161280,161275,161280,161287,161279,161276,161264,161269,161264,161268,161267,161270,161270,161251,161274,161267,161281,161266,161268,161271,161266,161262,161266,161275,161278,161271,161274,161270,161279,161259,161263,161279,161268,161263,161260,161280,161276,161263,161273,161270,161271,161262,161262,161271,161269,161273,161263,161277,161272,161253,161257,161272,161277,161258,161270,161271,161273,161267,161259,161279,161275,161267,161262,161260,161270,161265,161273,161284,161277,161277,161268,161281,161284,161266,161271,161272,161275,161269,161280,161276,161271,161264,161266,161273,161307,161266,161273,161275,161271,161262,161271,161267,161274,161273,161257,161264,161258,161241,161258,161259,161269,161253,161257,161268,161279,161249,161266,161269,161270,161278,161274,161279,161273,161274,161276,161285,161276,161272,161258,161279,161272,161260,161267,161278,161270,161267,161268,161280,161279,161264,161274,161283,161269,161266,161271,161274,161281,161278,161273,161280,161279,161269,161277,161271,161275,161268,161267,161273,161263,161271,161262,161277,161278,161276,161273,161281,161284,161275,161283,161283,161279,161280,161280,161268,161276,161279,161267,161274,161282,161269,161277,161276,161283,161280,161273,161279,161290,161287,161275,161289,161297,161282,161288,161279,161286,161285,161282,161285,161276,161282,161273,161283,161290,161282,161279,161289,161280,161276,161275,161276,161287,161277,161267,161264,161284,161285,161266,161282,161278,161273,161275,161276,161284,161266,161267,161278,161288,161275,161281,161279,161281,161281,161277,161275,161289,161267,161270,161270,161266,161269,161264,161277,161272,161274,161267,161276,161275,161282,161272,161271,161288,161272,161276,161278,161275,161273,161276,161282,161282,161265,161280,161271,161275,161279,161275,161281,161286,161270,161269,161272,161281,161280,161276,161283,161270,161278,161280,161274,161275,161273,161287,161278,161276,161276,161269,161279,161274,161279,161276,161270,161275,161277,161275,161273,161265,161267,161279,161276,161271,161271,161278,161285,161280,161283,161287,161283,161274,161271,161271,161275,161274,161267,161279,161280,161281,161280,161276,161271,161283,161281,161277,161267,161288,161274,161279,161275,161286,161279,161279,161281,161279,161267,161276,161282,161278,161278,161281,161279,161275,161270,161284,161281,161283,161281,161283,161285,161272,161278,161270,161274,161277,161270,161273,161281,161278,161277,161283,161285,161283,161272,161280,161290,161267,161274,161282,161282,161278,161283,161280,161262,161290,161281,161289,161286,161284,161291,161276,161273,161281,161285,161282,161286,161280,161277,161280,161290,161286,161276,161279,161279,161285,161274,161274,161280,161279,161272,161283,161284,161281,161271,161266,161272,161269,161278,161266,161277,161275,161261,161271,161277,161269,161271,161280,161284,161279,161283,161280,161279,161285,161278,161281,161276,161282,161268,161286,161295,161298,161277,161282,161299,161308,161286,161291,161280,161290,161289,161295,161294,161285,161289,161285,161279,161288,161283,161276,161278,161280,161280,161275,161283,161280,161284,161289,161280,161291,161272,161284,161277,161274,161269,161278,161282,161274,161275,161277,161280,161287,161290,161281,161286,161297,161288,161282,161282,161286,161275,161286,161287,161280,161281,161282,161287,161292,161281,161282,161284,161290,161289,161285,161292,161292,161289,161285,161288,161294,161294,161294,161279,161300,161289,161285,161287,161297,161295,161286,161274,161290,161281,161278,161278,161282,161291,161290,161289,161298,161293,161284,161291,161296,161289,161282,161284,161288,161298,161289,161296,161293,161286,161289,161286,161284,161283,161273,161290,161282,161284,161289,161295,161290,161293,161292,161290,161289,161288,161283,161283,161293,161282,161287,161290,161289,161288,161294,161291,161297,161282,161289,161282,161284,161289,161289,161272,161274,161281,161281,161278,161277,161287,161291,161290,161290,161290,161283,161289,161291,161293,161291,161284,161294,161287,161283,161283,161285,161292,161275,161287,161291,161280,161290,161300,161297,161294,161290,161298,161285,161290,161284,161292,161295};

#endif

spo2AlgorithmOutput_t g_SPO2_out;
spo2AlgorithmInput_t g_spo2_input;
SPO2config g_spo2config;

uint16_t RED_u16[DATASAMPLES] = {0};
uint16_t IR_u16[DATASAMPLES] = {0};
uint16_t AMB_u16[DATASAMPLES] = {0};

void prepare_input(uint32_t *input,uint16_t *p_output)
{

     uint8_t bit_shift_num = 3;
    uint32_t t = *input;
    uint32_t temp = t >> bit_shift_num;

    if (temp == 0xffff + 1) {
        // Value is one bit greater in case of saturation //
        temp--;
    }

    *p_output = (uint16_t)temp;

}

spo2AlgorithmCalibration_t Awtcalibration = {0,6247,9351,495,495};
void RawDataPassAlgo(uint32_t *Redr, uint32_t *Irr,uint32_t *Amr)
{
    uint32_t X=0,RPD =100,IRPD=100;

    spo2UpdateCalibration(&Awtcalibration);
     for(int i=0; i<DATASAMPLES;i++)
    {   
        #if !UMESH
        prepare_input(&Redr[i],&RED_u16[i]);
        prepare_input(&Irr[i],&IR_u16[i]);
        prepare_input(&AMB[i],&AMB_u16[i]);
        #else
        prepare_input(&RED[i],&RED_u16[i]);
        prepare_input(&IR[i],&IR_u16[i]);
        prepare_input(&AMB[i],&AMB_u16[i]);
        #endif
    }
    
    spo2Initialise(0,&g_SPO2_out);

 for (int i=0;i<DATASAMPLES;i+=200)
    {
        for(int j=i,l=0,m=0, k=0;k<200;k++,j++)
        {

        g_spo2config.ir_avg +=  IR_u16[j];
        g_spo2config.red_avg +=  RED_u16[j];
        g_spo2config.amb_avg += AMB_u16[j];

        l++;
        if(l>9)
        {

            g_spo2_input.infrared[m].absValue = g_spo2config.ir_avg / l;
            g_spo2_input.red[m].absValue = g_spo2config.red_avg / l;
            g_spo2_input.ambient[m] = g_spo2config.amb_avg / l ;
            g_spo2_input.pdOffsetIR[m] = IRPD;
            g_spo2_input.pdOffsetRed[m] = RPD;
            m++;

             l=0;
             memset(&g_spo2config,0,sizeof(g_spo2config));
        }

        }
        spo2Input(&g_spo2_input);
        spo2Calculate(&g_SPO2_out);
        printk("g_SPO2_out.spo2 = %d\n",g_SPO2_out.spo2);

        if(g_SPO2_out.spo2 >= 100)
            g_SPO2_out.spo2 = 100;

        if(g_SPO2_out.spo2 >= 80)
        {
           senddata_over_ble(g_SPO2_out.spo2);
        }
    }
}

void CallBackExternal(void)
{

    External_interrupt_As7058=1;

}

/***************************External Interrupt Init********/
void External_GpioPIn_init(void)
{
    int ret;
    static struct gpio_callback sensor_cb_data;
    static const struct gpio_dt_spec sensor_gpio = GPIO_DT_SPEC_GET(INTR_NODE, gpios); 

    if (!device_is_ready(sensor_gpio.port)) {
        return;
    }

    ret = gpio_pin_configure_dt(&sensor_gpio, GPIO_INPUT | GPIO_PULL_UP | GPIO_INT_ENABLE);

    if (ret < 0) 
    {
        return;
    }

    ret = gpio_pin_interrupt_configure_dt(&sensor_gpio, GPIO_INT_EDGE_RISING);//GPIO_INT_EDGE_TO_ACTIVE); GPIO_INT_EDGE_RISING
    gpio_init_callback(&sensor_cb_data, CallBackExternal, BIT(sensor_gpio.pin));
    gpio_add_callback(sensor_gpio.port, &sensor_cb_data);

}
#endif

void IDreadAS7058(void)
{
    uint8_t retvalu = 0;
    uint8_t Address = 0xAB; //As7058
    uint8_t valu=0;
    static const struct i2c_dt_spec dev_i2c1 = I2C_DT_SPEC_GET(I2C2_NODE);
    retvalu = i2c_burst_read_dt(&dev_i2c1,Address,&valu,1);
    printk("valu = %d\n",valu);
    if(retvalu==0)
    {
        return 0;
    }
}

// /************chrage controlling******************/
const struct device *adc_dev;

#define ADC_RESOLUTION 12
#define ADC_GAIN ADC_GAIN_1_6

#define ADC_ACQUISITION_TIME ADC_ACQ_TIME(ADC_ACQ_TIME_MICROSECONDS, 10)
#define ADC_CHANNEL_ID 0
#define ADC_CHANNEL_INPUT NRF_SAADC_INPUT_AIN0

#define BATTERY_MAX_VOLTAGE 4.2000   // Maximum voltage corresponding to 100%
#define BATTERY_MIN_VOLTAGE 3.5000   // Minimum voltage corresponding to 0%


#define BUFFER_SIZE 1
static int16_t sample_buffer[BUFFER_SIZE];

static const struct adc_channel_cfg channel_cfg = {
    .gain = ADC_GAIN,
    .reference = ADC_REF_INTERNAL,//ADC_REF_VDD_1_4,
    .acquisition_time = ADC_ACQUISITION_TIME,
    .channel_id = ADC_CHANNEL_ID,
#if defined(CONFIG_ADC_CONFIGURABLE_INPUTS)
    .input_positive = ADC_CHANNEL_INPUT,
#endif
};

const struct adc_sequence sequence = {
    .channels = BIT(ADC_CHANNEL_ID),
    .buffer = sample_buffer,
    .buffer_size = sizeof(sample_buffer),
    .resolution = ADC_RESOLUTION,
};

void adc_sample(void)
{
    int ret = adc_read(adc_dev, &sequence);
    if (ret) {
        printk("ADC reading failed with code %d\n", ret);
    } else {
      
       float volts = (sample_buffer[0]  * (3.6))/4096;
       float voltage = (volts * 1.68); // (R1+R2/R1) // (100+68)/100 ohm
     

  // Calculate battery percentage
        float battery = ((voltage - BATTERY_MIN_VOLTAGE) / (BATTERY_MAX_VOLTAGE - BATTERY_MIN_VOLTAGE))*100;
        int bat = battery;
        printk("ADC Reading: %d, Voltage: %.4f V,  Battery: %.4f%%\n", sample_buffer[0], voltage, battery);
        senddata_over_ble(bat);
    }
}

void enable_charing(void)
{
    adc_dev = DEVICE_DT_GET_ONE(nordic_nrf_saadc);
    if (!device_is_ready(adc_dev)) {
        printk("ADC device not found\n");
        return;
    }

    adc_channel_setup(adc_dev, &channel_cfg);

}

void Bt_Notify_to_start_as7058(void)
{
    
    if(Ble_Notify_Flag == 1)
    {
       Ble_Notify_Flag = 0; 
       SPo2_Enable_Flag = 1;
       As7058_Spo2_Init();

    }
    if(Ble_Notify_Flag == 2)
    {
        Ble_Notify_Flag = 0; 
        HR_Enable_Flag = 2;
        // HeartRateInit();
        as7058_stop_measurement();
        // uint8_t buff[240]={1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240};
        //  Ble_Notify_Flag = 0;
        //  for(int c=1;c<10;c++)
        //  {
        //     send_rawdata_over_BLE(buff,180);
        //    // k_sleep(K_MSEC(1));
        //  }
    }

    if(Ble_Notify_Flag == 3)
    {
         Ble_Notify_Flag = 0; 
         Charging_Flag = 3;
         adc_sample();
    }

    if(External_interrupt_As7058 == 1)
    {
       External_interrupt_As7058 = 0;

       if(SPo2_Enable_Flag == 1)
       {
         interrupt_handler();
       }

       if(HR_Enable_Flag == 2)
       {
          interrupt_handler();
       }

    }
   

}

void Green_LED_Set_Reset(uint8_t val)
{
    int ret;
    
    static const struct gpio_dt_spec gled_t = GPIO_DT_SPEC_GET(GREEN_LED_NODE, gpios);
    if (!gpio_is_ready_dt(&gled_t)) {
		return 0;
	}
    ret = gpio_pin_configure_dt(&gled_t, GPIO_OUTPUT_ACTIVE);
    if (ret < 0) 
    {
        return;
    }
    ret = gpio_pin_set_dt(&gled_t, val); //0 ->set 1-> reset
    if (ret < 0) 
    {
    return;
    }

}


/******************************MAIN *********************************/
extern void send_ble_buff(void);
static struct k_timer timer_50ms;

#define GPIO_PORT DT_NODELABEL(gpio1)
#define PIN 10

void main(void)
{

    const struct device *gpio_dev;
    // int ret;

printf("Hello World! %s\n", CONFIG_BOARD);

#if INTTERRUPT_ENABLE
   External_GpioPIn_init();
#endif
     gpio_pin_set(&gpio_dev, PIN, 1);// Enable sensor ON 
#if BLE_ENABLE
    ble_init();
#endif
    z_nrf_clock_control_get_onoff(CLOCK_CONTROL_NRF_SUBSYS_HF);

int input = 5;
float result;

    static const struct gpio_dt_spec sensor_t = GPIO_DT_SPEC_GET(SENSOR_NODE, gpios);
    int ret;
    if (!gpio_is_ready_dt(&sensor_t)) {
		return 0;
	}
    ret = gpio_pin_configure_dt(&sensor_t, GPIO_OUTPUT);
    if (ret < 0) 
    {
        return;
    }
    ret = gpio_pin_set_dt(&sensor_t, 1);
    if (ret < 0) 
    {
        return;
    }
   //k_timer_init(&timer_50ms, send_ble_buff, NULL);

   //k_timer_start(&timer_50ms, K_MSEC(0), K_MSEC(168));

    enable_charing();


#if AS7058ENABLE
   IDreadAS7058();
#endif

    while (1)
    {   
    //  k_sleep(K_MSEC(100));
 
       
    //   k_sleep(K_MSEC(1000));
    //   gpio_pin_set(&gpio_dev, PIN, 0);
    k_sleep(K_MSEC(1));
    Bt_Notify_to_start_as7058();
    send_ble_buff();
    }
}
