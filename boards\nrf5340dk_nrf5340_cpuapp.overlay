// To get started, press Ctrl+Space to bring up the completion menu and view the available nodes.

// You can also use the buttons in the sidebar to perform actions on nodes.
// Actions currently available include:

// * Enabling / disabling the node
// * Adding the bus to a bus
// * Removing the node
// * Connecting ADC channels

// For more help, browse the DeviceTree documentation at https://docs.zephyrproject.org/latest/guides/dts/index.html
// You can also visit the nRF DeviceTree extension documentation at https://nrfconnect.github.io/vscode-nrf-connect/devicetree/nrfdevicetree.html

&i2c2 {
    compatible = "nordic,nrf-twim";
    status = "okay";
    clock-frequency = <I2C_BITRATE_FAST>; //umesh added 400 KBits/sec speed
    pinctrl-0 = <&i2c2_default>;
    pinctrl-1 = <&i2c2_sleep>;
    pinctrl-names = "default", "sleep";
    zephyr,concat-buf-size = <1024>;
    bio_sen: bio_sen@55{
        compatible = "i2c-device";
        reg = <0x55>;  //0x57->max, 0x55->as7058
        label = "BIO_SEN";
        int-gpios = <&gpio0 27(GPIO_PULL_UP | GPIO_ACTIVE_LOW)>; 
    };
};
&i2c1 {
    status = "disabled";
};
// /delete-node/ &i2c1_default;
// /delete-node/ &i2c1_sleep;
&pinctrl {
    i2c2_default: i2c2_default {
        group1 {
            psels = <NRF_PSEL(TWIM_SDA, 1, 2)>,
                <NRF_PSEL(TWIM_SCL, 1, 3)>;
        };
    };
    i2c2_sleep: i2c2_sleep {
        group1 {
            psels = <NRF_PSEL(TWIM_SDA, 1, 2)>,
                <NRF_PSEL(TWIM_SCL, 1, 3)>;
        };
    };
};
