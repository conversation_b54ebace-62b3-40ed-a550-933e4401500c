add_custom_target(devicetree_target)

set_target_properties(devicetree_target PROPERTIES "DT_CHOSEN|zephyr,entropy" "/soc/random@41009000")
set_target_properties(devicetree_target PROPERTIES "DT_CHOSEN|zephyr,flash-controller" "/soc/flash-controller@41080000")
set_target_properties(devicetree_target PROPERTIES "DT_CHOSEN|zephyr,console" "/soc/uart@41013000")
set_target_properties(devicetree_target PROPERTIES "DT_CHOSEN|zephyr,shell-uart" "/soc/uart@41013000")
set_target_properties(devicetree_target PROPERTIES "DT_CHOSEN|zephyr,uart-mcumgr" "/soc/uart@41013000")
set_target_properties(devicetree_target PROPERTIES "DT_CHOSEN|zephyr,bt-mon-uart" "/soc/uart@41013000")
set_target_properties(devicetree_target PROPERTIES "DT_CHOSEN|zephyr,bt-c2h-uart" "/soc/uart@41013000")
set_target_properties(devicetree_target PROPERTIES "DT_CHOSEN|zephyr,bt-hci-rpmsg-ipc" "/ipc/ipc0")
set_target_properties(devicetree_target PROPERTIES "DT_CHOSEN|nordic,802154-spinel-ipc" "/ipc/ipc0")
set_target_properties(devicetree_target PROPERTIES "DT_CHOSEN|zephyr,sram" "/soc/memory@21000000")
set_target_properties(devicetree_target PROPERTIES "DT_CHOSEN|zephyr,flash" "/soc/flash-controller@41080000/flash@1000000")
set_target_properties(devicetree_target PROPERTIES "DT_CHOSEN|zephyr,code-partition" "/soc/flash-controller@41080000/flash@1000000/partitions/partition@c000")
set_target_properties(devicetree_target PROPERTIES "DT_CHOSEN|zephyr,ieee802154" "/soc/radio@41008000/ieee802154")
set_target_properties(devicetree_target PROPERTIES "DT_CHOSEN|zephyr,ipc_shm" "/reserved-memory/memory@20070000")
set_target_properties(devicetree_target PROPERTIES "DT_ALIAS|watchdog0" "/soc/watchdog@4100b000")
set_target_properties(devicetree_target PROPERTIES "DT_ALIAS|led0" "/leds/led_0")
set_target_properties(devicetree_target PROPERTIES "DT_ALIAS|bootloader-led0" "/leds/led_0")
set_target_properties(devicetree_target PROPERTIES "DT_ALIAS|mcuboot-led0" "/leds/led_0")
set_target_properties(devicetree_target PROPERTIES "DT_ALIAS|led1" "/leds/led_1")
set_target_properties(devicetree_target PROPERTIES "DT_ALIAS|led2" "/leds/led_2")
set_target_properties(devicetree_target PROPERTIES "DT_ALIAS|led3" "/leds/led_3")
set_target_properties(devicetree_target PROPERTIES "DT_ALIAS|sw0" "/buttons/button_0")
set_target_properties(devicetree_target PROPERTIES "DT_ALIAS|mcuboot-button0" "/buttons/button_0")
set_target_properties(devicetree_target PROPERTIES "DT_ALIAS|sw1" "/buttons/button_1")
set_target_properties(devicetree_target PROPERTIES "DT_ALIAS|sw2" "/buttons/button_2")
set_target_properties(devicetree_target PROPERTIES "DT_ALIAS|sw3" "/buttons/button_3")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/|compatible" "nordic,nrf5340-dk-nrf5340-cpunet;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/chosen" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_REG|/chosen|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/chosen|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/chosen|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/aliases" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_REG|/aliases|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/aliases|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/aliases|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc|compatible" "nordic,nrf5340-cpunet-qkaa;nordic,nrf5340-cpunet;nordic,nrf53;simple-bus;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc|ranges" "None")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/interrupt-controller@e000e100" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|nvic" "/soc/interrupt-controller@e000e100")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/interrupt-controller@e000e100|reg" "3758153984;3072;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/interrupt-controller@e000e100|arm,num-irq-priority-bits" "3")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/interrupt-controller@e000e100|interrupt-controller" "True")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/interrupt-controller@e000e100|compatible" "arm,v8m-nvic;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/interrupt-controller@e000e100|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/interrupt-controller@e000e100|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/interrupt-controller@e000e100|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/interrupt-controller@e000e100|ADDR" "0xe000e100;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/interrupt-controller@e000e100|SIZE" "0xc00;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/timer@e000e010" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|systick" "/soc/timer@e000e010")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@e000e010|reg" "3758153744;16;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@e000e010|status" "disabled")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@e000e010|compatible" "arm,armv8m-systick;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@e000e010|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@e000e010|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/timer@e000e010|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/timer@e000e010|ADDR" "0xe000e010;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/timer@e000e010|SIZE" "0x10;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/ficr@1ff0000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|ficr" "/soc/ficr@1ff0000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ficr@1ff0000|reg" "33488896;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ficr@1ff0000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ficr@1ff0000|compatible" "nordic,nrf-ficr;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ficr@1ff0000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ficr@1ff0000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/ficr@1ff0000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/ficr@1ff0000|ADDR" "0x1ff0000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/ficr@1ff0000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/uicr@1ff8000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|uicr" "/soc/uicr@1ff8000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uicr@1ff8000|reg" "33521664;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uicr@1ff8000|nfct-pins-as-gpios" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uicr@1ff8000|gpio-as-nreset" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uicr@1ff8000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uicr@1ff8000|compatible" "nordic,nrf-uicr;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uicr@1ff8000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uicr@1ff8000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/uicr@1ff8000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/uicr@1ff8000|ADDR" "0x1ff8000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/uicr@1ff8000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/memory@20000000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|sram0" "/soc/memory@20000000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/memory@20000000|reg" "536870912;524288;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/memory@20000000|compatible" "mmio-sram;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/memory@20000000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/memory@20000000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/memory@20000000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/memory@20000000|ADDR" "0x20000000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/memory@20000000|SIZE" "0x80000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/memory@21000000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|sram1" "/soc/memory@21000000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/memory@21000000|zephyr,memory-region" "SRAM1")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/memory@21000000|compatible" "zephyr,memory-region;mmio-sram;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/memory@21000000|reg" "553648128;65536;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/memory@21000000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/memory@21000000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/memory@21000000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/memory@21000000|ADDR" "0x21000000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/memory@21000000|SIZE" "0x10000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/clock@41005000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|clock" "/soc/clock@41005000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/clock@41005000|reg" "1090539520;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/clock@41005000|interrupts" "5;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/clock@41005000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/clock@41005000|compatible" "nordic,nrf-clock;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/clock@41005000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/clock@41005000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/clock@41005000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/clock@41005000|ADDR" "0x41005000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/clock@41005000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/power@41005000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|power" "/soc/power@41005000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/power@41005000|reg" "1090539520;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/power@41005000|interrupts" "5;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/power@41005000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/power@41005000|compatible" "nordic,nrf-power;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/power@41005000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/power@41005000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/power@41005000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/power@41005000|ADDR" "0x41005000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/power@41005000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/power@41005000/gpregret1@4100551c" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|gpregret1" "/soc/power@41005000/gpregret1@4100551c")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/power@41005000/gpregret1@4100551c|reg" "1090540828;1;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/power@41005000/gpregret1@4100551c|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/power@41005000/gpregret1@4100551c|compatible" "nordic,nrf-gpregret;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/power@41005000/gpregret1@4100551c|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/power@41005000/gpregret1@4100551c|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/power@41005000/gpregret1@4100551c|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/power@41005000/gpregret1@4100551c|ADDR" "0x4100551c;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/power@41005000/gpregret1@4100551c|SIZE" "0x1;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/power@41005000/gpregret2@41005520" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|gpregret2" "/soc/power@41005000/gpregret2@41005520")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/power@41005000/gpregret2@41005520|reg" "1090540832;1;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/power@41005000/gpregret2@41005520|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/power@41005000/gpregret2@41005520|compatible" "nordic,nrf-gpregret;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/power@41005000/gpregret2@41005520|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/power@41005000/gpregret2@41005520|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/power@41005000/gpregret2@41005520|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/power@41005000/gpregret2@41005520|ADDR" "0x41005520;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/power@41005000/gpregret2@41005520|SIZE" "0x1;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/radio@41008000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|radio" "/soc/radio@41008000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000|reg" "1090551808;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000|interrupts" "8;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000|dfe-supported" "True")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000|dfe-antenna-num" "12")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000|dfe-pdu-antenna" "0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000|ieee802154-supported" "True")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000|ble-2mbps-supported" "True")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000|ble-coded-phy-supported" "True")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000|tx-high-power-supported" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000|compatible" "nordic,nrf-radio;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/radio@41008000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/radio@41008000|ADDR" "0x41008000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/radio@41008000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/radio@41008000/ieee802154" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|ieee802154" "/soc/radio@41008000/ieee802154")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000/ieee802154|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000/ieee802154|compatible" "nordic,nrf-ieee802154;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000/ieee802154|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/radio@41008000/ieee802154|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/radio@41008000/ieee802154|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/radio@41008000/ieee802154|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/radio@41008000/ieee802154|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/random@41009000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|rng" "/soc/random@41009000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/random@41009000|reg" "1090555904;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/random@41009000|interrupts" "9;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/random@41009000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/random@41009000|compatible" "nordic,nrf-rng;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/random@41009000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/random@41009000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/random@41009000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/random@41009000|ADDR" "0x41009000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/random@41009000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/gpiote@4100a000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|gpiote" "/soc/gpiote@4100a000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpiote@4100a000|reg" "1090560000;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpiote@4100a000|interrupts" "10;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpiote@4100a000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpiote@4100a000|compatible" "nordic,nrf-gpiote;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpiote@4100a000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpiote@4100a000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/gpiote@4100a000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/gpiote@4100a000|ADDR" "0x4100a000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/gpiote@4100a000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/watchdog@4100b000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|wdt" "/soc/watchdog@4100b000")
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|wdt0" "/soc/watchdog@4100b000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/watchdog@4100b000|reg" "1090564096;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/watchdog@4100b000|interrupts" "11;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/watchdog@4100b000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/watchdog@4100b000|compatible" "nordic,nrf-wdt;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/watchdog@4100b000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/watchdog@4100b000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/watchdog@4100b000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/watchdog@4100b000|ADDR" "0x4100b000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/watchdog@4100b000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/timer@4100c000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|timer0" "/soc/timer@4100c000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@4100c000|reg" "1090568192;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@4100c000|cc-num" "8")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@4100c000|max-bit-width" "32")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@4100c000|interrupts" "12;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@4100c000|prescaler" "0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@4100c000|zli" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@4100c000|status" "disabled")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@4100c000|compatible" "nordic,nrf-timer;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@4100c000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@4100c000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/timer@4100c000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/timer@4100c000|ADDR" "0x4100c000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/timer@4100c000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/ecb@4100d000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|ecb" "/soc/ecb@4100d000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ecb@4100d000|reg" "1090572288;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ecb@4100d000|interrupts" "13;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ecb@4100d000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ecb@4100d000|compatible" "nordic,nrf-ecb;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ecb@4100d000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ecb@4100d000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/ecb@4100d000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/ecb@4100d000|ADDR" "0x4100d000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/ecb@4100d000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/ccm@4100e000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|ccm" "/soc/ccm@4100e000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ccm@4100e000|reg" "1090576384;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ccm@4100e000|interrupts" "14;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ccm@4100e000|length-field-length-8-bits" "True")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ccm@4100e000|headermask-supported" "True")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ccm@4100e000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ccm@4100e000|compatible" "nordic,nrf-ccm;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ccm@4100e000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/ccm@4100e000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/ccm@4100e000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/ccm@4100e000|ADDR" "0x4100e000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/ccm@4100e000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/dppic@4100f000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|dppic" "/soc/dppic@4100f000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/dppic@4100f000|reg" "1090580480;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/dppic@4100f000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/dppic@4100f000|compatible" "nordic,nrf-dppic;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/dppic@4100f000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/dppic@4100f000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/dppic@4100f000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/dppic@4100f000|ADDR" "0x4100f000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/dppic@4100f000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/temp@41010000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|temp" "/soc/temp@41010000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/temp@41010000|reg" "1090584576;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/temp@41010000|interrupts" "16;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/temp@41010000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/temp@41010000|compatible" "nordic,nrf-temp;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/temp@41010000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/temp@41010000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/temp@41010000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/temp@41010000|ADDR" "0x41010000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/temp@41010000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/rtc@41011000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|rtc0" "/soc/rtc@41011000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41011000|reg" "1090588672;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41011000|cc-num" "4")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41011000|ppi-wrap" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41011000|fixed-top" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41011000|zli" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41011000|interrupts" "17;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41011000|status" "disabled")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41011000|compatible" "nordic,nrf-rtc;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41011000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41011000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/rtc@41011000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/rtc@41011000|ADDR" "0x41011000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/rtc@41011000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/mbox@41012000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|mbox" "/soc/mbox@41012000")
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|ipc" "/soc/mbox@41012000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/mbox@41012000|tx-mask" "65535")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/mbox@41012000|rx-mask" "65535")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/mbox@41012000|interrupts" "18;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/mbox@41012000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/mbox@41012000|compatible" "nordic,mbox-nrf-ipc;nordic,nrf-ipc;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/mbox@41012000|reg" "1090592768;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/mbox@41012000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/mbox@41012000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/mbox@41012000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/mbox@41012000|ADDR" "0x41012000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/mbox@41012000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/i2c@41013000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|i2c0" "/soc/i2c@41013000")
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|arduino_i2c" "/soc/i2c@41013000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/i2c@41013000|zephyr,concat-buf-size" "16")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/i2c@41013000|zephyr,flash-buf-max-size" "16")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/i2c@41013000|reg" "1090596864;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/i2c@41013000|interrupts" "19;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/i2c@41013000|clock-frequency" "100000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/i2c@41013000|status" "disabled")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/i2c@41013000|compatible" "nordic,nrf-twim;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/i2c@41013000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/i2c@41013000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/i2c@41013000|pinctrl-names" "default;sleep;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/i2c@41013000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/i2c@41013000|ADDR" "0x41013000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/i2c@41013000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/spi@41013000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|spi0" "/soc/spi@41013000")
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|arduino_spi" "/soc/spi@41013000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/spi@41013000|anomaly-58-workaround" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/spi@41013000|rx-delay-supported" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/spi@41013000|reg" "1090596864;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/spi@41013000|interrupts" "19;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/spi@41013000|max-frequency" "8000000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/spi@41013000|overrun-character" "255")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/spi@41013000|easydma-maxcnt-bits" "16")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/spi@41013000|status" "disabled")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/spi@41013000|compatible" "nordic,nrf-spim;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/spi@41013000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/spi@41013000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/spi@41013000|pinctrl-names" "default;sleep;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/spi@41013000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/spi@41013000|ADDR" "0x41013000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/spi@41013000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/uart@41013000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|uart0" "/soc/uart@41013000")
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|arduino_serial" "/soc/uart@41013000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uart@41013000|reg" "1090596864;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uart@41013000|interrupts" "19;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uart@41013000|disable-rx" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uart@41013000|current-speed" "115200")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uart@41013000|hw-flow-control" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uart@41013000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uart@41013000|compatible" "nordic,nrf-uarte;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uart@41013000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uart@41013000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/uart@41013000|pinctrl-names" "default;sleep;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/uart@41013000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/uart@41013000|ADDR" "0x41013000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/uart@41013000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/egu@41014000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|egu0" "/soc/egu@41014000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/egu@41014000|reg" "1090600960;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/egu@41014000|interrupts" "20;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/egu@41014000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/egu@41014000|compatible" "nordic,nrf-egu;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/egu@41014000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/egu@41014000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/egu@41014000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/egu@41014000|ADDR" "0x41014000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/egu@41014000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/rtc@41016000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|rtc1" "/soc/rtc@41016000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41016000|reg" "1090609152;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41016000|cc-num" "4")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41016000|ppi-wrap" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41016000|fixed-top" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41016000|zli" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41016000|interrupts" "22;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41016000|status" "disabled")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41016000|compatible" "nordic,nrf-rtc;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41016000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/rtc@41016000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/rtc@41016000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/rtc@41016000|ADDR" "0x41016000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/rtc@41016000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/timer@41018000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|timer1" "/soc/timer@41018000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41018000|reg" "1090617344;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41018000|cc-num" "8")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41018000|max-bit-width" "32")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41018000|interrupts" "24;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41018000|prescaler" "0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41018000|zli" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41018000|status" "disabled")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41018000|compatible" "nordic,nrf-timer;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41018000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41018000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/timer@41018000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/timer@41018000|ADDR" "0x41018000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/timer@41018000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/timer@41019000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|timer2" "/soc/timer@41019000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41019000|reg" "1090621440;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41019000|cc-num" "8")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41019000|max-bit-width" "32")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41019000|interrupts" "25;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41019000|prescaler" "0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41019000|zli" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41019000|status" "disabled")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41019000|compatible" "nordic,nrf-timer;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41019000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/timer@41019000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/timer@41019000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/timer@41019000|ADDR" "0x41019000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/timer@41019000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/swi@4101a000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|swi0" "/soc/swi@4101a000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101a000|reg" "1090625536;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101a000|interrupts" "26;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101a000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101a000|compatible" "nordic,nrf-swi;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101a000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101a000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/swi@4101a000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/swi@4101a000|ADDR" "0x4101a000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/swi@4101a000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/swi@4101b000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|swi1" "/soc/swi@4101b000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101b000|reg" "1090629632;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101b000|interrupts" "27;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101b000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101b000|compatible" "nordic,nrf-swi;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101b000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101b000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/swi@4101b000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/swi@4101b000|ADDR" "0x4101b000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/swi@4101b000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/swi@4101c000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|swi2" "/soc/swi@4101c000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101c000|reg" "1090633728;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101c000|interrupts" "28;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101c000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101c000|compatible" "nordic,nrf-swi;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101c000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101c000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/swi@4101c000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/swi@4101c000|ADDR" "0x4101c000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/swi@4101c000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/swi@4101d000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|swi3" "/soc/swi@4101d000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101d000|reg" "1090637824;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101d000|interrupts" "29;5;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101d000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101d000|compatible" "nordic,nrf-swi;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101d000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/swi@4101d000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/swi@4101d000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/swi@4101d000|ADDR" "0x4101d000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/swi@4101d000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/acl@41080000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|acl" "/soc/acl@41080000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/acl@41080000|reg" "1091043328;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/acl@41080000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/acl@41080000|compatible" "nordic,nrf-acl;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/acl@41080000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/acl@41080000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/acl@41080000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/acl@41080000|ADDR" "0x41080000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/acl@41080000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/flash-controller@41080000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|flash_controller" "/soc/flash-controller@41080000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000|partial-erase" "True")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000|reg" "1091043328;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000|compatible" "nordic,nrf53-flash-controller;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000|ADDR" "0x41080000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/flash-controller@41080000/flash@1000000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|flash1" "/soc/flash-controller@41080000/flash@1000000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000|erase-block-size" "2048")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000|write-block-size" "4")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000|compatible" "soc-nv-flash;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000|reg" "16777216;262144;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000|ADDR" "0x1000000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000|SIZE" "0x40000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/flash-controller@41080000/flash@1000000/partitions" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000/partitions|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000/partitions|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000/partitions|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/flash-controller@41080000/flash@1000000/partitions/partition@0" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|boot_partition" "/soc/flash-controller@41080000/flash@1000000/partitions/partition@0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000/partitions/partition@0|label" "mcuboot")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000/partitions/partition@0|read-only" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000/partitions/partition@0|reg" "0;49152;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000/partitions/partition@0|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000/partitions/partition@0|ADDR" "0x0;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000/partitions/partition@0|SIZE" "0xc000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/flash-controller@41080000/flash@1000000/partitions/partition@c000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|slot0_partition" "/soc/flash-controller@41080000/flash@1000000/partitions/partition@c000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000/partitions/partition@c000|label" "image-0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000/partitions/partition@c000|read-only" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000/partitions/partition@c000|reg" "49152;94208;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000/partitions/partition@c000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000/partitions/partition@c000|ADDR" "0xc000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000/partitions/partition@c000|SIZE" "0x17000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/flash-controller@41080000/flash@1000000/partitions/partition@23000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|slot1_partition" "/soc/flash-controller@41080000/flash@1000000/partitions/partition@23000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000/partitions/partition@23000|label" "image-1")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000/partitions/partition@23000|read-only" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000/partitions/partition@23000|reg" "143360;94208;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000/partitions/partition@23000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000/partitions/partition@23000|ADDR" "0x23000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000/partitions/partition@23000|SIZE" "0x17000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/flash-controller@41080000/flash@1000000/partitions/partition@3a000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|storage_partition" "/soc/flash-controller@41080000/flash@1000000/partitions/partition@3a000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000/partitions/partition@3a000|label" "storage")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000/partitions/partition@3a000|read-only" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/flash-controller@41080000/flash@1000000/partitions/partition@3a000|reg" "237568;24576;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000/partitions/partition@3a000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000/partitions/partition@3a000|ADDR" "0x3a000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/flash-controller@41080000/flash@1000000/partitions/partition@3a000|SIZE" "0x6000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/vmc@41081000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|vmc" "/soc/vmc@41081000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/vmc@41081000|reg" "1091047424;4096;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/vmc@41081000|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/vmc@41081000|compatible" "nordic,nrf-vmc;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/vmc@41081000|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/vmc@41081000|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/vmc@41081000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/vmc@41081000|ADDR" "0x41081000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/vmc@41081000|SIZE" "0x1000;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/gpio@418c0500" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|gpio0" "/soc/gpio@418c0500")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpio@418c0500|reg" "1099695360;768;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpio@418c0500|port" "0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpio@418c0500|gpio-controller" "True")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpio@418c0500|ngpios" "32")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpio@418c0500|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpio@418c0500|compatible" "nordic,nrf-gpio;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpio@418c0500|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpio@418c0500|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/gpio@418c0500|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/gpio@418c0500|ADDR" "0x418c0500;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/gpio@418c0500|SIZE" "0x300;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/soc/gpio@418c0800" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|gpio1" "/soc/gpio@418c0800")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpio@418c0800|reg" "1099696128;768;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpio@418c0800|port" "1")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpio@418c0800|gpio-controller" "True")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpio@418c0800|ngpios" "16")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpio@418c0800|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpio@418c0800|compatible" "nordic,nrf-gpio;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpio@418c0800|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/soc/gpio@418c0800|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/gpio@418c0800|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/gpio@418c0800|ADDR" "0x418c0800;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/soc/gpio@418c0800|SIZE" "0x300;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/pin-controller" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|pinctrl" "/pin-controller")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller|compatible" "nordic,nrf-pinctrl;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/pin-controller/uart0_default" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|uart0_default" "/pin-controller/uart0_default")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/uart0_default|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/uart0_default|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/uart0_default|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/pin-controller/uart0_default/group1" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_default/group1|psels" "33;131083;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_default/group1|nordic,drive-mode" "0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_default/group1|nordic,invert" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_default/group1|bias-disable" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_default/group1|bias-pull-up" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_default/group1|bias-pull-down" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_default/group1|low-power-enable" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/uart0_default/group1|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/uart0_default/group1|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/uart0_default/group1|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/pin-controller/uart0_default/group2" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_default/group2|psels" "65568;196618;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_default/group2|nordic,drive-mode" "0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_default/group2|nordic,invert" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_default/group2|bias-disable" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_default/group2|bias-pull-up" "True")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_default/group2|bias-pull-down" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_default/group2|low-power-enable" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/uart0_default/group2|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/uart0_default/group2|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/uart0_default/group2|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/pin-controller/uart0_sleep" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|uart0_sleep" "/pin-controller/uart0_sleep")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/uart0_sleep|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/uart0_sleep|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/uart0_sleep|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/pin-controller/uart0_sleep/group1" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_sleep/group1|psels" "33;65568;131083;196618;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_sleep/group1|nordic,drive-mode" "0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_sleep/group1|nordic,invert" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_sleep/group1|bias-disable" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_sleep/group1|bias-pull-up" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_sleep/group1|bias-pull-down" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/uart0_sleep/group1|low-power-enable" "True")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/uart0_sleep/group1|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/uart0_sleep/group1|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/uart0_sleep/group1|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/pin-controller/i2c0_default" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|i2c0_default" "/pin-controller/i2c0_default")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/i2c0_default|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/i2c0_default|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/i2c0_default|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/pin-controller/i2c0_default/group1" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/i2c0_default/group1|psels" "786466;720931;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/i2c0_default/group1|nordic,drive-mode" "0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/i2c0_default/group1|nordic,invert" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/i2c0_default/group1|bias-disable" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/i2c0_default/group1|bias-pull-up" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/i2c0_default/group1|bias-pull-down" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/i2c0_default/group1|low-power-enable" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/i2c0_default/group1|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/i2c0_default/group1|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/i2c0_default/group1|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/pin-controller/i2c0_sleep" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|i2c0_sleep" "/pin-controller/i2c0_sleep")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/i2c0_sleep|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/i2c0_sleep|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/i2c0_sleep|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/pin-controller/i2c0_sleep/group1" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/i2c0_sleep/group1|psels" "786466;720931;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/i2c0_sleep/group1|nordic,drive-mode" "0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/i2c0_sleep/group1|nordic,invert" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/i2c0_sleep/group1|bias-disable" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/i2c0_sleep/group1|bias-pull-up" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/i2c0_sleep/group1|bias-pull-down" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/i2c0_sleep/group1|low-power-enable" "True")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/i2c0_sleep/group1|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/i2c0_sleep/group1|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/i2c0_sleep/group1|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/pin-controller/spi0_default" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|spi0_default" "/pin-controller/spi0_default")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/spi0_default|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/spi0_default|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/spi0_default|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/pin-controller/spi0_default/group1" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/spi0_default/group1|psels" "262191;393262;327725;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/spi0_default/group1|nordic,drive-mode" "0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/spi0_default/group1|nordic,invert" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/spi0_default/group1|bias-disable" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/spi0_default/group1|bias-pull-up" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/spi0_default/group1|bias-pull-down" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/spi0_default/group1|low-power-enable" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/spi0_default/group1|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/spi0_default/group1|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/spi0_default/group1|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/pin-controller/spi0_sleep" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|spi0_sleep" "/pin-controller/spi0_sleep")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/spi0_sleep|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/spi0_sleep|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/spi0_sleep|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/pin-controller/spi0_sleep/group1" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/spi0_sleep/group1|psels" "262191;393262;327725;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/spi0_sleep/group1|nordic,drive-mode" "0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/spi0_sleep/group1|nordic,invert" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/spi0_sleep/group1|bias-disable" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/spi0_sleep/group1|bias-pull-up" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/spi0_sleep/group1|bias-pull-down" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/pin-controller/spi0_sleep/group1|low-power-enable" "True")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/spi0_sleep/group1|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/spi0_sleep/group1|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/pin-controller/spi0_sleep/group1|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/entropy_bt_hci" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|rng_hci" "/entropy_bt_hci")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/entropy_bt_hci|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/entropy_bt_hci|compatible" "zephyr,bt-hci-entropy;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/entropy_bt_hci|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/entropy_bt_hci|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/entropy_bt_hci|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/entropy_bt_hci|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/entropy_bt_hci|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/sw-pwm" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|sw_pwm" "/sw-pwm")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/sw-pwm|clock-prescaler" "0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/sw-pwm|status" "disabled")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/sw-pwm|compatible" "nordic,nrf-sw-pwm;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/sw-pwm|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/sw-pwm|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/sw-pwm|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/sw-pwm|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/sw-pwm|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/cpus" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_REG|/cpus|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/cpus|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/cpus|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/cpus/cpu@1" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/cpus/cpu@1|compatible" "arm,cortex-m33;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/cpus/cpu@1|reg" "1;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/cpus/cpu@1|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/cpus/cpu@1|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/cpus/cpu@1|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/cpus/cpu@1|ADDR" "0x1;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/cpus/cpu@1|SIZE" "NONE;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/cpus/cpu@1/mpu@e000ed90" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|mpu" "/cpus/cpu@1/mpu@e000ed90")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/cpus/cpu@1/mpu@e000ed90|reg" "3758157200;64;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/cpus/cpu@1/mpu@e000ed90|arm,num-mpu-regions" "8")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/cpus/cpu@1/mpu@e000ed90|compatible" "arm,armv8m-mpu;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/cpus/cpu@1/mpu@e000ed90|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/cpus/cpu@1/mpu@e000ed90|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/cpus/cpu@1/mpu@e000ed90|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/cpus/cpu@1/mpu@e000ed90|ADDR" "0xe000ed90;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/cpus/cpu@1/mpu@e000ed90|SIZE" "0x40;")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/ipc" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_REG|/ipc|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/ipc|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/ipc|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/ipc/ipc0" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|ipc0" "/ipc/ipc0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/ipc/ipc0|role" "remote")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/ipc/ipc0|mbox-names" "rx;tx;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/ipc/ipc0|status" "okay")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/ipc/ipc0|compatible" "zephyr,ipc-openamp-static-vrings;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/ipc/ipc0|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/ipc/ipc0|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/ipc/ipc0|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/ipc/ipc0|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/ipc/ipc0|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/leds" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/leds|compatible" "gpio-leds;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/leds|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/leds|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/leds|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/leds/led_0" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|led0" "/leds/led_0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/leds/led_0|label" "Green LED 0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/leds/led_0|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/leds/led_0|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/leds/led_0|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/leds/led_1" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|led1" "/leds/led_1")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/leds/led_1|label" "Green LED 1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/leds/led_1|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/leds/led_1|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/leds/led_1|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/leds/led_2" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|led2" "/leds/led_2")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/leds/led_2|label" "Green LED 2")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/leds/led_2|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/leds/led_2|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/leds/led_2|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/leds/led_3" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|led3" "/leds/led_3")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/leds/led_3|label" "Green LED 3")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/leds/led_3|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/leds/led_3|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/leds/led_3|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/buttons" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/buttons|debounce-interval-ms" "30")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/buttons|compatible" "gpio-keys;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/buttons|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/buttons|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/buttons|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/buttons|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/buttons|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/buttons/button_0" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|button0" "/buttons/button_0")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/buttons/button_0|label" "Push button 1")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/buttons/button_0|zephyr,code" "11")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/buttons/button_0|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/buttons/button_0|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/buttons/button_0|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/buttons/button_1" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|button1" "/buttons/button_1")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/buttons/button_1|label" "Push button 2")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/buttons/button_1|zephyr,code" "2")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/buttons/button_1|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/buttons/button_1|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/buttons/button_1|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/buttons/button_2" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|button2" "/buttons/button_2")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/buttons/button_2|label" "Push button 3")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/buttons/button_2|zephyr,code" "3")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/buttons/button_2|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/buttons/button_2|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/buttons/button_2|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/buttons/button_3" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|button3" "/buttons/button_3")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/buttons/button_3|label" "Push button 4")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/buttons/button_3|zephyr,code" "4")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/buttons/button_3|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/buttons/button_3|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/buttons/button_3|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/connector" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|arduino_header" "/connector")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/connector|compatible" "arduino-header-r3;")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/connector|wakeup-source" "False")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/connector|zephyr,pm-device-runtime-auto" "False")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/connector|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/connector|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/connector|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/reserved-memory" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/reserved-memory|ranges" "None")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/reserved-memory|NUM" "0")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/reserved-memory|ADDR" "")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/reserved-memory|SIZE" "")
set_target_properties(devicetree_target PROPERTIES "DT_NODE|/reserved-memory/memory@20070000" TRUE)
set_target_properties(devicetree_target PROPERTIES "DT_NODELABEL|sram0_shared" "/reserved-memory/memory@20070000")
set_target_properties(devicetree_target PROPERTIES "DT_PROP|/reserved-memory/memory@20070000|reg" "537329664;65536;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/reserved-memory/memory@20070000|NUM" "1")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/reserved-memory/memory@20070000|ADDR" "0x20070000;")
set_target_properties(devicetree_target PROPERTIES "DT_REG|/reserved-memory/memory@20070000|SIZE" "0x10000;")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf5340-dk-nrf5340-cpunet" "/")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf5340-cpunet-qkaa" "/soc")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf5340-cpunet" "/soc")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf53" "/soc")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|simple-bus" "/soc")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|arm,v8m-nvic" "/soc/interrupt-controller@e000e100")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|arm,armv8m-systick" "/soc/timer@e000e010")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-ficr" "/soc/ficr@1ff0000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-uicr" "/soc/uicr@1ff8000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|mmio-sram" "/soc/memory@20000000;/soc/memory@21000000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|zephyr,memory-region" "/soc/memory@21000000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-clock" "/soc/clock@41005000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-power" "/soc/power@41005000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-gpregret" "/soc/power@41005000/gpregret1@4100551c;/soc/power@41005000/gpregret2@41005520")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-radio" "/soc/radio@41008000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-ieee802154" "/soc/radio@41008000/ieee802154")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-rng" "/soc/random@41009000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-gpiote" "/soc/gpiote@4100a000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-wdt" "/soc/watchdog@4100b000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-timer" "/soc/timer@4100c000;/soc/timer@41018000;/soc/timer@41019000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-ecb" "/soc/ecb@4100d000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-ccm" "/soc/ccm@4100e000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-dppic" "/soc/dppic@4100f000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-temp" "/soc/temp@41010000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-rtc" "/soc/rtc@41011000;/soc/rtc@41016000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,mbox-nrf-ipc" "/soc/mbox@41012000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-ipc" "/soc/mbox@41012000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-twim" "/soc/i2c@41013000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-spim" "/soc/spi@41013000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-uarte" "/soc/uart@41013000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-egu" "/soc/egu@41014000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-swi" "/soc/swi@4101a000;/soc/swi@4101b000;/soc/swi@4101c000;/soc/swi@4101d000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-acl" "/soc/acl@41080000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf53-flash-controller" "/soc/flash-controller@41080000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|soc-nv-flash" "/soc/flash-controller@41080000/flash@1000000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-vmc" "/soc/vmc@41081000")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-gpio" "/soc/gpio@418c0500;/soc/gpio@418c0800")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-pinctrl" "/pin-controller")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|zephyr,bt-hci-entropy" "/entropy_bt_hci")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|nordic,nrf-sw-pwm" "/sw-pwm")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|arm,cortex-m33" "/cpus/cpu@1")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|arm,armv8m-mpu" "/cpus/cpu@1/mpu@e000ed90")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|zephyr,ipc-openamp-static-vrings" "/ipc/ipc0")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|gpio-leds" "/leds")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|gpio-keys" "/buttons")
set_target_properties(devicetree_target PROPERTIES "DT_COMP|arduino-header-r3" "/connector")
