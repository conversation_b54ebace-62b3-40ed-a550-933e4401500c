/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_PS2_H
#define Z_INCLUDE_SYSCALLS_PS2_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_ps2_config(const struct device * dev, ps2_callback_t callback_isr);

__pinned_func
static inline int ps2_config(const struct device * dev, ps2_callback_t callback_isr)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; ps2_callback_t val; } parm1 = { .val = callback_isr };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_PS2_CONFIG);
	}
#endif
	compiler_barrier();
	return z_impl_ps2_config(dev, callback_isr);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define ps2_config(dev, callback_isr) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_PS2_CONFIG, ps2_config, dev, callback_isr); 	syscall__retval = ps2_config(dev, callback_isr); 	sys_port_trace_syscall_exit(K_SYSCALL_PS2_CONFIG, ps2_config, dev, callback_isr, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_ps2_write(const struct device * dev, uint8_t value);

__pinned_func
static inline int ps2_write(const struct device * dev, uint8_t value)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t val; } parm1 = { .val = value };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_PS2_WRITE);
	}
#endif
	compiler_barrier();
	return z_impl_ps2_write(dev, value);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define ps2_write(dev, value) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_PS2_WRITE, ps2_write, dev, value); 	syscall__retval = ps2_write(dev, value); 	sys_port_trace_syscall_exit(K_SYSCALL_PS2_WRITE, ps2_write, dev, value, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_ps2_read(const struct device * dev, uint8_t * value);

__pinned_func
static inline int ps2_read(const struct device * dev, uint8_t * value)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t * val; } parm1 = { .val = value };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_PS2_READ);
	}
#endif
	compiler_barrier();
	return z_impl_ps2_read(dev, value);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define ps2_read(dev, value) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_PS2_READ, ps2_read, dev, value); 	syscall__retval = ps2_read(dev, value); 	sys_port_trace_syscall_exit(K_SYSCALL_PS2_READ, ps2_read, dev, value, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_ps2_enable_callback(const struct device * dev);

__pinned_func
static inline int ps2_enable_callback(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_PS2_ENABLE_CALLBACK);
	}
#endif
	compiler_barrier();
	return z_impl_ps2_enable_callback(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define ps2_enable_callback(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_PS2_ENABLE_CALLBACK, ps2_enable_callback, dev); 	syscall__retval = ps2_enable_callback(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_PS2_ENABLE_CALLBACK, ps2_enable_callback, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_ps2_disable_callback(const struct device * dev);

__pinned_func
static inline int ps2_disable_callback(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_PS2_DISABLE_CALLBACK);
	}
#endif
	compiler_barrier();
	return z_impl_ps2_disable_callback(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define ps2_disable_callback(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_PS2_DISABLE_CALLBACK, ps2_disable_callback, dev); 	syscall__retval = ps2_disable_callback(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_PS2_DISABLE_CALLBACK, ps2_disable_callback, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
