/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_KERNEL_H
#define Z_INCLUDE_SYSCALLS_KERNEL_H




#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern k_thread_stack_t * z_impl_k_thread_stack_alloc(size_t size, int flags);

__pinned_func
static inline k_thread_stack_t * k_thread_stack_alloc(size_t size, int flags)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; size_t val; } parm0 = { .val = size };
		union { uintptr_t x; int val; } parm1 = { .val = flags };
		return (k_thread_stack_t *) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_THREAD_STACK_ALLOC);
	}
#endif
	compiler_barrier();
	return z_impl_k_thread_stack_alloc(size, flags);
}


extern int z_impl_k_thread_stack_free(k_thread_stack_t * stack);

__pinned_func
static inline int k_thread_stack_free(k_thread_stack_t * stack)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; k_thread_stack_t * val; } parm0 = { .val = stack };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_THREAD_STACK_FREE);
	}
#endif
	compiler_barrier();
	return z_impl_k_thread_stack_free(stack);
}


extern k_tid_t z_impl_k_thread_create(struct k_thread * new_thread, k_thread_stack_t * stack, size_t stack_size, k_thread_entry_t entry, void * p1, void * p2, void * p3, int prio, uint32_t options, k_timeout_t delay);

__pinned_func
static inline k_tid_t k_thread_create(struct k_thread * new_thread, k_thread_stack_t * stack, size_t stack_size, k_thread_entry_t entry, void * p1, void * p2, void * p3, int prio, uint32_t options, k_timeout_t delay)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_thread * val; } parm0 = { .val = new_thread };
		union { uintptr_t x; k_thread_stack_t * val; } parm1 = { .val = stack };
		union { uintptr_t x; size_t val; } parm2 = { .val = stack_size };
		union { uintptr_t x; k_thread_entry_t val; } parm3 = { .val = entry };
		union { uintptr_t x; void * val; } parm4 = { .val = p1 };
		union { uintptr_t x; void * val; } parm5 = { .val = p2 };
		union { uintptr_t x; void * val; } parm6 = { .val = p3 };
		union { uintptr_t x; int val; } parm7 = { .val = prio };
		union { uintptr_t x; uint32_t val; } parm8 = { .val = options };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm9 = { .val = delay };
		uintptr_t more[] = {
			parm5.x,
			parm6.x,
			parm7.x,
			parm8.x,
			parm9.split.lo,
			parm9.split.hi
		};
		return (k_tid_t) arch_syscall_invoke6(parm0.x, parm1.x, parm2.x, parm3.x, parm4.x, (uintptr_t) &more, K_SYSCALL_K_THREAD_CREATE);
	}
#endif
	compiler_barrier();
	return z_impl_k_thread_create(new_thread, stack, stack_size, entry, p1, p2, p3, prio, options, delay);
}


extern int z_impl_k_thread_stack_space_get(const struct k_thread * thread, size_t * unused_ptr);

__pinned_func
static inline int k_thread_stack_space_get(const struct k_thread * thread, size_t * unused_ptr)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct k_thread * val; } parm0 = { .val = thread };
		union { uintptr_t x; size_t * val; } parm1 = { .val = unused_ptr };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_THREAD_STACK_SPACE_GET);
	}
#endif
	compiler_barrier();
	return z_impl_k_thread_stack_space_get(thread, unused_ptr);
}


extern int z_impl_k_thread_join(struct k_thread * thread, k_timeout_t timeout);

__pinned_func
static inline int k_thread_join(struct k_thread * thread, k_timeout_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_thread * val; } parm0 = { .val = thread };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm1 = { .val = timeout };
		return (int) arch_syscall_invoke3(parm0.x, parm1.split.lo, parm1.split.hi, K_SYSCALL_K_THREAD_JOIN);
	}
#endif
	compiler_barrier();
	return z_impl_k_thread_join(thread, timeout);
}


extern int32_t z_impl_k_sleep(k_timeout_t timeout);

__pinned_func
static inline int32_t k_sleep(k_timeout_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm0 = { .val = timeout };
		return (int32_t) arch_syscall_invoke2(parm0.split.lo, parm0.split.hi, K_SYSCALL_K_SLEEP);
	}
#endif
	compiler_barrier();
	return z_impl_k_sleep(timeout);
}


extern int32_t z_impl_k_usleep(int32_t us);

__pinned_func
static inline int32_t k_usleep(int32_t us)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; int32_t val; } parm0 = { .val = us };
		return (int32_t) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_USLEEP);
	}
#endif
	compiler_barrier();
	return z_impl_k_usleep(us);
}


extern void z_impl_k_busy_wait(uint32_t usec_to_wait);

__pinned_func
static inline void k_busy_wait(uint32_t usec_to_wait)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; uint32_t val; } parm0 = { .val = usec_to_wait };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_BUSY_WAIT);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_busy_wait(usec_to_wait);
}


extern void z_impl_k_yield(void);

__pinned_func
static inline void k_yield(void)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		(void) arch_syscall_invoke0(K_SYSCALL_K_YIELD);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_yield();
}


extern void z_impl_k_wakeup(k_tid_t thread);

__pinned_func
static inline void k_wakeup(k_tid_t thread)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; k_tid_t val; } parm0 = { .val = thread };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_WAKEUP);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_wakeup(thread);
}


extern k_tid_t z_impl_z_current_get(void);

__pinned_func
static inline k_tid_t z_current_get(void)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		return (k_tid_t) arch_syscall_invoke0(K_SYSCALL_Z_CURRENT_GET);
	}
#endif
	compiler_barrier();
	return z_impl_z_current_get();
}


extern void z_impl_k_thread_abort(k_tid_t thread);

__pinned_func
static inline void k_thread_abort(k_tid_t thread)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; k_tid_t val; } parm0 = { .val = thread };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_THREAD_ABORT);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_thread_abort(thread);
}


extern void z_impl_k_thread_start(k_tid_t thread);

__pinned_func
static inline void k_thread_start(k_tid_t thread)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; k_tid_t val; } parm0 = { .val = thread };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_THREAD_START);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_thread_start(thread);
}


extern k_ticks_t z_impl_k_thread_timeout_expires_ticks(const struct k_thread * t);

__pinned_func
static inline k_ticks_t k_thread_timeout_expires_ticks(const struct k_thread * t)
{
#ifdef CONFIG_USERSPACE
	uint64_t ret64;
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct k_thread * val; } parm0 = { .val = t };
		(void) arch_syscall_invoke2(parm0.x, (uintptr_t)&ret64, K_SYSCALL_K_THREAD_TIMEOUT_EXPIRES_TICKS);
		return (k_ticks_t) ret64;
	}
#endif
	compiler_barrier();
	return z_impl_k_thread_timeout_expires_ticks(t);
}


extern k_ticks_t z_impl_k_thread_timeout_remaining_ticks(const struct k_thread * t);

__pinned_func
static inline k_ticks_t k_thread_timeout_remaining_ticks(const struct k_thread * t)
{
#ifdef CONFIG_USERSPACE
	uint64_t ret64;
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct k_thread * val; } parm0 = { .val = t };
		(void) arch_syscall_invoke2(parm0.x, (uintptr_t)&ret64, K_SYSCALL_K_THREAD_TIMEOUT_REMAINING_TICKS);
		return (k_ticks_t) ret64;
	}
#endif
	compiler_barrier();
	return z_impl_k_thread_timeout_remaining_ticks(t);
}


extern int z_impl_k_thread_priority_get(k_tid_t thread);

__pinned_func
static inline int k_thread_priority_get(k_tid_t thread)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; k_tid_t val; } parm0 = { .val = thread };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_THREAD_PRIORITY_GET);
	}
#endif
	compiler_barrier();
	return z_impl_k_thread_priority_get(thread);
}


extern void z_impl_k_thread_priority_set(k_tid_t thread, int prio);

__pinned_func
static inline void k_thread_priority_set(k_tid_t thread, int prio)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; k_tid_t val; } parm0 = { .val = thread };
		union { uintptr_t x; int val; } parm1 = { .val = prio };
		(void) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_THREAD_PRIORITY_SET);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_thread_priority_set(thread, prio);
}


extern void z_impl_k_thread_deadline_set(k_tid_t thread, int deadline);

__pinned_func
static inline void k_thread_deadline_set(k_tid_t thread, int deadline)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; k_tid_t val; } parm0 = { .val = thread };
		union { uintptr_t x; int val; } parm1 = { .val = deadline };
		(void) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_THREAD_DEADLINE_SET);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_thread_deadline_set(thread, deadline);
}


extern void z_impl_k_thread_suspend(k_tid_t thread);

__pinned_func
static inline void k_thread_suspend(k_tid_t thread)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; k_tid_t val; } parm0 = { .val = thread };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_THREAD_SUSPEND);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_thread_suspend(thread);
}


extern void z_impl_k_thread_resume(k_tid_t thread);

__pinned_func
static inline void k_thread_resume(k_tid_t thread)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; k_tid_t val; } parm0 = { .val = thread };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_THREAD_RESUME);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_thread_resume(thread);
}


extern int z_impl_k_is_preempt_thread(void);

__pinned_func
static inline int k_is_preempt_thread(void)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		return (int) arch_syscall_invoke0(K_SYSCALL_K_IS_PREEMPT_THREAD);
	}
#endif
	compiler_barrier();
	return z_impl_k_is_preempt_thread();
}


extern void z_impl_k_thread_custom_data_set(void * value);

__pinned_func
static inline void k_thread_custom_data_set(void * value)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; void * val; } parm0 = { .val = value };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_THREAD_CUSTOM_DATA_SET);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_thread_custom_data_set(value);
}


extern void * z_impl_k_thread_custom_data_get(void);

__pinned_func
static inline void * k_thread_custom_data_get(void)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		return (void *) arch_syscall_invoke0(K_SYSCALL_K_THREAD_CUSTOM_DATA_GET);
	}
#endif
	compiler_barrier();
	return z_impl_k_thread_custom_data_get();
}


extern int z_impl_k_thread_name_set(k_tid_t thread, const char * str);

__pinned_func
static inline int k_thread_name_set(k_tid_t thread, const char * str)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; k_tid_t val; } parm0 = { .val = thread };
		union { uintptr_t x; const char * val; } parm1 = { .val = str };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_THREAD_NAME_SET);
	}
#endif
	compiler_barrier();
	return z_impl_k_thread_name_set(thread, str);
}


extern int z_impl_k_thread_name_copy(k_tid_t thread, char * buf, size_t size);

__pinned_func
static inline int k_thread_name_copy(k_tid_t thread, char * buf, size_t size)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; k_tid_t val; } parm0 = { .val = thread };
		union { uintptr_t x; char * val; } parm1 = { .val = buf };
		union { uintptr_t x; size_t val; } parm2 = { .val = size };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_K_THREAD_NAME_COPY);
	}
#endif
	compiler_barrier();
	return z_impl_k_thread_name_copy(thread, buf, size);
}


extern void z_impl_k_timer_start(struct k_timer * timer, k_timeout_t duration, k_timeout_t period);

__pinned_func
static inline void k_timer_start(struct k_timer * timer, k_timeout_t duration, k_timeout_t period)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_timer * val; } parm0 = { .val = timer };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm1 = { .val = duration };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm2 = { .val = period };
		(void) arch_syscall_invoke5(parm0.x, parm1.split.lo, parm1.split.hi, parm2.split.lo, parm2.split.hi, K_SYSCALL_K_TIMER_START);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_timer_start(timer, duration, period);
}


extern void z_impl_k_timer_stop(struct k_timer * timer);

__pinned_func
static inline void k_timer_stop(struct k_timer * timer)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_timer * val; } parm0 = { .val = timer };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_TIMER_STOP);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_timer_stop(timer);
}


extern uint32_t z_impl_k_timer_status_get(struct k_timer * timer);

__pinned_func
static inline uint32_t k_timer_status_get(struct k_timer * timer)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_timer * val; } parm0 = { .val = timer };
		return (uint32_t) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_TIMER_STATUS_GET);
	}
#endif
	compiler_barrier();
	return z_impl_k_timer_status_get(timer);
}


extern uint32_t z_impl_k_timer_status_sync(struct k_timer * timer);

__pinned_func
static inline uint32_t k_timer_status_sync(struct k_timer * timer)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_timer * val; } parm0 = { .val = timer };
		return (uint32_t) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_TIMER_STATUS_SYNC);
	}
#endif
	compiler_barrier();
	return z_impl_k_timer_status_sync(timer);
}


extern k_ticks_t z_impl_k_timer_expires_ticks(const struct k_timer * timer);

__pinned_func
static inline k_ticks_t k_timer_expires_ticks(const struct k_timer * timer)
{
#ifdef CONFIG_USERSPACE
	uint64_t ret64;
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct k_timer * val; } parm0 = { .val = timer };
		(void) arch_syscall_invoke2(parm0.x, (uintptr_t)&ret64, K_SYSCALL_K_TIMER_EXPIRES_TICKS);
		return (k_ticks_t) ret64;
	}
#endif
	compiler_barrier();
	return z_impl_k_timer_expires_ticks(timer);
}


extern k_ticks_t z_impl_k_timer_remaining_ticks(const struct k_timer * timer);

__pinned_func
static inline k_ticks_t k_timer_remaining_ticks(const struct k_timer * timer)
{
#ifdef CONFIG_USERSPACE
	uint64_t ret64;
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct k_timer * val; } parm0 = { .val = timer };
		(void) arch_syscall_invoke2(parm0.x, (uintptr_t)&ret64, K_SYSCALL_K_TIMER_REMAINING_TICKS);
		return (k_ticks_t) ret64;
	}
#endif
	compiler_barrier();
	return z_impl_k_timer_remaining_ticks(timer);
}


extern void z_impl_k_timer_user_data_set(struct k_timer * timer, void * user_data);

__pinned_func
static inline void k_timer_user_data_set(struct k_timer * timer, void * user_data)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_timer * val; } parm0 = { .val = timer };
		union { uintptr_t x; void * val; } parm1 = { .val = user_data };
		(void) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_TIMER_USER_DATA_SET);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_timer_user_data_set(timer, user_data);
}


extern void * z_impl_k_timer_user_data_get(const struct k_timer * timer);

__pinned_func
static inline void * k_timer_user_data_get(const struct k_timer * timer)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct k_timer * val; } parm0 = { .val = timer };
		return (void *) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_TIMER_USER_DATA_GET);
	}
#endif
	compiler_barrier();
	return z_impl_k_timer_user_data_get(timer);
}


extern int64_t z_impl_k_uptime_ticks(void);

__pinned_func
static inline int64_t k_uptime_ticks(void)
{
#ifdef CONFIG_USERSPACE
	uint64_t ret64;
	if (z_syscall_trap()) {
		(void) arch_syscall_invoke1((uintptr_t)&ret64, K_SYSCALL_K_UPTIME_TICKS);
		return (int64_t) ret64;
	}
#endif
	compiler_barrier();
	return z_impl_k_uptime_ticks();
}


extern void z_impl_k_queue_init(struct k_queue * queue);

__pinned_func
static inline void k_queue_init(struct k_queue * queue)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_queue * val; } parm0 = { .val = queue };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_QUEUE_INIT);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_queue_init(queue);
}


extern void z_impl_k_queue_cancel_wait(struct k_queue * queue);

__pinned_func
static inline void k_queue_cancel_wait(struct k_queue * queue)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_queue * val; } parm0 = { .val = queue };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_QUEUE_CANCEL_WAIT);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_queue_cancel_wait(queue);
}


extern int32_t z_impl_k_queue_alloc_append(struct k_queue * queue, void * data);

__pinned_func
static inline int32_t k_queue_alloc_append(struct k_queue * queue, void * data)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_queue * val; } parm0 = { .val = queue };
		union { uintptr_t x; void * val; } parm1 = { .val = data };
		return (int32_t) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_QUEUE_ALLOC_APPEND);
	}
#endif
	compiler_barrier();
	return z_impl_k_queue_alloc_append(queue, data);
}


extern int32_t z_impl_k_queue_alloc_prepend(struct k_queue * queue, void * data);

__pinned_func
static inline int32_t k_queue_alloc_prepend(struct k_queue * queue, void * data)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_queue * val; } parm0 = { .val = queue };
		union { uintptr_t x; void * val; } parm1 = { .val = data };
		return (int32_t) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_QUEUE_ALLOC_PREPEND);
	}
#endif
	compiler_barrier();
	return z_impl_k_queue_alloc_prepend(queue, data);
}


extern void * z_impl_k_queue_get(struct k_queue * queue, k_timeout_t timeout);

__pinned_func
static inline void * k_queue_get(struct k_queue * queue, k_timeout_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_queue * val; } parm0 = { .val = queue };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm1 = { .val = timeout };
		return (void *) arch_syscall_invoke3(parm0.x, parm1.split.lo, parm1.split.hi, K_SYSCALL_K_QUEUE_GET);
	}
#endif
	compiler_barrier();
	return z_impl_k_queue_get(queue, timeout);
}


extern int z_impl_k_queue_is_empty(struct k_queue * queue);

__pinned_func
static inline int k_queue_is_empty(struct k_queue * queue)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_queue * val; } parm0 = { .val = queue };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_QUEUE_IS_EMPTY);
	}
#endif
	compiler_barrier();
	return z_impl_k_queue_is_empty(queue);
}


extern void * z_impl_k_queue_peek_head(struct k_queue * queue);

__pinned_func
static inline void * k_queue_peek_head(struct k_queue * queue)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_queue * val; } parm0 = { .val = queue };
		return (void *) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_QUEUE_PEEK_HEAD);
	}
#endif
	compiler_barrier();
	return z_impl_k_queue_peek_head(queue);
}


extern void * z_impl_k_queue_peek_tail(struct k_queue * queue);

__pinned_func
static inline void * k_queue_peek_tail(struct k_queue * queue)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_queue * val; } parm0 = { .val = queue };
		return (void *) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_QUEUE_PEEK_TAIL);
	}
#endif
	compiler_barrier();
	return z_impl_k_queue_peek_tail(queue);
}


extern int z_impl_k_futex_wait(struct k_futex * futex, int expected, k_timeout_t timeout);

__pinned_func
static inline int k_futex_wait(struct k_futex * futex, int expected, k_timeout_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_futex * val; } parm0 = { .val = futex };
		union { uintptr_t x; int val; } parm1 = { .val = expected };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm2 = { .val = timeout };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.split.lo, parm2.split.hi, K_SYSCALL_K_FUTEX_WAIT);
	}
#endif
	compiler_barrier();
	return z_impl_k_futex_wait(futex, expected, timeout);
}


extern int z_impl_k_futex_wake(struct k_futex * futex, bool wake_all);

__pinned_func
static inline int k_futex_wake(struct k_futex * futex, bool wake_all)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_futex * val; } parm0 = { .val = futex };
		union { uintptr_t x; bool val; } parm1 = { .val = wake_all };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_FUTEX_WAKE);
	}
#endif
	compiler_barrier();
	return z_impl_k_futex_wake(futex, wake_all);
}


extern void z_impl_k_event_init(struct k_event * event);

__pinned_func
static inline void k_event_init(struct k_event * event)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_event * val; } parm0 = { .val = event };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_EVENT_INIT);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_event_init(event);
}


extern uint32_t z_impl_k_event_post(struct k_event * event, uint32_t events);

__pinned_func
static inline uint32_t k_event_post(struct k_event * event, uint32_t events)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_event * val; } parm0 = { .val = event };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = events };
		return (uint32_t) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_EVENT_POST);
	}
#endif
	compiler_barrier();
	return z_impl_k_event_post(event, events);
}


extern uint32_t z_impl_k_event_set(struct k_event * event, uint32_t events);

__pinned_func
static inline uint32_t k_event_set(struct k_event * event, uint32_t events)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_event * val; } parm0 = { .val = event };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = events };
		return (uint32_t) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_EVENT_SET);
	}
#endif
	compiler_barrier();
	return z_impl_k_event_set(event, events);
}


extern uint32_t z_impl_k_event_set_masked(struct k_event * event, uint32_t events, uint32_t events_mask);

__pinned_func
static inline uint32_t k_event_set_masked(struct k_event * event, uint32_t events, uint32_t events_mask)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_event * val; } parm0 = { .val = event };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = events };
		union { uintptr_t x; uint32_t val; } parm2 = { .val = events_mask };
		return (uint32_t) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_K_EVENT_SET_MASKED);
	}
#endif
	compiler_barrier();
	return z_impl_k_event_set_masked(event, events, events_mask);
}


extern uint32_t z_impl_k_event_clear(struct k_event * event, uint32_t events);

__pinned_func
static inline uint32_t k_event_clear(struct k_event * event, uint32_t events)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_event * val; } parm0 = { .val = event };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = events };
		return (uint32_t) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_EVENT_CLEAR);
	}
#endif
	compiler_barrier();
	return z_impl_k_event_clear(event, events);
}


extern uint32_t z_impl_k_event_wait(struct k_event * event, uint32_t events, bool reset, k_timeout_t timeout);

__pinned_func
static inline uint32_t k_event_wait(struct k_event * event, uint32_t events, bool reset, k_timeout_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_event * val; } parm0 = { .val = event };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = events };
		union { uintptr_t x; bool val; } parm2 = { .val = reset };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm3 = { .val = timeout };
		return (uint32_t) arch_syscall_invoke5(parm0.x, parm1.x, parm2.x, parm3.split.lo, parm3.split.hi, K_SYSCALL_K_EVENT_WAIT);
	}
#endif
	compiler_barrier();
	return z_impl_k_event_wait(event, events, reset, timeout);
}


extern uint32_t z_impl_k_event_wait_all(struct k_event * event, uint32_t events, bool reset, k_timeout_t timeout);

__pinned_func
static inline uint32_t k_event_wait_all(struct k_event * event, uint32_t events, bool reset, k_timeout_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_event * val; } parm0 = { .val = event };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = events };
		union { uintptr_t x; bool val; } parm2 = { .val = reset };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm3 = { .val = timeout };
		return (uint32_t) arch_syscall_invoke5(parm0.x, parm1.x, parm2.x, parm3.split.lo, parm3.split.hi, K_SYSCALL_K_EVENT_WAIT_ALL);
	}
#endif
	compiler_barrier();
	return z_impl_k_event_wait_all(event, events, reset, timeout);
}


extern int32_t z_impl_k_stack_alloc_init(struct k_stack * stack, uint32_t num_entries);

__pinned_func
static inline int32_t k_stack_alloc_init(struct k_stack * stack, uint32_t num_entries)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_stack * val; } parm0 = { .val = stack };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = num_entries };
		return (int32_t) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_STACK_ALLOC_INIT);
	}
#endif
	compiler_barrier();
	return z_impl_k_stack_alloc_init(stack, num_entries);
}


extern int z_impl_k_stack_push(struct k_stack * stack, stack_data_t data);

__pinned_func
static inline int k_stack_push(struct k_stack * stack, stack_data_t data)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_stack * val; } parm0 = { .val = stack };
		union { uintptr_t x; stack_data_t val; } parm1 = { .val = data };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_STACK_PUSH);
	}
#endif
	compiler_barrier();
	return z_impl_k_stack_push(stack, data);
}


extern int z_impl_k_stack_pop(struct k_stack * stack, stack_data_t * data, k_timeout_t timeout);

__pinned_func
static inline int k_stack_pop(struct k_stack * stack, stack_data_t * data, k_timeout_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_stack * val; } parm0 = { .val = stack };
		union { uintptr_t x; stack_data_t * val; } parm1 = { .val = data };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm2 = { .val = timeout };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.split.lo, parm2.split.hi, K_SYSCALL_K_STACK_POP);
	}
#endif
	compiler_barrier();
	return z_impl_k_stack_pop(stack, data, timeout);
}


extern int z_impl_k_mutex_init(struct k_mutex * mutex);

__pinned_func
static inline int k_mutex_init(struct k_mutex * mutex)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_mutex * val; } parm0 = { .val = mutex };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_MUTEX_INIT);
	}
#endif
	compiler_barrier();
	return z_impl_k_mutex_init(mutex);
}


extern int z_impl_k_mutex_lock(struct k_mutex * mutex, k_timeout_t timeout);

__pinned_func
static inline int k_mutex_lock(struct k_mutex * mutex, k_timeout_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_mutex * val; } parm0 = { .val = mutex };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm1 = { .val = timeout };
		return (int) arch_syscall_invoke3(parm0.x, parm1.split.lo, parm1.split.hi, K_SYSCALL_K_MUTEX_LOCK);
	}
#endif
	compiler_barrier();
	return z_impl_k_mutex_lock(mutex, timeout);
}


extern int z_impl_k_mutex_unlock(struct k_mutex * mutex);

__pinned_func
static inline int k_mutex_unlock(struct k_mutex * mutex)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_mutex * val; } parm0 = { .val = mutex };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_MUTEX_UNLOCK);
	}
#endif
	compiler_barrier();
	return z_impl_k_mutex_unlock(mutex);
}


extern int z_impl_k_condvar_init(struct k_condvar * condvar);

__pinned_func
static inline int k_condvar_init(struct k_condvar * condvar)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_condvar * val; } parm0 = { .val = condvar };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_CONDVAR_INIT);
	}
#endif
	compiler_barrier();
	return z_impl_k_condvar_init(condvar);
}


extern int z_impl_k_condvar_signal(struct k_condvar * condvar);

__pinned_func
static inline int k_condvar_signal(struct k_condvar * condvar)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_condvar * val; } parm0 = { .val = condvar };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_CONDVAR_SIGNAL);
	}
#endif
	compiler_barrier();
	return z_impl_k_condvar_signal(condvar);
}


extern int z_impl_k_condvar_broadcast(struct k_condvar * condvar);

__pinned_func
static inline int k_condvar_broadcast(struct k_condvar * condvar)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_condvar * val; } parm0 = { .val = condvar };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_CONDVAR_BROADCAST);
	}
#endif
	compiler_barrier();
	return z_impl_k_condvar_broadcast(condvar);
}


extern int z_impl_k_condvar_wait(struct k_condvar * condvar, struct k_mutex * mutex, k_timeout_t timeout);

__pinned_func
static inline int k_condvar_wait(struct k_condvar * condvar, struct k_mutex * mutex, k_timeout_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_condvar * val; } parm0 = { .val = condvar };
		union { uintptr_t x; struct k_mutex * val; } parm1 = { .val = mutex };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm2 = { .val = timeout };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.split.lo, parm2.split.hi, K_SYSCALL_K_CONDVAR_WAIT);
	}
#endif
	compiler_barrier();
	return z_impl_k_condvar_wait(condvar, mutex, timeout);
}


extern int z_impl_k_sem_init(struct k_sem * sem, unsigned int initial_count, unsigned int limit);

__pinned_func
static inline int k_sem_init(struct k_sem * sem, unsigned int initial_count, unsigned int limit)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_sem * val; } parm0 = { .val = sem };
		union { uintptr_t x; unsigned int val; } parm1 = { .val = initial_count };
		union { uintptr_t x; unsigned int val; } parm2 = { .val = limit };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_K_SEM_INIT);
	}
#endif
	compiler_barrier();
	return z_impl_k_sem_init(sem, initial_count, limit);
}


extern int z_impl_k_sem_take(struct k_sem * sem, k_timeout_t timeout);

__pinned_func
static inline int k_sem_take(struct k_sem * sem, k_timeout_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_sem * val; } parm0 = { .val = sem };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm1 = { .val = timeout };
		return (int) arch_syscall_invoke3(parm0.x, parm1.split.lo, parm1.split.hi, K_SYSCALL_K_SEM_TAKE);
	}
#endif
	compiler_barrier();
	return z_impl_k_sem_take(sem, timeout);
}


extern void z_impl_k_sem_give(struct k_sem * sem);

__pinned_func
static inline void k_sem_give(struct k_sem * sem)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_sem * val; } parm0 = { .val = sem };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_SEM_GIVE);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_sem_give(sem);
}


extern void z_impl_k_sem_reset(struct k_sem * sem);

__pinned_func
static inline void k_sem_reset(struct k_sem * sem)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_sem * val; } parm0 = { .val = sem };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_SEM_RESET);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_sem_reset(sem);
}


extern unsigned int z_impl_k_sem_count_get(struct k_sem * sem);

__pinned_func
static inline unsigned int k_sem_count_get(struct k_sem * sem)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_sem * val; } parm0 = { .val = sem };
		return (unsigned int) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_SEM_COUNT_GET);
	}
#endif
	compiler_barrier();
	return z_impl_k_sem_count_get(sem);
}


extern int z_impl_k_msgq_alloc_init(struct k_msgq * msgq, size_t msg_size, uint32_t max_msgs);

__pinned_func
static inline int k_msgq_alloc_init(struct k_msgq * msgq, size_t msg_size, uint32_t max_msgs)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_msgq * val; } parm0 = { .val = msgq };
		union { uintptr_t x; size_t val; } parm1 = { .val = msg_size };
		union { uintptr_t x; uint32_t val; } parm2 = { .val = max_msgs };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_K_MSGQ_ALLOC_INIT);
	}
#endif
	compiler_barrier();
	return z_impl_k_msgq_alloc_init(msgq, msg_size, max_msgs);
}


extern int z_impl_k_msgq_put(struct k_msgq * msgq, const void * data, k_timeout_t timeout);

__pinned_func
static inline int k_msgq_put(struct k_msgq * msgq, const void * data, k_timeout_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_msgq * val; } parm0 = { .val = msgq };
		union { uintptr_t x; const void * val; } parm1 = { .val = data };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm2 = { .val = timeout };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.split.lo, parm2.split.hi, K_SYSCALL_K_MSGQ_PUT);
	}
#endif
	compiler_barrier();
	return z_impl_k_msgq_put(msgq, data, timeout);
}


extern int z_impl_k_msgq_get(struct k_msgq * msgq, void * data, k_timeout_t timeout);

__pinned_func
static inline int k_msgq_get(struct k_msgq * msgq, void * data, k_timeout_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_msgq * val; } parm0 = { .val = msgq };
		union { uintptr_t x; void * val; } parm1 = { .val = data };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm2 = { .val = timeout };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.split.lo, parm2.split.hi, K_SYSCALL_K_MSGQ_GET);
	}
#endif
	compiler_barrier();
	return z_impl_k_msgq_get(msgq, data, timeout);
}


extern int z_impl_k_msgq_peek(struct k_msgq * msgq, void * data);

__pinned_func
static inline int k_msgq_peek(struct k_msgq * msgq, void * data)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_msgq * val; } parm0 = { .val = msgq };
		union { uintptr_t x; void * val; } parm1 = { .val = data };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_MSGQ_PEEK);
	}
#endif
	compiler_barrier();
	return z_impl_k_msgq_peek(msgq, data);
}


extern int z_impl_k_msgq_peek_at(struct k_msgq * msgq, void * data, uint32_t idx);

__pinned_func
static inline int k_msgq_peek_at(struct k_msgq * msgq, void * data, uint32_t idx)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_msgq * val; } parm0 = { .val = msgq };
		union { uintptr_t x; void * val; } parm1 = { .val = data };
		union { uintptr_t x; uint32_t val; } parm2 = { .val = idx };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_K_MSGQ_PEEK_AT);
	}
#endif
	compiler_barrier();
	return z_impl_k_msgq_peek_at(msgq, data, idx);
}


extern void z_impl_k_msgq_purge(struct k_msgq * msgq);

__pinned_func
static inline void k_msgq_purge(struct k_msgq * msgq)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_msgq * val; } parm0 = { .val = msgq };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_MSGQ_PURGE);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_msgq_purge(msgq);
}


extern uint32_t z_impl_k_msgq_num_free_get(struct k_msgq * msgq);

__pinned_func
static inline uint32_t k_msgq_num_free_get(struct k_msgq * msgq)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_msgq * val; } parm0 = { .val = msgq };
		return (uint32_t) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_MSGQ_NUM_FREE_GET);
	}
#endif
	compiler_barrier();
	return z_impl_k_msgq_num_free_get(msgq);
}


extern void z_impl_k_msgq_get_attrs(struct k_msgq * msgq, struct k_msgq_attrs * attrs);

__pinned_func
static inline void k_msgq_get_attrs(struct k_msgq * msgq, struct k_msgq_attrs * attrs)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_msgq * val; } parm0 = { .val = msgq };
		union { uintptr_t x; struct k_msgq_attrs * val; } parm1 = { .val = attrs };
		(void) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_MSGQ_GET_ATTRS);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_msgq_get_attrs(msgq, attrs);
}


extern uint32_t z_impl_k_msgq_num_used_get(struct k_msgq * msgq);

__pinned_func
static inline uint32_t k_msgq_num_used_get(struct k_msgq * msgq)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_msgq * val; } parm0 = { .val = msgq };
		return (uint32_t) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_MSGQ_NUM_USED_GET);
	}
#endif
	compiler_barrier();
	return z_impl_k_msgq_num_used_get(msgq);
}


extern int z_impl_k_pipe_alloc_init(struct k_pipe * pipe, size_t size);

__pinned_func
static inline int k_pipe_alloc_init(struct k_pipe * pipe, size_t size)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_pipe * val; } parm0 = { .val = pipe };
		union { uintptr_t x; size_t val; } parm1 = { .val = size };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_PIPE_ALLOC_INIT);
	}
#endif
	compiler_barrier();
	return z_impl_k_pipe_alloc_init(pipe, size);
}


extern int z_impl_k_pipe_put(struct k_pipe * pipe, void * data, size_t bytes_to_write, size_t * bytes_written, size_t min_xfer, k_timeout_t timeout);

__pinned_func
static inline int k_pipe_put(struct k_pipe * pipe, void * data, size_t bytes_to_write, size_t * bytes_written, size_t min_xfer, k_timeout_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_pipe * val; } parm0 = { .val = pipe };
		union { uintptr_t x; void * val; } parm1 = { .val = data };
		union { uintptr_t x; size_t val; } parm2 = { .val = bytes_to_write };
		union { uintptr_t x; size_t * val; } parm3 = { .val = bytes_written };
		union { uintptr_t x; size_t val; } parm4 = { .val = min_xfer };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm5 = { .val = timeout };
		uintptr_t more[] = {
			parm5.split.lo,
			parm5.split.hi
		};
		return (int) arch_syscall_invoke6(parm0.x, parm1.x, parm2.x, parm3.x, parm4.x, (uintptr_t) &more, K_SYSCALL_K_PIPE_PUT);
	}
#endif
	compiler_barrier();
	return z_impl_k_pipe_put(pipe, data, bytes_to_write, bytes_written, min_xfer, timeout);
}


extern int z_impl_k_pipe_get(struct k_pipe * pipe, void * data, size_t bytes_to_read, size_t * bytes_read, size_t min_xfer, k_timeout_t timeout);

__pinned_func
static inline int k_pipe_get(struct k_pipe * pipe, void * data, size_t bytes_to_read, size_t * bytes_read, size_t min_xfer, k_timeout_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_pipe * val; } parm0 = { .val = pipe };
		union { uintptr_t x; void * val; } parm1 = { .val = data };
		union { uintptr_t x; size_t val; } parm2 = { .val = bytes_to_read };
		union { uintptr_t x; size_t * val; } parm3 = { .val = bytes_read };
		union { uintptr_t x; size_t val; } parm4 = { .val = min_xfer };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm5 = { .val = timeout };
		uintptr_t more[] = {
			parm5.split.lo,
			parm5.split.hi
		};
		return (int) arch_syscall_invoke6(parm0.x, parm1.x, parm2.x, parm3.x, parm4.x, (uintptr_t) &more, K_SYSCALL_K_PIPE_GET);
	}
#endif
	compiler_barrier();
	return z_impl_k_pipe_get(pipe, data, bytes_to_read, bytes_read, min_xfer, timeout);
}


extern size_t z_impl_k_pipe_read_avail(struct k_pipe * pipe);

__pinned_func
static inline size_t k_pipe_read_avail(struct k_pipe * pipe)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_pipe * val; } parm0 = { .val = pipe };
		return (size_t) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_PIPE_READ_AVAIL);
	}
#endif
	compiler_barrier();
	return z_impl_k_pipe_read_avail(pipe);
}


extern size_t z_impl_k_pipe_write_avail(struct k_pipe * pipe);

__pinned_func
static inline size_t k_pipe_write_avail(struct k_pipe * pipe)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_pipe * val; } parm0 = { .val = pipe };
		return (size_t) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_PIPE_WRITE_AVAIL);
	}
#endif
	compiler_barrier();
	return z_impl_k_pipe_write_avail(pipe);
}


extern void z_impl_k_pipe_flush(struct k_pipe * pipe);

__pinned_func
static inline void k_pipe_flush(struct k_pipe * pipe)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_pipe * val; } parm0 = { .val = pipe };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_PIPE_FLUSH);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_pipe_flush(pipe);
}


extern void z_impl_k_pipe_buffer_flush(struct k_pipe * pipe);

__pinned_func
static inline void k_pipe_buffer_flush(struct k_pipe * pipe)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_pipe * val; } parm0 = { .val = pipe };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_PIPE_BUFFER_FLUSH);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_pipe_buffer_flush(pipe);
}


extern int z_impl_k_poll(struct k_poll_event * events, int num_events, k_timeout_t timeout);

__pinned_func
static inline int k_poll(struct k_poll_event * events, int num_events, k_timeout_t timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_poll_event * val; } parm0 = { .val = events };
		union { uintptr_t x; int val; } parm1 = { .val = num_events };
		union { struct { uintptr_t lo, hi; } split; k_timeout_t val; } parm2 = { .val = timeout };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.split.lo, parm2.split.hi, K_SYSCALL_K_POLL);
	}
#endif
	compiler_barrier();
	return z_impl_k_poll(events, num_events, timeout);
}


extern void z_impl_k_poll_signal_init(struct k_poll_signal * sig);

__pinned_func
static inline void k_poll_signal_init(struct k_poll_signal * sig)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_poll_signal * val; } parm0 = { .val = sig };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_POLL_SIGNAL_INIT);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_poll_signal_init(sig);
}


extern void z_impl_k_poll_signal_reset(struct k_poll_signal * sig);

__pinned_func
static inline void k_poll_signal_reset(struct k_poll_signal * sig)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_poll_signal * val; } parm0 = { .val = sig };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_POLL_SIGNAL_RESET);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_poll_signal_reset(sig);
}


extern void z_impl_k_poll_signal_check(struct k_poll_signal * sig, unsigned int * signaled, int * result);

__pinned_func
static inline void k_poll_signal_check(struct k_poll_signal * sig, unsigned int * signaled, int * result)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_poll_signal * val; } parm0 = { .val = sig };
		union { uintptr_t x; unsigned int * val; } parm1 = { .val = signaled };
		union { uintptr_t x; int * val; } parm2 = { .val = result };
		(void) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_K_POLL_SIGNAL_CHECK);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_poll_signal_check(sig, signaled, result);
}


extern int z_impl_k_poll_signal_raise(struct k_poll_signal * sig, int result);

__pinned_func
static inline int k_poll_signal_raise(struct k_poll_signal * sig, int result)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_poll_signal * val; } parm0 = { .val = sig };
		union { uintptr_t x; int val; } parm1 = { .val = result };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_POLL_SIGNAL_RAISE);
	}
#endif
	compiler_barrier();
	return z_impl_k_poll_signal_raise(sig, result);
}


extern void z_impl_k_str_out(char * c, size_t n);

__pinned_func
static inline void k_str_out(char * c, size_t n)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; char * val; } parm0 = { .val = c };
		union { uintptr_t x; size_t val; } parm1 = { .val = n };
		(void) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_STR_OUT);
		return;
	}
#endif
	compiler_barrier();
	z_impl_k_str_out(c, n);
}


extern int z_impl_k_float_disable(struct k_thread * thread);

__pinned_func
static inline int k_float_disable(struct k_thread * thread)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_thread * val; } parm0 = { .val = thread };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_K_FLOAT_DISABLE);
	}
#endif
	compiler_barrier();
	return z_impl_k_float_disable(thread);
}


extern int z_impl_k_float_enable(struct k_thread * thread, unsigned int options);

__pinned_func
static inline int k_float_enable(struct k_thread * thread, unsigned int options)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct k_thread * val; } parm0 = { .val = thread };
		union { uintptr_t x; unsigned int val; } parm1 = { .val = options };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_K_FLOAT_ENABLE);
	}
#endif
	compiler_barrier();
	return z_impl_k_float_enable(thread, options);
}


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
