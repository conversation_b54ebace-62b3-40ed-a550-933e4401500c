# ninja log v5
354	1463	7767640733218316	zephyr/include/generated/ncs_version.h	74652f22c6382b51
354	1463	7767640733218316	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/ncs_version.h	74652f22c6382b51
323	2596	7767640744392525	zephyr/misc/generated/syscalls.json	e152e50d8ef9dd3d
323	2596	7767640744392525	zephyr/misc/generated/struct_tags.json	e152e50d8ef9dd3d
323	2596	7767640744392525	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/misc/generated/syscalls.json	e152e50d8ef9dd3d
323	2596	7767640744392525	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/misc/generated/struct_tags.json	e152e50d8ef9dd3d
2597	4072	7767640756819911	zephyr/include/generated/syscall_dispatch.c	4d2f0b1a15cc81ed
2597	4072	7767640756819911	zephyr/include/generated/syscall_list.h	4d2f0b1a15cc81ed
2597	4072	7767640756819911	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/syscall_dispatch.c	4d2f0b1a15cc81ed
2597	4072	7767640756819911	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/syscall_list.h	4d2f0b1a15cc81ed
1464	4449	7767640760549393	zephyr/include/generated/version.h	4b94c1aff19e2411
1464	4449	7767640760549393	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/version.h	4b94c1aff19e2411
2626	5345	7767640772349971	zephyr/include/generated/driver-validation.h	1ef762475b09cb1c
2626	5345	7767640772349971	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/driver-validation.h	1ef762475b09cb1c
2611	5871	7767640776211782	zephyr/include/generated/kobj-types-enum.h	c87963a40221765d
2611	5871	7767640776211782	zephyr/include/generated/otype-to-str.h	c87963a40221765d
2611	5871	7767640776211782	zephyr/include/generated/otype-to-size.h	c87963a40221765d
2611	5871	7767640776211782	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/kobj-types-enum.h	c87963a40221765d
2611	5871	7767640776211782	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/otype-to-str.h	c87963a40221765d
2611	5871	7767640776211782	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/otype-to-size.h	c87963a40221765d
5872	7737	7767640795881240	zephyr/CMakeFiles/offsets.dir/arch/arm/core/offsets/offsets.c.obj	c84b51f55a27f871
7741	9517	7767640813770606	zephyr/include/generated/offsets.h	e5397e3be93bf165
7741	9517	7767640813770606	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/offsets.h	e5397e3be93bf165
9541	10920	7767640827548357	zephyr/CMakeFiles/zephyr.dir/subsys/net/lib/utils/addr_utils.c.obj	96301d3883992635
9530	11373	7767640830335971	zephyr/CMakeFiles/zephyr.dir/subsys/tracing/tracing_none.c.obj	2e184cf61c518403
9569	11973	7767640838335991	zephyr/arch/common/CMakeFiles/isr_tables.dir/isr_tables.c.obj	b228220a6072d0aa
9575	11988	7767640838335991	zephyr/arch/common/CMakeFiles/arch__common.dir/sw_isr_common.c.obj	ff0553fb434459aa
9536	12072	7767640838662451	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/nrf/subsys/partition_manager/flash_map_partition_manager.c.obj	1beacc26728c8475
9524	12232	7767640840726278	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/tinycrypt/lib/source/aes_decrypt.c.obj	37003a05f6d557f2
9548	12614	7767640844330740	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/tinycrypt/lib/source/aes_encrypt.c.obj	8bc9255d88ae05cd
9518	12794	7767640844707126	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/tinycrypt/lib/source/utils.c.obj	30046053fe2f1e8e
9581	13182	7767640849621640	zephyr/drivers/serial/CMakeFiles/drivers__serial.dir/uart_nrfx_uarte.c.obj	c293cd6986906428
10921	13765	7767640854427862	zephyr/drivers/timer/CMakeFiles/drivers__timer.dir/sys_clock_init.c.obj	1f7ab5919b67e2f9
12614	14793	7767640865927880	zephyr/CMakeFiles/zephyr.dir/lib/os/dec.c.obj	9980ea4925c0d41b
12232	15002	7767640867790387	zephyr/CMakeFiles/zephyr.dir/lib/os/hex.c.obj	d1ac6252236a4ed6
11988	15317	7767640871565740	modules/nrf/lib/fatal_error/CMakeFiles/..__nrf__lib__fatal_error.dir/fatal_error.c.obj	9342dba1589bb00f
9553	15505	7767640843301201	zephyr/linker_zephyr_pre0.cmd	e4cf9a00f136f0dd
9553	15505	7767640843301201	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/linker_zephyr_pre0.cmd	e4cf9a00f136f0dd
13182	16015	7767640875036412	zephyr/drivers/serial/libdrivers__serial.a	2a412403eeb555f8
12072	16299	7767640880275260	CMakeFiles/app.dir/src/main.c.obj	5421075853329942
12794	16353	7767640879763052	zephyr/CMakeFiles/zephyr.dir/lib/os/cbprintf_packaged.c.obj	573199a1cf1872d0
11974	16769	7767640881413332	zephyr/arch/common/libisr_tables.a	888c6013ec37b842
11374	16797	7767640885753586	zephyr/drivers/timer/CMakeFiles/drivers__timer.dir/nrf_rtc_timer.c.obj	9097db9baf721132
14793	17122	7767640889637312	zephyr/CMakeFiles/zephyr.dir/lib/os/timeutil.c.obj	77e7e9e2ea8326ca
15505	17147	7767640888924143	zephyr/CMakeFiles/zephyr_pre0.dir/misc/empty_file.c.obj	2fa48a609baac504
16015	17451	7767640892540384	zephyr/CMakeFiles/zephyr.dir/lib/os/printk.c.obj	4772124ce03f29ca
15002	17590	7767640893669853	zephyr/CMakeFiles/zephyr.dir/lib/os/rb.c.obj	6a08c0284b3a7fa2
13766	17894	7767640895207247	zephyr/CMakeFiles/zephyr.dir/lib/os/fdtable.c.obj	56f3669892eee847
16353	18005	7767640897604993	zephyr/CMakeFiles/zephyr.dir/lib/os/sem.c.obj	6210a0627ead49b6
15318	18648	7767640904068694	modules/nrf/lib/fatal_error/lib..__nrf__lib__fatal_error.a	dd301dbcf112783f
17123	19119	7767640909212885	zephyr/CMakeFiles/zephyr.dir/lib/os/thread_entry.c.obj	53573f05a70fd2ae
16769	19125	7767640909192885	zephyr/arch/common/libarch__common.a	d9e4650e9fc87651
17452	19132	7767640909222896	zephyr/CMakeFiles/zephyr.dir/lib/os/notify.c.obj	204186d891c434d4
17590	19214	7767640910280728	zephyr/CMakeFiles/zephyr.dir/lib/os/multi_heap.c.obj	1145d7caab6edda
16798	19235	7767640906806208	zephyr/drivers/timer/libdrivers__timer.a	a343f6265a2ad3d9
17148	19242	7767640910560598	zephyr/CMakeFiles/zephyr.dir/lib/os/heap.c.obj	5255c7bcb3baa423
17894	20105	7767640916467783	zephyr/CMakeFiles/zephyr.dir/lib/os/heap-validate.c.obj	d0ac833b3c01c097
18005	20112	7767640916527782	zephyr/CMakeFiles/zephyr.dir/lib/os/onoff.c.obj	2944dbe58bbc1082
16299	20118	7767640911276533	app/libapp.a	134f94b2b87290f4
18648	20700	7767640926197273	zephyr/CMakeFiles/zephyr.dir/lib/os/bitarray.c.obj	4ada84e2273164bd
19235	21067	7767640929637928	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/common/soc_nrf_common.S.obj	c30195a0ed46b2ed
19133	21374	7767640931875436	zephyr/CMakeFiles/zephyr.dir/misc/generated/configs.c.obj	c5dfa37eb334d3f5
19242	21382	7767640931885453	zephyr/CMakeFiles/zephyr.dir/lib/os/reboot.c.obj	3a2785829eed0454
19120	21459	7767640933271979	zephyr/CMakeFiles/zephyr.dir/lib/os/cbprintf_complete.c.obj	3ba70fe1fcad1441
19215	21915	7767640938619163	zephyr/CMakeFiles/zephyr.dir/lib/os/assert.c.obj	523c4419af9bdf5
20106	22006	7767640937947903	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/validate_enabled_instances.c.obj	761d233b68cdf7f1
20119	22404	7767640943668950	zephyr/CMakeFiles/zephyr.dir/subsys/ipc/ipc_service/lib/ipc_rpmsg.c.obj	2f8d8a4d016f1d1e
21459	22817	7767640946931876	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/nrfx_glue.c.obj	f504536d31c92c4c
19126	22888	7767640947928123	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/nrf53/soc.c.obj	cd2cf66a15808151
20701	22941	7767640947706735	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/validate_base_addresses.c.obj	106a71a31dcb27a0
21915	22999	7767640948734038	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/mdk/system_nrf5340_network.c.obj	753805efbdfb9339
21383	23217	7767640950390467	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/helpers/nrfx_flag32_allocator.c.obj	78bd581b5c619baa
20113	23308	7767640951653889	zephyr/CMakeFiles/zephyr.dir/subsys/ipc/ipc_service/backends/ipc_rpmsg_static_vrings.c.obj	a909b4c0b5e7370d
21374	23516	7767640952595390	zephyr/CMakeFiles/zephyr.dir/subsys/ipc/ipc_service/lib/ipc_static_vrings.c.obj	49b092ed4d14832f
22999	24293	7767640960837770	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/init.c.obj	735d5f9701af9281
21067	24649	7767640960243041	zephyr/CMakeFiles/zephyr.dir/subsys/ipc/ipc_service/ipc_service.c.obj	d9c1c4bd4889cc9e
22007	25490	7767640972713820	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/helpers/nrfx_gppi_dppi.c.obj	e70c50573c203984
23517	25539	7767640971942370	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/dma.c.obj	beb532cb6bee3b36
23309	25552	7767640974450542	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/device.c.obj	45adce272cc9331e
22405	25626	7767640974967039	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_clock.c.obj	59263e919f5019f5
22941	25689	7767640975194292	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_ipc.c.obj	81108ee58ca1fff
22889	25910	7767640977207590	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_dppi.c.obj	8b0ffba7086aa1ec
23218	25920	7767640977227663	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/io.c.obj	2d7d3d47bd5272e7
24294	26933	7767640988420209	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/softirq.c.obj	57fd61f3ad0cafbe
25911	26979	7767640988774276	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/condition.c.obj	5a86067f3ce0fcc7
25540	27122	7767640989144395	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/version.c.obj	a7e68d81b3923556
25689	27381	7767640991765045	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/log.c.obj	7b247fb4822c60a4
25491	27781	7767640996588682	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/shmem.c.obj	92a1b849de170f9c
25627	27982	7767640997876474	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/irq.c.obj	140cba419fc51553
25920	28169	7767641000027432	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/init.c.obj	929f2ba6d1a70de
25552	28308	7767641001209038	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/alloc.c.obj	9d4e9ffa9913be8d
22817	28734	7767641004163729	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_gpiote.c.obj	bfe13275ccc1cbe0
27382	28964	7767641007754321	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/shmem.c.obj	21e86f33075a7655
27122	29252	7767641009462441	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/log.c.obj	3fceed47cd0a44ad
28308	29269	7767641008978603	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/cpu_idle.S.obj	24901fc46a03bbba
26934	29335	7767641012942440	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/device.c.obj	1e4258c7f49628a0
26979	29504	7767641011457753	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/irq.c.obj	c3a175920c61f2d
24649	29516	7767641010286128	zephyr/libzephyr.a	3a56b10db8c41627
27782	29982	7767641015232637	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/sys.c.obj	b8b900dd2a612fd7
27982	30245	7767641018407012	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/time.c.obj	42e69236d69568c
28170	30720	7767641022121800	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/irq_manage.c.obj	30390f5b8913e51d
29505	30736	7767641026418278	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/nmi_on_reset.S.obj	4f298153e4697e28
29269	30883	7767641027034464	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/swap_helper.S.obj	27e4e47411d9fa31
29516	30890	7767641028452685	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/swap.c.obj	c47575af7fbdbfcf
29252	31170	7767641029029291	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/nmi.c.obj	51b5774ad53693cf
29983	31553	7767641034644952	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/C_/ncs/v2.5.99-dev1/zephyr/arch/arm/core/common/tls.c.obj	f8513d2217a0941e
28965	31560	7767641033962884	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/fatal.c.obj	4e8e337599f35d28
29335	31630	7767641035131154	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/prep_c.c.obj	d54f603e20ab5104
30721	32366	7767641038909841	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/isr_wrapper.S.obj	89434186d62d55a3
30884	32389	7767641043454360	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/exc_exit.S.obj	47505a33e037dcc2
31171	32538	7767641044362486	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/fault_s.S.obj	61db0b3738344667
30890	32666	7767641045469995	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/fault.c.obj	2fec29fedd9addcd
30736	32755	7767641045767854	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/thread.c.obj	ad6215570c258814
31554	32866	7767641047269344	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/reset.S.obj	c16fe2271c659d76
31630	33058	7767641048646009	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/fpu.c.obj	39a7143e48bb1013
31561	33689	7767641053024333	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/irq_init.c.obj	a92927326e2b8a2c
28735	33700	7767641052650545	modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a	609c386ab8208eb2
32366	33740	7767641055926307	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/__aeabi_read_tp.S.obj	f8c77611b19d14ca
30246	33927	7767641050098068	modules/libmetal/libmetal/lib/libmetal.a	7e07eec9788c44cf
32389	34142	7767641059713604	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/scb.c.obj	f52f068c3fec3be1
32866	34292	7767641061585913	zephyr/arch/arch/arm/core/aarch32/cortex_m/cmse/CMakeFiles/arch__arm__core__aarch32__cortex_m__cmse.dir/arm_core_cmse.c.obj	c2313989495b32e2
32539	34466	7767641062228244	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/vector_table.S.obj	e40988f3bcf2f288
32666	34601	7767641063930841	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/thread_abort.c.obj	59f988a1c63b8c57
33059	34793	7767641065887615	zephyr/arch/arch/arm/core/aarch32/mpu/CMakeFiles/arch__arm__core__aarch32__mpu.dir/arm_core_mpu.c.obj	fb661a4765e51e15
34143	35418	7767641071443200	zephyr/soc/soc/arm/common/cortex_m/CMakeFiles/soc__arm__common__cortex_m.dir/arm_mpu_regions.c.obj	a3079124e4cd2c99
34293	35485	7767641073196248	zephyr/arch/arch/arm/core/aarch32/cortex_m/cmse/libarch__arm__core__aarch32__cortex_m__cmse.a	1a6a9b25f733497f
33741	35577	7767641072525995	zephyr/lib/libc/common/CMakeFiles/lib__libc__common.dir/source/stdlib/abort.c.obj	6edc616aed84e9a4
32755	35738	7767641073746062	zephyr/arch/arch/arm/core/aarch32/libarch__arm__core__aarch32.a	ed12d847a534d955
33927	35949	7767641078131712	zephyr/lib/libc/common/CMakeFiles/lib__libc__common.dir/source/stdlib/malloc.c.obj	a4ca278398c1389
34467	36318	7767641080939976	zephyr/subsys/random/CMakeFiles/subsys__random.dir/rand32_entropy_device.c.obj	a6f5766b6a2f36ab
33700	36406	7767641080899994	zephyr/lib/libc/picolibc/CMakeFiles/lib__libc__picolibc.dir/libc-hooks.c.obj	62d6a945e97adc48
33690	36735	7767641085901286	zephyr/arch/arch/arm/core/aarch32/mpu/CMakeFiles/arch__arm__core__aarch32__mpu.dir/arm_mpu.c.obj	f81d0a632413d132
34794	36861	7767641087071090	zephyr/subsys/bluetooth/common/CMakeFiles/subsys__bluetooth__common.dir/addr.c.obj	5bb823f0a9e8620c
34601	37067	7767641086968608	zephyr/arch/arch/arm/core/aarch32/cortex_m/libarch__arm__core__aarch32__cortex_m.a	3acc7edeef0316b2
35577	37393	7767641091918214	zephyr/subsys/bluetooth/common/CMakeFiles/subsys__bluetooth__common.dir/dummy.c.obj	3a8b84326a1f53f9
35418	37750	7767641091950434	zephyr/soc/soc/arm/common/cortex_m/libsoc__arm__common__cortex_m.a	612c3647cc6b280f
35949	38219	7767641098245953	zephyr/lib/libc/common/liblib__libc__common.a	3f72ef3f796a90f1
35485	38225	7767641100695155	zephyr/subsys/bluetooth/common/CMakeFiles/subsys__bluetooth__common.dir/bt_str.c.obj	6e4f107e13fe2988
35738	38268	7767641100735204	zephyr/subsys/bluetooth/common/CMakeFiles/subsys__bluetooth__common.dir/rpa.c.obj	16f7ce642c3f36b6
36318	38542	7767641100755203	zephyr/subsys/random/libsubsys__random.a	4e74aed5ad6bf309
36862	38752	7767641105649122	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/hci_common.c.obj	2a975be02baefefc
36406	39032	7767641104795281	zephyr/lib/libc/picolibc/liblib__libc__picolibc.a	3d7568bb104c6805
38269	39390	7767641112519599	zephyr/subsys/bluetooth/common/libsubsys__bluetooth__common.a	d955646107234d60
37068	39650	7767641115496221	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/hci_raw.c.obj	85fdf9449103809c
37394	39822	7767641115808422	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/util/memq.c.obj	846f5128abfe8272
37750	40043	7767641116783864	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/util/dbuf.c.obj	770d398483226062
36736	40146	7767641114996707	zephyr/arch/arch/arm/core/aarch32/mpu/libarch__arm__core__aarch32__mpu.a	f740e9bed1b5a594
38219	40383	7767641122088602	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/util/mayfly.c.obj	219136ddef8b503
38225	40471	7767641121315851	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/util/mem.c.obj	d0c9817b5eeb6c7f
39033	41385	7767641131989716	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ll_addr.c.obj	d37c6753329fed92
39390	41393	7767641132457203	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ll_feat.c.obj	894639ead6680545
38752	41971	7767641137158629	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ll_tx_pwr.c.obj	fd242989f671664d
39650	42057	7767641136372494	zephyr/subsys/bluetooth/host/libsubsys__bluetooth__host.a	61b912e66661546b
40146	42218	7767641138227377	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/lll_common.c.obj	8d9eae09427cc786
39823	42485	7767641143246983	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/util/util.c.obj	a1f421789040dbe4
38543	43383	7767641149961021	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ticker/ticker.c.obj	9aaa8a312195f53f
40043	43901	7767641157073978	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/hci/hci_driver.c.obj	b06b6bc3f962e1f4
40384	44263	7767641161292658	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull.c.obj	a0765fcc276d4179
41394	44624	7767641164639039	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_phy.c.obj	b0b5146b51f43909
41971	45128	7767641169864599	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_adv.c.obj	1f881a79fc2e332e
40472	45260	7767641170720640	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/hci/hci.c.obj	ecb540281ab245e5
42058	45271	7767641170955543	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_enc.c.obj	bbbad106eb428f63
42219	45534	7767641172271218	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_scan.c.obj	f52b97bd62710f37
42486	45600	7767641174953033	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_pdu.c.obj	3177d170e4cd73e0
43383	45608	7767641173608061	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_tx_queue.c.obj	53afd1e80bb91839
41386	45960	7767641177133730	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_conn.c.obj	d70b7cbf532699a1
43902	46169	7767641180959898	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_local.c.obj	651c161d3432897a
44264	46193	7767641181083175	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp.c.obj	87b6ced94abfd1b1
45129	47359	7767641190940246	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_remote.c.obj	12f1c4968db25f64
46169	47439	7767641192627405	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_chan.c.obj	59f6a81d1f33edcf
45261	47789	7767641195576003	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_chmu.c.obj	6f62a08577b5c95c
45601	48001	7767641198304726	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_central.c.obj	189f0e718583d9c4
45272	48355	7767641201821843	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_conn_upd.c.obj	1eed46b83bd2777
45961	48584	7767641202406095	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_sched.c.obj	8a388efce4b42db
44624	48703	7767641203081633	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_common.c.obj	cc2360eab3e90675
46193	48921	7767641206217475	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_df.c.obj	77490c677bab1a66
45535	49007	7767641208165171	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_peripheral.c.obj	3dd8aaf8e2728a35
47360	49161	7767641210569225	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/lll_chan.c.obj	23f6a8656e5b6615
45608	49247	7767641211046515	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_filter.c.obj	273a4bbfa12291d6
47789	49510	7767641214079139	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_clock.c.obj	d9457e09636bdeda
47440	50313	7767641220989738	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_scan.c.obj	60a84dc1a49baf35
48002	50423	7767641221980938	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/crypto/crypto.c.obj	4c4443950e9a4148
48355	51273	7767641230705099	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll.c.obj	8f891f12ad075a90
48922	51489	7767641233647282	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/cntr.c.obj	4789ba9d799602d9
49510	51496	7767641233712014	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_peripheral.c.obj	29c68bcb4c9a495f
48703	51504	7767641233387486	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_conn.c.obj	b7235e3643002927
48585	51664	7767641235170026	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_adv.c.obj	422564f6838250e0
49162	51959	7767641237492358	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_df.c.obj	b0ff9b7a0dd7efd1
49008	52102	7767641237283289	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_central.c.obj	11f66ae1349c96f4
49248	52452	7767641243284661	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/radio/radio_df.c.obj	a98c87c747802310
50314	52714	7767641245291739	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/ecb.c.obj	e15c6618172e52be
51274	52878	7767641248181707	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/ticker.c.obj	d8395a3e6a9ff54f
50424	52886	7767641247332984	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/radio/radio.c.obj	64da4bb0aa96abb8
51505	53442	7767641252073821	zephyr/subsys/net/CMakeFiles/subsys__net.dir/buf.c.obj	315556fe960f989a
51489	53589	7767641254138589	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/hci/nordic/hci_vendor.c.obj	208fc1d2beb36503
51496	53675	7767641255001499	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/mayfly.c.obj	28c8dffc0dc247a6
51664	53694	7767641255463875	zephyr/subsys/net/CMakeFiles/subsys__net.dir/buf_simple.c.obj	b4087ba999ac39f8
52103	54137	7767641259065054	zephyr/drivers/console/CMakeFiles/drivers__console.dir/uart_console.c.obj	adf503a704934c27
52886	54211	7767641257511858	zephyr/drivers/pinctrl/CMakeFiles/drivers__pinctrl.dir/common.c.obj	d7062637f880622b
51959	54355	7767641260889354	zephyr/drivers/clock_control/CMakeFiles/drivers__clock_control.dir/clock_control_nrf.c.obj	d523ce02c84b3e97
53589	54653	7767641265428878	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/version.c.obj	2b6963f65119ab92
52453	54680	7767641265498934	zephyr/drivers/entropy/CMakeFiles/drivers__entropy.dir/entropy_nrf5.c.obj	5b75772c2e02a23c
52878	55297	7767641271673810	zephyr/drivers/mbox/CMakeFiles/drivers__mbox.dir/mbox_nrfx_ipc.c.obj	ba47ea86c02f3ee
52714	55897	7767641277098928	zephyr/drivers/gpio/CMakeFiles/drivers__gpio.dir/gpio_nrfx.c.obj	8286c8d6b6ca775c
53442	56154	7767641280134859	zephyr/drivers/pinctrl/CMakeFiles/drivers__pinctrl.dir/pinctrl_nrf.c.obj	86a1a3e191a2d3e4
54137	56366	7767641280487156	zephyr/drivers/console/libdrivers__console.a	d7396e9945245cef
54355	56396	7767641280994404	zephyr/drivers/clock_control/libdrivers__clock_control.a	c9d276d007ad2eb7
54211	56561	7767641283587684	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/rpmsg/rpmsg.c.obj	fba18efe93329065
54653	56569	7767641283070329	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/virtio/virtqueue.c.obj	5524e9bae7a316f2
54681	56820	7767641282769066	zephyr/drivers/entropy/libdrivers__entropy.a	f33c717c25d0d190
53676	56902	7767641284817124	zephyr/subsys/bluetooth/controller/libsubsys__bluetooth__controller.a	76acdb86121e86b2
53694	56909	7767641284757173	zephyr/subsys/net/libsubsys__net.a	f93de3af22efd25f
55297	57519	7767641291266993	zephyr/drivers/mbox/libdrivers__mbox.a	8e662a5bba5dda6b
55897	58119	7767641296277056	zephyr/drivers/gpio/libdrivers__gpio.a	c0896c6c826c5fd2
56909	59186	7767641310812884	zephyr/kernel/CMakeFiles/kernel.dir/banner.c.obj	c17c26ab318db0c1
56155	59362	7767641309085676	zephyr/drivers/pinctrl/libdrivers__pinctrl.a	6b5d0066f76ba9a8
56366	59370	7767641310852909	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/virtio/virtio.c.obj	849d2a142266bea3
56561	59531	7767641312649710	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/remoteproc/remoteproc_virtio.c.obj	a51a7461526a1323
56397	59559	7767641314732921	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/rpmsg/rpmsg_virtio.c.obj	adf7718a5db633d2
56820	59719	7767641315683516	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/remoteproc/elf_loader.c.obj	4b6664401a906acb
57519	59734	7767641316083013	zephyr/kernel/CMakeFiles/kernel.dir/errno.c.obj	26dc5d32d766e24c
56903	59883	7767641317643380	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/remoteproc/rsc_table_parser.c.obj	30a14fed1f5bb27f
56569	59890	7767641317663380	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/remoteproc/remoteproc.c.obj	1320073bffb9b316
59371	60580	7767641324520209	zephyr/kernel/CMakeFiles/kernel.dir/device.c.obj	5e644ebe3efa317d
58120	60788	7767641324842090	zephyr/kernel/CMakeFiles/kernel.dir/busy_wait.c.obj	9ea12c220cc91268
59720	60939	7767641327297358	zephyr/kernel/CMakeFiles/kernel.dir/version.c.obj	978f8298d4fd317d
59362	60998	7767641328328165	zephyr/kernel/CMakeFiles/kernel.dir/main_weak.c.obj	1e8c7e635c190bb4
59186	62006	7767641338347548	zephyr/kernel/CMakeFiles/kernel.dir/fatal.c.obj	f02b3a476a484143
59734	62211	7767641340574719	zephyr/kernel/CMakeFiles/kernel.dir/kheap.c.obj	8c3517657951da44
59560	62281	7767641341737643	zephyr/kernel/CMakeFiles/kernel.dir/thread.c.obj	e00374abdf89d4af
59532	62479	7767641343205577	zephyr/kernel/CMakeFiles/kernel.dir/mem_slab.c.obj	c48f8a4da05f93f7
59884	62488	7767641343407922	zephyr/kernel/CMakeFiles/kernel.dir/init.c.obj	140ae342f9e03614
60788	62819	7767641344758403	zephyr/kernel/CMakeFiles/kernel.dir/idle.c.obj	e3320420a3d98dae
60939	62892	7767641345496897	zephyr/kernel/CMakeFiles/kernel.dir/msg_q.c.obj	dbd5cc4bfd58bb67
60998	63234	7767641351903549	zephyr/kernel/CMakeFiles/kernel.dir/queue.c.obj	f4336c06aa07843e
60581	63311	7767641351251825	zephyr/kernel/CMakeFiles/kernel.dir/mutex.c.obj	d99ccdfef8d3c850
62211	64027	7767641359633348	zephyr/kernel/CMakeFiles/kernel.dir/system_work_q.c.obj	47c51c025b46a667
59891	64126	7767641358656314	modules/open-amp/open-amp/lib/libopen_amp.a	52d4b55891f3721c
62006	64133	7767641359738058	zephyr/kernel/CMakeFiles/kernel.dir/mailbox.c.obj	157976ecb4bbb42
62893	64165	7767641360820957	zephyr/kernel/CMakeFiles/kernel.dir/condvar.c.obj	d73621d056c5290
62281	64821	7767641366709376	zephyr/kernel/CMakeFiles/kernel.dir/sem.c.obj	907a96387a99142
62480	65097	7767641369628993	zephyr/kernel/CMakeFiles/kernel.dir/stack.c.obj	47652644d8319fad
63235	65145	7767641369789995	zephyr/kernel/CMakeFiles/kernel.dir/xip.c.obj	927f06882b3befc0
62820	65470	7767641373770922	zephyr/kernel/CMakeFiles/kernel.dir/work.c.obj	c72f73a8b84f37a
64126	65823	7767641376952240	zephyr/kernel/CMakeFiles/kernel.dir/mempool.c.obj	c9a474ebd8126bb4
62489	65848	7767641377102225	zephyr/kernel/CMakeFiles/kernel.dir/sched.c.obj	d09565ddb3ef9aa0
63312	66036	7767641379300739	zephyr/kernel/CMakeFiles/kernel.dir/timer.c.obj	d85b5ec46622f0ef
64166	66072	7767641379475637	zephyr/kernel/CMakeFiles/kernel.dir/dynamic_disabled.c.obj	c3bbcb4bf0993857
64027	66230	7767641381769582	zephyr/kernel/CMakeFiles/kernel.dir/timeout.c.obj	5165fe112db29428
64133	66240	7767641381103894	zephyr/kernel/CMakeFiles/kernel.dir/poll.c.obj	52c8504e1eac4703
66241	67815	7767641393693401	zephyr/kernel/libkernel.a	391089fa16c95bf8
67815	71625	7767641421365019	zephyr/zephyr_pre0.elf	1671c92340dc40d6
67815	71625	7767641421365019	zephyr/zephyr_pre0.map	1671c92340dc40d6
67815	71625	7767641421365019	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr_pre0.map	1671c92340dc40d6
71625	73683	7767641442326748	zephyr/linker.cmd	22b277841a389aee
71625	73683	7767641442326748	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/linker.cmd	22b277841a389aee
73683	78298	7767641498646253	zephyr/isr_tables.c	2f949d3c65894f8c
73683	78298	7767641498646253	zephyr/isrList.bin	2f949d3c65894f8c
73683	78298	7767641498646253	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/isr_tables.c	2f949d3c65894f8c
73683	78298	7767641498646253	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/isrList.bin	2f949d3c65894f8c
78305	79784	7767641516144563	zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj	af03ed081f2c23c3
78298	80284	7767641520826040	zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj	54bb8b8dc62b4dd3
80285	96006	7767641585182689	zephyr/zephyr.elf	56313c7d5afbb7cf
80285	96006	7767641585182689	zephyr/zephyr.map	56313c7d5afbb7cf
80285	96006	7767641585182689	zephyr/zephyr.hex	56313c7d5afbb7cf
80285	96006	7767641585182689	zephyr/zephyr.bin	56313c7d5afbb7cf
80285	96006	7767641585182689	zephyr/zephyr.stat	56313c7d5afbb7cf
80285	96006	7767641585182689	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr.map	56313c7d5afbb7cf
80285	96006	7767641585182689	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr.hex	56313c7d5afbb7cf
80285	96006	7767641585182689	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr.bin	56313c7d5afbb7cf
80285	96006	7767641585182689	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr.stat	56313c7d5afbb7cf
96007	97696	7767641693197538	zephyr/app.hex	20884c6b441850b1
96007	97696	7767641693197538	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/app.hex	20884c6b441850b1
97696	100394	7767641717664558	zephyr/merged_CPUNET.hex	bc34f85055d92f37
97696	100394	7767641717664558	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/merged_CPUNET.hex	bc34f85055d92f37
