# ninja log v5
101	587	7766801551774632	zephyr/include/generated/ncs_version.h	74652f22c6382b51
101	587	7766801551774632	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/ncs_version.h	74652f22c6382b51
57	1795	7766801563622998	zephyr/misc/generated/syscalls.json	e152e50d8ef9dd3d
57	1795	7766801563622998	zephyr/misc/generated/struct_tags.json	e152e50d8ef9dd3d
57	1795	7766801563622998	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/misc/generated/syscalls.json	e152e50d8ef9dd3d
57	1795	7766801563622998	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/misc/generated/struct_tags.json	e152e50d8ef9dd3d
1796	2545	7766801568766950	zephyr/include/generated/syscall_dispatch.c	4d2f0b1a15cc81ed
1796	2545	7766801568766950	zephyr/include/generated/syscall_list.h	4d2f0b1a15cc81ed
1796	2545	7766801568766950	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/syscall_dispatch.c	4d2f0b1a15cc81ed
1796	2545	7766801568766950	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/syscall_list.h	4d2f0b1a15cc81ed
1807	2643	7766801571741714	zephyr/include/generated/kobj-types-enum.h	c87963a40221765d
1807	2643	7766801571741714	zephyr/include/generated/otype-to-str.h	c87963a40221765d
1807	2643	7766801571741714	zephyr/include/generated/otype-to-size.h	c87963a40221765d
1807	2643	7766801571741714	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/kobj-types-enum.h	c87963a40221765d
1807	2643	7766801571741714	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/otype-to-str.h	c87963a40221765d
1807	2643	7766801571741714	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/otype-to-size.h	c87963a40221765d
1819	2770	7766801573135670	zephyr/include/generated/driver-validation.h	1ef762475b09cb1c
1819	2770	7766801573135670	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/driver-validation.h	1ef762475b09cb1c
588	2832	7766801574341861	zephyr/include/generated/version.h	4b94c1aff19e2411
588	2832	7766801574341861	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/version.h	4b94c1aff19e2411
2770	3436	7766801580803225	zephyr/CMakeFiles/offsets.dir/arch/arm/core/offsets/offsets.c.obj	c84b51f55a27f871
3440	3821	7766801584711756	zephyr/include/generated/offsets.h	e5397e3be93bf165
3440	3821	7766801584711756	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/offsets.h	e5397e3be93bf165
3827	4370	7766801589634860	zephyr/CMakeFiles/zephyr.dir/subsys/net/lib/utils/addr_utils.c.obj	96301d3883992635
3837	4375	7766801589644887	zephyr/CMakeFiles/zephyr.dir/subsys/tracing/tracing_none.c.obj	2e184cf61c518403
3863	4528	7766801591396932	zephyr/arch/common/CMakeFiles/isr_tables.dir/isr_tables.c.obj	b228220a6072d0aa
3822	4552	7766801591406954	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/tinycrypt/lib/source/utils.c.obj	30046053fe2f1e8e
3841	4650	7766801592386381	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/nrf/subsys/partition_manager/flash_map_partition_manager.c.obj	1beacc26728c8475
3867	4901	7766801594218444	zephyr/arch/common/CMakeFiles/arch__common.dir/sw_isr_common.c.obj	ff0553fb434459aa
4370	5146	7766801597049065	zephyr/drivers/timer/CMakeFiles/drivers__timer.dir/sys_clock_init.c.obj	1f7ab5919b67e2f9
3832	5154	7766801597394090	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/tinycrypt/lib/source/aes_decrypt.c.obj	37003a05f6d557f2
3846	5201	7766801598191721	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/tinycrypt/lib/source/aes_encrypt.c.obj	8bc9255d88ae05cd
3851	5534	7766801595017447	zephyr/linker_zephyr_pre0.cmd	e4cf9a00f136f0dd
3851	5534	7766801595017447	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/linker_zephyr_pre0.cmd	e4cf9a00f136f0dd
4902	5774	7766801603761380	zephyr/CMakeFiles/zephyr.dir/lib/os/hex.c.obj	d1ac6252236a4ed6
4553	5830	7766801604423555	modules/nrf/lib/fatal_error/CMakeFiles/..__nrf__lib__fatal_error.dir/fatal_error.c.obj	9342dba1589bb00f
5154	6010	7766801606258067	zephyr/CMakeFiles/zephyr.dir/lib/os/dec.c.obj	9980ea4925c0d41b
3872	6268	7766801608904658	zephyr/drivers/serial/CMakeFiles/drivers__serial.dir/uart_nrfx_uarte.c.obj	c293cd6986906428
4375	6381	7766801609902187	zephyr/drivers/timer/CMakeFiles/drivers__timer.dir/nrf_rtc_timer.c.obj	9097db9baf721132
5535	6518	7766801611296163	zephyr/CMakeFiles/zephyr_pre0.dir/misc/empty_file.c.obj	2fa48a609baac504
4650	6698	7766801612878780	CMakeFiles/app.dir/src/main.c.obj	5421075853329942
5202	6711	7766801612948894	zephyr/CMakeFiles/zephyr.dir/lib/os/fdtable.c.obj	56f3669892eee847
6011	6739	7766801613406252	zephyr/CMakeFiles/zephyr.dir/lib/os/printk.c.obj	4772124ce03f29ca
4528	6953	7766801613228698	zephyr/arch/common/libisr_tables.a	888c6013ec37b842
5146	7066	7766801616853301	zephyr/CMakeFiles/zephyr.dir/lib/os/cbprintf_packaged.c.obj	573199a1cf1872d0
5774	7200	7766801617661638	zephyr/CMakeFiles/zephyr.dir/lib/os/rb.c.obj	6a08c0284b3a7fa2
6268	7534	7766801620114522	zephyr/drivers/serial/libdrivers__serial.a	2a412403eeb555f8
6518	7540	7766801621252385	zephyr/CMakeFiles/zephyr.dir/lib/os/thread_entry.c.obj	53573f05a70fd2ae
5830	7607	7766801621332386	modules/nrf/lib/fatal_error/lib..__nrf__lib__fatal_error.a	dd301dbcf112783f
6739	7614	7766801621933979	zephyr/CMakeFiles/zephyr.dir/lib/os/timeutil.c.obj	77e7e9e2ea8326ca
6381	7620	7766801621432385	zephyr/drivers/timer/libdrivers__timer.a	a343f6265a2ad3d9
6712	7747	7766801623778924	zephyr/CMakeFiles/zephyr.dir/lib/os/sem.c.obj	6210a0627ead49b6
6699	8176	7766801625139907	app/libapp.a	134f94b2b87290f4
7541	8548	7766801631233420	zephyr/CMakeFiles/zephyr.dir/lib/os/multi_heap.c.obj	1145d7caab6edda
7201	8554	7766801631393419	zephyr/CMakeFiles/zephyr.dir/lib/os/bitarray.c.obj	4ada84e2273164bd
6953	8560	7766801629711890	zephyr/arch/common/libarch__common.a	d9e4650e9fc87651
7066	8637	7766801632043010	zephyr/CMakeFiles/zephyr.dir/lib/os/heap.c.obj	5255c7bcb3baa423
7747	8854	7766801633620137	zephyr/CMakeFiles/zephyr.dir/misc/generated/configs.c.obj	c5dfa37eb334d3f5
7535	8892	7766801634290126	zephyr/CMakeFiles/zephyr.dir/lib/os/notify.c.obj	204186d891c434d4
7614	9391	7766801639692544	zephyr/CMakeFiles/zephyr.dir/lib/os/heap-validate.c.obj	d0ac833b3c01c097
7608	9414	7766801639972540	zephyr/CMakeFiles/zephyr.dir/lib/os/onoff.c.obj	2944dbe58bbc1082
8176	9452	7766801640458387	zephyr/CMakeFiles/zephyr.dir/lib/os/reboot.c.obj	3a2785829eed0454
7620	9468	7766801640498387	zephyr/CMakeFiles/zephyr.dir/lib/os/cbprintf_complete.c.obj	3ba70fe1fcad1441
8555	9502	7766801641075266	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/common/soc_nrf_common.S.obj	c30195a0ed46b2ed
8548	10074	7766801645743795	zephyr/CMakeFiles/zephyr.dir/lib/os/assert.c.obj	523c4419af9bdf5
8637	10903	7766801655259606	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/validate_enabled_instances.c.obj	761d233b68cdf7f1
9502	11020	7766801655404728	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/helpers/nrfx_flag32_allocator.c.obj	78bd581b5c619baa
9414	11067	7766801656694834	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/validate_base_addresses.c.obj	106a71a31dcb27a0
9468	11119	7766801657137139	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/nrfx_glue.c.obj	f504536d31c92c4c
9392	11191	7766801657326630	zephyr/CMakeFiles/zephyr.dir/subsys/ipc/ipc_service/lib/ipc_rpmsg.c.obj	2f8d8a4d016f1d1e
9453	11197	7766801657580763	zephyr/CMakeFiles/zephyr.dir/subsys/ipc/ipc_service/lib/ipc_static_vrings.c.obj	49b092ed4d14832f
8560	11281	7766801658775821	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/nrf53/soc.c.obj	cd2cf66a15808151
8854	11455	7766801660516260	zephyr/CMakeFiles/zephyr.dir/subsys/ipc/ipc_service/ipc_service.c.obj	d9c1c4bd4889cc9e
8893	11462	7766801660619056	zephyr/CMakeFiles/zephyr.dir/subsys/ipc/ipc_service/backends/ipc_rpmsg_static_vrings.c.obj	a909b4c0b5e7370d
10074	11531	7766801661362691	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/mdk/system_nrf5340_network.c.obj	753805efbdfb9339
11021	12288	7766801668245629	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_clock.c.obj	59263e919f5019f5
11532	12561	7766801671263444	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/init.c.obj	735d5f9701af9281
11191	12569	7766801671740607	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_ipc.c.obj	81108ee58ca1fff
11198	12632	7766801671882053	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/dma.c.obj	beb532cb6bee3b36
10904	12691	7766801672042043	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/helpers/nrfx_gppi_dppi.c.obj	e70c50573c203984
11281	12699	7766801672747555	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/device.c.obj	45adce272cc9331e
11067	12887	7766801674792798	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_dppi.c.obj	8b0ffba7086aa1ec
11456	13303	7766801679048167	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/io.c.obj	2d7d3d47bd5272e7
12561	13443	7766801679335535	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/version.c.obj	a7e68d81b3923556
12692	13552	7766801681495162	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/log.c.obj	7b247fb4822c60a4
12288	13639	7766801682444776	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/softirq.c.obj	57fd61f3ad0cafbe
12570	13741	7766801683356220	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/alloc.c.obj	9d4e9ffa9913be8d
12632	13920	7766801684587500	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/shmem.c.obj	92a1b849de170f9c
12699	13935	7766801685306286	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/irq.c.obj	140cba419fc51553
12888	14002	7766801685916275	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/init.c.obj	929f2ba6d1a70de
13553	15012	7766801695536887	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/irq.c.obj	c3a175920c61f2d
13304	15019	7766801695850927	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/condition.c.obj	5a86067f3ce0fcc7
13920	15026	7766801696075594	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/sys.c.obj	b8b900dd2a612fd7
13640	15066	7766801695546906	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/device.c.obj	1e4258c7f49628a0
13443	15189	7766801697913625	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/log.c.obj	3fceed47cd0a44ad
11462	15225	7766801695662754	zephyr/libzephyr.a	3a56b10db8c41627
13742	15285	7766801698826944	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/shmem.c.obj	21e86f33075a7655
14003	15340	7766801699699102	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/irq_manage.c.obj	30390f5b8913e51d
15026	15564	7766801701543078	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/cpu_idle.S.obj	24901fc46a03bbba
13936	15737	7766801703137658	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/time.c.obj	42e69236d69568c
15225	15803	7766801704441681	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/swap_helper.S.obj	27e4e47411d9fa31
15340	15884	7766801704925132	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/isr_wrapper.S.obj	89434186d62d55a3
15066	15959	7766801705395279	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/swap.c.obj	c47575af7fbdbfcf
15286	15966	7766801705245278	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/nmi_on_reset.S.obj	4f298153e4697e28
15189	16273	7766801708945181	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/prep_c.c.obj	d54f603e20ab5104
15012	16310	7766801709374479	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/nmi.c.obj	51b5774ad53693cf
11120	16487	7766801710594277	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_gpiote.c.obj	bfe13275ccc1cbe0
15020	16615	7766801712092727	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/fatal.c.obj	4e8e337599f35d28
15804	16654	7766801712237891	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/C_/ncs/v2.5.99-dev1/zephyr/arch/arm/core/common/tls.c.obj	f8513d2217a0941e
15564	16715	7766801712387571	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/thread.c.obj	ad6215570c258814
15959	16811	7766801713734566	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/exc_exit.S.obj	47505a33e037dcc2
16274	17067	7766801716868408	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/fault_s.S.obj	61db0b3738344667
16615	17100	7766801716998417	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/reset.S.obj	c16fe2271c659d76
16310	17112	7766801717003480	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/irq_init.c.obj	a92927326e2b8a2c
16655	17130	7766801717255582	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/__aeabi_read_tp.S.obj	f8c77611b19d14ca
15967	17219	7766801718293668	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/fpu.c.obj	39a7143e48bb1013
17101	17514	7766801721393408	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/vector_table.S.obj	e40988f3bcf2f288
16812	17520	7766801721503389	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/thread_abort.c.obj	59f988a1c63b8c57
15885	17750	7766801723018954	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/fault.c.obj	2fec29fedd9addcd
15738	17950	7766801722798984	modules/libmetal/libmetal/lib/libmetal.a	7e07eec9788c44cf
16487	18124	7766801723891574	modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a	609c386ab8208eb2
16715	18193	7766801724769902	zephyr/arch/arch/arm/core/aarch32/libarch__arm__core__aarch32.a	ed12d847a534d955
17130	18199	7766801727967078	zephyr/arch/arch/arm/core/aarch32/mpu/CMakeFiles/arch__arm__core__aarch32__mpu.dir/arm_core_mpu.c.obj	fb661a4765e51e15
17067	18215	7766801728117071	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/scb.c.obj	f52f068c3fec3be1
17113	18319	7766801729233257	zephyr/arch/arch/arm/core/aarch32/cortex_m/cmse/CMakeFiles/arch__arm__core__aarch32__cortex_m__cmse.dir/arm_core_cmse.c.obj	c2313989495b32e2
17521	18516	7766801730931787	zephyr/lib/libc/common/CMakeFiles/lib__libc__common.dir/source/stdlib/malloc.c.obj	a4ca278398c1389
17514	18743	7766801733688998	zephyr/lib/libc/picolibc/CMakeFiles/lib__libc__picolibc.dir/libc-hooks.c.obj	62d6a945e97adc48
17950	18839	7766801734665477	zephyr/soc/soc/arm/common/cortex_m/CMakeFiles/soc__arm__common__cortex_m.dir/arm_mpu_regions.c.obj	a3079124e4cd2c99
18124	18907	7766801735295809	zephyr/subsys/random/CMakeFiles/subsys__random.dir/rand32_entropy_device.c.obj	a6f5766b6a2f36ab
17751	18950	7766801735624462	zephyr/lib/libc/common/CMakeFiles/lib__libc__common.dir/source/stdlib/abort.c.obj	6edc616aed84e9a4
17219	18993	7766801735983402	zephyr/arch/arch/arm/core/aarch32/mpu/CMakeFiles/arch__arm__core__aarch32__mpu.dir/arm_mpu.c.obj	f81d0a632413d132
18320	19392	7766801738211666	zephyr/arch/arch/arm/core/aarch32/cortex_m/cmse/libarch__arm__core__aarch32__cortex_m__cmse.a	1a6a9b25f733497f
18215	19654	7766801741319232	zephyr/arch/arch/arm/core/aarch32/cortex_m/libarch__arm__core__aarch32__cortex_m.a	3acc7edeef0316b2
18744	19819	7766801742697799	zephyr/lib/libc/picolibc/liblib__libc__picolibc.a	3d7568bb104c6805
18907	19924	7766801743491978	zephyr/subsys/random/libsubsys__random.a	4e74aed5ad6bf309
18839	20280	7766801745529240	zephyr/soc/soc/arm/common/cortex_m/libsoc__arm__common__cortex_m.a	612c3647cc6b280f
18950	20372	7766801747113416	zephyr/lib/libc/common/liblib__libc__common.a	3f72ef3f796a90f1
18193	20699	7766801753133486	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll.c.obj	8f891f12ad075a90
18199	20764	7766801753812215	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_adv.c.obj	422564f6838250e0
19393	20950	7766801754720852	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_clock.c.obj	d9457e09636bdeda
18993	20957	7766801754192070	zephyr/arch/arch/arm/core/aarch32/mpu/libarch__arm__core__aarch32__mpu.a	f740e9bed1b5a594
18517	21187	7766801757690271	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_scan.c.obj	60a84dc1a49baf35
20280	21302	7766801759260878	zephyr/subsys/bluetooth/common/CMakeFiles/subsys__bluetooth__common.dir/addr.c.obj	5bb823f0a9e8620c
19655	21308	7766801759028840	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_conn.c.obj	b7235e3643002927
19924	21459	7766801760270089	zephyr/subsys/bluetooth/common/CMakeFiles/subsys__bluetooth__common.dir/dummy.c.obj	3a8b84326a1f53f9
19820	21829	7766801762991169	zephyr/subsys/bluetooth/common/CMakeFiles/subsys__bluetooth__common.dir/bt_str.c.obj	6e4f107e13fe2988
20372	21928	7766801764796688	zephyr/subsys/bluetooth/common/CMakeFiles/subsys__bluetooth__common.dir/rpa.c.obj	16f7ce642c3f36b6
20765	22166	7766801766225290	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/hci_common.c.obj	2a975be02baefefc
21188	22405	7766801770026738	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/util/dbuf.c.obj	770d398483226062
20951	22452	7766801770299086	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/util/mem.c.obj	d0c9817b5eeb6c7f
20700	22458	7766801770329141	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/hci_raw.c.obj	85fdf9449103809c
20957	22651	7766801772722549	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/util/mayfly.c.obj	219136ddef8b503
21302	22701	7766801773047375	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/util/memq.c.obj	846f5128abfe8272
21308	22836	7766801773932690	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ll_addr.c.obj	d37c6753329fed92
21459	23246	7766801777730147	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/util/util.c.obj	a1f421789040dbe4
22166	23340	7766801779479778	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ll_feat.c.obj	894639ead6680545
22406	23603	7766801781550959	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ll_tx_pwr.c.obj	fd242989f671664d
21929	24134	7766801784541859	zephyr/subsys/bluetooth/common/libsubsys__bluetooth__common.a	d955646107234d60
22459	24170	7766801785579828	zephyr/subsys/bluetooth/host/libsubsys__bluetooth__host.a	61b912e66661546b
22701	24218	7766801788163484	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/lll_common.c.obj	8d9eae09427cc786
21830	24702	7766801792524914	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ticker/ticker.c.obj	9aaa8a312195f53f
22652	25050	7766801796218165	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/hci/hci_driver.c.obj	b06b6bc3f962e1f4
22836	25411	7766801799421258	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull.c.obj	a0765fcc276d4179
23247	25731	7766801803113168	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_adv.c.obj	1f881a79fc2e332e
24218	25758	7766801803484297	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_tx_queue.c.obj	53afd1e80bb91839
23603	25958	7766801805559311	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_enc.c.obj	bbbad106eb428f63
24134	26160	7766801807095522	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_scan.c.obj	f52b97bd62710f37
24170	26439	7766801810401517	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_phy.c.obj	b0b5146b51f43909
24702	26583	7766801811892738	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_local.c.obj	651c161d3432897a
23341	27045	7766801816605572	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_conn.c.obj	d70b7cbf532699a1
22453	27058	7766801816575572	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/hci/hci.c.obj	ecb540281ab245e5
25758	27192	7766801818088238	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_remote.c.obj	12f1c4968db25f64
25731	27340	7766801819233209	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_common.c.obj	cc2360eab3e90675
26161	27601	7766801821480928	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_chmu.c.obj	6f62a08577b5c95c
25051	27698	7766801822951176	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp.c.obj	87b6ced94abfd1b1
25412	27796	7766801824057164	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_pdu.c.obj	3177d170e4cd73e0
25958	27905	7766801825350972	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_conn_upd.c.obj	1eed46b83bd2777
27046	27991	7766801825875865	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_chan.c.obj	59f6a81d1f33edcf
27059	28339	7766801829310703	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_df.c.obj	77490c677bab1a66
26584	28396	7766801829848403	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_central.c.obj	189f0e718583d9c4
27340	28681	7766801832497763	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_sched.c.obj	8a388efce4b42db
27601	28723	7766801832717814	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/lll_chan.c.obj	23f6a8656e5b6615
26439	28877	7766801834824780	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_peripheral.c.obj	3dd8aaf8e2728a35
27699	29095	7766801836768253	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/crypto/crypto.c.obj	4c4443950e9a4148
27796	29163	7766801836908252	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_df.c.obj	b0ff9b7a0dd7efd1
27992	29630	7766801842226239	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_central.c.obj	11f66ae1349c96f4
27192	29708	7766801843038126	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_filter.c.obj	273a4bbfa12291d6
28340	29749	7766801843541729	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_peripheral.c.obj	29c68bcb4c9a495f
28397	29790	7766801843748328	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/cntr.c.obj	4789ba9d799602d9
27906	29909	7766801844541763	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/radio/radio_df.c.obj	a98c87c747802310
28723	29981	7766801845504446	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/mayfly.c.obj	28c8dffc0dc247a6
28682	29989	7766801845514467	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/ticker.c.obj	d8395a3e6a9ff54f
29163	30442	7766801850472349	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/hci/nordic/hci_vendor.c.obj	208fc1d2beb36503
29095	30494	7766801850696845	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/ecb.c.obj	e15c6618172e52be
29631	31080	7766801856788335	zephyr/subsys/net/CMakeFiles/subsys__net.dir/buf_simple.c.obj	b4087ba999ac39f8
29790	31090	7766801857052028	zephyr/drivers/console/CMakeFiles/drivers__console.dir/uart_console.c.obj	adf503a704934c27
29708	31132	7766801857215160	zephyr/subsys/net/CMakeFiles/subsys__net.dir/buf.c.obj	315556fe960f989a
29989	31165	7766801857404043	zephyr/drivers/mbox/CMakeFiles/drivers__mbox.dir/mbox_nrfx_ipc.c.obj	ba47ea86c02f3ee
28878	31195	7766801858048902	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/radio/radio.c.obj	64da4bb0aa96abb8
29910	31380	7766801858952217	zephyr/drivers/entropy/CMakeFiles/drivers__entropy.dir/entropy_nrf5.c.obj	5b75772c2e02a23c
30495	31442	7766801860219107	zephyr/drivers/pinctrl/CMakeFiles/drivers__pinctrl.dir/common.c.obj	d7062637f880622b
29750	31521	7766801861041810	zephyr/drivers/clock_control/CMakeFiles/drivers__clock_control.dir/clock_control_nrf.c.obj	d523ce02c84b3e97
31081	32292	7766801868908359	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/virtio/virtio.c.obj	849d2a142266bea3
31132	32312	7766801868813577	zephyr/subsys/net/libsubsys__net.a	f93de3af22efd25f
30443	32329	7766801869208897	zephyr/drivers/pinctrl/CMakeFiles/drivers__pinctrl.dir/pinctrl_nrf.c.obj	86a1a3e191a2d3e4
31380	32370	7766801869453589	zephyr/drivers/entropy/libdrivers__entropy.a	f33c717c25d0d190
29981	32376	7766801869308895	zephyr/drivers/gpio/CMakeFiles/drivers__gpio.dir/gpio_nrfx.c.obj	8286c8d6b6ca775c
31091	32453	7766801869503551	zephyr/drivers/console/libdrivers__console.a	d7396e9945245cef
31165	32497	7766801869463549	zephyr/drivers/mbox/libdrivers__mbox.a	8e662a5bba5dda6b
31521	32564	7766801869999899	zephyr/drivers/clock_control/libdrivers__clock_control.a	c9d276d007ad2eb7
31196	32617	7766801870794866	zephyr/subsys/bluetooth/controller/libsubsys__bluetooth__controller.a	76acdb86121e86b2
32313	32762	7766801873836732	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/version.c.obj	2b6963f65119ab92
31442	32813	7766801874378617	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/virtio/virtqueue.c.obj	5524e9bae7a316f2
32293	33299	7766801878819345	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/rpmsg/rpmsg.c.obj	fba18efe93329065
32376	33449	7766801878527184	zephyr/drivers/gpio/libdrivers__gpio.a	c0896c6c826c5fd2
32814	33609	7766801882097786	zephyr/kernel/CMakeFiles/kernel.dir/device.c.obj	5e644ebe3efa317d
32370	33643	7766801882334957	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/remoteproc/elf_loader.c.obj	4b6664401a906acb
32330	33721	7766801881797709	zephyr/drivers/pinctrl/libdrivers__pinctrl.a	6b5d0066f76ba9a8
32617	33751	7766801883573092	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/remoteproc/rsc_table_parser.c.obj	30a14fed1f5bb27f
32762	33842	7766801884566806	zephyr/kernel/CMakeFiles/kernel.dir/main_weak.c.obj	1e8c7e635c190bb4
32497	33959	7766801885100418	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/remoteproc/remoteproc_virtio.c.obj	a51a7461526a1323
32564	34042	7766801886135447	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/remoteproc/remoteproc.c.obj	1320073bffb9b316
32454	34134	7766801886990456	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/rpmsg/rpmsg_virtio.c.obj	adf7718a5db633d2
33300	34213	7766801888495281	zephyr/kernel/CMakeFiles/kernel.dir/banner.c.obj	c17c26ab318db0c1
33643	34444	7766801890717086	zephyr/kernel/CMakeFiles/kernel.dir/errno.c.obj	26dc5d32d766e24c
33449	34457	7766801890924354	zephyr/kernel/CMakeFiles/kernel.dir/fatal.c.obj	f02b3a476a484143
33609	34541	7766801891047368	zephyr/kernel/CMakeFiles/kernel.dir/busy_wait.c.obj	9ea12c220cc91268
34043	34699	7766801892813367	zephyr/kernel/CMakeFiles/kernel.dir/version.c.obj	978f8298d4fd317d
33751	34726	7766801893382507	zephyr/kernel/CMakeFiles/kernel.dir/kheap.c.obj	8c3517657951da44
33960	34945	7766801894709414	zephyr/kernel/CMakeFiles/kernel.dir/mem_slab.c.obj	c48f8a4da05f93f7
33721	35200	7766801897730351	zephyr/kernel/CMakeFiles/kernel.dir/thread.c.obj	e00374abdf89d4af
34541	35343	7766801899510033	zephyr/kernel/CMakeFiles/kernel.dir/idle.c.obj	e3320420a3d98dae
33843	35402	7766801899590029	zephyr/kernel/CMakeFiles/kernel.dir/init.c.obj	140ae342f9e03614
34214	35408	7766801900017446	zephyr/kernel/CMakeFiles/kernel.dir/msg_q.c.obj	dbd5cc4bfd58bb67
34445	35588	7766801901331061	zephyr/kernel/CMakeFiles/kernel.dir/mailbox.c.obj	157976ecb4bbb42
34699	35728	7766801902992215	zephyr/kernel/CMakeFiles/kernel.dir/queue.c.obj	f4336c06aa07843e
34134	35771	7766801901171084	modules/open-amp/open-amp/lib/libopen_amp.a	52d4b55891f3721c
34727	36033	7766801906712026	zephyr/kernel/CMakeFiles/kernel.dir/work.c.obj	c72f73a8b84f37a
34457	36051	7766801905562634	zephyr/kernel/CMakeFiles/kernel.dir/mutex.c.obj	d99ccdfef8d3c850
34945	36291	7766801908844477	zephyr/kernel/CMakeFiles/kernel.dir/condvar.c.obj	d73621d056c5290
35200	36304	7766801909056157	zephyr/kernel/CMakeFiles/kernel.dir/sem.c.obj	907a96387a99142
35408	36669	7766801912548328	zephyr/kernel/CMakeFiles/kernel.dir/stack.c.obj	47652644d8319fad
35729	37052	7766801916144868	zephyr/kernel/CMakeFiles/kernel.dir/poll.c.obj	52c8504e1eac4703
35589	37056	7766801916531416	zephyr/kernel/CMakeFiles/kernel.dir/xip.c.obj	927f06882b3befc0
35771	37103	7766801916631414	zephyr/kernel/CMakeFiles/kernel.dir/timeout.c.obj	5165fe112db29428
36033	37107	7766801917224460	zephyr/kernel/CMakeFiles/kernel.dir/mempool.c.obj	c9a474ebd8126bb4
36051	37139	7766801917457231	zephyr/kernel/CMakeFiles/kernel.dir/timer.c.obj	d85b5ec46622f0ef
35344	37140	7766801917591858	zephyr/kernel/CMakeFiles/kernel.dir/system_work_q.c.obj	47c51c025b46a667
36291	37246	7766801918502929	zephyr/kernel/CMakeFiles/kernel.dir/dynamic_disabled.c.obj	c3bbcb4bf0993857
35402	37392	7766801920297357	zephyr/kernel/CMakeFiles/kernel.dir/sched.c.obj	d09565ddb3ef9aa0
37393	37964	7766801926099855	zephyr/kernel/libkernel.a	391089fa16c95bf8
37964	38744	7766801932713484	zephyr/zephyr_pre0.elf	1671c92340dc40d6
37964	38744	7766801932713484	zephyr/zephyr_pre0.map	1671c92340dc40d6
37964	38744	7766801932713484	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr_pre0.map	1671c92340dc40d6
38744	39021	7766801935459990	zephyr/linker.cmd	22b277841a389aee
38744	39021	7766801935459990	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/linker.cmd	22b277841a389aee
39022	39843	7766801944889138	zephyr/isr_tables.c	2f949d3c65894f8c
39022	39843	7766801944889138	zephyr/isrList.bin	2f949d3c65894f8c
39022	39843	7766801944889138	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/isr_tables.c	2f949d3c65894f8c
39022	39843	7766801944889138	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/isrList.bin	2f949d3c65894f8c
39850	39963	7766801946168218	zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj	54bb8b8dc62b4dd3
39843	40111	7766801947653562	zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj	af03ed081f2c23c3
40112	42764	7766801954079121	zephyr/zephyr.elf	56313c7d5afbb7cf
40112	42764	7766801954079121	zephyr/zephyr.map	56313c7d5afbb7cf
40112	42764	7766801954079121	zephyr/zephyr.hex	56313c7d5afbb7cf
40112	42764	7766801954079121	zephyr/zephyr.bin	56313c7d5afbb7cf
40112	42764	7766801954079121	zephyr/zephyr.stat	56313c7d5afbb7cf
40112	42764	7766801954079121	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr.map	56313c7d5afbb7cf
40112	42764	7766801954079121	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr.hex	56313c7d5afbb7cf
40112	42764	7766801954079121	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr.bin	56313c7d5afbb7cf
40112	42764	7766801954079121	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr.stat	56313c7d5afbb7cf
42764	43047	7766801977048347	zephyr/app.hex	20884c6b441850b1
42764	43047	7766801977048347	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/app.hex	20884c6b441850b1
43047	43515	7766801981653234	zephyr/merged_CPUNET.hex	bc34f85055d92f37
43047	43515	7766801981653234	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/merged_CPUNET.hex	bc34f85055d92f37
