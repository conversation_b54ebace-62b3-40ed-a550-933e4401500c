# ninja log v5
52	259	7790355358614363	zephyr/include/generated/ncs_version.h	74652f22c6382b51
52	259	7790355358614363	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/ncs_version.h	74652f22c6382b51
260	1570	7790355371686778	zephyr/include/generated/version.h	4b94c1aff19e2411
260	1570	7790355371686778	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/version.h	4b94c1aff19e2411
39	24449	7790355600500088	zephyr/misc/generated/syscalls.json	e152e50d8ef9dd3d
39	24449	7790355600500088	zephyr/misc/generated/struct_tags.json	e152e50d8ef9dd3d
39	24449	7790355600500088	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/misc/generated/syscalls.json	e152e50d8ef9dd3d
39	24449	7790355600500088	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/misc/generated/struct_tags.json	e152e50d8ef9dd3d
24450	24757	7790355602689834	zephyr/include/generated/syscall_dispatch.c	4d2f0b1a15cc81ed
24450	24757	7790355602689834	zephyr/include/generated/syscall_list.h	4d2f0b1a15cc81ed
24450	24757	7790355602689834	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/syscall_dispatch.c	4d2f0b1a15cc81ed
24450	24757	7790355602689834	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/syscall_list.h	4d2f0b1a15cc81ed
24459	24879	7790355604706348	zephyr/include/generated/driver-validation.h	1ef762475b09cb1c
24459	24879	7790355604706348	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/driver-validation.h	1ef762475b09cb1c
24469	24944	7790355605320386	zephyr/include/generated/kobj-types-enum.h	c87963a40221765d
24469	24944	7790355605320386	zephyr/include/generated/otype-to-str.h	c87963a40221765d
24469	24944	7790355605320386	zephyr/include/generated/otype-to-size.h	c87963a40221765d
24469	24944	7790355605320386	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/kobj-types-enum.h	c87963a40221765d
24469	24944	7790355605320386	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/otype-to-str.h	c87963a40221765d
24469	24944	7790355605320386	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/otype-to-size.h	c87963a40221765d
24945	25757	7790355613644377	zephyr/CMakeFiles/offsets.dir/arch/arm/core/offsets/offsets.c.obj	c84b51f55a27f871
25760	25974	7790355615779895	zephyr/include/generated/offsets.h	e5397e3be93bf165
25760	25974	7790355615779895	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/include/generated/offsets.h	e5397e3be93bf165
26003	26695	7790355622299587	zephyr/drivers/timer/CMakeFiles/drivers__timer.dir/sys_clock_init.c.obj	1f7ab5919b67e2f9
25983	26770	7790355623122286	zephyr/arch/common/CMakeFiles/isr_tables.dir/isr_tables.c.obj	b228220a6072d0aa
25987	26827	7790355623611095	zephyr/arch/common/CMakeFiles/arch__common.dir/sw_isr_common.c.obj	ff0553fb434459aa
26048	26987	7790355624555962	zephyr/CMakeFiles/zephyr.dir/lib/os/hex.c.obj	d1ac6252236a4ed6
26012	27005	7790355625333074	modules/nrf/lib/fatal_error/CMakeFiles/..__nrf__lib__fatal_error.dir/fatal_error.c.obj	9342dba1589bb00f
26007	27221	7790355627723296	zephyr/drivers/timer/CMakeFiles/drivers__timer.dir/nrf_rtc_timer.c.obj	9097db9baf721132
26705	27245	7790355627663303	zephyr/CMakeFiles/zephyr.dir/lib/os/dec.c.obj	9980ea4925c0d41b
26053	27268	7790355628044374	zephyr/CMakeFiles/zephyr.dir/lib/os/fdtable.c.obj	56f3669892eee847
25992	27350	7790355628748110	zephyr/drivers/serial/CMakeFiles/drivers__serial.dir/uart_nrfx_uarte.c.obj	c293cd6986906428
26998	27540	7790355631463089	zephyr/CMakeFiles/zephyr.dir/lib/os/sem.c.obj	6210a0627ead49b6
26018	27717	7790355632733794	CMakeFiles/app.dir/src/main.c.obj	5421075853329942
27249	27926	7790355634923118	zephyr/CMakeFiles/zephyr.dir/lib/os/timeutil.c.obj	77e7e9e2ea8326ca
26813	28018	7790355635728659	zephyr/arch/common/libisr_tables.a	888c6013ec37b842
27224	28030	7790355635821062	zephyr/drivers/timer/libdrivers__timer.a	a343f6265a2ad3d9
27392	28040	7790355635885599	zephyr/drivers/serial/libdrivers__serial.a	2a412403eeb555f8
27329	28046	7790355636045138	zephyr/CMakeFiles/zephyr.dir/lib/os/printk.c.obj	4772124ce03f29ca
26883	28088	7790355636499362	zephyr/CMakeFiles/zephyr.dir/lib/os/cbprintf_packaged.c.obj	573199a1cf1872d0
25974	28100	7790355633581118	zephyr/linker_zephyr_pre0.cmd	e4cf9a00f136f0dd
25974	28100	7790355633581118	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/linker_zephyr_pre0.cmd	e4cf9a00f136f0dd
27017	28106	7790355635985137	modules/nrf/lib/fatal_error/lib..__nrf__lib__fatal_error.a	dd301dbcf112783f
27746	28452	7790355639096353	app/libapp.a	134f94b2b87290f4
28046	28829	7790355640660235	zephyr/CMakeFiles/zephyr.dir/lib/os/multi_heap.c.obj	1145d7caab6edda
27937	28836	7790355642500318	zephyr/CMakeFiles/zephyr.dir/lib/os/thread_entry.c.obj	53573f05a70fd2ae
28101	28842	7790355643127870	zephyr/CMakeFiles/zephyr_pre0.dir/misc/empty_file.c.obj	2fa48a609baac504
27540	28848	7790355643362495	zephyr/CMakeFiles/zephyr.dir/lib/os/rb.c.obj	6a08c0284b3a7fa2
28089	28875	7790355644394831	zephyr/CMakeFiles/zephyr.dir/lib/os/notify.c.obj	204186d891c434d4
28018	29130	7790355644938158	zephyr/arch/common/libarch__common.a	d9e4650e9fc87651
28040	29183	7790355647187182	zephyr/CMakeFiles/zephyr.dir/lib/os/heap-validate.c.obj	d0ac833b3c01c097
28876	29293	7790355648925574	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/common/soc_nrf_common.S.obj	c30195a0ed46b2ed
28034	29347	7790355648675576	zephyr/CMakeFiles/zephyr.dir/lib/os/heap.c.obj	5255c7bcb3baa423
28106	29507	7790355649388497	zephyr/CMakeFiles/zephyr.dir/lib/os/onoff.c.obj	2944dbe58bbc1082
28842	29702	7790355650687126	zephyr/CMakeFiles/zephyr.dir/lib/os/reboot.c.obj	3a2785829eed0454
28836	29715	7790355650983125	zephyr/CMakeFiles/zephyr.dir/lib/os/assert.c.obj	523c4419af9bdf5
29170	29725	7790355651183130	zephyr/CMakeFiles/zephyr.dir/misc/generated/configs.c.obj	c5dfa37eb334d3f5
28612	29818	7790355653460642	zephyr/CMakeFiles/zephyr.dir/lib/os/bitarray.c.obj	4ada84e2273164bd
28830	30103	7790355656786278	zephyr/CMakeFiles/zephyr.dir/lib/os/cbprintf_complete.c.obj	3ba70fe1fcad1441
29822	30202	7790355657584247	zephyr/CMakeFiles/zephyr.dir/subsys/net/lib/utils/addr_utils.c.obj	96301d3883992635
29726	30260	7790355658074935	zephyr/CMakeFiles/zephyr.dir/subsys/tracing/tracing_none.c.obj	2e184cf61c518403
29708	30501	7790355660581110	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/validate_base_addresses.c.obj	106a71a31dcb27a0
29590	30551	7790355660922168	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/validate_enabled_instances.c.obj	761d233b68cdf7f1
28848	30600	7790355661146067	zephyr/CMakeFiles/zephyr.dir/soc/arm/nordic_nrf/nrf53/soc.c.obj	cd2cf66a15808151
30261	30677	7790355662193983	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/tinycrypt/lib/source/utils.c.obj	30046053fe2f1e8e
29716	30779	7790355663295992	zephyr/CMakeFiles/zephyr.dir/subsys/ipc/ipc_service/lib/ipc_static_vrings.c.obj	49b092ed4d14832f
29341	30927	7790355664852716	zephyr/CMakeFiles/zephyr.dir/subsys/ipc/ipc_service/lib/ipc_rpmsg.c.obj	2f8d8a4d016f1d1e
30600	31070	7790355666073472	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/mdk/system_nrf5340_network.c.obj	753805efbdfb9339
30253	31392	7790355666093474	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/tinycrypt/lib/source/aes_decrypt.c.obj	37003a05f6d557f2
30136	31422	7790355666242945	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/nrf/subsys/partition_manager/flash_map_partition_manager.c.obj	1beacc26728c8475
30593	31434	7790355669482499	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/helpers/nrfx_flag32_allocator.c.obj	78bd581b5c619baa
29434	31474	7790355669472464	zephyr/CMakeFiles/zephyr.dir/subsys/ipc/ipc_service/ipc_service.c.obj	d9c1c4bd4889cc9e
30529	31574	7790355669656374	zephyr/CMakeFiles/zephyr.dir/C_/ncs/v2.5.99-dev1/modules/crypto/tinycrypt/lib/source/aes_encrypt.c.obj	8bc9255d88ae05cd
29229	31711	7790355669820309	zephyr/CMakeFiles/zephyr.dir/subsys/ipc/ipc_service/backends/ipc_rpmsg_static_vrings.c.obj	a909b4c0b5e7370d
30678	31737	7790355670166015	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/nrfx_glue.c.obj	f504536d31c92c4c
30824	31777	7790355673253913	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/helpers/nrfx_gppi_dppi.c.obj	e70c50573c203984
31271	32303	7790355678314227	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_dppi.c.obj	8b0ffba7086aa1ec
31766	32354	7790355678916587	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/init.c.obj	735d5f9701af9281
30965	32387	7790355678941537	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_clock.c.obj	59263e919f5019f5
31479	32503	7790355680667373	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/device.c.obj	45adce272cc9331e
31459	32573	7790355681162010	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/io.c.obj	2d7d3d47bd5272e7
31416	32742	7790355681563531	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_ipc.c.obj	81108ee58ca1fff
31595	32753	7790355681765365	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/dma.c.obj	beb532cb6bee3b36
31938	32761	7790355682968180	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/version.c.obj	a7e68d81b3923556
32324	32768	7790355683042631	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/log.c.obj	7b247fb4822c60a4
32355	33464	7790355690301558	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/irq.c.obj	140cba419fc51553
32439	33475	7790355690203422	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/shmem.c.obj	92a1b849de170f9c
32509	33485	7790355690291507	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/softirq.c.obj	57fd61f3ad0cafbe
32743	33610	7790355691479298	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/condition.c.obj	5a86067f3ce0fcc7
32761	33833	7790355693888227	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/alloc.c.obj	9d4e9ffa9913be8d
32755	33902	7790355694088216	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/init.c.obj	929f2ba6d1a70de
32768	33919	7790355694705537	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/device.c.obj	1e4258c7f49628a0
32639	33925	7790355694856890	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/irq.c.obj	c3a175920c61f2d
31723	34076	7790355694936923	zephyr/libzephyr.a	3a56b10db8c41627
33476	34461	7790355700004264	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/time.c.obj	42e69236d69568c
33464	34524	7790355700732731	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/shmem.c.obj	21e86f33075a7655
33906	34662	7790355700896654	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/cpu_idle.S.obj	24901fc46a03bbba
33485	34710	7790355702099878	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/sys.c.obj	b8b900dd2a612fd7
33675	34744	7790355702613741	modules/libmetal/libmetal/lib/CMakeFiles/metal.dir/system/zephyr/log.c.obj	3fceed47cd0a44ad
33925	34759	7790355702826185	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/irq_manage.c.obj	30390f5b8913e51d
34695	35030	7790355705798145	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/nmi_on_reset.S.obj	4f298153e4697e28
33919	35127	7790355706336127	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/fatal.c.obj	4e8e337599f35d28
34076	35227	7790355707865998	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/nmi.c.obj	51b5774ad53693cf
34485	35337	7790355708376413	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/swap_helper.S.obj	27e4e47411d9fa31
34737	35596	7790355710057847	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/swap.c.obj	c47575af7fbdbfcf
35079	35627	7790355711032184	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/isr_wrapper.S.obj	89434186d62d55a3
34609	35651	7790355711052158	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/prep_c.c.obj	d54f603e20ab5104
34759	35661	7790355711622214	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/C_/ncs/v2.5.99-dev1/zephyr/arch/arm/core/common/tls.c.obj	f8513d2217a0941e
35228	35682	7790355712066818	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/exc_exit.S.obj	47505a33e037dcc2
34744	35703	7790355711894834	modules/libmetal/libmetal/lib/libmetal.a	7e07eec9788c44cf
33851	35711	7790355712247262	zephyr/arch/arch/arm/core/aarch32/mpu/CMakeFiles/arch__arm__core__aarch32__mpu.dir/arm_mpu.c.obj	f81d0a632413d132
31428	35718	7790355712635515	modules/hal_nordic/nrfx/CMakeFiles/modules__hal_nordic__nrfx.dir/C_/ncs/v2.5.99-dev1/modules/hal/nordic/nrfx/drivers/src/nrfx_gpiote.c.obj	bfe13275ccc1cbe0
35668	36094	7790355716889424	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/fault_s.S.obj	61db0b3738344667
35690	36121	7790355716859481	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/__aeabi_read_tp.S.obj	f8c77611b19d14ca
35143	36152	7790355716999425	zephyr/arch/arch/arm/core/aarch32/CMakeFiles/arch__arm__core__aarch32.dir/thread.c.obj	ad6215570c258814
35655	36174	7790355717355011	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/reset.S.obj	c16fe2271c659d76
35609	36359	7790355719154651	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/fpu.c.obj	39a7143e48bb1013
35645	36407	7790355719308767	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/irq_init.c.obj	a92927326e2b8a2c
36094	36428	7790355719703123	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/vector_table.S.obj	e40988f3bcf2f288
35706	36458	7790355719983716	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/thread_abort.c.obj	59f988a1c63b8c57
35712	36621	7790355721527483	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/scb.c.obj	f52f068c3fec3be1
35719	36910	7790355720883007	modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a	609c386ab8208eb2
36121	36944	7790355724470248	zephyr/arch/arch/arm/core/aarch32/cortex_m/cmse/CMakeFiles/arch__arm__core__aarch32__cortex_m__cmse.dir/arm_core_cmse.c.obj	c2313989495b32e2
36175	37065	7790355726209655	zephyr/arch/arch/arm/core/aarch32/mpu/CMakeFiles/arch__arm__core__aarch32__mpu.dir/arm_core_mpu.c.obj	fb661a4765e51e15
35387	37145	7790355726770673	zephyr/arch/arch/arm/core/aarch32/cortex_m/CMakeFiles/arch__arm__core__aarch32__cortex_m.dir/fault.c.obj	2fec29fedd9addcd
36152	37184	7790355725866184	zephyr/arch/arch/arm/core/aarch32/libarch__arm__core__aarch32.a	ed12d847a534d955
36423	37191	7790355727358016	zephyr/lib/libc/common/CMakeFiles/lib__libc__common.dir/source/stdlib/abort.c.obj	6edc616aed84e9a4
36440	37250	7790355727840050	zephyr/lib/libc/common/CMakeFiles/lib__libc__common.dir/source/stdlib/malloc.c.obj	a4ca278398c1389
36472	37263	7790355728061843	zephyr/soc/soc/arm/common/cortex_m/CMakeFiles/soc__arm__common__cortex_m.dir/arm_mpu_regions.c.obj	a3079124e4cd2c99
36938	37419	7790355730063180	zephyr/subsys/bluetooth/common/CMakeFiles/subsys__bluetooth__common.dir/dummy.c.obj	3a8b84326a1f53f9
36375	37435	7790355730126147	zephyr/lib/libc/picolibc/CMakeFiles/lib__libc__picolibc.dir/libc-hooks.c.obj	62d6a945e97adc48
36737	37510	7790355730868817	zephyr/subsys/random/CMakeFiles/subsys__random.dir/rand32_entropy_device.c.obj	a6f5766b6a2f36ab
37191	37590	7790355731410054	zephyr/subsys/bluetooth/common/CMakeFiles/subsys__bluetooth__common.dir/addr.c.obj	5bb823f0a9e8620c
36955	37753	7790355732669410	zephyr/arch/arch/arm/core/aarch32/cortex_m/cmse/libarch__arm__core__aarch32__cortex_m__cmse.a	1a6a9b25f733497f
37164	37759	7790355732739417	zephyr/arch/arch/arm/core/aarch32/cortex_m/libarch__arm__core__aarch32__cortex_m.a	3acc7edeef0316b2
37251	37765	7790355733174445	zephyr/lib/libc/common/liblib__libc__common.a	3f72ef3f796a90f1
37114	37771	7790355733314459	zephyr/arch/arch/arm/core/aarch32/mpu/libarch__arm__core__aarch32__mpu.a	f740e9bed1b5a594
37264	37919	7790355733554443	zephyr/soc/soc/arm/common/cortex_m/libsoc__arm__common__cortex_m.a	612c3647cc6b280f
37185	38135	7790355737048865	zephyr/subsys/bluetooth/common/CMakeFiles/subsys__bluetooth__common.dir/bt_str.c.obj	6e4f107e13fe2988
37435	38151	7790355735426755	zephyr/lib/libc/picolibc/liblib__libc__picolibc.a	3d7568bb104c6805
37420	38218	7790355737647648	zephyr/subsys/bluetooth/common/CMakeFiles/subsys__bluetooth__common.dir/rpa.c.obj	16f7ce642c3f36b6
37511	38553	7790355737915370	zephyr/subsys/random/libsubsys__random.a	4e74aed5ad6bf309
37753	38647	7790355741936149	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/hci_common.c.obj	2a975be02baefefc
37590	38689	7790355742281242	zephyr/subsys/bluetooth/host/CMakeFiles/subsys__bluetooth__host.dir/hci_raw.c.obj	85fdf9449103809c
37759	38709	7790355742478049	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/util/mayfly.c.obj	219136ddef8b503
37766	38717	7790355742511810	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/util/mem.c.obj	d0c9817b5eeb6c7f
37771	38766	7790355743263047	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/util/memq.c.obj	846f5128abfe8272
38138	38782	7790355743853423	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ll_feat.c.obj	894639ead6680545
37919	38831	7790355744075424	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/util/dbuf.c.obj	770d398483226062
38180	39095	7790355746769116	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ll_addr.c.obj	d37c6753329fed92
38218	39396	7790355748176506	zephyr/subsys/bluetooth/common/libsubsys__bluetooth__common.a	d955646107234d60
38772	39543	7790355751217900	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/lll_common.c.obj	8d9eae09427cc786
38695	39555	7790355750578166	zephyr/subsys/bluetooth/host/libsubsys__bluetooth__host.a	61b912e66661546b
38571	39616	7790355751549501	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/util/util.c.obj	a1f421789040dbe4
38657	39710	7790355752605038	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ll_tx_pwr.c.obj	fd242989f671664d
38783	40773	7790355763217153	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull.c.obj	a0765fcc276d4179
38831	40969	7790355765248705	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/hci/hci_driver.c.obj	b06b6bc3f962e1f4
39409	40988	7790355765468704	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_scan.c.obj	f52b97bd62710f37
38710	41027	7790355765478704	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ticker/ticker.c.obj	9aaa8a312195f53f
39103	41099	7790355766501849	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_phy.c.obj	b0b5146b51f43909
39711	41149	7790355766654048	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_common.c.obj	cc2360eab3e90675
39617	41216	7790355767717869	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_enc.c.obj	bbbad106eb428f63
39555	41307	7790355768601903	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_adv.c.obj	1f881a79fc2e332e
41020	41769	7790355773137192	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_tx_queue.c.obj	53afd1e80bb91839
38718	42162	7790355777202230	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/hci/hci.c.obj	ecb540281ab245e5
40800	42185	7790355777576733	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_local.c.obj	651c161d3432897a
39543	42257	7790355777898363	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_conn.c.obj	d70b7cbf532699a1
41121	42475	7790355778371428	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_chmu.c.obj	6f62a08577b5c95c
41155	42481	7790355779986355	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_remote.c.obj	12f1c4968db25f64
41309	42488	7790355780735633	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_conn_upd.c.obj	1eed46b83bd2777
40981	42637	7790355782207516	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp_pdu.c.obj	3177d170e4cd73e0
41217	42698	7790355782683017	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_peripheral.c.obj	3dd8aaf8e2728a35
41040	42708	7790355782962375	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_llcp.c.obj	87b6ced94abfd1b1
42247	43086	7790355786638386	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/lll_chan.c.obj	23f6a8656e5b6615
42482	43138	7790355786501854	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_chan.c.obj	59f6a81d1f33edcf
41792	43164	7790355787019161	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_central.c.obj	189f0e718583d9c4
42167	43316	7790355788843829	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_sched.c.obj	8a388efce4b42db
42475	43730	7790355792820466	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_df.c.obj	77490c677bab1a66
42637	43809	7790355793481199	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll.c.obj	8f891f12ad075a90
42489	44105	7790355795399660	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_adv.c.obj	422564f6838250e0
42700	44117	7790355796272384	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_clock.c.obj	d9457e09636bdeda
43324	44263	7790355796711137	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/cntr.c.obj	4789ba9d799602d9
42718	44272	7790355796764173	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_scan.c.obj	60a84dc1a49baf35
43114	44284	7790355797068320	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/crypto/crypto.c.obj	4c4443950e9a4148
43164	44308	7790355797343035	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_peripheral.c.obj	29c68bcb4c9a495f
42387	44343	7790355797751406	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/ull_filter.c.obj	273a4bbfa12291d6
43158	44827	7790355804085623	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_conn.c.obj	b7235e3643002927
44108	45073	7790355806364114	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_central.c.obj	11f66ae1349c96f4
44276	45116	7790355806514585	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/ticker.c.obj	d8395a3e6a9ff54f
43945	45356	7790355808619090	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/lll/lll_df.c.obj	b0ff9b7a0dd7efd1
44123	45381	7790355809172796	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/hci/nordic/hci_vendor.c.obj	208fc1d2beb36503
44326	45392	7790355809276583	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/mayfly.c.obj	28c8dffc0dc247a6
44264	45691	7790355809378460	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/ecb.c.obj	e15c6618172e52be
43756	45720	7790355809598466	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/radio/radio_df.c.obj	a98c87c747802310
44343	45753	7790355813173483	zephyr/subsys/net/CMakeFiles/subsys__net.dir/buf_simple.c.obj	b4087ba999ac39f8
44292	46136	7790355815275712	zephyr/subsys/bluetooth/controller/CMakeFiles/subsys__bluetooth__controller.dir/ll_sw/nordic/hal/nrf5/radio/radio.c.obj	64da4bb0aa96abb8
44894	46195	7790355816251485	zephyr/subsys/net/CMakeFiles/subsys__net.dir/buf.c.obj	315556fe960f989a
45279	46421	7790355817341393	zephyr/drivers/console/CMakeFiles/drivers__console.dir/uart_console.c.obj	adf503a704934c27
45357	46450	7790355818313379	zephyr/drivers/entropy/CMakeFiles/drivers__entropy.dir/entropy_nrf5.c.obj	5b75772c2e02a23c
45108	46472	7790355819677143	zephyr/drivers/clock_control/CMakeFiles/drivers__clock_control.dir/clock_control_nrf.c.obj	d523ce02c84b3e97
45730	46493	7790355820163755	zephyr/drivers/pinctrl/CMakeFiles/drivers__pinctrl.dir/common.c.obj	d7062637f880622b
45474	46562	7790355821279302	zephyr/drivers/mbox/CMakeFiles/drivers__mbox.dir/mbox_nrfx_ipc.c.obj	ba47ea86c02f3ee
46497	46872	7790355824322566	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/version.c.obj	2b6963f65119ab92
46430	47133	7790355826260709	zephyr/drivers/console/libdrivers__console.a	d7396e9945245cef
46475	47221	7790355827007454	zephyr/drivers/clock_control/libdrivers__clock_control.a	c9d276d007ad2eb7
46451	47226	7790355827037459	zephyr/drivers/entropy/libdrivers__entropy.a	f33c717c25d0d190
46009	47237	7790355828195366	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/rpmsg/rpmsg.c.obj	fba18efe93329065
46322	47310	7790355827960454	zephyr/subsys/net/libsubsys__net.a	f93de3af22efd25f
46563	47318	7790355828356686	zephyr/drivers/mbox/libdrivers__mbox.a	8e662a5bba5dda6b
45387	47359	7790355828895352	zephyr/drivers/gpio/CMakeFiles/drivers__gpio.dir/gpio_nrfx.c.obj	8286c8d6b6ca775c
45712	47477	7790355830481134	zephyr/drivers/pinctrl/CMakeFiles/drivers__pinctrl.dir/pinctrl_nrf.c.obj	86a1a3e191a2d3e4
46162	47492	7790355830386545	zephyr/subsys/bluetooth/controller/libsubsys__bluetooth__controller.a	76acdb86121e86b2
46873	47904	7790355834850111	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/virtio/virtqueue.c.obj	5524e9bae7a316f2
47163	47986	7790355835756605	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/virtio/virtio.c.obj	849d2a142266bea3
47359	48278	7790355836151107	zephyr/drivers/gpio/libdrivers__gpio.a	c0896c6c826c5fd2
47226	48294	7790355838465620	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/remoteproc/remoteproc_virtio.c.obj	a51a7461526a1323
47478	48488	7790355838363993	zephyr/drivers/pinctrl/libdrivers__pinctrl.a	6b5d0066f76ba9a8
47320	48564	7790355838893650	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/remoteproc/rsc_table_parser.c.obj	30a14fed1f5bb27f
47492	48577	7790355839609560	zephyr/kernel/CMakeFiles/kernel.dir/main_weak.c.obj	1e8c7e635c190bb4
47312	48604	7790355841048758	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/rpmsg/rpmsg_virtio.c.obj	adf7718a5db633d2
47221	48629	7790355841088756	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/remoteproc/elf_loader.c.obj	4b6664401a906acb
47908	48636	7790355841801585	zephyr/kernel/CMakeFiles/kernel.dir/busy_wait.c.obj	9ea12c220cc91268
48121	49002	7790355845109186	zephyr/kernel/CMakeFiles/kernel.dir/errno.c.obj	26dc5d32d766e24c
48418	49119	7790355846536103	zephyr/kernel/CMakeFiles/kernel.dir/device.c.obj	5e644ebe3efa317d
48651	49190	7790355846466102	zephyr/kernel/CMakeFiles/kernel.dir/version.c.obj	978f8298d4fd317d
48286	49327	7790355846446105	zephyr/kernel/CMakeFiles/kernel.dir/banner.c.obj	c17c26ab318db0c1
47238	49365	7790355847561452	modules/open-amp/open-amp/lib/CMakeFiles/open_amp.dir/remoteproc/remoteproc.c.obj	1320073bffb9b316
48621	49386	7790355849582207	zephyr/kernel/CMakeFiles/kernel.dir/kheap.c.obj	8c3517657951da44
48585	49482	7790355850197122	zephyr/kernel/CMakeFiles/kernel.dir/fatal.c.obj	f02b3a476a484143
48545	49491	7790355850227119	zephyr/kernel/CMakeFiles/kernel.dir/mem_slab.c.obj	c48f8a4da05f93f7
48571	49501	7790355850187124	zephyr/kernel/CMakeFiles/kernel.dir/init.c.obj	140ae342f9e03614
48630	49855	7790355854254200	zephyr/kernel/CMakeFiles/kernel.dir/thread.c.obj	e00374abdf89d4af
49016	50005	7790355855846910	zephyr/kernel/CMakeFiles/kernel.dir/mutex.c.obj	d99ccdfef8d3c850
49236	50158	7790355857088598	zephyr/kernel/CMakeFiles/kernel.dir/mailbox.c.obj	157976ecb4bbb42
49350	50294	7790355857914325	zephyr/kernel/CMakeFiles/kernel.dir/msg_q.c.obj	dbd5cc4bfd58bb67
49502	50320	7790355858820196	zephyr/kernel/CMakeFiles/kernel.dir/stack.c.obj	47652644d8319fad
49168	50364	7790355859400076	zephyr/kernel/CMakeFiles/kernel.dir/idle.c.obj	e3320420a3d98dae
49367	50478	7790355859420084	modules/open-amp/open-amp/lib/libopen_amp.a	52d4b55891f3721c
49491	50783	7790355860688980	zephyr/kernel/CMakeFiles/kernel.dir/queue.c.obj	f4336c06aa07843e
49386	50831	7790355863210882	zephyr/kernel/CMakeFiles/kernel.dir/work.c.obj	c72f73a8b84f37a
49886	50864	7790355864141337	zephyr/kernel/CMakeFiles/kernel.dir/sem.c.obj	907a96387a99142
50081	50968	7790355864956451	zephyr/kernel/CMakeFiles/kernel.dir/system_work_q.c.obj	47c51c025b46a667
50346	51038	7790355865941338	zephyr/kernel/CMakeFiles/kernel.dir/condvar.c.obj	d73621d056c5290
50181	51153	7790355867301113	zephyr/kernel/CMakeFiles/kernel.dir/timer.c.obj	d85b5ec46622f0ef
49484	51199	7790355867849012	zephyr/kernel/CMakeFiles/kernel.dir/sched.c.obj	d09565ddb3ef9aa0
50374	51264	7790355868395266	zephyr/kernel/CMakeFiles/kernel.dir/poll.c.obj	52c8504e1eac4703
50651	51276	7790355868525267	zephyr/kernel/CMakeFiles/kernel.dir/xip.c.obj	927f06882b3befc0
50306	51278	7790355868588517	zephyr/kernel/CMakeFiles/kernel.dir/timeout.c.obj	5165fe112db29428
50859	51335	7790355869386099	zephyr/kernel/CMakeFiles/kernel.dir/dynamic_disabled.c.obj	c3bbcb4bf0993857
50789	51348	7790355869546095	zephyr/kernel/CMakeFiles/kernel.dir/mempool.c.obj	c9a474ebd8126bb4
51349	51537	7790355871423064	zephyr/kernel/libkernel.a	391089fa16c95bf8
51537	51861	7790355874135849	zephyr/zephyr_pre0.elf	1671c92340dc40d6
51537	51861	7790355874135849	zephyr/zephyr_pre0.map	1671c92340dc40d6
51537	51861	7790355874135849	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr_pre0.map	1671c92340dc40d6
51861	51982	7790355875312521	zephyr/linker.cmd	22b277841a389aee
51861	51982	7790355875312521	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/linker.cmd	22b277841a389aee
51982	52460	7790355880588956	zephyr/isr_tables.c	2f949d3c65894f8c
51982	52460	7790355880588956	zephyr/isrList.bin	2f949d3c65894f8c
51982	52460	7790355880588956	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/isr_tables.c	2f949d3c65894f8c
51982	52460	7790355880588956	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/isrList.bin	2f949d3c65894f8c
52460	52520	7790355881257435	zephyr/CMakeFiles/zephyr_final.dir/misc/empty_file.c.obj	54bb8b8dc62b4dd3
52463	52626	7790355882336332	zephyr/CMakeFiles/zephyr_final.dir/isr_tables.c.obj	af03ed081f2c23c3
52627	54361	7790355886871906	zephyr/zephyr.elf	56313c7d5afbb7cf
52627	54361	7790355886871906	zephyr/zephyr.map	56313c7d5afbb7cf
52627	54361	7790355886871906	zephyr/zephyr.hex	56313c7d5afbb7cf
52627	54361	7790355886871906	zephyr/zephyr.bin	56313c7d5afbb7cf
52627	54361	7790355886871906	zephyr/zephyr.stat	56313c7d5afbb7cf
52627	54361	7790355886871906	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr.map	56313c7d5afbb7cf
52627	54361	7790355886871906	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr.hex	56313c7d5afbb7cf
52627	54361	7790355886871906	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr.bin	56313c7d5afbb7cf
52627	54361	7790355886871906	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/zephyr.stat	56313c7d5afbb7cf
54362	54658	7790355902614150	zephyr/app.hex	20884c6b441850b1
54362	54658	7790355902614150	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/app.hex	20884c6b441850b1
54658	55087	7790355906896797	zephyr/merged_CPUNET.hex	bc34f85055d92f37
54658	55087	7790355906896797	C:/Users/<USER>/Music/nordic-nRF-as7058/build_1/hci_rpmsg/zephyr/merged_CPUNET.hex	bc34f85055d92f37
