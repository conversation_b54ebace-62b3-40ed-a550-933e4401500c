# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.21

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: hci_rpmsg
# Configurations: 
# =============================================================================
# =============================================================================

#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for compiling C files.

rule C_COMPILER__app_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__app_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__zephyr_pre0_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C executable.

rule C_EXECUTABLE_LINKER__zephyr_pre0_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $FLAGS $LINK_FLAGS $in -o $TARGET_FILE $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking C executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__offsets_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER__zephyr_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER__zephyr_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__zephyr_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__zephyr_final_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C executable.

rule C_EXECUTABLE_LINKER__zephyr_final_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $FLAGS $LINK_FLAGS $in -o $TARGET_FILE $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking C executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__arch__common_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__arch__common_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__isr_tables_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__isr_tables_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER__arch__arm__core__aarch32_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER__arch__arm__core__aarch32_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__arch__arm__core__aarch32_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER__arch__arm__core__aarch32__cortex_m_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER__arch__arm__core__aarch32__cortex_m_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__arch__arm__core__aarch32__cortex_m_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__arch__arm__core__aarch32__cortex_m__cmse_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__arch__arm__core__aarch32__cortex_m__cmse_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__arch__arm__core__aarch32__mpu_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__arch__arm__core__aarch32__mpu_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lib__libc__picolibc_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lib__libc__picolibc_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lib__libc__common_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lib__libc__common_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__lib__posix_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__lib__posix_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__soc__arm__common__cortex_m_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__soc__arm__common__cortex_m_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__subsys__random_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__subsys__random_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__subsys__bluetooth__common_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__subsys__bluetooth__common_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__subsys__bluetooth__host_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__subsys__bluetooth__host_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__subsys__bluetooth__controller_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__subsys__bluetooth__controller_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__subsys__net_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__subsys__net_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__interrupt_controller_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__interrupt_controller_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__clock_control_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__clock_control_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__console_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__console_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__entropy_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__entropy_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__gpio_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__gpio_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__mbox_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__mbox_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__pinctrl_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__pinctrl_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__serial_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__serial_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__drivers__timer_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__drivers__timer_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__.2e.2e__nrf__lib__fatal_error_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__.2e.2e__nrf__lib__fatal_error_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__modules__hal_nordic__nrfx_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__modules__hal_nordic__nrfx_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__metal_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__metal_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__open_amp_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__open_amp_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__kernel_
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__kernel_
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe --regenerate-during-build -SC:\ncs\v2.5.99-dev1\zephyr\samples\bluetooth\hci_rpmsg -BC:\Users\<USER>\Music\nordic-nRF-as7058\build_1\hci_rpmsg
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning additional files.

rule CLEAN_ADDITIONAL
  command = C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -DCONFIG=$CONFIG -P CMakeFiles\clean_additional.cmake
  description = Cleaning additional files...


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = C:\ncs\toolchains\cf2149caf2\opt\bin\ninja.exe $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = C:\ncs\toolchains\cf2149caf2\opt\bin\ninja.exe -t targets
  description = All primary targets available:

