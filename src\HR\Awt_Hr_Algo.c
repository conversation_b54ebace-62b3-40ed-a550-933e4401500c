
#include <zephyr/devicetree.h>
#include <zephyr/as7058/Awt_Hr_Algo.h>

void FFT_NEW(float *PPG_DATA)
{
    memset(xr_n, 0, sizeof(xr_n));
    memset(xi_n, 0, sizeof(xi_n));
    memset(fft_result_n, 0, sizeof(fft_result_n));
    int samples_count = 276;
    int i, j;
    for (i = 0; i < samples_count; i++)
    {
        xr_n[i] = 0.0;
        xi_n[i] = 0.0;
        for (j = 0; j < samples_count; j++)
        {
            xr_n[i] += PPG_DATA[j] * cos(2 * 3.14159265358979323846 * i * j / samples_count);
            xi_n[i] += PPG_DATA[j] * sin(2 * 3.14159265358979323846 * i * j / samples_count);
        }
    }
    for (int i = 0; i < samples_count; i++)
    {
        fft_result_n[i] = fabs(sqrt(xr_n[i] * xr_n[i] + xi_n[i] * xi_n[i]));
    }
}

void downsample_new(int *signal)
{
    memset(Downsampled_data_new, 0, sizeof(Downsampled_data_new));
    int factor = 200 / 40;
    for (int i = 0; i < 280; i++)
    {
        Downsampled_data_new[i] = signal[i * factor];
    }
}

void fftfreq_new(float *fft_input)
{
    float d = 0.025;
    int n = 276;
    float val = 1 / (n * d);
    int N = (n - 1) / 2 + 1;
    int i;

    for (i = 0; i < N; i++)
    {
        fft_input[i] = i;
    }
    for (i = -(n / 2); i < 0; i++)
    {
        fft_input[N++] = i;
    }
    for (i = 0; i < n; i++)
    {
        fft_input[i] *= val;
    }
}

void movingaverage_new(int *arr)
{
    int Moving_avg_window_size = 5;
    for (int i = 0; i < 276; i++)
    {
        float sum = 0;
        for (int j = i; j < i + Moving_avg_window_size; j++)
        {
            sum += arr[j];
        }
        New_movinng_average[i] = sum / Moving_avg_window_size;
    }
}
int freq_filter[276];
float filtered_freq[134];
float filtered_FFT[134];
void find_dominant_frequency(float *FFT_OUT, float *FFT_FREQ, int n, float *Dom_Freq)
{
    int count = 0;
    n = 276;
    // Assuming freq and FFT are already filled with values
    for (int i = 0; i < n; i++)
    {
        if (FFT_FREQ[i] >= 0.5)
        {
            freq_filter[count++] = i;
        }
    }


    for (int i = 0; i < count; i++)
    {
        filtered_freq[i] = FFT_FREQ[freq_filter[i]];
        filtered_FFT[i] = FFT_OUT[freq_filter[i]];
    }

    int maxIndex = 0;
    float maxFFT = fabs(filtered_FFT[0]);
    for (int i = 1; i < count; i++)
    {
        if (fabs(filtered_FFT[i]) > maxFFT)
        {
            maxIndex = i;
            maxFFT = fabs(filtered_FFT[i]);
        }
    }
    *Dom_Freq = filtered_freq[maxIndex];
}

float Calculate_Heart_rate(int PPG_ADC[])
{
    float Dominat_freq;
    downsample_new(PPG_ADC);
    movingaverage_new(Downsampled_data_new);
    FFT_NEW(New_movinng_average);
    memset(fft_backup, 0, sizeof(fft_backup));
    for (int i = 0; i < 276; i++)
    {
        fft_backup[i] = fft_result_n[i];
    }

    fftfreq_new(fft_result_n);
    find_dominant_frequency(fft_backup, fft_result_n, 276, &Dominat_freq);

    return Dominat_freq;
}
uint32_t Original_array[1400];// = {166631,166621,166624,166622,166630,166627,166626,166622,166625,166633,166633,166635,166622,166623,166626,166629,166627,166630,166624,166624,166625,166629,166617,166623,166627,166624,166627,166625,166624,166627,166628,166634,166632,166624,166627,166626,166628,166631,166624,166622,166621,166627,166623,166630,166634,166625,166628,166641,166635,166635,166629,166635,166632,166633,166636,166643,166639,166637,166639,166640,166639,166634,166641,166641,166640,166641,166646,166648,166647,166647,166650,166653,166651,166657,166654,166657,166659,166653,166655,166662,166666,166662,166661,166658,166664,166669,166673,166680,166676,166674,166675,166669,166678,166679,166680,166682,166676,166676,166678,166682,166681,166679,166677,166685,166682,166683,166688,166686,166689,166691,166688,166688,166691,166686,166688,166687,166686,166685,166683,166688,166682,166691,166682,166679,166676,166689,166683,166670,166662,166657,166656,166660,166655,166648,166653,166649,166651,166647,166635,166632,166636,166636,166632,166626,166634,166628,166626,166623,166623,166616,166613,166613,166612,166618,166620,166614,166617,166613,166614,166609,166609,166616,166605,166613,166606,166603,166603,166599,166604,166610,166609,166601,166600,166606,166603,166607,166601,166602,166603,166607,166606,166601,166601,166601,166608,166613,166610,166608,166609,166608,166610,166611,166614,166612,166609,166617,166614,166602,166605,166603,166608,166609,166612,166613,166608,166610,166607,166609,166613,166607,166609,166605,166611,166614,166611,166613,166623,166619,166617,166618,166614,166615,166615,166616,166616,166618,166615,166614,166612,166613,166616,166614,166615,166621,166620,166633,166623,166619,166623,166623,166618,166614,166624,166630,166631,166635,166636,166636,166636,166635,166631,166631,166633,166637,166637,166636,166635,166639,166640,166641,166643,166639,166633,166639,166643,166643,166648,166651,166648,166644,166649,166646,166643,166646,166647,166645,166643,166647,166655,166651,166656,166654,166660,166654,166660,166663,166663,166665,166667,166665,166671,166670,166672,166670,166664,166658,166659,166665,166666,166666,166672,166661,166663,166660,166655,166655,166644,166646,166655,166650,166650,166640,166634,166633,166634,166629,166630,166633,166632,166626,166621,166617,166624,166612,166618,166612,166610,166616,166610,166611,166608,166607,166607,166605,166603,166595,166602,166603,166599,166594,166591,166594,166595,166599,166598,166599,166611,166594,166597,166596,166594,166592,166595,166598,166597,166594,166593,166591,166593,166599,166593,166588,166592,166591,166594,166586,166588,166590,166590,166591,166585,166593,166589,166585,166594,166593,166601,166593,166597,166588,166582,166589,166596,166601,166594,166597,166592,166593,166592,166598,166596,166593,166599,166600,166602,166599,166599,166599,166594,166602,166599,166597,166607,166609,166602,166603,166606,166609,166607,166617,166612,166605,166618,166621,166615,166615,166611,166612,166615,166622,166620,166624,166627,166620,166621,166627,166626,166624,166628,166628,166632,166629,166628,166627,166630,166637,166632,166630,166626,166627,166626,166620,166631,166631,166622,166632,166637,166628,166637,166637,166641,166634,166637,166642,166643,166642,166641,166644,166648,166636,166640,166652,166650,166648,166647,166645,166644,166647,166651,166651,166650,166655,166653,166657,166660,166658,166655,166660,166661,166658,166668,166662,166657,166651,166659,166656,166649,166645,166643,166646,166644,166645,166637,166635,166637,166632,166632,166639,166629,166627,166625,166624,166615,166616,166616,166609,166613,166612,166614,166608,166609,166610,166601,166595,166603,166600,166596,166597,166600,166593,166601,166592,166593,166594,166601,166600,166605,166606,166603,166596,166596,166594,166595,166589,166598,166597,166590,166593,166591,166598,166602,166592,166595,166599,166597,166593,166594,166594,166590,166594,166596,166603,166598,166598,166597,166597,166598,166590,166590,166589,166589,166587,166590,166592,166590,166589,166591,166591,166595,166598,166598,166598,166603,166594,166596,166597,166597,166592,166591,166589,166594,166596,166598,166607,166604,166597,166598,166603,166604,166601,166598,166609,166604,166609,166604,166601,166603,166603,166604,166604,166608,166617,166620,166610,166610,166614,166608,166603,166607,166608,166610,166614,166623,166624,166620,166623,166628,166622,166624,166632,166632,166633,166632,166628,166630,166634,166634,166638,166637,166636,166640,166638,166641,166639,166636,166645,166645,166648,166644,166648,166652,166648,166652,166654,166647,166646,166650,166648,166651,166646,166652,166656,166652,166653,166654,166653,166654,166655,166660,166656,166659,166657,166653,166655,166652,166651,166652,166647,166652,166645,166640,166644,166635,166641,166632,166632,166627,166630,166621,166621,166622,166618,166618,166615,166618,166613,166609,166612,166607,166604,166603,166602,166598,166594,166602,166596,166595,166594,166588,166589,166595,166594,166598,166595,166592,166590,166600,166598,166589,166595,166594,166592,166593,166591,166593,166592,166589,166596,166593,166588,166585,166590,166587,166593,166602,166601,166592,166595,166602,166596,166592,166594,166588,166587,166588,166590,166596,166596,166592,166594,166594,166588,166591,166592,166589,166596,166590,166595,166594,166589,166587,166594,166595,166592,166584,166592,166599,166600,166603,166598,166601,166603,166608,166607,166604,166606,166608,166605,166605,166606,166609,166609,166611,166613,166613,166613,166619,166618,166620,166615,166616,166624,166624,166620,166617,166618,166614,166606,166619,166627,166621,166622,166623,166623,166622,166625,166624,166625,166633,166626,166629,166628,166630,166637,166642,166634,166632,166635,166635,166640,166633,166628,166632,166640,166648,166648,166650,166647,166637,166650,166649,166650,166655,166652,166653,166651,166654,166658,166655,166652,166660,166665,166660,166657,166658,166653,166660,166654,166657,166665,166666,166668,166668,166664,166672,166667,166669,166667,166666,166656,166665,166657,166655,166651,166648,166646,166644,166643,166643,166633,166634,166632,166625,166626,166628,166628,166623,166629,166621,166622,166615,166613,166611,166615,166616,166619,166608,166614,166611,166614,166610,166601,166601,166607,166607,166599,166605,166608,166606,166602,166600,166595,166590,166597,166603,166595,166598,166590,166587,166596,166590,166598,166596,166598,166597,166594,166597,166603,166593,166601,166592,166590,166586,166590,166589,166591,166589,166597,166591,166590,166592,166589,166587,166593,166596,166590,166591,166591,166595,166588,166598,166602,166597,166597,166594,166588,166591,166597,166588,166599,166599,166598,166597,166603,166594,166589,166596,166595,166596,166605,166607,166606,166611,166605,166598,166607,166602,166609,166600,166610,166609,166603,166608,166609,166614,166606,166611,166609,166607,166613,166608,166608,166613,166610,166608,166615,166615,166618,166621,166620,166626,166627,166628,166623,166619,166626,166630,166634,166632,166624,166633,166627,166631,166625,166633,166644,166650,166643,166644,166645,166639,166639,166640,166644,166637,166635,166641,166650,166647,166641,166643,166646,166658,166644,166652,166652,166646,166648,166644,166651,166653,166650,166655,166650,166660,166653,166655,166655,166662,166657,166652,166658,166652,166659,166655,166643,166643,166630,166641,166637,166629,166630,166626,166621,166626,166618,166619,166620,166608,166611,166611,166598,166605,166606,166598,166593,166594,166592,166593,166596,166598,166603,166588,166588,166588,166594,166597,166585,166587,166586,166580,166580,166579,166579,166583,166591,166588,166585,166584,166584,166584,166574,166579,166582,166585,166576,166581,166584,166579,166574,166578,166586,166576,166588,166587,166581,166586,166583,166581,166579,166583,166581,166584,166582,166586,166589,166588,166586,166588,166582,166588,166585,166586,166579,166583,166583,166587,166587,166591,166587,166586,166582,166589,166590,166595,166589,166590,166588,166595,166597,166606,166600,166594,166598,166598,166593,166594,166602,166599,166597,166599,166598,166597,166599,166602,166603,166601,166608,166605,166599,166604,166602,166602,166606,166600,166600,166599,166603,166603,166605,166607,166607,166606,166610,166604,166610,166612,166615,166618,166615,166616,166618,166613,166617,166616,166622,166624,166629,166625,166623,166624,166630,166633,166630,166637,166641,166635,166631,166627,166625,166630,166627,166632,166632,166637,166631,166630,166630,166633,166644,166638,166638,166636,166635,166636,166639,166637,166644,166638,166637,166645,166652,166641,166644,166637,166632,166630,166625,166622,166617,166616,166616,166612,166618,166613,166610,166616,166609,166607,166602,166604,166594,166595,166597,166592,166597,166595,166601,166595,166589,166584,166589,166589,166591,166594,166594,166594,166585,166581,166580,166583,166577,166578,166582,166574,166581,166578,166577,166580,166574,166576,166578,166577,166571,166576,166581,166583,166576,166575,166576,166573,166565,166567,166571,166577,166574,166576,166574,166572,166576,166566,166573,166572,166575,166578,166579,166576,166575,166579,166572,166570,166571,166576,166574,166575,166568,166570,166576,166571,166572,166573,166573,166571,166570,166569,166569,166572,166575,166579,166572,166577,166577,166574,166577,166582,166582,166581,166583,166578,166583,166580,166584,166582,166586,166593,166589,166595,166595,166591,166586,166594,166589,166588,166582,166591,166593,166601,166592,166595,166592,166591,166595,166593,166596,166602,166605,166605,166603,166608,166602,166608,166602,166609,166599,166601,166607,166601,166603,166602,166607,166613,166607,166611,166605,166609,166606,166610,166607,166612,166613,166624,166623,166614,166614,166619,166614,166618,166619,166623,166627,166624,166628,166633,166634,166634,166632,166632,166627,166629,166628,166624,166620,166628,166621,166624,166615,166616,166612,166600,166599};
#define SIZELENGTH 1400
int countRaw;
int sampleHr;
int HrAlgorthim(uint32_t *ppgGreen)
{
    sampleHr = 0;
    while(sampleHr<3)
    {
    for(int i=0;i<SIZELENGTH;i++)
    {
        Original_array[i]= ppgGreen[countRaw++];
    }
    //int Original_array[1400];
    float Fft_freq = 0.0;
    Fft_freq = Calculate_Heart_rate(Original_array);
    int Heart_rate = Fft_freq * 60;
    printf("Heartrate=%d\n", Heart_rate);
    senddata_over_ble(Heart_rate);// send data to ble hr
    memset(Original_array,0,sizeof(Original_array));
    sampleHr++;
    }
    return 0;
}