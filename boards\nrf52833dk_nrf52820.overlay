/*
 * Copyright (c) 2022 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

&radio {
	status = "okay";
	/* This is the number of antennas that are available on the antenna matrix
	 * designed by Nordic Semiconductor. For more information see README.rst.
	 */
	dfe-antenna-num = <12>;
	/* This is a setting that enables antenna 0 (in antenna matrix designed
	 * by Nordic Semiconductor) for Rx PDU. For more information see README.rst.
	 */
	dfe-pdu-antenna = <0x0>;

	/* These are GPIO pin numbers that are provided to
	 * Radio peripheral. The pins will be acquired by Radio to
	 * drive antenna switching when AoA is enabled.
	 * Pin numbers are selected to drive switches on antenna matrix
	 * desinged by Nordic Semiconductor. For more information see README.rst.
	 */
	dfegpio0-gpios = <&gpio0 3 0>;
	dfegpio1-gpios = <&gpio0 4 0>;
	dfegpio2-gpios = <&gpio0 28 0>;
	dfegpio3-gpios = <&gpio0 29 0>;
};
