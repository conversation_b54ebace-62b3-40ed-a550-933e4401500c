# 0 "C:/ncs/v2.5.99-dev1/zephyr/misc/empty_file.c"
# 0 "<built-in>"
# 0 "<command-line>"
# 1 "C:/ncs/v2.5.99-dev1/zephyr/boards/arm/nrf5340dk_nrf5340/nrf5340dk_nrf5340_cpunet.dts" 1






/dts-v1/;
# 1 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf5340_cpunet_qkaa.dtsi" 1 3 4






# 1 "C:/ncs/v2.5.99-dev1/zephyr/dts/common/mem.h" 1 3 4
# 8 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf5340_cpunet_qkaa.dtsi" 2 3 4
# 1 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf5340_cpunet.dtsi" 1 3 4






# 1 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/armv8-m.dtsi" 1 3 4


# 1 "C:/ncs/v2.5.99-dev1/zephyr/dts/common/skeleton.dtsi" 1 3 4
# 9 "C:/ncs/v2.5.99-dev1/zephyr/dts/common/skeleton.dtsi" 3 4
/ {
 #address-cells = <1>;
 #size-cells = <1>;
 chosen { };
 aliases { };
};
# 4 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/armv8-m.dtsi" 2 3 4

/ {
 soc {
  #address-cells = <1>;
  #size-cells = <1>;
  compatible = "simple-bus";
  interrupt-parent = <&nvic>;
  ranges;

  nvic: interrupt-controller@e000e100 {
   #address-cells = <1>;
   compatible = "arm,v8m-nvic";
   reg = <0xe000e100 0xc00>;
   interrupt-controller;
   #interrupt-cells = <2>;
  };

  systick: timer@e000e010 {
   compatible = "arm,armv8m-systick";
   reg = <0xe000e010 0x10>;
  };
 };
};
# 8 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf5340_cpunet.dtsi" 2 3 4
# 1 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf_common.dtsi" 1 3 4






# 1 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/adc/adc.h" 1 3 4
# 8 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf_common.dtsi" 2 3 4
# 1 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/adc/nrf-adc.h" 1 3 4
# 10 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/adc/nrf-adc.h" 3 4
# 1 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/dt-util.h" 1 3 4
# 19 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/dt-util.h" 3 4
# 1 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/sys/util_macro.h" 1 3 4
# 34 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/sys/util_macro.h" 3 4
# 1 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/sys/util_internal.h" 1 3 4
# 18 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/sys/util_internal.h" 3 4
# 1 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/sys/util_loops.h" 1 3 4
# 19 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/sys/util_internal.h" 2 3 4
# 35 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/sys/util_macro.h" 2 3 4
# 20 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/dt-util.h" 2 3 4
# 11 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/adc/nrf-adc.h" 2 3 4
# 9 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf_common.dtsi" 2 3 4
# 1 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/gpio/gpio.h" 1 3 4
# 10 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf_common.dtsi" 2 3 4
# 1 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/i2c/i2c.h" 1 3 4
# 11 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf_common.dtsi" 2 3 4
# 1 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/pinctrl/nrf-pinctrl.h" 1 3 4
# 12 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf_common.dtsi" 2 3 4
# 1 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/pwm/pwm.h" 1 3 4
# 13 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf_common.dtsi" 2 3 4

# 1 "C:/ncs/v2.5.99-dev1/zephyr/dts/common/freq.h" 1 3 4
# 15 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf_common.dtsi" 2 3 4
# 1 "C:/ncs/v2.5.99-dev1/zephyr/samples/bluetooth/hci_rpmsg/dts/arm/nordic/override.dtsi" 1 3 4
# 16 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf_common.dtsi" 2 3 4
# 24 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf_common.dtsi" 3 4
/ {
 pinctrl: pin-controller {




  compatible = "nordic,nrf-pinctrl";
 };

 rng_hci: entropy_bt_hci {
  compatible = "zephyr,bt-hci-entropy";
  status = "okay";
 };

 sw_pwm: sw-pwm {
  compatible = "nordic,nrf-sw-pwm";
  status = "disabled";
  generator = <&timer1>;
  clock-prescaler = <0>;
  #pwm-cells = <3>;
 };
};

&systick {




 status = "disabled";
};
# 9 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf5340_cpunet.dtsi" 2 3 4

/ {
 chosen {
  zephyr,entropy = &rng;
  zephyr,flash-controller = &flash_controller;
 };

 cpus {
  #address-cells = <1>;
  #size-cells = <0>;

  cpu@1 {
   device_type = "cpu";
   compatible = "arm,cortex-m33";
   reg = <1>;
   #address-cells = <1>;
   #size-cells = <1>;

   mpu: mpu@e000ed90 {
    compatible = "arm,armv8m-mpu";
    reg = <0xe000ed90 0x40>;
   };
  };
 };

 soc {
  ficr: ficr@1ff0000 {
   compatible = "nordic,nrf-ficr";
   reg = <0x01ff0000 0x1000>;
   status = "okay";
  };

  uicr: uicr@1ff8000 {
   compatible = "nordic,nrf-uicr";
   reg = <0x01ff8000 0x1000>;
   status = "okay";
  };

  sram0: memory@20000000 {
   compatible = "mmio-sram";
  };

  sram1: memory@21000000 {
   compatible = "zephyr,memory-region", "mmio-sram";
   zephyr,memory-region = "SRAM1";
  };

  clock: clock@41005000 {
   compatible = "nordic,nrf-clock";
   reg = <0x41005000 0x1000>;
   interrupts = <5 5>;
   status = "okay";
  };

  power: power@41005000 {
   compatible = "nordic,nrf-power";
   reg = <0x41005000 0x1000>;
   interrupts = <5 5>;
   status = "okay";
   #address-cells = <1>;
   #size-cells = <1>;

   gpregret1: gpregret1@4100551c {
    #address-cells = <1>;
    #size-cells = <1>;
    compatible = "nordic,nrf-gpregret";
    reg = <0x4100551c 0x1>;
    status = "okay";
   };

   gpregret2: gpregret2@41005520 {
    #address-cells = <1>;
    #size-cells = <1>;
    compatible = "nordic,nrf-gpregret";
    reg = <0x41005520 0x1>;
    status = "okay";
   };
  };

  radio: radio@41008000 {
   compatible = "nordic,nrf-radio";
   reg = <0x41008000 0x1000>;
   interrupts = <8 5>;
   status = "okay";
   dfe-supported;
   ieee802154-supported;
   ble-2mbps-supported;
   ble-coded-phy-supported;

   ieee802154: ieee802154 {
    compatible = "nordic,nrf-ieee802154";
    status = "disabled";
   };
  };

  rng: random@41009000 {
   compatible = "nordic,nrf-rng";
   reg = <0x41009000 0x1000>;
   interrupts = <9 5>;
   status = "okay";
  };

  gpiote: gpiote@4100a000 {
   compatible = "nordic,nrf-gpiote";
   reg = <0x4100a000 0x1000>;
   interrupts = <10 5>;
   status = "disabled";
  };

  wdt: wdt0: watchdog@4100b000 {
   compatible = "nordic,nrf-wdt";
   reg = <0x4100b000 0x1000>;
   interrupts = <11 5>;
   status = "okay";
  };

  timer0: timer@4100c000 {
   compatible = "nordic,nrf-timer";
   status = "disabled";
   reg = <0x4100c000 0x1000>;
   cc-num = <8>;
   max-bit-width = <32>;
   interrupts = <12 5>;
   prescaler = <0>;
  };

  ecb: ecb@4100d000 {
   compatible = "nordic,nrf-ecb";
   reg = <0x4100d000 0x1000>;
   interrupts = <13 5>;
   status = "okay";
  };

  ccm: ccm@4100e000 {
   compatible = "nordic,nrf-ccm";
   reg = <0x4100e000 0x1000>;
   interrupts = <14 5>;
   length-field-length-8-bits;
   headermask-supported;
   status = "okay";
  };

  dppic: dppic@4100f000 {
   compatible = "nordic,nrf-dppic";
   reg = <0x4100f000 0x1000>;
   status = "okay";
  };

  temp: temp@41010000 {
   compatible = "nordic,nrf-temp";
   reg = <0x41010000 0x1000>;
   interrupts = <16 5>;
   status = "okay";
  };

  rtc0: rtc@41011000 {
   compatible = "nordic,nrf-rtc";
   reg = <0x41011000 0x1000>;
   cc-num = <4>;
   interrupts = <17 5>;
   status = "disabled";
  };

  mbox: ipc: mbox@41012000 {
   compatible = "nordic,mbox-nrf-ipc", "nordic,nrf-ipc";
   reg = <0x41012000 0x1000>;
   tx-mask = <0x0000ffff>;
   rx-mask = <0x0000ffff>;
   interrupts = <18 5>;
   #mbox-cells = <1>;
   status = "okay";
  };

  i2c0: i2c@41013000 {






   compatible = "nordic,nrf-twim";
   #address-cells = <1>;
   #size-cells = <0>;
   reg = <0x41013000 0x1000>;
   clock-frequency = <100000>;
   interrupts = <19 5>;
   status = "disabled";
  };

  spi0: spi@41013000 {






   compatible = "nordic,nrf-spim";
   #address-cells = <1>;
   #size-cells = <0>;
   reg = <0x41013000 0x1000>;
   interrupts = <19 5>;
   max-frequency = <((8) * 1000 * 1000)>;
   easydma-maxcnt-bits = <16>;
   status = "disabled";
  };

  uart0: uart@41013000 {
   compatible = "nordic,nrf-uarte";
   reg = <0x41013000 0x1000>;
   interrupts = <19 5>;
   status = "disabled";
  };

  egu0: egu@41014000 {
   compatible = "nordic,nrf-egu";
   reg = <0x41014000 0x1000>;
   interrupts = <20 5>;
   status = "okay";
  };

  rtc1: rtc@41016000 {
   compatible = "nordic,nrf-rtc";
   reg = <0x41016000 0x1000>;
   cc-num = <4>;
   interrupts = <22 5>;
   status = "disabled";
  };

  timer1: timer@41018000 {
   compatible = "nordic,nrf-timer";
   status = "disabled";
   reg = <0x41018000 0x1000>;
   cc-num = <8>;
   max-bit-width = <32>;
   interrupts = <24 5>;
   prescaler = <0>;
  };

  timer2: timer@41019000 {
   compatible = "nordic,nrf-timer";
   status = "disabled";
   reg = <0x41019000 0x1000>;
   cc-num = <8>;
   max-bit-width = <32>;
   interrupts = <25 5>;
   prescaler = <0>;
  };

  swi0: swi@4101a000 {
   compatible = "nordic,nrf-swi";
   reg = <0x4101a000 0x1000>;
   interrupts = <26 5>;
   status = "okay";
  };

  swi1: swi@4101b000 {
   compatible = "nordic,nrf-swi";
   reg = <0x4101b000 0x1000>;
   interrupts = <27 5>;
   status = "okay";
  };

  swi2: swi@4101c000 {
   compatible = "nordic,nrf-swi";
   reg = <0x4101c000 0x1000>;
   interrupts = <28 5>;
   status = "okay";
  };

  swi3: swi@4101d000 {
   compatible = "nordic,nrf-swi";
   reg = <0x4101d000 0x1000>;
   interrupts = <29 5>;
   status = "okay";
  };

  acl: acl@41080000 {
   compatible = "nordic,nrf-acl";
   reg = <0x41080000 0x1000>;
   status = "okay";
  };

  flash_controller: flash-controller@41080000 {
   compatible = "nordic,nrf53-flash-controller";
   reg = <0x41080000 0x1000>;
   partial-erase;

   #address-cells = <1>;
   #size-cells = <1>;


   flash1: flash@1000000 {
    compatible = "soc-nv-flash";
    erase-block-size = <2048>;
    write-block-size = <4>;
   };
  };

  vmc: vmc@41081000 {
   compatible = "nordic,nrf-vmc";
   reg = <0x41081000 0x1000>;
   status = "okay";
  };

  gpio0: gpio@418c0500 {
   compatible = "nordic,nrf-gpio";
   gpio-controller;
   reg = <0x418c0500 0x300>;
   #gpio-cells = <2>;
   status = "disabled";
   port = <0>;
  };

  gpio1: gpio@418c0800 {
   compatible = "nordic,nrf-gpio";
   gpio-controller;
   reg = <0x418c0800 0x300>;
   #gpio-cells = <2>;
   ngpios = <16>;
   status = "disabled";
   port = <1>;
  };
 };


 ipc {
  ipc0: ipc0 {
   compatible = "zephyr,ipc-openamp-static-vrings";
   memory-region = <&sram0_shared>;
   mboxes = <&mbox 0>, <&mbox 1>;
   mbox-names = "rx", "tx";
   role = "remote";
   status = "okay";
  };
 };
};

&nvic {
 arm,num-irq-priority-bits = <3>;
};
# 9 "C:/ncs/v2.5.99-dev1/zephyr/dts/arm/nordic/nrf5340_cpunet_qkaa.dtsi" 2 3 4

&flash1 {
 reg = <0x01000000 ((256) * 1024)>;
};

&sram0 {
 reg = <0x20000000 ((512) * 1024)>;
};

&sram1 {
 reg = <0x21000000 ((64) * 1024)>;
};

&mpu {
 arm,num-mpu-regions = <8>;
};

/ {
 soc {
  compatible = "nordic,nrf5340-cpunet-qkaa", "nordic,nrf5340-cpunet",
        "nordic,nrf53", "simple-bus";
 };
};
# 9 "C:/ncs/v2.5.99-dev1/zephyr/boards/arm/nrf5340dk_nrf5340/nrf5340dk_nrf5340_cpunet.dts" 2
# 1 "C:/ncs/v2.5.99-dev1/zephyr/boards/arm/nrf5340dk_nrf5340/nrf5340dk_nrf5340_cpunet-pinctrl.dtsi" 1





&pinctrl {
 uart0_default: uart0_default {
  group1 {
   psels = <((((((1) * 32U) + (1)) & 0x7FU) << 0U) | ((0U & 0xFFFFU) << 16U))>,
    <((((((0) * 32U) + (11)) & 0x7FU) << 0U) | ((2U & 0xFFFFU) << 16U))>;
  };
  group2 {
   psels = <((((((1) * 32U) + (0)) & 0x7FU) << 0U) | ((1U & 0xFFFFU) << 16U))>,
    <((((((0) * 32U) + (10)) & 0x7FU) << 0U) | ((3U & 0xFFFFU) << 16U))>;
   bias-pull-up;
  };
 };

 uart0_sleep: uart0_sleep {
  group1 {
   psels = <((((((1) * 32U) + (1)) & 0x7FU) << 0U) | ((0U & 0xFFFFU) << 16U))>,
    <((((((1) * 32U) + (0)) & 0x7FU) << 0U) | ((1U & 0xFFFFU) << 16U))>,
    <((((((0) * 32U) + (11)) & 0x7FU) << 0U) | ((2U & 0xFFFFU) << 16U))>,
    <((((((0) * 32U) + (10)) & 0x7FU) << 0U) | ((3U & 0xFFFFU) << 16U))>;
   low-power-enable;
  };
 };

 i2c0_default: i2c0_default {
  group1 {
   psels = <((((((1) * 32U) + (2)) & 0x7FU) << 0U) | ((12U & 0xFFFFU) << 16U))>,
    <((((((1) * 32U) + (3)) & 0x7FU) << 0U) | ((11U & 0xFFFFU) << 16U))>;
  };
 };

 i2c0_sleep: i2c0_sleep {
  group1 {
   psels = <((((((1) * 32U) + (2)) & 0x7FU) << 0U) | ((12U & 0xFFFFU) << 16U))>,
    <((((((1) * 32U) + (3)) & 0x7FU) << 0U) | ((11U & 0xFFFFU) << 16U))>;
   low-power-enable;
  };
 };

 spi0_default: spi0_default {
  group1 {
   psels = <((((((1) * 32U) + (15)) & 0x7FU) << 0U) | ((4U & 0xFFFFU) << 16U))>,
    <((((((1) * 32U) + (14)) & 0x7FU) << 0U) | ((6U & 0xFFFFU) << 16U))>,
    <((((((1) * 32U) + (13)) & 0x7FU) << 0U) | ((5U & 0xFFFFU) << 16U))>;
  };
 };

 spi0_sleep: spi0_sleep {
  group1 {
   psels = <((((((1) * 32U) + (15)) & 0x7FU) << 0U) | ((4U & 0xFFFFU) << 16U))>,
    <((((((1) * 32U) + (14)) & 0x7FU) << 0U) | ((6U & 0xFFFFU) << 16U))>,
    <((((((1) * 32U) + (13)) & 0x7FU) << 0U) | ((5U & 0xFFFFU) << 16U))>;
   low-power-enable;
  };
 };

};
# 10 "C:/ncs/v2.5.99-dev1/zephyr/boards/arm/nrf5340dk_nrf5340/nrf5340dk_nrf5340_cpunet.dts" 2
# 1 "C:/ncs/v2.5.99-dev1/zephyr/include/zephyr/dt-bindings/input/input-event-codes.h" 1 3 4
# 11 "C:/ncs/v2.5.99-dev1/zephyr/boards/arm/nrf5340dk_nrf5340/nrf5340dk_nrf5340_cpunet.dts" 2

/ {
 model = "Nordic NRF5340 DK NRF5340 Network";
 compatible = "nordic,nrf5340-dk-nrf5340-cpunet";

 chosen {
  zephyr,console = &uart0;
  zephyr,shell-uart = &uart0;
  zephyr,uart-mcumgr = &uart0;
  zephyr,bt-mon-uart = &uart0;
  zephyr,bt-c2h-uart = &uart0;
  zephyr,bt-hci-rpmsg-ipc = &ipc0;
  nordic,802154-spinel-ipc = &ipc0;
  zephyr,sram = &sram1;
  zephyr,flash = &flash1;
  zephyr,code-partition = &slot0_partition;
  zephyr,ieee802154 = &ieee802154;
 };

 leds {
  compatible = "gpio-leds";
  led0: led_0 {
   gpios = <&gpio0 28 (1 << 0)>;
   label = "Green LED 0";
  };
  led1: led_1 {
   gpios = <&gpio0 29 (1 << 0)>;
   label = "Green LED 1";
  };
  led2: led_2 {
   gpios = <&gpio0 30 (1 << 0)>;
   label = "Green LED 2";
  };
  led3: led_3 {
   gpios = <&gpio0 31 (1 << 0)>;
   label = "Green LED 3";
  };
 };

 buttons {
  compatible = "gpio-keys";
  button0: button_0 {
   gpios = <&gpio0 23 ((1 << 4) | (1 << 0))>;
   label = "Push button 1";
   zephyr,code = <11>;
  };
  button1: button_1 {
   gpios = <&gpio0 24 ((1 << 4) | (1 << 0))>;
   label = "Push button 2";
   zephyr,code = <2>;
  };
  button2: button_2 {
   gpios = <&gpio0 8 ((1 << 4) | (1 << 0))>;
   label = "Push button 3";
   zephyr,code = <3>;
  };
  button3: button_3 {
   gpios = <&gpio0 9 ((1 << 4) | (1 << 0))>;
   label = "Push button 4";
   zephyr,code = <4>;
  };
 };

 arduino_header: connector {
  compatible = "arduino-header-r3";
  #gpio-cells = <2>;
  gpio-map-mask = <0xffffffff 0xffffffc0>;
  gpio-map-pass-thru = <0 0x3f>;
  gpio-map = <0 0 &gpio0 4 0>,
      <1 0 &gpio0 5 0>,
      <2 0 &gpio0 6 0>,
      <3 0 &gpio0 7 0>,
      <4 0 &gpio0 25 0>,
      <5 0 &gpio0 26 0>,
      <6 0 &gpio1 0 0>,
      <7 0 &gpio1 1 0>,
      <8 0 &gpio1 4 0>,
      <9 0 &gpio1 5 0>,
      <10 0 &gpio1 6 0>,
      <11 0 &gpio1 7 0>,
      <12 0 &gpio1 8 0>,
      <13 0 &gpio1 9 0>,
      <14 0 &gpio1 10 0>,
      <15 0 &gpio1 11 0>,
      <16 0 &gpio1 12 0>,
      <17 0 &gpio1 13 0>,
      <18 0 &gpio1 14 0>,
      <19 0 &gpio1 15 0>,
      <20 0 &gpio1 2 0>,
      <21 0 &gpio1 3 0>;
 };


 aliases {
  led0 = &led0;
  led1 = &led1;
  led2 = &led2;
  led3 = &led3;
  sw0 = &button0;
  sw1 = &button1;
  sw2 = &button2;
  sw3 = &button3;
  bootloader-led0 = &led0;
  mcuboot-button0 = &button0;
  mcuboot-led0 = &led0;
  watchdog0 = &wdt0;
 };
};

&gpiote {
 status = "okay";
};

&gpio0 {
 status = "okay";
};

&gpio1 {
 status = "okay";
};

&uart0 {
 status = "okay";
 current-speed = <115200>;
 pinctrl-0 = <&uart0_default>;
 pinctrl-1 = <&uart0_sleep>;
 pinctrl-names = "default", "sleep";
};

arduino_serial: &uart0{};

arduino_i2c: &i2c0 {
 compatible = "nordic,nrf-twim";


 pinctrl-0 = <&i2c0_default>;
 pinctrl-1 = <&i2c0_sleep>;
 pinctrl-names = "default", "sleep";
};

arduino_spi: &spi0 {
 compatible = "nordic,nrf-spim";


 cs-gpios = <&arduino_header 16 (1 << 0)>;
 pinctrl-0 = <&spi0_default>;
 pinctrl-1 = <&spi0_sleep>;
 pinctrl-names = "default", "sleep";
};

&flash1 {

 partitions {
  compatible = "fixed-partitions";
  #address-cells = <1>;
  #size-cells = <1>;

  boot_partition: partition@0 {
   label = "mcuboot";
   reg = <0x00000000 0xc000>;
  };
  slot0_partition: partition@c000 {
   label = "image-0";
   reg = <0x0000C000 0x17000>;
  };
  slot1_partition: partition@23000 {
   label = "image-1";
   reg = <0x00023000 0x17000>;
  };
  storage_partition: partition@3a000 {
   label = "storage";
   reg = <0x0003a000 0x6000>;
  };
 };
};

&ieee802154 {
 status = "okay";
};


# 1 "C:/ncs/v2.5.99-dev1/zephyr/boards/arm/nrf5340dk_nrf5340/nrf5340_shared_sram_planning_conf.dtsi" 1
# 14 "C:/ncs/v2.5.99-dev1/zephyr/boards/arm/nrf5340dk_nrf5340/nrf5340_shared_sram_planning_conf.dtsi"
/ {
 chosen {

  zephyr,ipc_shm = &sram0_shared;
 };

 reserved-memory {
  #address-cells = <1>;
  #size-cells = <1>;
  ranges;

  sram0_shared: memory@20070000 {

   reg = <0x20070000 0x10000>;
  };
 };
};
# 193 "C:/ncs/v2.5.99-dev1/zephyr/boards/arm/nrf5340dk_nrf5340/nrf5340dk_nrf5340_cpunet.dts" 2
# 0 "<command-line>" 2
# 1 "C:/Users/<USER>/Music/nordic-nRF-as7058/child_image/hci_rpmsg/boards/nrf5340dk_nrf5340_cpunet.overlay" 1






&radio {
 status = "okay";



 dfe-antenna-num = <12>;



 dfe-pdu-antenna = <0x0>;
# 27 "C:/Users/<USER>/Music/nordic-nRF-as7058/child_image/hci_rpmsg/boards/nrf5340dk_nrf5340_cpunet.overlay"
 dfegpio0-gpios = <&gpio0 4 0>;
 dfegpio1-gpios = <&gpio0 5 0>;
 dfegpio2-gpios = <&gpio0 6 0>;
 dfegpio3-gpios = <&gpio0 7 0>;
};
# 0 "<command-line>" 2
# 1 "C:/ncs/v2.5.99-dev1/zephyr/misc/empty_file.c"
