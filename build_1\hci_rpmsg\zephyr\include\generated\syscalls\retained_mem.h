/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_RETAINED_MEM_H
#define Z_INCLUDE_SYSCALLS_RETAINED_MEM_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern ssize_t z_impl_retained_mem_size(const struct device * dev);

__pinned_func
static inline ssize_t retained_mem_size(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (ssize_t) arch_syscall_invoke1(parm0.x, K_SYSCALL_RETAINED_MEM_SIZE);
	}
#endif
	compiler_barrier();
	return z_impl_retained_mem_size(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define retained_mem_size(dev) ({ 	ssize_t syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_RETAINED_MEM_SIZE, retained_mem_size, dev); 	syscall__retval = retained_mem_size(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_RETAINED_MEM_SIZE, retained_mem_size, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_retained_mem_read(const struct device * dev, off_t offset, uint8_t * buffer, size_t size);

__pinned_func
static inline int retained_mem_read(const struct device * dev, off_t offset, uint8_t * buffer, size_t size)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; off_t val; } parm1 = { .val = offset };
		union { uintptr_t x; uint8_t * val; } parm2 = { .val = buffer };
		union { uintptr_t x; size_t val; } parm3 = { .val = size };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_RETAINED_MEM_READ);
	}
#endif
	compiler_barrier();
	return z_impl_retained_mem_read(dev, offset, buffer, size);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define retained_mem_read(dev, offset, buffer, size) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_RETAINED_MEM_READ, retained_mem_read, dev, offset, buffer, size); 	syscall__retval = retained_mem_read(dev, offset, buffer, size); 	sys_port_trace_syscall_exit(K_SYSCALL_RETAINED_MEM_READ, retained_mem_read, dev, offset, buffer, size, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_retained_mem_write(const struct device * dev, off_t offset, const uint8_t * buffer, size_t size);

__pinned_func
static inline int retained_mem_write(const struct device * dev, off_t offset, const uint8_t * buffer, size_t size)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; off_t val; } parm1 = { .val = offset };
		union { uintptr_t x; const uint8_t * val; } parm2 = { .val = buffer };
		union { uintptr_t x; size_t val; } parm3 = { .val = size };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_RETAINED_MEM_WRITE);
	}
#endif
	compiler_barrier();
	return z_impl_retained_mem_write(dev, offset, buffer, size);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define retained_mem_write(dev, offset, buffer, size) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_RETAINED_MEM_WRITE, retained_mem_write, dev, offset, buffer, size); 	syscall__retval = retained_mem_write(dev, offset, buffer, size); 	sys_port_trace_syscall_exit(K_SYSCALL_RETAINED_MEM_WRITE, retained_mem_write, dev, offset, buffer, size, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_retained_mem_clear(const struct device * dev);

__pinned_func
static inline int retained_mem_clear(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_RETAINED_MEM_CLEAR);
	}
#endif
	compiler_barrier();
	return z_impl_retained_mem_clear(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define retained_mem_clear(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_RETAINED_MEM_CLEAR, retained_mem_clear, dev); 	syscall__retval = retained_mem_clear(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_RETAINED_MEM_CLEAR, retained_mem_clear, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
