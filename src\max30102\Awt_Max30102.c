
#include <zephyr/drivers/i2c.h>
#include<zephyr/max30102/driver_max30102.h>
#include <zephyr/max30102/driver_max30102_fifo.h>
#include<zephyr/max30102/Awt_Max30102.h>
#include <zephyr/max30102/spo2_algorithm.h>

#define BUKKA 0
#define UMESH 1

#define I2CENABLE 1
#define MAX102ENABLE 0
#define AS7058ENABLE 0
#define AS7050_HR_ENABLE 1


/********************i2C Bus declaration****************************/
#if I2CENABLE
#define I2C0_NODE DT_NODELABEL(bio_sen)
static const struct i2c_dt_spec dev_i2c1 = I2C_DT_SPEC_GET(I2C0_NODE);
#endif

extern int External_interrupt_As7058;

#if MAX102ENABLE

#define MAX30102_SLAVE_ADDRESS 0x57
static uint32_t gs_raw_red[32];            /**< raw red buffer */
static uint32_t gs_raw_ir[32];             /**< raw ir buffer */


/*********************Gpio Pin ************************************/
int gs_flag = 0;
void max30102_receive_callback(uint8_t type)
{
    uint8_t res=0;
    uint8_t len;
    /* read data */
    len = 32;
    res = max30102_fifo_read((uint32_t *)gs_raw_red, (uint32_t *)gs_raw_ir, (uint8_t *)&len);   
    fifoRawData(gs_raw_ir,gs_raw_red);
    #if UMESHBUKKA
    switch (type)
    {
        case MAX30102_INTERRUPT_STATUS_FIFO_FULL :
        {
            #if DEBUG
            uint8_t res;
            uint8_t len;
            /* read data */
            len = 32;
           // res = max30102_fifo_read((uint32_t *)gs_raw_red, (uint32_t *)gs_raw_ir, (uint8_t *)&len);
            if (res != 0)
            {
                max30102_interface_debug_print("max30102: read failed.\n");
            }
           #endif
            max30102_interface_debug_print("max30102: irq fifo full with %d.\n", len);
            
            gs_flag = 1;
            
            break;
        }
        case MAX30102_INTERRUPT_STATUS_PPG_RDY :
        {
            max30102_interface_debug_print("max30102: irq ppg rdy.\n");
            
            break;
        }
        case MAX30102_INTERRUPT_STATUS_ALC_OVF :
        {
            max30102_interface_debug_print("max30102: irq alc ovf.\n");
            
            break;
        }
        case MAX30102_INTERRUPT_STATUS_PWR_RDY :
        {
            max30102_interface_debug_print("max30102: irq pwr rdy.\n");
            
            break;
        }
        case MAX30102_INTERRUPT_STATUS_DIE_TEMP_RDY :
        {
            max30102_interface_debug_print("max30102: irq die temp rdy.\n");
            
            break;
        }
        default :
        {
            max30102_interface_debug_print("max30102: unknown code.\n");
            
            break;
        }
    }
    #endif
}

/***********************************ID Read***********************/

uint8_t retvalu = 0;
void IDread(void)
{

//uint8_t Address = 0xEC; //As7058
uint8_t Address = 0xFF; //max30102
uint8_t valu=0;
//retvalu = i2c_burst_read_dt(&dev_i2c1,Address,&valu,1);
retvalu=max30102_interface_iic_read(MAX30102_SLAVE_ADDRESS,Address,&valu,1);
 printk("valu = %d\n",valu);
if(retvalu==0)
 {
    return 0;
 }
}


/******************Interrupt status read************************/
void ClearTheInterrupt(void)
{
    uint8_t Address1=0x00;
    uint8_t Address2=0x01;
    uint8_t status1=0;
    uint8_t status2=0;
    uint8_t resu=0;
    resu = max30102_interface_iic_read(MAX30102_SLAVE_ADDRESS,Address1,&status1,1);
   // resu= max30102_interface_iic_read(MAX30102_SLAVE_ADDRESS,Address2,&status2,1);
    max30102_receive_callback(status1);
   
}

int32_t bufferLength=100; //data length
int32_t spo2; //SPO2 value
int8_t validSPO2; //indicator to show if the SPO2 calculation is valid
int32_t heartRate; //heart rate value
int8_t validHeartRate; //indicator to show if the heart rate calculation is valid

int IR_raw[100]; //100->3000 umesh
int RED_raw[100]; //100->3000 umesh
static int Rawcount = 0;

void fifoRawData(uint32_t *Ir, uint32_t *red)
{
    for(int i=0;i<=16;i++)
    {
     IR_raw[Rawcount] = Ir[i];
     RED_raw[Rawcount] = red[i];
    // printk("Ir=%d red=%d\n",IR_raw[Rawcount],RED_raw[Rawcount]);
     Rawcount++;
    for (int m = 25; m < 100; m++)
    {
      RED_raw[m - 25] = RED_raw[m];
      IR_raw[m - 25] = IR_raw[m];
    }
     if(Rawcount == 100)
     {
        Rawcount = 0;

          //After gathering 25 new samples recalculate HR and SP02
          maxim_heart_rate_and_oxygen_saturation(IR_raw, bufferLength, RED_raw, &spo2, &validSPO2, &heartRate, &validHeartRate);
          printk("spo2=%d Heart rate=%d\n",spo2,heartRate);
          memset(IR_raw,0,sizeof(IR_raw));
          memset(RED_raw,0,sizeof(RED_raw));
     }
    }
    
}

/**********************Init Fun**********************/
void Max30102_Init(void)
{
   max30102_fifo_init(max30102_receive_callback);//umesh max30102_interface_receive_callback);

}

void IDreadAMAX30102(void)
{
uint8_t retvalu = 0;

uint8_t Address = 0xFF; //max30102
uint8_t valu=0;
retvalu=max30102_interface_iic_read(MAX30102_SLAVE_ADDRESS,Address,&valu,1);
 printk("valu = %d\n",valu);
if(retvalu==0)
 {
    return 0;
 }
}
#endif



