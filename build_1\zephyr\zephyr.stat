ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0xff69
  Start of program headers:          52 (bytes into file)
  Start of section headers:          4340568 (bytes into file)
  Flags:                             0x5000200, Version5 EABI, soft-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         6
  Size of section headers:           40 (bytes)
  Number of section headers:         42
  Section header string table index: 41

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00008000 000100 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00008158 000258 01e750 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       000268a8 01e9a8 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        000268b0 01e9b0 0000e8 00   A  0   0  4
  [ 5] device_area       PROGBITS        00026998 01ea98 0000f0 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        00026a88 01eb88 000228 00  WA  0   0  4
  [ 7] bt_l2cap_fix[...] PROGBITS        00026cb0 01edb0 000024 00   A  0   0  4
  [ 8] bt_conn_cb_area   PROGBITS        00026cd4 01edd4 00001c 00   A  0   0  4
  [ 9] bt_gatt_serv[...] PROGBITS        00026cf0 01edf0 000020 00   A  0   0  4
  [10] log_const_area    PROGBITS        00026d10 01ee10 000170 00   A  0   0  4
  [11] log_backend_area  PROGBITS        00026e80 01ef80 000010 00   A  0   0  4
  [12] settings_han[...] PROGBITS        00026e90 01ef90 000078 00   A  0   0  4
  [13] tbss              NOBITS          00026f08 01f008 000008 00 WAT  0   0  4
  [14] rodata            PROGBITS        00026f10 01f010 0019a8 00   A  0   0 16
  [15] .ramfunc          PROGBITS        20008000 0213cc 000000 00   W  0   0  1
  [16] datas             PROGBITS        20008000 0209b8 000738 00  WA  0   0  8
  [17] device_states     PROGBITS        20008738 0210f0 000018 00  WA  0   0  1
  [18] log_mpsc_pbu[...] PROGBITS        20008750 021108 000040 00  WA  0   0  4
  [19] log_msg_ptr_area  PROGBITS        20008790 021148 000004 00  WA  0   0  4
  [20] k_mem_slab_area   PROGBITS        20008794 02114c 000054 00  WA  0   0  4
  [21] k_heap_area       PROGBITS        200087e8 0211a0 000014 00  WA  0   0  4
  [22] k_mutex_area      PROGBITS        200087fc 0211b4 00003c 00  WA  0   0  4
  [23] k_sem_area        PROGBITS        20008838 0211f0 000048 00  WA  0   0  4
  [24] k_queue_area      PROGBITS        20008880 021238 000030 00  WA  0   0  4
  [25] net_buf_pool_area PROGBITS        200088b0 021268 000160 00  WA  0   0  4
  [26] bss               NOBITS          20008a10 0213d0 02a4fc 00  WA  0   0  8
  [27] noinit            NOBITS          20032f10 0213d0 0056bb 00  WA  0   0  8
  [28] .comment          PROGBITS        00000000 0213cc 000065 01  MS  0   0  1
  [29] .debug_aranges    PROGBITS        00000000 021438 004c40 00      0   0  8
  [30] .debug_info       PROGBITS        00000000 026078 28427a 00      0   0  1
  [31] .debug_abbrev     PROGBITS        00000000 2aa2f2 026106 00      0   0  1
  [32] .debug_line       PROGBITS        00000000 2d03f8 06aa1d 00      0   0  1
  [33] .debug_frame      PROGBITS        00000000 33ae18 00de0c 00      0   0  4
  [34] .debug_str        PROGBITS        00000000 348c24 02666f 01  MS  0   0  1
  [35] .debug_loc        PROGBITS        00000000 36f293 079870 00      0   0  1
  [36] .debug_ranges     PROGBITS        00000000 3e8b08 010d50 00      0   0  8
  [37] .ARM.attributes   ARM_ATTRIBUTES  00000000 3f9858 00003c 00      0   0  1
  [38] .last_section     PROGBITS        000292c8 0213c8 000004 00   A  0   0  1
  [39] .symtab           SYMTAB          00000000 3f9894 0188c0 10     40 4075  4
  [40] .strtab           STRTAB          00000000 412154 0117f6 00      0   0  1
  [41] .shstrtab         STRTAB          00000000 42394a 00020e 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x01e9a8 0x000268a8 0x000268a8 0x00008 0x00008 R   0x4
  LOAD           0x000100 0x00008000 0x00008000 0x208b8 0x208b8 RWE 0x10
  LOAD           0x0209b8 0x20008000 0x000288b8 0x00a10 0x00a10 RW  0x8
  LOAD           0x0213c8 0x000292c8 0x000292c8 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x20008a10 0x20008a10 0x00000 0x2fbbb RW  0x8
  TLS            0x01f008 0x00026f08 0x00026f08 0x00000 0x00008 R   0x4

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table bt_l2cap_fixed_chan_area bt_conn_cb_area bt_gatt_service_static_area log_const_area log_backend_area settings_handler_static_area rodata 
   02     datas device_states log_mpsc_pbuf_area log_msg_ptr_area k_mem_slab_area k_heap_area k_mutex_area k_sem_area k_queue_area net_buf_pool_area 
   03     .last_section 
   04     bss noinit 
   05     tbss 
