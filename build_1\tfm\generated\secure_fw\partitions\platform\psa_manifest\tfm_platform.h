/*
 * Copyright (c) 2019-2022, Arm Limited. All rights reserved.
 *
 * SPDX-License-Identifier: BSD-3-Clause
 *
 */

/***********  WARNING: This is an auto-generated file. Do not edit!  ***********/

#ifndef __PSA_MANIFEST_TFM_PLATFORM_H__
#define __PSA_MANIFEST_TFM_PLATFORM_H__

#ifdef __cplusplus
extern "C" {
#endif

#define TFM_SP_PLATFORM_MODEL_IPC                               0
#define TFM_SP_PLATFORM_MODEL_SFN                               1

psa_status_t tfm_platform_service_sfn(const psa_msg_t* msg);


#ifdef __cplusplus
}
#endif

#endif /* __PSA_MANIFEST_TFM_PLATFORM_H__ */
