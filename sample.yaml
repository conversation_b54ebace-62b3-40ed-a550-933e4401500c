sample:
  name: Direction Finding Peripheral
  description: Sample application showing peripheral role of Direction Finding in connected mode
tests:
  sample.bluetooth.direction_finding_peripheral_nrf:
    build_only: true
    platform_allow: nrf52833dk_nrf52833 nrf52833dk_nrf52820 nrf5340dk_nrf5340_cpuapp
    tags: bluetooth
    integration_platforms:
      - nrf52833dk_nrf52833
      - nrf52833dk_nrf52820
      - nrf5340dk_nrf5340_cpuapp
  sample.bluetooth.direction_finding_peripheral_nrf.aod:
    extra_args: OVERLAY_CONFIG="overlay-aoa.conf"
    build_only: true
    platform_allow: nrf52833dk_nrf52833 nrf52833dk_nrf52820 nrf5340dk_nrf5340_cpuapp
    tags: bluetooth
    integration_platforms:
      - nrf52833dk_nrf52833
      - nrf52833dk_nrf52820
      - nrf5340dk_nrf5340_cpuapp
