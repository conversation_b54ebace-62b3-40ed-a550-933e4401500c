menu "nrf (C:/ncs/v2.5.99-dev1/nrf)"
osource "C:/ncs/v2.5.99-dev1/nrf/Kconfig.nrf"
config ZEPHYR_NRF_MODULE
	bool
	default y
endmenu
menu "wfa-qt-control-app (C:/ncs/v2.5.99-dev1/modules/lib/wfa-qt-control-app)"
osource "C:/ncs/v2.5.99-dev1/modules/lib/wfa-qt-control-app/zephyr/Kconfig"
config ZEPHYR_WFA_QT_CONTROL_APP_MODULE
	bool
	default y
endmenu
menu "mcuboot (C:/ncs/v2.5.99-dev1/bootloader/mcuboot)"
osource "$(ZEPHYR_MCUBOOT_KCONFIG)"
config ZEPHYR_MCUBOOT_MODULE
	bool
	default y
endmenu
menu "mbedtls (C:/ncs/v2.5.99-dev1/modules/crypto/mbedtls)"
osource "$(ZEPHYR_MBEDTLS_KCONFIG)"
config ZEPHYR_MBEDTLS_MODULE
	bool
	default y
endmenu
menu "trusted-firmware-m (C:/ncs/v2.5.99-dev1/modules/tee/tf-m/trusted-firmware-m)"
osource "$(ZEPHYR_TRUSTED_FIRMWARE_M_KCONFIG)"
config ZEPHYR_TRUSTED_FIRMWARE_M_MODULE
	bool
	default y
endmenu
menu "cjson (C:/ncs/v2.5.99-dev1/modules/lib/cjson)"
osource "$(ZEPHYR_CJSON_KCONFIG)"
config ZEPHYR_CJSON_MODULE
	bool
	default y
endmenu
menu "azure-sdk-for-c (C:/ncs/v2.5.99-dev1/modules/lib/azure-sdk-for-c)"
osource "$(ZEPHYR_AZURE_SDK_FOR_C_KCONFIG)"
config ZEPHYR_AZURE_SDK_FOR_C_MODULE
	bool
	default y
endmenu
menu "cirrus-logic (C:/ncs/v2.5.99-dev1/modules/hal/cirrus-logic)"
osource "C:/ncs/v2.5.99-dev1/modules/hal/cirrus-logic/Kconfig"
config ZEPHYR_CIRRUS_LOGIC_MODULE
	bool
	default y
endmenu
menu "openthread (C:/ncs/v2.5.99-dev1/modules/lib/openthread)"
osource "$(ZEPHYR_OPENTHREAD_KCONFIG)"
config ZEPHYR_OPENTHREAD_MODULE
	bool
	default y
endmenu
menu "memfault-firmware-sdk (C:/ncs/v2.5.99-dev1/modules/lib/memfault-firmware-sdk)"
osource "C:/ncs/v2.5.99-dev1/modules/lib/memfault-firmware-sdk/ports/zephyr/Kconfig"
config ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE
	bool
	default y
endmenu
menu "canopennode (C:/ncs/v2.5.99-dev1/modules/lib/canopennode)"
osource "$(ZEPHYR_CANOPENNODE_KCONFIG)"
config ZEPHYR_CANOPENNODE_MODULE
	bool
	default y
endmenu
menu "chre (C:/ncs/v2.5.99-dev1/modules/lib/chre)"
osource "C:/ncs/v2.5.99-dev1/modules/lib/chre/platform/zephyr/Kconfig"
config ZEPHYR_CHRE_MODULE
	bool
	default y
endmenu
menu "cmsis (C:/ncs/v2.5.99-dev1/modules/hal/cmsis)"
osource "$(ZEPHYR_CMSIS_KCONFIG)"
config ZEPHYR_CMSIS_MODULE
	bool
	default y
endmenu
menu "fatfs (C:/ncs/v2.5.99-dev1/modules/fs/fatfs)"
osource "$(ZEPHYR_FATFS_KCONFIG)"
config ZEPHYR_FATFS_MODULE
	bool
	default y
endmenu
menu "hal_nordic (C:/ncs/v2.5.99-dev1/modules/hal/nordic)"
osource "$(ZEPHYR_HAL_NORDIC_KCONFIG)"
config ZEPHYR_HAL_NORDIC_MODULE
	bool
	default y
endmenu
menu "liblc3 (C:/ncs/v2.5.99-dev1/modules/lib/liblc3)"
osource "$(ZEPHYR_LIBLC3_KCONFIG)"
config ZEPHYR_LIBLC3_MODULE
	bool
	default y
endmenu
menu "littlefs (C:/ncs/v2.5.99-dev1/modules/fs/littlefs)"
osource "$(ZEPHYR_LITTLEFS_KCONFIG)"
config ZEPHYR_LITTLEFS_MODULE
	bool
	default y
endmenu
menu "loramac-node (C:/ncs/v2.5.99-dev1/modules/lib/loramac-node)"
osource "$(ZEPHYR_LORAMAC_NODE_KCONFIG)"
config ZEPHYR_LORAMAC_NODE_MODULE
	bool
	default y
endmenu
menu "lvgl (C:/ncs/v2.5.99-dev1/modules/lib/gui/lvgl)"
osource "C:/ncs/v2.5.99-dev1/modules/lib/gui/lvgl/Kconfig"
config ZEPHYR_LVGL_MODULE
	bool
	default y
endmenu
menu "lz4 (C:/ncs/v2.5.99-dev1/modules/lib/lz4)"
osource "$(ZEPHYR_LZ4_KCONFIG)"
config ZEPHYR_LZ4_MODULE
	bool
	default y
endmenu
menu "nanopb (C:/ncs/v2.5.99-dev1/modules/lib/nanopb)"
osource "$(ZEPHYR_NANOPB_KCONFIG)"
config ZEPHYR_NANOPB_MODULE
	bool
	default y
endmenu
menu "picolibc (C:/ncs/v2.5.99-dev1/modules/lib/picolibc)"
osource "C:/ncs/v2.5.99-dev1/modules/lib/picolibc/zephyr/Kconfig"
config ZEPHYR_PICOLIBC_MODULE
	bool
	default y
endmenu
menu "segger (C:/ncs/v2.5.99-dev1/modules/debug/segger)"
osource "$(ZEPHYR_SEGGER_KCONFIG)"
config ZEPHYR_SEGGER_MODULE
	bool
	default y
endmenu
menu "TraceRecorder (C:/ncs/v2.5.99-dev1/modules/debug/TraceRecorder)"
osource "C:/ncs/v2.5.99-dev1/modules/debug/TraceRecorder/kernelports/Zephyr/Kconfig"
config ZEPHYR_TRACERECORDER_MODULE
	bool
	default y
endmenu
menu "uoscore-uedhoc (C:/ncs/v2.5.99-dev1/modules/lib/uoscore-uedhoc)"
osource "$(ZEPHYR_UOSCORE_UEDHOC_KCONFIG)"
config ZEPHYR_UOSCORE_UEDHOC_MODULE
	bool
	default y
endmenu
menu "zcbor (C:/ncs/v2.5.99-dev1/modules/lib/zcbor)"
osource "$(ZEPHYR_ZCBOR_KCONFIG)"
config ZEPHYR_ZCBOR_MODULE
	bool
	default y
endmenu
menu "zscilib (C:/ncs/v2.5.99-dev1/modules/lib/zscilib)"
osource "C:/ncs/v2.5.99-dev1/modules/lib/zscilib/Kconfig.zscilib"
config ZEPHYR_ZSCILIB_MODULE
	bool
	default y
endmenu
menu "nrfxlib (C:/ncs/v2.5.99-dev1/nrfxlib)"
osource "C:/ncs/v2.5.99-dev1/nrfxlib/Kconfig.nrfxlib"
config ZEPHYR_NRFXLIB_MODULE
	bool
	default y
endmenu
menu "connectedhomeip (C:/ncs/v2.5.99-dev1/modules/lib/matter)"
osource "C:/ncs/v2.5.99-dev1/modules/lib/matter/config/nrfconnect/chip-module/Kconfig"
config ZEPHYR_CONNECTEDHOMEIP_MODULE
	bool
	default y
endmenu
