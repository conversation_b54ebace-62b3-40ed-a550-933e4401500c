# WARNING. THIS FILE IS AUTO-GENERATED. DO NOT MODIFY!
#
# This file contains build system settings derived from your snippets.
# Its contents are an implementation detail that should not be used outside
# of Zephyr's snippets CMake module.
#
# See the Snippets guide in the Zephyr documentation for more information.

###############################################################################
# Global information about all snippets.

# The name of every snippet that was discovered.
set(SNIPPET_NAMES "cdc-acm-console" "nrf70-debug" "nrf91-modem-trace-uart" "tfm-enable-share-uart")
# The paths to all the snippet.yml files. One snippet
# can have multiple snippet.yml files.
set(SNIPPET_PATHS "C:\/ncs/v2.5.99-dev1/nrf/snippets/nrf70-debug/snippet.yml" "C:\/ncs/v2.5.99-dev1/nrf/snippets/nrf91-modem-trace-uart/snippet.yml" "C:\/ncs/v2.5.99-dev1/nrf/snippets/tfm-enable-share-uart/snippet.yml" "C:\/ncs/v2.5.99-dev1/zephyr/snippets/cdc-acm-console/snippet.yml")

# Create variable scope for snippets build variables
zephyr_create_scope(snippets)

