/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_NET_IF_H
#define Z_INCLUDE_SYSCALLS_NET_IF_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_net_if_ipv6_addr_lookup_by_index(const struct in6_addr * addr);

__pinned_func
static inline int net_if_ipv6_addr_lookup_by_index(const struct in6_addr * addr)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct in6_addr * val; } parm0 = { .val = addr };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_NET_IF_IPV6_ADDR_LOOKUP_BY_INDEX);
	}
#endif
	compiler_barrier();
	return z_impl_net_if_ipv6_addr_lookup_by_index(addr);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define net_if_ipv6_addr_lookup_by_index(addr) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_NET_IF_IPV6_ADDR_LOOKUP_BY_INDEX, net_if_ipv6_addr_lookup_by_index, addr); 	syscall__retval = net_if_ipv6_addr_lookup_by_index(addr); 	sys_port_trace_syscall_exit(K_SYSCALL_NET_IF_IPV6_ADDR_LOOKUP_BY_INDEX, net_if_ipv6_addr_lookup_by_index, addr, syscall__retval); 	syscall__retval; })
#endif
#endif


extern bool z_impl_net_if_ipv6_addr_add_by_index(int index, struct in6_addr * addr, enum net_addr_type addr_type, uint32_t vlifetime);

__pinned_func
static inline bool net_if_ipv6_addr_add_by_index(int index, struct in6_addr * addr, enum net_addr_type addr_type, uint32_t vlifetime)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; int val; } parm0 = { .val = index };
		union { uintptr_t x; struct in6_addr * val; } parm1 = { .val = addr };
		union { uintptr_t x; enum net_addr_type val; } parm2 = { .val = addr_type };
		union { uintptr_t x; uint32_t val; } parm3 = { .val = vlifetime };
		return (bool) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_NET_IF_IPV6_ADDR_ADD_BY_INDEX);
	}
#endif
	compiler_barrier();
	return z_impl_net_if_ipv6_addr_add_by_index(index, addr, addr_type, vlifetime);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define net_if_ipv6_addr_add_by_index(index, addr, addr_type, vlifetime) ({ 	bool syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_NET_IF_IPV6_ADDR_ADD_BY_INDEX, net_if_ipv6_addr_add_by_index, index, addr, addr_type, vlifetime); 	syscall__retval = net_if_ipv6_addr_add_by_index(index, addr, addr_type, vlifetime); 	sys_port_trace_syscall_exit(K_SYSCALL_NET_IF_IPV6_ADDR_ADD_BY_INDEX, net_if_ipv6_addr_add_by_index, index, addr, addr_type, vlifetime, syscall__retval); 	syscall__retval; })
#endif
#endif


extern bool z_impl_net_if_ipv6_addr_rm_by_index(int index, const struct in6_addr * addr);

__pinned_func
static inline bool net_if_ipv6_addr_rm_by_index(int index, const struct in6_addr * addr)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; int val; } parm0 = { .val = index };
		union { uintptr_t x; const struct in6_addr * val; } parm1 = { .val = addr };
		return (bool) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_NET_IF_IPV6_ADDR_RM_BY_INDEX);
	}
#endif
	compiler_barrier();
	return z_impl_net_if_ipv6_addr_rm_by_index(index, addr);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define net_if_ipv6_addr_rm_by_index(index, addr) ({ 	bool syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_NET_IF_IPV6_ADDR_RM_BY_INDEX, net_if_ipv6_addr_rm_by_index, index, addr); 	syscall__retval = net_if_ipv6_addr_rm_by_index(index, addr); 	sys_port_trace_syscall_exit(K_SYSCALL_NET_IF_IPV6_ADDR_RM_BY_INDEX, net_if_ipv6_addr_rm_by_index, index, addr, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_net_if_ipv4_addr_lookup_by_index(const struct in_addr * addr);

__pinned_func
static inline int net_if_ipv4_addr_lookup_by_index(const struct in_addr * addr)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct in_addr * val; } parm0 = { .val = addr };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_NET_IF_IPV4_ADDR_LOOKUP_BY_INDEX);
	}
#endif
	compiler_barrier();
	return z_impl_net_if_ipv4_addr_lookup_by_index(addr);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define net_if_ipv4_addr_lookup_by_index(addr) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_NET_IF_IPV4_ADDR_LOOKUP_BY_INDEX, net_if_ipv4_addr_lookup_by_index, addr); 	syscall__retval = net_if_ipv4_addr_lookup_by_index(addr); 	sys_port_trace_syscall_exit(K_SYSCALL_NET_IF_IPV4_ADDR_LOOKUP_BY_INDEX, net_if_ipv4_addr_lookup_by_index, addr, syscall__retval); 	syscall__retval; })
#endif
#endif


extern bool z_impl_net_if_ipv4_addr_add_by_index(int index, struct in_addr * addr, enum net_addr_type addr_type, uint32_t vlifetime);

__pinned_func
static inline bool net_if_ipv4_addr_add_by_index(int index, struct in_addr * addr, enum net_addr_type addr_type, uint32_t vlifetime)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; int val; } parm0 = { .val = index };
		union { uintptr_t x; struct in_addr * val; } parm1 = { .val = addr };
		union { uintptr_t x; enum net_addr_type val; } parm2 = { .val = addr_type };
		union { uintptr_t x; uint32_t val; } parm3 = { .val = vlifetime };
		return (bool) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_NET_IF_IPV4_ADDR_ADD_BY_INDEX);
	}
#endif
	compiler_barrier();
	return z_impl_net_if_ipv4_addr_add_by_index(index, addr, addr_type, vlifetime);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define net_if_ipv4_addr_add_by_index(index, addr, addr_type, vlifetime) ({ 	bool syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_NET_IF_IPV4_ADDR_ADD_BY_INDEX, net_if_ipv4_addr_add_by_index, index, addr, addr_type, vlifetime); 	syscall__retval = net_if_ipv4_addr_add_by_index(index, addr, addr_type, vlifetime); 	sys_port_trace_syscall_exit(K_SYSCALL_NET_IF_IPV4_ADDR_ADD_BY_INDEX, net_if_ipv4_addr_add_by_index, index, addr, addr_type, vlifetime, syscall__retval); 	syscall__retval; })
#endif
#endif


extern bool z_impl_net_if_ipv4_addr_rm_by_index(int index, const struct in_addr * addr);

__pinned_func
static inline bool net_if_ipv4_addr_rm_by_index(int index, const struct in_addr * addr)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; int val; } parm0 = { .val = index };
		union { uintptr_t x; const struct in_addr * val; } parm1 = { .val = addr };
		return (bool) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_NET_IF_IPV4_ADDR_RM_BY_INDEX);
	}
#endif
	compiler_barrier();
	return z_impl_net_if_ipv4_addr_rm_by_index(index, addr);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define net_if_ipv4_addr_rm_by_index(index, addr) ({ 	bool syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_NET_IF_IPV4_ADDR_RM_BY_INDEX, net_if_ipv4_addr_rm_by_index, index, addr); 	syscall__retval = net_if_ipv4_addr_rm_by_index(index, addr); 	sys_port_trace_syscall_exit(K_SYSCALL_NET_IF_IPV4_ADDR_RM_BY_INDEX, net_if_ipv4_addr_rm_by_index, index, addr, syscall__retval); 	syscall__retval; })
#endif
#endif


extern bool z_impl_net_if_ipv4_set_netmask_by_index(int index, const struct in_addr * netmask);

__pinned_func
static inline bool net_if_ipv4_set_netmask_by_index(int index, const struct in_addr * netmask)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; int val; } parm0 = { .val = index };
		union { uintptr_t x; const struct in_addr * val; } parm1 = { .val = netmask };
		return (bool) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_NET_IF_IPV4_SET_NETMASK_BY_INDEX);
	}
#endif
	compiler_barrier();
	return z_impl_net_if_ipv4_set_netmask_by_index(index, netmask);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define net_if_ipv4_set_netmask_by_index(index, netmask) ({ 	bool syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_NET_IF_IPV4_SET_NETMASK_BY_INDEX, net_if_ipv4_set_netmask_by_index, index, netmask); 	syscall__retval = net_if_ipv4_set_netmask_by_index(index, netmask); 	sys_port_trace_syscall_exit(K_SYSCALL_NET_IF_IPV4_SET_NETMASK_BY_INDEX, net_if_ipv4_set_netmask_by_index, index, netmask, syscall__retval); 	syscall__retval; })
#endif
#endif


extern bool z_impl_net_if_ipv4_set_gw_by_index(int index, const struct in_addr * gw);

__pinned_func
static inline bool net_if_ipv4_set_gw_by_index(int index, const struct in_addr * gw)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; int val; } parm0 = { .val = index };
		union { uintptr_t x; const struct in_addr * val; } parm1 = { .val = gw };
		return (bool) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_NET_IF_IPV4_SET_GW_BY_INDEX);
	}
#endif
	compiler_barrier();
	return z_impl_net_if_ipv4_set_gw_by_index(index, gw);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define net_if_ipv4_set_gw_by_index(index, gw) ({ 	bool syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_NET_IF_IPV4_SET_GW_BY_INDEX, net_if_ipv4_set_gw_by_index, index, gw); 	syscall__retval = net_if_ipv4_set_gw_by_index(index, gw); 	sys_port_trace_syscall_exit(K_SYSCALL_NET_IF_IPV4_SET_GW_BY_INDEX, net_if_ipv4_set_gw_by_index, index, gw, syscall__retval); 	syscall__retval; })
#endif
#endif


extern struct net_if * z_impl_net_if_get_by_index(int index);

__pinned_func
static inline struct net_if * net_if_get_by_index(int index)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; int val; } parm0 = { .val = index };
		return (struct net_if *) arch_syscall_invoke1(parm0.x, K_SYSCALL_NET_IF_GET_BY_INDEX);
	}
#endif
	compiler_barrier();
	return z_impl_net_if_get_by_index(index);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define net_if_get_by_index(index) ({ 	struct net_if * syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_NET_IF_GET_BY_INDEX, net_if_get_by_index, index); 	syscall__retval = net_if_get_by_index(index); 	sys_port_trace_syscall_exit(K_SYSCALL_NET_IF_GET_BY_INDEX, net_if_get_by_index, index, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
