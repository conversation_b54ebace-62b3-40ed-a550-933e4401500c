#include<zephyr/as7058/as7058_i2c/as7058_i2c.h>
#include <zephyr/devicetree.h>
#include <zephyr/kernel.h>

#include <nrfx_twim.h>
#include <nrfx_twis.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/device.h>

void as7058_read(const struct i2c_dt_spec *spec, uint8_t Rd_address, uint8_t Read_data)
{
    uint8_t ret=0,yt=0;
   ret= i2c_write_dt(&spec, &Rd_address, sizeof(Rd_address));
    yt=i2c_read_dt(&spec, &Read_data, sizeof(Read_data));
 //return Read_data;
}

