#define CONFIG_LV_DPI_DEF 130
#define CONFIG_ADC_INIT_PRIORITY 50
#define CONFIG_BT_HCI_ACL_FLOW_CONTROL 1
#define CONFIG_BT_HCI_VS_EXT 1
#define CONFIG_BOARD "nrf5340dk_nrf5340_cpuapp"
#define CONFIG_FLASH_LOAD_SIZE 0x30000
#define CONFIG_SRAM_SIZE 192
#define CONFIG_FLASH_LOAD_OFFSET 0x50000
#define CONFIG_MBOX_NRFX_IPC 1
#define CONFIG_HEAP_MEM_POOL_SIZE 4096
#define CONFIG_NUM_IRQS 69
#define CONFIG_SOC_SERIES "nrf53"
#define CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC 32768
#define CONFIG_SOC "nRF5340_CPUAPP_QKAA"
#define CONFIG_CLOCK_CONTROL_INIT_PRIORITY 30
#define CONFIG_FLASH_SIZE 1024
#define CONFIG_FLASH_BASE_ADDRESS 0x0
#define CONFIG_ICACHE_LINE_SIZE 32
#define CONFIG_DCACHE_LINE_SIZE 32
#define CONFIG_ROM_START_OFFSET 0x0
#define CONFIG_PINCTRL 1
#define CONFIG_CLOCK_CONTROL 1
#define CONFIG_GPIO 1
#define CONFIG_SOC_HAS_TIMING_FUNCTIONS 1
#define CONFIG_ARCH_HAS_CUSTOM_BUSY_WAIT 1
#define CONFIG_IPC_SERVICE_BACKEND_RPMSG_SHMEM_RESET 1
#define CONFIG_LOG_DOMAIN_NAME ""
#define CONFIG_NRF_RTC_TIMER 1
#define CONFIG_SYS_CLOCK_TICKS_PER_SEC 32768
#define CONFIG_BUILD_OUTPUT_HEX 1
#define CONFIG_SERIAL_INIT_PRIORITY 55
#define CONFIG_TINYCRYPT 1
#define CONFIG_SERIAL 1
#define CONFIG_MAIN_STACK_SIZE 1024
#define CONFIG_PLATFORM_SPECIFIC_INIT 1
#define CONFIG_IDLE_STACK_SIZE 320
#define CONFIG_BUILD_OUTPUT_BIN 1
#define CONFIG_MP_MAX_NUM_CPUS 1
#define CONFIG_HAS_DTS 1
#define CONFIG_DT_HAS_ARDUINO_UNO_ADC_ENABLED 1
#define CONFIG_DT_HAS_ARDUINO_HEADER_R3_ENABLED 1
#define CONFIG_DT_HAS_ARM_ARMV8M_MPU_ENABLED 1
#define CONFIG_DT_HAS_ARM_CORTEX_M33F_ENABLED 1
#define CONFIG_DT_HAS_ARM_V8M_NVIC_ENABLED 1
#define CONFIG_DT_HAS_FIXED_PARTITIONS_ENABLED 1
#define CONFIG_DT_HAS_GPIO_KEYS_ENABLED 1
#define CONFIG_DT_HAS_GPIO_LEDS_ENABLED 1
#define CONFIG_DT_HAS_MMIO_SRAM_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_MBOX_NRF_IPC_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_CLOCK_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_CTRLAPPERI_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_DCNF_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_DPPIC_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_EGU_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_GPIO_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_GPIO_FORWARDER_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_GPIOTE_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_GPREGRET_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_IEEE802154_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_IPC_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_KMU_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_MUTEX_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_NFCT_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_OSCILLATORS_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_PINCTRL_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_POWER_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_PWM_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_QSPI_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_REGULATORS_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_RESET_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_SAADC_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_SPIM_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_TWIM_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_UARTE_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_USBD_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_USBREG_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_VMC_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_WDT_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF53_FLASH_CONTROLLER_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_QSPI_NOR_ENABLED 1
#define CONFIG_DT_HAS_PWM_LEDS_ENABLED 1
#define CONFIG_DT_HAS_SOC_NV_FLASH_ENABLED 1
#define CONFIG_DT_HAS_ZEPHYR_BT_HCI_ENTROPY_ENABLED 1
#define CONFIG_DT_HAS_ZEPHYR_IPC_OPENAMP_STATIC_VRINGS_ENABLED 1
#define CONFIG_DT_HAS_ZEPHYR_PSA_CRYPTO_RNG_ENABLED 1
#define CONFIG_NUM_METAIRQ_PRIORITIES 0
#define CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE 2048
#define CONFIG_LOG_BUFFER_SIZE 1024
#define CONFIG_MBEDTLS_CIPHER_MODE_CFB 1
#define CONFIG_MBEDTLS_CIPHER_MODE_OFB 1
#define CONFIG_MBEDTLS_SHA512_C 1
#define CONFIG_MBEDTLS_MD_C 1
#define CONFIG_MBEDTLS_LEGACY_CRYPTO_C 1
#define CONFIG_TFM_MCUBOOT_IMAGE_NUMBER 1
#define CONFIG_TFM_CRYPTO_RNG_MODULE_ENABLED 1
#define CONFIG_TFM_PARTITION_CRYPTO 1
#define CONFIG_TFM_PARTITION_PLATFORM 1
#define CONFIG_BOOT_BANNER_STRING "Booting nRF Connect SDK"
#define CONFIG_WARN_EXPERIMENTAL 1
#define CONFIG_PRIVILEGED_STACK_SIZE 1024
#define CONFIG_BT_BUF_CMD_TX_COUNT 10
#define CONFIG_ENTROPY_GENERATOR 1
#define CONFIG_INIT_ARCH_HW_AT_BOOT 1
#define CONFIG_NORDIC_QSPI_NOR 1
#define CONFIG_NORDIC_QSPI_NOR_FLASH_LAYOUT_PAGE_SIZE 4096
#define CONFIG_NCS_INCLUDE_RPMSG_CHILD_IMAGE 1
#define CONFIG_NCS_SAMPLE_HCI_RPMSG_CHILD_IMAGE 1
#define CONFIG_NORDIC_QSPI_NOR_STACK_WRITE_BUFFER_SIZE 4
#define CONFIG_BT_L2CAP_TX_MTU 247
#define CONFIG_BT_BUF_ACL_TX_SIZE 251
#define CONFIG_BT_BUF_ACL_RX_SIZE 251
#define CONFIG_PM_PARTITION_SIZE_PROVISION 0x280
#define CONFIG_PM_PARTITION_SIZE_B0_IMAGE 0x8000
#define CONFIG_SB_VALIDATION_INFO_MAGIC 0x86518483
#define CONFIG_SB_VALIDATION_POINTER_MAGIC 0x6919b47e
#define CONFIG_SB_VALIDATION_INFO_CRYPTO_ID 1
#define CONFIG_SB_VALIDATION_INFO_VERSION 2
#define CONFIG_SB_VALIDATION_METADATA_OFFSET 0
#define CONFIG_SB_VALIDATE_FW_SIGNATURE 1
#define CONFIG_BT_MAX_CONN 1
#define CONFIG_BT_LL_SOFTDEVICE_HEADERS_INCLUDE 1
#define CONFIG_BT_HCI_TX_STACK_SIZE 1024
#define CONFIG_BT_RX_STACK_SIZE 2200
#define CONFIG_BT_DIS 1
#define CONFIG_BT_GATT_AUTO_SEC_REQ 1
#define CONFIG_BT_DRIVERS 1
#define CONFIG_BT_PERIPHERAL 1
#define CONFIG_BT_BROADCASTER 1
#define CONFIG_BT_CONN 1
#define CONFIG_BT_PHY_UPDATE 1
#define CONFIG_BT_DATA_LEN_UPDATE 1
#define CONFIG_BT_DIS_MODEL "nRF5340_CPUAPP_QKAA"
#define CONFIG_BT_DIS_MANUF "Manufacturer"
#define CONFIG_BT_DIS_PNP 1
#define CONFIG_BT_DIS_PNP_VID_SRC 1
#define CONFIG_BT_DIS_PNP_VID 0x0
#define CONFIG_BT_DIS_PNP_PID 0x0
#define CONFIG_BT_DIS_PNP_VER 0x1
#define CONFIG_BT_BUF_ACL_TX_COUNT 3
#define CONFIG_BT_BUF_ACL_RX_COUNT 6
#define CONFIG_BT_BUF_EVT_RX_SIZE 68
#define CONFIG_BT_BUF_EVT_RX_COUNT 10
#define CONFIG_BT_BUF_EVT_DISCARDABLE_SIZE 43
#define CONFIG_BT_BUF_EVT_DISCARDABLE_COUNT 3
#define CONFIG_BT_BUF_CMD_TX_SIZE 65
#define CONFIG_BT_HAS_HCI_VS 1
#define CONFIG_BT_HCI_VS 1
#define CONFIG_BT_RPA 1
#define CONFIG_BT_ASSERT 1
#define CONFIG_BT_ASSERT_VERBOSE 1
#define CONFIG_BT_DEBUG_NONE 1
#define CONFIG_BT_LONG_WQ 1
#define CONFIG_BT_LONG_WQ_STACK_SIZE 1300
#define CONFIG_BT_LONG_WQ_PRIO 10
#define CONFIG_BT_LONG_WQ_INIT_PRIO 50
#define CONFIG_BT_HCI_HOST 1
#define CONFIG_BT_HCI_TX_PRIO 7
#define CONFIG_BT_HCI_RESERVE 1
#define CONFIG_BT_RECV_WORKQ_BT 1
#define CONFIG_BT_RX_PRIO 8
#define CONFIG_BT_DRIVER_RX_HIGH_PRIO 6
#define CONFIG_BT_HOST_CRYPTO 1
#define CONFIG_BT_HOST_CRYPTO_PRNG 1
#define CONFIG_BT_SETTINGS 1
#define CONFIG_BT_SETTINGS_CCC_LAZY_LOADING 1
#define CONFIG_BT_SETTINGS_DELAYED_STORE 1
#define CONFIG_BT_SETTINGS_DELAYED_STORE_MS 1000
#define CONFIG_BT_SETTINGS_CCC_STORE_ON_WRITE 1
#define CONFIG_BT_SETTINGS_CF_STORE_ON_WRITE 1
#define CONFIG_BT_SETTINGS_USE_PRINTK 1
#define CONFIG_BT_LIM_ADV_TIMEOUT 30
#define CONFIG_BT_CONN_TX_USER_DATA_SIZE 8
#define CONFIG_BT_CONN_TX_MAX 3
#define CONFIG_BT_AUTO_PHY_UPDATE 1
#define CONFIG_BT_AUTO_DATA_LEN_UPDATE 1
#define CONFIG_BT_SMP 1
#define CONFIG_BT_PRIVACY 1
#define CONFIG_BT_RPA_TIMEOUT 900
#define CONFIG_BT_SIGNING 1
#define CONFIG_BT_BONDABLE 1
#define CONFIG_BT_SMP_ENFORCE_MITM 1
#define CONFIG_BT_KEYS_OVERWRITE_OLDEST 1
#define CONFIG_BT_SMP_MIN_ENC_KEY_SIZE 7
#define CONFIG_BT_L2CAP_TX_BUF_COUNT 3
#define CONFIG_BT_L2CAP_TX_FRAG_COUNT 2
#define CONFIG_BT_L2CAP_RESCHED_MS 1000
#define CONFIG_BT_ATT_ENFORCE_FLOW 1
#define CONFIG_BT_ATT_PREPARE_COUNT 1
#define CONFIG_BT_ATT_RETRY_ON_SEC_ERR 1
#define CONFIG_BT_GATT_SERVICE_CHANGED 1
#define CONFIG_BT_GATT_CACHING 1
#define CONFIG_BT_GATT_ENFORCE_SUBSCRIPTION 1
#define CONFIG_BT_GATT_READ_MULTIPLE 1
#define CONFIG_BT_GATT_READ_MULT_VAR_LEN 1
#define CONFIG_BT_GAP_AUTO_UPDATE_CONN_PARAMS 1
#define CONFIG_BT_GAP_PERIPHERAL_PREF_PARAMS 1
#define CONFIG_BT_PERIPHERAL_PREF_MIN_INT 24
#define CONFIG_BT_PERIPHERAL_PREF_MAX_INT 40
#define CONFIG_BT_PERIPHERAL_PREF_LATENCY 0
#define CONFIG_BT_PERIPHERAL_PREF_TIMEOUT 42
#define CONFIG_BT_MAX_PAIRED 1
#define CONFIG_BT_CREATE_CONN_TIMEOUT 3
#define CONFIG_BT_CONN_PARAM_UPDATE_TIMEOUT 5000
#define CONFIG_BT_CONN_PARAM_RETRY_COUNT 3
#define CONFIG_BT_CONN_PARAM_RETRY_TIMEOUT 5000
#define CONFIG_BT_DEVICE_NAME "BASE"
#define CONFIG_BT_DEVICE_APPEARANCE 833
#define CONFIG_BT_ID_MAX 1
#define CONFIG_BT_DF 1
#define CONFIG_BT_DF_CONNECTION_CTE_TX 1
#define CONFIG_BT_DF_CONNECTION_CTE_RSP 1
#define CONFIG_BT_DF_CTE_TX_AOD 1
#define CONFIG_BT_ECC 1
#define CONFIG_BT_CRYPTO 1
#define CONFIG_PCD_VERSION_PAGE_BUF_SIZE 2046
#define CONFIG_NRF_CLOUD_CLIENT_ID_SRC_COMPILE_TIME 1
#define CONFIG_NRF_CLOUD_CLIENT_ID "my-client-id"
#define CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL_DEFAULT 1
#define CONFIG_NRF_CLOUD_ALERT_LOG_LEVEL 0
#define CONFIG_NRF_CLOUD_LOG_OUTPUT_LEVEL 1
#define CONFIG_NRF_CLOUD_LOG_BUF_SIZE 256
#define CONFIG_NRF_CLOUD_LOG_LOG_LEVEL_DEFAULT 1
#define CONFIG_NRF_CLOUD_LOG_LOG_LEVEL 0
#define CONFIG_NRF_CLOUD_LOG_LEVEL_DEFAULT 1
#define CONFIG_NRF_CLOUD_LOG_LEVEL 0
#define CONFIG_MPSL_FEM_LOG_LEVEL_DEFAULT 1
#define CONFIG_MPSL_FEM_LOG_LEVEL 0
#define CONFIG_MPSL_THREAD_COOP_PRIO 8
#define CONFIG_MPSL_WORK_STACK_SIZE 1024
#define CONFIG_MPSL_TIMESLOT_SESSION_COUNT 0
#define CONFIG_MPSL_LOG_LEVEL_DEFAULT 1
#define CONFIG_MPSL_LOG_LEVEL 0
#define CONFIG_PARTITION_MANAGER_ENABLED 1
#define CONFIG_FLASH_MAP_CUSTOM 1
#define CONFIG_SRAM_BASE_ADDRESS 0x20040000
#define CONFIG_RPMSG_NRF53_SRAM_SIZE 0x10000
#define CONFIG_PM_PARTITION_SIZE_SETTINGS_STORAGE 0x2000
#define CONFIG_PM_PARTITION_ALIGN_SETTINGS_STORAGE 0x4000
#define CONFIG_PM_SINGLE_IMAGE 1
#define CONFIG_PM_EXTERNAL_FLASH_HAS_DRIVER 1
#define CONFIG_PM_EXTERNAL_FLASH_BASE 0x0
#define CONFIG_PM_EXTERNAL_FLASH_PATH ""
#define CONFIG_PM_EXTERNAL_FLASH_SIZE_BITS 0
#define CONFIG_PM_SRAM_BASE 0x20000000
#define CONFIG_PM_SRAM_SIZE 0x80000
#define CONFIG_MGMT_FMFU_LOG_LEVEL_DEFAULT 1
#define CONFIG_MGMT_FMFU_LOG_LEVEL 0
#define CONFIG_NORDIC_SECURITY_BACKEND 1
#define CONFIG_NRF_SECURITY 1
#define CONFIG_MBEDTLS_CFG_FILE "nrf-config.h"
#define CONFIG_MBEDTLS_USER_CONFIG_FILE "nrf-config-user.h"
#define CONFIG_GENERATE_MBEDTLS_CFG_FILE 1
#define CONFIG_MBEDTLS_PSA_CRYPTO_C 1
#define CONFIG_PSA_WANT_GENERATE_RANDOM 1
#define CONFIG_PSA_WANT_ALG_CTR_DRBG 1
#define CONFIG_MBEDTLS_PSA_CRYPTO_EXTERNAL_RNG 1
#define CONFIG_PSA_CORE_OBERON 1
#define CONFIG_MBEDTLS_PSA_CRYPTO_DRIVERS 1
#define CONFIG_PSA_DEFAULT_OFF 1
#define CONFIG_MBEDTLS_PSA_CRYPTO_CLIENT 1
#define CONFIG_PSA_CRYPTO_DRIVER_OBERON 1
#define CONFIG_PSA_USE_CTR_DRBG_DRIVER 1
#define CONFIG_PSA_USE_CC3XX_CTR_DRBG_DRIVER 1
#define CONFIG_PSA_WANT_AES_KEY_SIZE_128 1
#define CONFIG_PSA_WANT_AES_KEY_SIZE_192 1
#define CONFIG_PSA_WANT_AES_KEY_SIZE_256 1
#define CONFIG_PSA_WANT_RSA_KEY_SIZE_1024 1
#define CONFIG_PSA_WANT_RSA_KEY_SIZE_2048 1
#define CONFIG_PSA_WANT_RSA_KEY_SIZE_3072 1
#define CONFIG_PSA_MAX_RSA_KEY_BITS 3072
#define CONFIG_PSA_ACCEL_GENERATE_RANDOM 1
#define CONFIG_PSA_NEED_CC3XX_CTR_DRBG_DRIVER 1
#define CONFIG_MBEDTLS_USE_PSA_CRYPTO 1
#define CONFIG_MBEDTLS_PSA_KEY_SLOT_COUNT 32
#define CONFIG_MBEDTLS_PLATFORM_MEMORY 1
#define CONFIG_MBEDTLS_PLATFORM_C 1
#define CONFIG_MBEDTLS_MEMORY_C 1
#define CONFIG_MBEDTLS_MEMORY_BUFFER_ALLOC_C 1
#define CONFIG_MBEDTLS_ENTROPY_HARDWARE_ALT 1
#define CONFIG_MBEDTLS_AES_SETKEY_ENC_ALT 1
#define CONFIG_MBEDTLS_AES_SETKEY_DEC_ALT 1
#define CONFIG_MBEDTLS_AES_ENCRYPT_ALT 1
#define CONFIG_MBEDTLS_AES_DECRYPT_ALT 1
#define CONFIG_MBEDTLS_CHACHA20_ALT 1
#define CONFIG_MBEDTLS_POLY1305_ALT 1
#define CONFIG_MBEDTLS_ECDH_GEN_PUBLIC_ALT 1
#define CONFIG_MBEDTLS_ECDH_COMPUTE_SHARED_ALT 1
#define CONFIG_MBEDTLS_ECDSA_GENKEY_ALT 1
#define CONFIG_MBEDTLS_ECDSA_SIGN_ALT 1
#define CONFIG_MBEDTLS_ECDSA_VERIFY_ALT 1
#define CONFIG_MBEDTLS_ECJPAKE_ALT 1
#define CONFIG_MBEDTLS_SHA1_ALT 1
#define CONFIG_MBEDTLS_SHA224_ALT 1
#define CONFIG_MBEDTLS_SHA256_ALT 1
#define CONFIG_MBEDTLS_ENTROPY_FORCE_SHA256 1
#define CONFIG_MBEDTLS_ENTROPY_MAX_SOURCES 1
#define CONFIG_MBEDTLS_NO_PLATFORM_ENTROPY 1
#define CONFIG_OBERON_ONLY_PSA_ENABLED 1
#define CONFIG_OBERON_ONLY_ENABLED 1
#define CONFIG_MBEDTLS_MPI_WINDOW_SIZE 6
#define CONFIG_MBEDTLS_MPI_MAX_SIZE 256
#define CONFIG_OBERON_BACKEND 1
#define CONFIG_MBEDTLS_HMAC_DRBG_C 1
#define CONFIG_MBEDTLS_AES_C 1
#define CONFIG_MBEDTLS_HKDF_C 1
#define CONFIG_MBEDTLS_SHA224_C 1
#define CONFIG_MBEDTLS_SHA256_C 1
#define CONFIG_MBEDTLS_SHA384_C 1
#define CONFIG_MBEDTLS_CIPHER_C 1
#define CONFIG_MBEDTLS_PKCS5_C 1
#define CONFIG_AUDIO_MODULE_NAME_SIZE 20
#define CONFIG_AUDIO_MODULE_LOG_LEVEL_DEFAULT 1
#define CONFIG_AUDIO_MODULE_LOG_LEVEL 0
#define CONFIG_TFM_BOARD "C:/ncs/v2.5.99-dev1/nrf/modules/tfm/tfm/boards/nrf5340_cpuapp"
#define CONFIG_TFM_PLATFORM_SERVICE_INPUT_BUFFER_SIZE 64
#define CONFIG_TFM_PLATFORM_SERVICE_OUTPUT_BUFFER_SIZE 64
#define CONFIG_TFM_PLATFORM_SP_STACK_SIZE 0x500
#define CONFIG_TFM_PLATFORM_NV_COUNTER_MODULE_DISABLED 1
#define CONFIG_TFM_CONN_HANDLE_MAX_NUM 8
#define CONFIG_POSIX_MAX_FDS 4
#define CONFIG_BLE_HCI_VSC_LOG_LEVEL_DEFAULT 1
#define CONFIG_BLE_HCI_VSC_LOG_LEVEL 0
#define CONFIG_NRF_SPU_FLASH_REGION_SIZE 0x4000
#define CONFIG_FPROTECT_BLOCK_SIZE 0x4000
#define CONFIG_RESET_ON_FATAL_ERROR 1
#define CONFIG_FATAL_ERROR_LOG_LEVEL_DEFAULT 1
#define CONFIG_FATAL_ERROR_LOG_LEVEL 0
#define CONFIG_HW_UNIQUE_KEY_PARTITION_SIZE 0x0
#define CONFIG_SENSOR 1
#define CONFIG_NRFX_GPIOTE_NUM_OF_EVT_HANDLERS 1
#define CONFIG_ZTEST_MULTICORE_DEFAULT_SETTINGS 1
#define CONFIG_ZEPHYR_NRF_MODULE 1
#define CONFIG_WFA_QT_LOG_LEVEL_DEFAULT 1
#define CONFIG_WFA_QT_LOG_LEVEL 0
#define CONFIG_WFA_QT_THREAD_STACK_SIZE 4096
#define CONFIG_ZEPHYR_WFA_QT_CONTROL_APP_MODULE 1
#define CONFIG_BOOT_SIGNATURE_KEY_FILE ""
#define CONFIG_DT_FLASH_WRITE_BLOCK_SIZE 4
#define CONFIG_MCUBOOT_USB_SUPPORT 1
#define CONFIG_ZEPHYR_MCUBOOT_MODULE 1
#define CONFIG_ZEPHYR_MBEDTLS_MODULE 1
#define CONFIG_TFM_ISOLATION_LEVEL 1
#define CONFIG_TFM_SECURE_UART 1
#define CONFIG_TFM_SECURE_UART1 1
#define CONFIG_NRF_UARTE1_SECURE 1
#define CONFIG_NRF_GPIO0_PIN_MASK_SECURE 0x00000000
#define CONFIG_NRF_GPIO1_PIN_MASK_SECURE 0x00000000
#define CONFIG_NRF_DPPI_CHANNEL_MASK_SECURE 0x00000000
#define CONFIG_TFM_PROFILE_TYPE_MINIMAL 1
#define CONFIG_TFM_QCBOR_PATH "C:/ncs/v2.5.99-dev1/modules/tee/tf-m/trusted-firmware-m/../qcbor"
#define CONFIG_TFM_CRYPTO_ENGINE_BUF_SIZE 1
#define CONFIG_TFM_CRYPTO_CONC_OPER_NUM 1
#define CONFIG_TFM_CRYPTO_IOVEC_BUFFER_SIZE 1024
#define CONFIG_TFM_CRYPTO_PARTITION_STACK_SIZE 0x800
#define CONFIG_TFM_ALLOW_NON_SECURE_RESET 1
#define CONFIG_TFM_S_CODE_VECTOR_TABLE_SIZE 0x154
#define CONFIG_ZEPHYR_TRUSTED_FIRMWARE_M_MODULE 1
#define CONFIG_BUILD_WITH_TFM 1
#define CONFIG_TFM_KEY_FILE_S "C:/ncs/v2.5.99-dev1/modules/tee/tf-m/trusted-firmware-m/bl2/ext/mcuboot/root-RSA-3072.pem"
#define CONFIG_TFM_KEY_FILE_NS "C:/ncs/v2.5.99-dev1/modules/tee/tf-m/trusted-firmware-m/bl2/ext/mcuboot/root-RSA-3072_1.pem"
#define CONFIG_TFM_CMAKE_BUILD_TYPE_MINSIZEREL 1
#define CONFIG_TFM_PARTITION_PLATFORM_CUSTOM_REBOOT 1
#define CONFIG_TFM_IMAGE_VERSION_S "0.0.0+0"
#define CONFIG_TFM_IMAGE_VERSION_NS "0.0.0+0"
#define CONFIG_TFM_IPC 1
#define CONFIG_TFM_PSA_TEST_NONE 1
#define CONFIG_TFM_FLASH_MERGED_BINARY 1
#define CONFIG_TFM_SPM_LOG_LEVEL_INFO 1
#define CONFIG_TFM_EXCEPTION_INFO_DUMP 1
#define CONFIG_PM_PARTITION_SIZE_TFM_SRAM 0x8000
#define CONFIG_PM_PARTITION_SIZE_BL2 0x0
#define CONFIG_PM_PARTITION_SIZE_TFM 0x8000
#define CONFIG_PM_PARTITION_SIZE_TFM_PROTECTED_STORAGE 0x0
#define CONFIG_PM_PARTITION_SIZE_TFM_INTERNAL_TRUSTED_STORAGE 0x0
#define CONFIG_PM_PARTITION_SIZE_TFM_OTP_NV_COUNTERS 0x0
#define CONFIG_ZEPHYR_CJSON_MODULE 1
#define CONFIG_ZEPHYR_AZURE_SDK_FOR_C_MODULE 1
#define CONFIG_ZEPHYR_CIRRUS_LOGIC_MODULE 1
#define CONFIG_ZEPHYR_OPENTHREAD_MODULE 1
#define CONFIG_ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE 1
#define CONFIG_ZEPHYR_CANOPENNODE_MODULE 1
#define CONFIG_ZEPHYR_CHRE_MODULE 1
#define CONFIG_HAS_CMSIS_CORE 1
#define CONFIG_HAS_CMSIS_CORE_M 1
#define CONFIG_ZEPHYR_CMSIS_MODULE 1
#define CONFIG_ZEPHYR_FATFS_MODULE 1
#define CONFIG_ZEPHYR_HAL_NORDIC_MODULE 1
#define CONFIG_HAS_NORDIC_DRIVERS 1
#define CONFIG_HAS_NRFX 1
#define CONFIG_NRFX_CLOCK 1
#define CONFIG_NRFX_CLOCK_LFXO_TWO_STAGE_ENABLED 1
#define CONFIG_NRFX_DPPI 1
#define CONFIG_NRFX_GPIOTE 1
#define CONFIG_NRFX_IPC 1
#define CONFIG_NRFX_NVMC 1
#define CONFIG_NRFX_QSPI 1
#define CONFIG_NRFX_TWIM 1
#define CONFIG_NRFX_TWIM2 1
#define CONFIG_ZEPHYR_LIBLC3_MODULE 1
#define CONFIG_ZEPHYR_LITTLEFS_MODULE 1
#define CONFIG_ZEPHYR_LORAMAC_NODE_MODULE 1
#define CONFIG_LV_COLOR_DEPTH_16 1
#define CONFIG_LV_COLOR_DEPTH 16
#define CONFIG_LV_COLOR_MIX_ROUND_OFS 128
#define CONFIG_LV_COLOR_CHROMA_KEY_HEX 0x00FF00
#define CONFIG_LV_MEM_SIZE_KILOBYTES 32
#define CONFIG_LV_MEM_ADDR 0x0
#define CONFIG_LV_MEM_BUF_MAX_NUM 16
#define CONFIG_LV_DISP_DEF_REFR_PERIOD 30
#define CONFIG_LV_INDEV_DEF_READ_PERIOD 30
#define CONFIG_LV_DRAW_COMPLEX 1
#define CONFIG_LV_SHADOW_CACHE_SIZE 0
#define CONFIG_LV_CIRCLE_CACHE_SIZE 4
#define CONFIG_LV_LAYER_SIMPLE_BUF_SIZE 24576
#define CONFIG_LV_IMG_CACHE_DEF_SIZE 0
#define CONFIG_LV_GRADIENT_MAX_STOPS 2
#define CONFIG_LV_GRAD_CACHE_DEF_SIZE 0
#define CONFIG_LV_DISP_ROT_MAX_BUF 10240
#define CONFIG_LV_USE_ASSERT_NULL 1
#define CONFIG_LV_USE_ASSERT_MALLOC 1
#define CONFIG_LV_ASSERT_HANDLER_INCLUDE "assert.h"
#define CONFIG_LV_USE_USER_DATA 1
#define CONFIG_LV_ATTRIBUTE_MEM_ALIGN_SIZE 1
#define CONFIG_LV_FONT_MONTSERRAT_14 1
#define CONFIG_LV_FONT_DEFAULT_MONTSERRAT_14 1
#define CONFIG_LV_USE_FONT_PLACEHOLDER 1
#define CONFIG_LV_TXT_ENC_UTF8 1
#define CONFIG_LV_TXT_BREAK_CHARS " ,.;:-_"
#define CONFIG_LV_TXT_LINE_BREAK_LONG_LEN 0
#define CONFIG_LV_TXT_COLOR_CMD "#"
#define CONFIG_LV_USE_ARC 1
#define CONFIG_LV_USE_BAR 1
#define CONFIG_LV_USE_BTN 1
#define CONFIG_LV_USE_BTNMATRIX 1
#define CONFIG_LV_USE_CANVAS 1
#define CONFIG_LV_USE_CHECKBOX 1
#define CONFIG_LV_USE_DROPDOWN 1
#define CONFIG_LV_USE_IMG 1
#define CONFIG_LV_USE_LABEL 1
#define CONFIG_LV_LABEL_TEXT_SELECTION 1
#define CONFIG_LV_LABEL_LONG_TXT_HINT 1
#define CONFIG_LV_USE_LINE 1
#define CONFIG_LV_USE_ROLLER 1
#define CONFIG_LV_ROLLER_INF_PAGES 7
#define CONFIG_LV_USE_SLIDER 1
#define CONFIG_LV_USE_SWITCH 1
#define CONFIG_LV_USE_TEXTAREA 1
#define CONFIG_LV_TEXTAREA_DEF_PWD_SHOW_TIME 1500
#define CONFIG_LV_USE_TABLE 1
#define CONFIG_LV_USE_ANIMIMG 1
#define CONFIG_LV_USE_CALENDAR 1
#define CONFIG_LV_USE_CALENDAR_HEADER_ARROW 1
#define CONFIG_LV_USE_CALENDAR_HEADER_DROPDOWN 1
#define CONFIG_LV_USE_CHART 1
#define CONFIG_LV_USE_COLORWHEEL 1
#define CONFIG_LV_USE_IMGBTN 1
#define CONFIG_LV_USE_KEYBOARD 1
#define CONFIG_LV_USE_LED 1
#define CONFIG_LV_USE_LIST 1
#define CONFIG_LV_USE_MENU 1
#define CONFIG_LV_USE_METER 1
#define CONFIG_LV_USE_MSGBOX 1
#define CONFIG_LV_USE_SPAN 1
#define CONFIG_LV_SPAN_SNIPPET_STACK_SIZE 64
#define CONFIG_LV_USE_SPINBOX 1
#define CONFIG_LV_USE_SPINNER 1
#define CONFIG_LV_USE_TABVIEW 1
#define CONFIG_LV_USE_TILEVIEW 1
#define CONFIG_LV_USE_WIN 1
#define CONFIG_LV_USE_THEME_DEFAULT 1
#define CONFIG_LV_THEME_DEFAULT_GROW 1
#define CONFIG_LV_THEME_DEFAULT_TRANSITION_TIME 80
#define CONFIG_LV_USE_THEME_BASIC 1
#define CONFIG_LV_USE_FLEX 1
#define CONFIG_LV_USE_GRID 1
#define CONFIG_LV_USE_SNAPSHOT 1
#define CONFIG_LV_BUILD_EXAMPLES 1
#define CONFIG_ZEPHYR_LVGL_MODULE 1
#define CONFIG_ZEPHYR_LZ4_MODULE 1
#define CONFIG_ZEPHYR_NANOPB_MODULE 1
#define CONFIG_ZEPHYR_PICOLIBC_MODULE 1
#define CONFIG_ZEPHYR_SEGGER_MODULE 1
#define CONFIG_HAS_SEGGER_RTT 1
#define CONFIG_USE_SEGGER_RTT 1
#define CONFIG_SEGGER_RTT_CUSTOM_LOCKING 1
#define CONFIG_SEGGER_RTT_MAX_NUM_UP_BUFFERS 3
#define CONFIG_SEGGER_RTT_MAX_NUM_DOWN_BUFFERS 3
#define CONFIG_SEGGER_RTT_BUFFER_SIZE_UP 1024
#define CONFIG_SEGGER_RTT_BUFFER_SIZE_DOWN 16
#define CONFIG_SEGGER_RTT_PRINTF_BUFFER_SIZE 64
#define CONFIG_SEGGER_RTT_MODE_NO_BLOCK_SKIP 1
#define CONFIG_SEGGER_RTT_MODE 0
#define CONFIG_SEGGER_RTT_SECTION_NONE 1
#define CONFIG_ZEPHYR_TRACERECORDER_MODULE 1
#define CONFIG_ZEPHYR_UOSCORE_UEDHOC_MODULE 1
#define CONFIG_ZEPHYR_ZCBOR_MODULE 1
#define CONFIG_ZEPHYR_ZSCILIB_MODULE 1
#define CONFIG_NRF_MODEM_SHMEM_CTRL_SIZE 0x4e8
#define CONFIG_HAS_HW_NRF_CC3XX 1
#define CONFIG_NRF_OBERON 1
#define CONFIG_NRF_802154_SOURCE_NRFXLIB 1
#define CONFIG_LC3_ENC_CHAN_MAX 1
#define CONFIG_LC3_DEC_CHAN_MAX 1
#define CONFIG_LC3_ENC_SAMPLE_RATE_8KHZ_SUPPORT 1
#define CONFIG_LC3_ENC_SAMPLE_RATE_16KHZ_SUPPORT 1
#define CONFIG_LC3_ENC_SAMPLE_RATE_24KHZ_SUPPORT 1
#define CONFIG_LC3_ENC_SAMPLE_RATE_32KHZ_SUPPORT 1
#define CONFIG_LC3_ENC_SAMPLE_RATE_441KHZ_SUPPORT 1
#define CONFIG_LC3_ENC_SAMPLE_RATE_48KHZ_SUPPORT 1
#define CONFIG_LC3_DEC_SAMPLE_RATE_8KHZ_SUPPORT 1
#define CONFIG_LC3_DEC_SAMPLE_RATE_16KHZ_SUPPORT 1
#define CONFIG_LC3_DEC_SAMPLE_RATE_24KHZ_SUPPORT 1
#define CONFIG_LC3_DEC_SAMPLE_RATE_32KHZ_SUPPORT 1
#define CONFIG_LC3_DEC_SAMPLE_RATE_441KHZ_SUPPORT 1
#define CONFIG_LC3_DEC_SAMPLE_RATE_48KHZ_SUPPORT 1
#define CONFIG_ZEPHYR_NRFXLIB_MODULE 1
#define CONFIG_ZEPHYR_CONNECTEDHOMEIP_MODULE 1
#define CONFIG_LIBMETAL 1
#define CONFIG_LIBMETAL_SRC_PATH "libmetal"
#define CONFIG_OPENAMP 1
#define CONFIG_OPENAMP_SRC_PATH "open-amp"
#define CONFIG_OPENAMP_MASTER 1
#define CONFIG_OPENAMP_SLAVE 1
#define CONFIG_TINYCRYPT_SHA256 1
#define CONFIG_TINYCRYPT_SHA256_HMAC 1
#define CONFIG_TINYCRYPT_SHA256_HMAC_PRNG 1
#define CONFIG_TINYCRYPT_AES 1
#define CONFIG_TINYCRYPT_AES_CMAC 1
#define CONFIG_BOARD_REVISION "$BOARD_REVISION"
#define CONFIG_BOARD_NRF5340DK_NRF5340_CPUAPP_NS 1
#define CONFIG_BOARD_ENABLE_DCDC_APP 1
#define CONFIG_BOARD_ENABLE_DCDC_NET 1
#define CONFIG_BOARD_ENABLE_DCDC_HV 1
#define CONFIG_BOARD_ENABLE_CPUNET 1
#define CONFIG_DOMAIN_CPUNET_BOARD "nrf5340dk_nrf5340_cpunet"
#define CONFIG_SOC_SERIES_NRF53X 1
#define CONFIG_CPU_HAS_ARM_MPU 1
#define CONFIG_CPU_HAS_NRF_IDAU 1
#define CONFIG_NRF_SPU_RAM_REGION_SIZE 0x2000
#define CONFIG_HAS_SWO 1
#define CONFIG_SOC_FAMILY "nordic_nrf"
#define CONFIG_GPIO_INIT_PRIORITY 40
#define CONFIG_SOC_FAMILY_NRF 1
#define CONFIG_HAS_HW_NRF_CC312 1
#define CONFIG_HAS_HW_NRF_CLOCK 1
#define CONFIG_HAS_HW_NRF_CTRLAP 1
#define CONFIG_HAS_HW_NRF_DCNF 1
#define CONFIG_HAS_HW_NRF_DPPIC 1
#define CONFIG_HAS_HW_NRF_EGU0 1
#define CONFIG_HAS_HW_NRF_EGU1 1
#define CONFIG_HAS_HW_NRF_EGU2 1
#define CONFIG_HAS_HW_NRF_EGU3 1
#define CONFIG_HAS_HW_NRF_EGU4 1
#define CONFIG_HAS_HW_NRF_EGU5 1
#define CONFIG_HAS_HW_NRF_GPIO0 1
#define CONFIG_HAS_HW_NRF_GPIO1 1
#define CONFIG_HAS_HW_NRF_GPIOTE 1
#define CONFIG_HAS_HW_NRF_IPC 1
#define CONFIG_HAS_HW_NRF_KMU 1
#define CONFIG_HAS_HW_NRF_MUTEX 1
#define CONFIG_HAS_HW_NRF_NFCT 1
#define CONFIG_HAS_HW_NRF_NVMC_PE 1
#define CONFIG_HAS_HW_NRF_OSCILLATORS 1
#define CONFIG_HAS_HW_NRF_POWER 1
#define CONFIG_HAS_HW_NRF_PWM0 1
#define CONFIG_HAS_HW_NRF_QSPI 1
#define CONFIG_HAS_HW_NRF_REGULATORS 1
#define CONFIG_HAS_HW_NRF_RESET 1
#define CONFIG_HAS_HW_NRF_SAADC 1
#define CONFIG_HAS_HW_NRF_SPIM4 1
#define CONFIG_HAS_HW_NRF_TWIM2 1
#define CONFIG_HAS_HW_NRF_UARTE0 1
#define CONFIG_HAS_HW_NRF_USBD 1
#define CONFIG_HAS_HW_NRF_USBREG 1
#define CONFIG_HAS_HW_NRF_VMC 1
#define CONFIG_HAS_HW_NRF_WDT0 1
#define CONFIG_SOC_NRF5340_CPUAPP 1
#define CONFIG_SOC_NRF5340_CPUAPP_QKAA 1
#define CONFIG_SOC_NRF53_ANOMALY_160_WORKAROUND_NEEDED 1
#define CONFIG_SOC_NRF53_ANOMALY_160_WORKAROUND 1
#define CONFIG_SOC_NRF53_RTC_PRETICK 1
#define CONFIG_SOC_NRF53_RTC_PRETICK_IPC_CH_FROM_NET 10
#define CONFIG_SOC_NRF53_RTC_PRETICK_IPC_CH_TO_NET 11
#define CONFIG_SOC_DCDC_NRF53X_APP 1
#define CONFIG_SOC_DCDC_NRF53X_NET 1
#define CONFIG_SOC_DCDC_NRF53X_HV 1
#define CONFIG_SOC_NRF_GPIO_FORWARDER_FOR_NRF5340 1
#define CONFIG_SOC_ENABLE_LFXO 1
#define CONFIG_SOC_LFXO_CAP_INT_7PF 1
#define CONFIG_SOC_HFXO_CAP_DEFAULT 1
#define CONFIG_NRF_ENABLE_CACHE 1
#define CONFIG_NRF53_SYNC_RTC 1
#define CONFIG_SYNC_RTC_LOG_LEVEL_DEFAULT 1
#define CONFIG_SYNC_RTC_LOG_LEVEL 0
#define CONFIG_NRF53_SYNC_RTC_INIT_PRIORITY 90
#define CONFIG_NRF_RTC_TIMER_USER_CHAN_COUNT 1
#define CONFIG_NRF53_SYNC_RTC_LOG_TIMESTAMP 1
#define CONFIG_NRF53_SYNC_RTC_IPM_OUT 7
#define CONFIG_NRF53_SYNC_RTC_IPM_IN 8
#define CONFIG_IPM_MSG_CH_8_ENABLE 1
#define CONFIG_IPM_MSG_CH_8_RX 1
#define CONFIG_NRF_SOC_SECURE_SUPPORTED 1
#define CONFIG_SOC_LOG_LEVEL_DEFAULT 1
#define CONFIG_SOC_LOG_LEVEL 0
#define CONFIG_SOC_COMPATIBLE_NRF 1
#define CONFIG_SOC_COMPATIBLE_NRF53X 1
#define CONFIG_SOC_COMPATIBLE_NRF5340_CPUAPP 1
#define CONFIG_ARCH "arm"
#define CONFIG_CPU_CORTEX 1
#define CONFIG_ARM_ON_ENTER_CPU_IDLE_HOOK 1
#define CONFIG_CPU_CORTEX_M 1
#define CONFIG_ISA_THUMB2 1
#define CONFIG_ASSEMBLER_ISA_THUMB2 1
#define CONFIG_COMPILER_ISA_THUMB2 1
#define CONFIG_STACK_ALIGN_DOUBLE_WORD 1
#define CONFIG_FAULT_DUMP 2
#define CONFIG_BUILTIN_STACK_GUARD 1
#define CONFIG_ARM_STACK_PROTECTION 1
#define CONFIG_ARM_NONSECURE_FIRMWARE 1
#define CONFIG_ARM_NONSECURE_PREEMPTIBLE_SECURE_CALLS 1
#define CONFIG_ARM_STORE_EXC_RETURN 1
#define CONFIG_FP16 1
#define CONFIG_FP16_IEEE 1
#define CONFIG_CPU_CORTEX_M33 1
#define CONFIG_CPU_CORTEX_M_HAS_SYSTICK 1
#define CONFIG_CPU_CORTEX_M_HAS_DWT 1
#define CONFIG_CPU_CORTEX_M_HAS_BASEPRI 1
#define CONFIG_CPU_CORTEX_M_HAS_VTOR 1
#define CONFIG_CPU_CORTEX_M_HAS_SPLIM 1
#define CONFIG_CPU_CORTEX_M_HAS_PROGRAMMABLE_FAULT_PRIOS 1
#define CONFIG_CPU_CORTEX_M_HAS_CMSE 1
#define CONFIG_ARMV7_M_ARMV8_M_MAINLINE 1
#define CONFIG_ARMV8_M_MAINLINE 1
#define CONFIG_ARMV8_M_SE 1
#define CONFIG_ARMV7_M_ARMV8_M_FP 1
#define CONFIG_ARMV8_M_DSP 1
#define CONFIG_GEN_ISR_TABLES 1
#define CONFIG_NULL_POINTER_EXCEPTION_DETECTION_NONE 1
#define CONFIG_ARM_TRUSTZONE_M 1
#define CONFIG_GEN_IRQ_VECTOR_TABLE 1
#define CONFIG_ARM_MPU 1
#define CONFIG_ARM_MPU_REGION_MIN_ALIGN_AND_SIZE 32
#define CONFIG_MPU_ALLOW_FLASH_WRITE 1
#define CONFIG_CUSTOM_SECTION_MIN_ALIGN_SIZE 32
#define CONFIG_ARM 1
#define CONFIG_ARCH_IS_SET 1
#define CONFIG_ARCH_LOG_LEVEL_DEFAULT 1
#define CONFIG_ARCH_LOG_LEVEL 0
#define CONFIG_LITTLE_ENDIAN 1
#define CONFIG_TRUSTED_EXECUTION_NONSECURE 1
#define CONFIG_HW_STACK_PROTECTION 1
#define CONFIG_KOBJECT_TEXT_AREA 256
#define CONFIG_KOBJECT_DATA_AREA_RESERVE_EXTRA_PERCENT 100
#define CONFIG_KOBJECT_RODATA_AREA_EXTRA_BYTES 16
#define CONFIG_GEN_PRIV_STACKS 1
#define CONFIG_ARCH_IRQ_VECTOR_TABLE_ALIGN 4
#define CONFIG_IRQ_VECTOR_TABLE_JUMP_BY_ADDRESS 1
#define CONFIG_GEN_SW_ISR_TABLE 1
#define CONFIG_ARCH_SW_ISR_TABLE_ALIGN 4
#define CONFIG_GEN_IRQ_START_VECTOR 0
#define CONFIG_ARCH_HAS_SINGLE_THREAD_SUPPORT 1
#define CONFIG_ARCH_HAS_TIMING_FUNCTIONS 1
#define CONFIG_ARCH_HAS_TRUSTED_EXECUTION 1
#define CONFIG_ARCH_HAS_STACK_PROTECTION 1
#define CONFIG_ARCH_HAS_USERSPACE 1
#define CONFIG_ARCH_HAS_EXECUTABLE_PAGE_BIT 1
#define CONFIG_ARCH_HAS_RAMFUNC_SUPPORT 1
#define CONFIG_ARCH_HAS_NESTED_EXCEPTION_DETECTION 1
#define CONFIG_ARCH_SUPPORTS_COREDUMP 1
#define CONFIG_ARCH_SUPPORTS_ARCH_HW_INIT 1
#define CONFIG_ARCH_HAS_EXTRA_EXCEPTION_INFO 1
#define CONFIG_ARCH_HAS_THREAD_LOCAL_STORAGE 1
#define CONFIG_ARCH_HAS_SUSPEND_TO_RAM 1
#define CONFIG_ARCH_HAS_THREAD_ABORT 1
#define CONFIG_ARCH_HAS_CODE_DATA_RELOCATION 1
#define CONFIG_CPU_HAS_TEE 1
#define CONFIG_CPU_HAS_FPU 1
#define CONFIG_CPU_HAS_MPU 1
#define CONFIG_MPU 1
#define CONFIG_MPU_LOG_LEVEL_DEFAULT 1
#define CONFIG_MPU_LOG_LEVEL 0
#define CONFIG_MPU_REQUIRES_NON_OVERLAPPING_REGIONS 1
#define CONFIG_MPU_GAP_FILLING 1
#define CONFIG_SRAM_REGION_PERMISSIONS 1
#define CONFIG_TOOLCHAIN_HAS_BUILTIN_FFS 1
#define CONFIG_KERNEL_LOG_LEVEL_DEFAULT 1
#define CONFIG_KERNEL_LOG_LEVEL 0
#define CONFIG_MULTITHREADING 1
#define CONFIG_NUM_COOP_PRIORITIES 16
#define CONFIG_NUM_PREEMPT_PRIORITIES 15
#define CONFIG_MAIN_THREAD_PRIORITY 0
#define CONFIG_COOP_ENABLED 1
#define CONFIG_PREEMPT_ENABLED 1
#define CONFIG_PRIORITY_CEILING -127
#define CONFIG_ISR_STACK_SIZE 2048
#define CONFIG_THREAD_STACK_INFO 1
#define CONFIG_LIBC_ERRNO 1
#define CONFIG_ERRNO 1
#define CONFIG_SCHED_DUMB 1
#define CONFIG_WAITQ_DUMB 1
#define CONFIG_BOOT_BANNER 1
#define CONFIG_BOOT_DELAY 0
#define CONFIG_SYSTEM_WORKQUEUE_PRIORITY -1
#define CONFIG_BARRIER_OPERATIONS_ARCH 1
#define CONFIG_ATOMIC_OPERATIONS_BUILTIN 1
#define CONFIG_TIMESLICING 1
#define CONFIG_TIMESLICE_SIZE 0
#define CONFIG_TIMESLICE_PRIORITY 0
#define CONFIG_POLL 1
#define CONFIG_NUM_MBOX_ASYNC_MSGS 10
#define CONFIG_KERNEL_MEM_POOL 1
#define CONFIG_ARCH_HAS_CUSTOM_SWAP_TO_MAIN 1
#define CONFIG_SWAP_NONATOMIC 1
#define CONFIG_SYS_CLOCK_EXISTS 1
#define CONFIG_TIMEOUT_64BIT 1
#define CONFIG_SYS_CLOCK_MAX_TIMEOUT_DAYS 365
#define CONFIG_XIP 1
#define CONFIG_KERNEL_INIT_PRIORITY_OBJECTS 30
#define CONFIG_KERNEL_INIT_PRIORITY_DEFAULT 40
#define CONFIG_KERNEL_INIT_PRIORITY_DEVICE 50
#define CONFIG_APPLICATION_INIT_PRIORITY 90
#define CONFIG_STACK_POINTER_RANDOM 0
#define CONFIG_MP_NUM_CPUS 1
#define CONFIG_TICKLESS_KERNEL 1
#define CONFIG_TOOLCHAIN_SUPPORTS_THREAD_LOCAL_STORAGE 1
#define CONFIG_THREAD_LOCAL_STORAGE 1
#define CONFIG_ADC 1
#define CONFIG_ADC_CONFIGURABLE_INPUTS 1
#define CONFIG_ADC_ASYNC 1
#define CONFIG_ADC_LOG_LEVEL_DEFAULT 1
#define CONFIG_ADC_LOG_LEVEL 0
#define CONFIG_ADC_NRFX_SAADC 1
#define CONFIG_BT_RPMSG 1
#define CONFIG_BT_DRV_TX_STACK_SIZE 256
#define CONFIG_BT_DRV_RX_STACK_SIZE 256
#define CONFIG_CLOCK_CONTROL_LOG_LEVEL_DEFAULT 1
#define CONFIG_CLOCK_CONTROL_LOG_LEVEL 0
#define CONFIG_CLOCK_CONTROL_NRF 1
#define CONFIG_CLOCK_CONTROL_NRF_K32SRC_XTAL 1
#define CONFIG_CLOCK_CONTROL_NRF_K32SRC_50PPM 1
#define CONFIG_CLOCK_CONTROL_NRF_ACCURACY 50
#define CONFIG_CONSOLE 1
#define CONFIG_CONSOLE_INPUT_MAX_LINE_LEN 128
#define CONFIG_CONSOLE_HAS_DRIVER 1
#define CONFIG_CONSOLE_INIT_PRIORITY 40
#define CONFIG_RTT_CONSOLE 1
#define CONFIG_RTT_TX_RETRY_CNT 2
#define CONFIG_RTT_TX_RETRY_DELAY_MS 2
#define CONFIG_UART_CONSOLE_LOG_LEVEL_DEFAULT 1
#define CONFIG_UART_CONSOLE_LOG_LEVEL 0
#define CONFIG_ENTROPY_LOG_LEVEL_DEFAULT 1
#define CONFIG_ENTROPY_LOG_LEVEL 0
#define CONFIG_ENTROPY_INIT_PRIORITY 50
#define CONFIG_ENTROPY_BT_HCI 1
#define CONFIG_ENTROPY_PSA_CRYPTO_RNG 1
#define CONFIG_ENTROPY_HAS_DRIVER 1
#define CONFIG_FLASH_HAS_DRIVER_ENABLED 1
#define CONFIG_FLASH_HAS_PAGE_LAYOUT 1
#define CONFIG_FLASH_JESD216 1
#define CONFIG_FLASH 1
#define CONFIG_FLASH_LOG_LEVEL_DEFAULT 1
#define CONFIG_FLASH_LOG_LEVEL 0
#define CONFIG_FLASH_PAGE_LAYOUT 1
#define CONFIG_FLASH_INIT_PRIORITY 50
#define CONFIG_SOC_FLASH_NRF 1
#define CONFIG_SOC_FLASH_NRF_RADIO_SYNC_NONE 1
#define CONFIG_NORDIC_QSPI_NOR_INIT_PRIORITY 41
#define CONFIG_GPIO_LOG_LEVEL_DEFAULT 1
#define CONFIG_GPIO_LOG_LEVEL 0
#define CONFIG_GPIO_NRFX 1
#define CONFIG_GPIO_NRFX_INTERRUPT 1
#define CONFIG_I2C 1
#define CONFIG_I2C_NRFX 1
#define CONFIG_I2C_NRFX_TWIM 1
#define CONFIG_I2C_NRFX_TRANSFER_TIMEOUT 500
#define CONFIG_I2C_INIT_PRIORITY 50
#define CONFIG_I2C_LOG_LEVEL_DEFAULT 1
#define CONFIG_I2C_LOG_LEVEL 0
#define CONFIG_INTC_INIT_PRIORITY 40
#define CONFIG_INTC_LOG_LEVEL_DEFAULT 1
#define CONFIG_INTC_LOG_LEVEL 0
#define CONFIG_1ST_LEVEL_INTERRUPT_BITS 8
#define CONFIG_2ND_LEVEL_INTERRUPT_BITS 8
#define CONFIG_3RD_LEVEL_INTERRUPT_BITS 8
#define CONFIG_MBOX 1
#define CONFIG_MBOX_INIT_PRIORITY 40
#define CONFIG_MBOX_LOG_LEVEL_DEFAULT 1
#define CONFIG_MBOX_LOG_LEVEL 0
#define CONFIG_PINCTRL_LOG_LEVEL_DEFAULT 1
#define CONFIG_PINCTRL_LOG_LEVEL 0
#define CONFIG_PINCTRL_STORE_REG 1
#define CONFIG_PINCTRL_NRF 1
#define CONFIG_SENSOR_LOG_LEVEL_DEFAULT 1
#define CONFIG_SENSOR_LOG_LEVEL 0
#define CONFIG_SENSOR_INIT_PRIORITY 90
#define CONFIG_VCMP_IT8XXX2_INIT_PRIORITY 90
#define CONFIG_TMP112_FULL_SCALE_RUNTIME 1
#define CONFIG_TMP112_SAMPLING_FREQUENCY_RUNTIME 1
#define CONFIG_SERIAL_HAS_DRIVER 1
#define CONFIG_SERIAL_SUPPORT_ASYNC 1
#define CONFIG_SERIAL_SUPPORT_INTERRUPT 1
#define CONFIG_UART_LOG_LEVEL_DEFAULT 1
#define CONFIG_UART_LOG_LEVEL 0
#define CONFIG_UART_USE_RUNTIME_CONFIGURE 1
#define CONFIG_UART_NRFX 1
#define CONFIG_UART_NRFX_UARTE 1
#define CONFIG_UART_0_ENHANCED_POLL_OUT 1
#define CONFIG_UART_0_NRF_TX_BUFFER_SIZE 32
#define CONFIG_UART_ENHANCED_POLL_OUT 1
#define CONFIG_TIMER_READS_ITS_FREQUENCY_AT_RUNTIME 1
#define CONFIG_SYSTEM_CLOCK_INIT_PRIORITY 0
#define CONFIG_TICKLESS_CAPABLE 1
#define CONFIG_SYSTEM_TIMER_HAS_DISABLE_SUPPORT 1
#define CONFIG_SYSTEM_CLOCK_WAIT_FOR_STABILITY 1
#define CONFIG_USBC_LOG_LEVEL_DEFAULT 1
#define CONFIG_USBC_LOG_LEVEL 0
#define CONFIG_FULL_LIBC_SUPPORTED 1
#define CONFIG_MINIMAL_LIBC_SUPPORTED 1
#define CONFIG_NEWLIB_LIBC_SUPPORTED 1
#define CONFIG_PICOLIBC_SUPPORTED 1
#define CONFIG_PICOLIBC 1
#define CONFIG_HAS_NEWLIB_LIBC_NANO 1
#define CONFIG_COMMON_LIBC_ABORT 1
#define CONFIG_COMMON_LIBC_MALLOC 1
#define CONFIG_COMMON_LIBC_MALLOC_ARENA_SIZE -1
#define CONFIG_COMMON_LIBC_CALLOC 1
#define CONFIG_COMMON_LIBC_REALLOCARRAY 1
#define CONFIG_PICOLIBC_HEAP_SIZE -2
#define CONFIG_PICOLIBC_IO_LONG_LONG 1
#define CONFIG_PICOLIBC_IO_FLOAT 1
#define CONFIG_STDOUT_CONSOLE 1
#define CONFIG_CRC 1
#define CONFIG_NOTIFY 1
#define CONFIG_MPSC_PBUF 1
#define CONFIG_ONOFF 1
#define CONFIG_REBOOT 1
#define CONFIG_HAS_POWEROFF 1
#define CONFIG_CBPRINTF_COMPLETE 1
#define CONFIG_CBPRINTF_FULL_INTEGRAL 1
#define CONFIG_CBPRINTF_FP_SUPPORT 1
#define CONFIG_CBPRINTF_N_SPECIFIER 1
#define CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL_DEFAULT 1
#define CONFIG_CBPRINTF_PACKAGE_LOG_LEVEL 0
#define CONFIG_SYS_HEAP_ALLOC_LOOPS 3
#define CONFIG_SYS_HEAP_AUTO 1
#define CONFIG_SEM_VALUE_MAX 32767
#define CONFIG_TIMER 1
#define CONFIG_MAX_TIMER_COUNT 5
#define CONFIG_TIMER_CREATE_WAIT 100
#define CONFIG_TIMER_DELAYTIMER_MAX 20
#define CONFIG_ACPI_HID_LEN_MAX 12
#define CONFIG_LIBGCC_RTLIB 1
#define CONFIG_BT 1
#define CONFIG_BT_HCI 1
#define CONFIG_BT_CONN_TX 1
#define CONFIG_BT_LOG 1
#define CONFIG_BT_LOG_LEGACY 1
#define CONFIG_BT_LOG_LEVEL_DEFAULT 1
#define CONFIG_BT_LOG_LEVEL 0
#define CONFIG_BT_HCI_DRIVER_LOG_LEVEL 0
#define CONFIG_BT_HCI_DRIVER_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_RPA_LOG_LEVEL 0
#define CONFIG_BT_RPA_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_AICS_LOG_LEVEL 0
#define CONFIG_BT_AICS_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_AICS_CLIENT_LOG_LEVEL 0
#define CONFIG_BT_AICS_CLIENT_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_BAP_STREAM_LOG_LEVEL 0
#define CONFIG_BT_BAP_STREAM_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_AUDIO_CODEC_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_AUDIO_CODEC_LOG_LEVEL 0
#define CONFIG_BT_ASCS_LOG_LEVEL 0
#define CONFIG_BT_ASCS_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_BAP_UNICAST_SERVER_LOG_LEVEL 0
#define CONFIG_BT_BAP_UNICAST_SERVER_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_BAP_UNICAST_CLIENT_LOG_LEVEL 0
#define CONFIG_BT_BAP_UNICAST_CLIENT_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_BAP_BROADCAST_SOURCE_LOG_LEVEL 0
#define CONFIG_BT_BAP_BROADCAST_SOURCE_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_BAP_BROADCAST_SINK_LOG_LEVEL 0
#define CONFIG_BT_BAP_BROADCAST_SINK_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_BAP_SCAN_DELEGATOR_LOG_LEVEL 0
#define CONFIG_BT_BAP_SCAN_DELEGATOR_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_BAP_BROADCAST_ASSISTANT_LOG_LEVEL 0
#define CONFIG_BT_BAP_BROADCAST_ASSISTANT_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_BAP_ISO_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_BAP_ISO_LOG_LEVEL 0
#define CONFIG_BT_CAP_ACCEPTOR_LOG_LEVEL 0
#define CONFIG_BT_CAP_ACCEPTOR_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_CAP_INITIATOR_LOG_LEVEL 0
#define CONFIG_BT_CAP_INITIATOR_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_CAP_STREAM_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_CAP_STREAM_LOG_LEVEL 0
#define CONFIG_BT_CSIP_SET_MEMBER_LOG_LEVEL 0
#define CONFIG_BT_CSIP_SET_MEMBER_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_CSIP_SET_COORDINATOR_LOG_LEVEL 0
#define CONFIG_BT_CSIP_SET_COORDINATOR_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_CSIP_SET_MEMBER_CRYPTO_LOG_LEVEL 0
#define CONFIG_BT_CSIP_SET_MEMBER_CRYPTO_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_HAS_LOG_LEVEL 0
#define CONFIG_BT_HAS_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_HAS_CLIENT_LOG_LEVEL 0
#define CONFIG_BT_HAS_CLIENT_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MCS_LOG_LEVEL 0
#define CONFIG_BT_MCS_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MCC_LOG_LEVEL 0
#define CONFIG_BT_MCC_LOG_LEVEL_INHERIT 1
#define CONFIG_MCTL_LOG_LEVEL 0
#define CONFIG_MCTL_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MICP_MIC_DEV_LOG_LEVEL 0
#define CONFIG_BT_MICP_MIC_DEV_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MICP_MIC_CTLR_LOG_LEVEL 0
#define CONFIG_BT_MICP_MIC_CTLR_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MPL_LOG_LEVEL 0
#define CONFIG_BT_MPL_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_PACS_LOG_LEVEL 0
#define CONFIG_BT_PACS_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_TBS_LOG_LEVEL 0
#define CONFIG_BT_TBS_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_TBS_CLIENT_LOG_LEVEL 0
#define CONFIG_BT_TBS_CLIENT_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_VCP_VOL_REND_LOG_LEVEL 0
#define CONFIG_BT_VCP_VOL_REND_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_VCP_VOL_CTLR_LOG_LEVEL 0
#define CONFIG_BT_VCP_VOL_CTLR_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_VOCS_LOG_LEVEL 0
#define CONFIG_BT_VOCS_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_VOCS_CLIENT_LOG_LEVEL 0
#define CONFIG_BT_VOCS_CLIENT_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_CRYPTO_LOG_LEVEL 0
#define CONFIG_BT_CRYPTO_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_ATT_LOG_LEVEL 0
#define CONFIG_BT_ATT_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_GATT_LOG_LEVEL 0
#define CONFIG_BT_GATT_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_L2CAP_LOG_LEVEL 0
#define CONFIG_BT_L2CAP_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_EAD_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_EAD_LOG_LEVEL 0
#define CONFIG_BT_DF_LOG_LEVEL 0
#define CONFIG_BT_DF_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_SETTINGS_LOG_LEVEL 0
#define CONFIG_BT_SETTINGS_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_HCI_CORE_LOG_LEVEL 0
#define CONFIG_BT_HCI_CORE_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_CONN_LOG_LEVEL 0
#define CONFIG_BT_CONN_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_ISO_LOG_LEVEL 0
#define CONFIG_BT_ISO_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_KEYS_LOG_LEVEL 0
#define CONFIG_BT_KEYS_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_SMP_LOG_LEVEL 0
#define CONFIG_BT_SMP_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_SERVICE_LOG_LEVEL 0
#define CONFIG_BT_SERVICE_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_CTLR_ISOAL_LOG_LEVEL 0
#define CONFIG_BT_CTLR_ISOAL_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_RFCOMM_LOG_LEVEL 0
#define CONFIG_BT_RFCOMM_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_HFP_HF_LOG_LEVEL 0
#define CONFIG_BT_HFP_HF_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_AVDTP_LOG_LEVEL 0
#define CONFIG_BT_AVDTP_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_A2DP_LOG_LEVEL 0
#define CONFIG_BT_A2DP_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_SDP_LOG_LEVEL 0
#define CONFIG_BT_SDP_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_LOG_LEVEL 0
#define CONFIG_BT_MESH_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_NET_LOG_LEVEL 0
#define CONFIG_BT_MESH_NET_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_RPL_LOG_LEVEL 0
#define CONFIG_BT_MESH_RPL_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_TRANS_LOG_LEVEL 0
#define CONFIG_BT_MESH_TRANS_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_BEACON_LOG_LEVEL 0
#define CONFIG_BT_MESH_BEACON_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_CRYPTO_LOG_LEVEL 0
#define CONFIG_BT_MESH_CRYPTO_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_KEYS_LOG_LEVEL 0
#define CONFIG_BT_MESH_KEYS_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_PROV_LOG_LEVEL 0
#define CONFIG_BT_MESH_PROV_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_PROVISIONER_LOG_LEVEL 0
#define CONFIG_BT_MESH_PROVISIONER_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_PROV_DEVICE_LOG_LEVEL 0
#define CONFIG_BT_MESH_PROV_DEVICE_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_ACCESS_LOG_LEVEL 0
#define CONFIG_BT_MESH_ACCESS_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_MODEL_LOG_LEVEL 0
#define CONFIG_BT_MESH_MODEL_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_DFU_LOG_LEVEL 0
#define CONFIG_BT_MESH_DFU_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_ADV_LOG_LEVEL 0
#define CONFIG_BT_MESH_ADV_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_LOW_POWER_LOG_LEVEL 0
#define CONFIG_BT_MESH_LOW_POWER_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_FRIEND_LOG_LEVEL 0
#define CONFIG_BT_MESH_FRIEND_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_PROXY_LOG_LEVEL 0
#define CONFIG_BT_MESH_PROXY_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_SETTINGS_LOG_LEVEL 0
#define CONFIG_BT_MESH_SETTINGS_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_CDB_LOG_LEVEL 0
#define CONFIG_BT_MESH_CDB_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_MESH_CFG_LOG_LEVEL 0
#define CONFIG_BT_MESH_CFG_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_BAS_LOG_LEVEL_DEFAULT 1
#define CONFIG_BT_BAS_LOG_LEVEL 0
#define CONFIG_BT_HRS_LOG_LEVEL_DEFAULT 1
#define CONFIG_BT_HRS_LOG_LEVEL 0
#define CONFIG_BT_TPS_LOG_LEVEL_DEFAULT 1
#define CONFIG_BT_TPS_LOG_LEVEL 0
#define CONFIG_BT_IAS_CLIENT_LOG_LEVEL 0
#define CONFIG_BT_IAS_CLIENT_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_IAS_LOG_LEVEL_DEFAULT 1
#define CONFIG_BT_IAS_LOG_LEVEL 0
#define CONFIG_BT_OTS_CLIENT_LOG_LEVEL 0
#define CONFIG_BT_OTS_CLIENT_LOG_LEVEL_INHERIT 1
#define CONFIG_BT_OTS_LOG_LEVEL_DEFAULT 1
#define CONFIG_BT_OTS_LOG_LEVEL 0
#define CONFIG_BT_COMPANY_ID 0x05F1
#define CONFIG_PRINTK 1
#define CONFIG_EARLY_CONSOLE 1
#define CONFIG_ASSERT_VERBOSE 1
#define CONFIG_NVS 1
#define CONFIG_NVS_LOG_LEVEL_DEFAULT 1
#define CONFIG_NVS_LOG_LEVEL 0
#define CONFIG_IPC_SERVICE 1
#define CONFIG_IPC_SERVICE_REG_BACKEND_PRIORITY 46
#define CONFIG_IPC_SERVICE_BACKEND_RPMSG 1
#define CONFIG_IPC_SERVICE_BACKEND_RPMSG_WQ_STACK_SIZE 1024
#define CONFIG_IPC_SERVICE_BACKEND_RPMSG_NUM_ENDPOINTS_PER_INSTANCE 2
#define CONFIG_IPC_SERVICE_RPMSG 1
#define CONFIG_IPC_SERVICE_STATIC_VRINGS 1
#define CONFIG_IPC_SERVICE_STATIC_VRINGS_MEM_ALIGNMENT 4
#define CONFIG_IPC_SERVICE_LOG_LEVEL_DEFAULT 1
#define CONFIG_IPC_SERVICE_LOG_LEVEL 0
#define CONFIG_LOG 1
#define CONFIG_LOG_CORE_INIT_PRIORITY 0
#define CONFIG_LOG_MODE_DEFERRED 1
#define CONFIG_LOG_DEFAULT_LEVEL 0
#define CONFIG_LOG_OVERRIDE_LEVEL 0
#define CONFIG_LOG_MAX_LEVEL 4
#define CONFIG_LOG_PRINTK 1
#define CONFIG_LOG_MODE_OVERFLOW 1
#define CONFIG_LOG_PROCESS_TRIGGER_THRESHOLD 10
#define CONFIG_LOG_PROCESS_THREAD 1
#define CONFIG_LOG_PROCESS_THREAD_STARTUP_DELAY_MS 0
#define CONFIG_LOG_PROCESS_THREAD_SLEEP_MS 1000
#define CONFIG_LOG_PROCESS_THREAD_STACK_SIZE 768
#define CONFIG_LOG_TRACE_SHORT_TIMESTAMP 1
#define CONFIG_LOG_FUNC_NAME_PREFIX_DBG 1
#define CONFIG_LOG_BACKEND_SHOW_COLOR 1
#define CONFIG_LOG_TAG_MAX_LEN 0
#define CONFIG_LOG_BACKEND_FORMAT_TIMESTAMP 1
#define CONFIG_LOG_BACKEND_RTT 1
#define CONFIG_LOG_BACKEND_RTT_MODE_BLOCK 1
#define CONFIG_LOG_BACKEND_RTT_OUTPUT_TEXT 1
#define CONFIG_LOG_BACKEND_RTT_OUTPUT_DEFAULT 0
#define CONFIG_LOG_BACKEND_RTT_OUTPUT_BUFFER_SIZE 16
#define CONFIG_LOG_BACKEND_RTT_RETRY_CNT 4
#define CONFIG_LOG_BACKEND_RTT_RETRY_DELAY_MS 5
#define CONFIG_LOG_BACKEND_RTT_BUFFER 0
#define CONFIG_LOG_BACKEND_RTT_FORCE_PRINTK 1
#define CONFIG_LOG_DOMAIN_ID 0
#define CONFIG_LOG_USE_VLA 1
#define CONFIG_LOG_FAILURE_REPORT_PERIOD 1000
#define CONFIG_LOG_OUTPUT 1
#define CONFIG_NET_BUF 1
#define CONFIG_NET_BUF_LOG_LEVEL_DEFAULT 1
#define CONFIG_NET_BUF_LOG_LEVEL 0
#define CONFIG_XOSHIRO_RANDOM_GENERATOR 1
#define CONFIG_CSPRING_ENABLED 1
#define CONFIG_HARDWARE_DEVICE_CS_GENERATOR 1
#define CONFIG_SETTINGS 1
#define CONFIG_SETTINGS_LOG_LEVEL_DEFAULT 1
#define CONFIG_SETTINGS_LOG_LEVEL 0
#define CONFIG_SETTINGS_DYNAMIC_HANDLERS 1
#define CONFIG_SETTINGS_NVS 1
#define CONFIG_SETTINGS_NVS_SECTOR_SIZE_MULT 1
#define CONFIG_SETTINGS_NVS_SECTOR_COUNT 8
#define CONFIG_FLASH_MAP 1
#define CONFIG_COVERAGE_GCOV_HEAP_SIZE 16384
#define CONFIG_TOOLCHAIN_ZEPHYR_0_16 1
#define CONFIG_TOOLCHAIN_ZEPHYR_SUPPORTS_THREAD_LOCAL_STORAGE 1
#define CONFIG_LINKER_ORPHAN_SECTION_WARN 1
#define CONFIG_HAS_FLASH_LOAD_OFFSET 1
#define CONFIG_LD_LINKER_SCRIPT_SUPPORTED 1
#define CONFIG_LD_LINKER_TEMPLATE 1
#define CONFIG_KERNEL_ENTRY "__start"
#define CONFIG_LINKER_SORT_BY_ALIGNMENT 1
#define CONFIG_SRAM_OFFSET 0x0
#define CONFIG_LINKER_GENERIC_SECTIONS_PRESENT_AT_BOOT 1
#define CONFIG_LINKER_LAST_SECTION_ID 1
#define CONFIG_LINKER_LAST_SECTION_ID_PATTERN 0xE015E015
#define CONFIG_LINKER_USE_RELAX 1
#define CONFIG_SIZE_OPTIMIZATIONS 1
#define CONFIG_COMPILER_TRACK_MACRO_EXPANSION 1
#define CONFIG_COMPILER_COLOR_DIAGNOSTICS 1
#define CONFIG_FORTIFY_SOURCE_COMPILE_TIME 1
#define CONFIG_COMPILER_OPT ""
#define CONFIG_RUNTIME_ERROR_CHECKS 1
#define CONFIG_KERNEL_BIN_NAME "zephyr"
#define CONFIG_OUTPUT_STAT 1
#define CONFIG_OUTPUT_PRINT_MEMORY_USAGE 1
#define CONFIG_BUILD_OUTPUT_STRIP_PATHS 1
#define CONFIG_CHECK_INIT_PRIORITIES 1
#define CONFIG_WARN_DEPRECATED 1
#define CONFIG_EXPERIMENTAL 1
#define CONFIG_ENFORCE_ZEPHYR_STDINT 1
#define CONFIG_COMPAT_INCLUDES 1
