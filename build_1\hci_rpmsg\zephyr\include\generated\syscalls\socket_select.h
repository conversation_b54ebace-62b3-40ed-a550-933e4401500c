/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_SOCKET_SELECT_H
#define Z_INCLUDE_SYSCALLS_SOCKET_SELECT_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_zsock_select(int nfds, zsock_fd_set * readfds, zsock_fd_set * writefds, zsock_fd_set * exceptfds, struct zsock_timeval * timeout);

__pinned_func
static inline int zsock_select(int nfds, zsock_fd_set * readfds, zsock_fd_set * writefds, zsock_fd_set * exceptfds, struct zsock_timeval * timeout)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; int val; } parm0 = { .val = nfds };
		union { uintptr_t x; zsock_fd_set * val; } parm1 = { .val = readfds };
		union { uintptr_t x; zsock_fd_set * val; } parm2 = { .val = writefds };
		union { uintptr_t x; zsock_fd_set * val; } parm3 = { .val = exceptfds };
		union { uintptr_t x; struct zsock_timeval * val; } parm4 = { .val = timeout };
		return (int) arch_syscall_invoke5(parm0.x, parm1.x, parm2.x, parm3.x, parm4.x, K_SYSCALL_ZSOCK_SELECT);
	}
#endif
	compiler_barrier();
	return z_impl_zsock_select(nfds, readfds, writefds, exceptfds, timeout);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define zsock_select(nfds, readfds, writefds, exceptfds, timeout) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_ZSOCK_SELECT, zsock_select, nfds, readfds, writefds, exceptfds, timeout); 	syscall__retval = zsock_select(nfds, readfds, writefds, exceptfds, timeout); 	sys_port_trace_syscall_exit(K_SYSCALL_ZSOCK_SELECT, zsock_select, nfds, readfds, writefds, exceptfds, timeout, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
