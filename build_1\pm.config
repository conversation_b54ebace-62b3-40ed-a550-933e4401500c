PM_TFM_OFFSET=0x0
PM_TFM_ADDRESS=0x0
PM_TFM_END_ADDRESS=0x8000
PM_TFM_SIZE=0x8000
PM_TFM_NAME=tfm
PM_TFM_ID=0
PM_tfm_ID=PM_TFM_ID
PM_tfm_IS_ENABLED=1
PM_0_LABEL=TFM
PM_TFM_SECURE_OFFSET=0x0
PM_TFM_SECURE_ADDRESS=0x0
PM_TFM_SECURE_END_ADDRESS=0x8000
PM_TFM_SECURE_SIZE=0x8000
PM_TFM_SECURE_NAME=tfm_secure
PM_TFM_SECURE_ID=1
PM_tfm_secure_ID=PM_TFM_SECURE_ID
PM_tfm_secure_IS_ENABLED=1
PM_1_LABEL=TFM_SECURE
PM_TFM_SECURE_SPAN="tfm"
PM_APP_OFFSET=0x8000
PM_APP_ADDRESS=0x8000
PM_APP_END_ADDRESS=0xfc000
PM_APP_SIZE=0xf4000
PM_APP_NAME=app
PM_APP_ID=2
PM_app_ID=PM_APP_ID
PM_app_IS_ENABLED=1
PM_2_LABEL=APP
PM_TFM_NONSECURE_OFFSET=0x8000
PM_TFM_NONSECURE_ADDRESS=0x8000
PM_TFM_NONSECURE_END_ADDRESS=0xfc000
PM_TFM_NONSECURE_SIZE=0xf4000
PM_TFM_NONSECURE_NAME=tfm_nonsecure
PM_TFM_NONSECURE_ID=3
PM_tfm_nonsecure_ID=PM_TFM_NONSECURE_ID
PM_tfm_nonsecure_IS_ENABLED=1
PM_3_LABEL=TFM_NONSECURE
PM_TFM_NONSECURE_SPAN="app"
PM_NONSECURE_STORAGE_OFFSET=0xfc000
PM_NONSECURE_STORAGE_ADDRESS=0xfc000
PM_NONSECURE_STORAGE_END_ADDRESS=0xfe000
PM_NONSECURE_STORAGE_SIZE=0x2000
PM_NONSECURE_STORAGE_NAME=nonsecure_storage
PM_NONSECURE_STORAGE_ID=4
PM_nonsecure_storage_ID=PM_NONSECURE_STORAGE_ID
PM_nonsecure_storage_IS_ENABLED=1
PM_4_LABEL=NONSECURE_STORAGE
PM_NONSECURE_STORAGE_SPAN="settings_storage"
PM_SETTINGS_STORAGE_OFFSET=0xfc000
PM_SETTINGS_STORAGE_ADDRESS=0xfc000
PM_SETTINGS_STORAGE_END_ADDRESS=0xfe000
PM_SETTINGS_STORAGE_SIZE=0x2000
PM_SETTINGS_STORAGE_NAME=settings_storage
PM_SETTINGS_STORAGE_ID=5
PM_settings_storage_ID=PM_SETTINGS_STORAGE_ID
PM_settings_storage_IS_ENABLED=1
PM_5_LABEL=SETTINGS_STORAGE
PM_EMPTY_0_OFFSET=0xfe000
PM_EMPTY_0_ADDRESS=0xfe000
PM_EMPTY_0_END_ADDRESS=0x100000
PM_EMPTY_0_SIZE=0x2000
PM_EMPTY_0_NAME=EMPTY_0
PM_EMPTY_0_ID=6
PM_empty_0_ID=PM_EMPTY_0_ID
PM_empty_0_IS_ENABLED=1
PM_6_LABEL=EMPTY_0
PM_OTP_OFFSET=0x0
PM_OTP_ADDRESS=0xff8100
PM_OTP_END_ADDRESS=0xff83fc
PM_OTP_SIZE=0x2fc
PM_OTP_NAME=otp
PM_SRAM_SECURE_OFFSET=0x0
PM_SRAM_SECURE_ADDRESS=0x20000000
PM_SRAM_SECURE_END_ADDRESS=0x20008000
PM_SRAM_SECURE_SIZE=0x8000
PM_SRAM_SECURE_NAME=sram_secure
PM_SRAM_SECURE_SPAN="tfm_sram"
PM_TFM_SRAM_OFFSET=0x0
PM_TFM_SRAM_ADDRESS=0x20000000
PM_TFM_SRAM_END_ADDRESS=0x20008000
PM_TFM_SRAM_SIZE=0x8000
PM_TFM_SRAM_NAME=tfm_sram
PM_SRAM_NONSECURE_OFFSET=0x8000
PM_SRAM_NONSECURE_ADDRESS=0x20008000
PM_SRAM_NONSECURE_END_ADDRESS=0x20080000
PM_SRAM_NONSECURE_SIZE=0x78000
PM_SRAM_NONSECURE_NAME=sram_nonsecure
PM_SRAM_NONSECURE_SPAN="sram_primary rpmsg_nrf53_sram"
PM_SRAM_PRIMARY_OFFSET=0x8000
PM_SRAM_PRIMARY_ADDRESS=0x20008000
PM_SRAM_PRIMARY_END_ADDRESS=0x20070000
PM_SRAM_PRIMARY_SIZE=0x68000
PM_SRAM_PRIMARY_NAME=sram_primary
PM_RPMSG_NRF53_SRAM_OFFSET=0x70000
PM_RPMSG_NRF53_SRAM_ADDRESS=0x20070000
PM_RPMSG_NRF53_SRAM_END_ADDRESS=0x20080000
PM_RPMSG_NRF53_SRAM_SIZE=0x10000
PM_RPMSG_NRF53_SRAM_NAME=rpmsg_nrf53_sram
PM_NUM=7
PM_ALL_BY_SIZE="otp EMPTY_0 settings_storage nonsecure_storage tfm tfm_sram sram_secure tfm_secure rpmsg_nrf53_sram sram_primary sram_nonsecure app tfm_nonsecure"