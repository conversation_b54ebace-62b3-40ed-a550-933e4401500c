/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_KSCAN_H
#define Z_INCLUDE_SYSCALLS_KSCAN_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_kscan_config(const struct device * dev, kscan_callback_t callback);

__pinned_func
static inline int kscan_config(const struct device * dev, kscan_callback_t callback)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; kscan_callback_t val; } parm1 = { .val = callback };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_KSCAN_CONFIG);
	}
#endif
	compiler_barrier();
	return z_impl_kscan_config(dev, callback);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define kscan_config(dev, callback) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_KSCAN_CONFIG, kscan_config, dev, callback); 	syscall__retval = kscan_config(dev, callback); 	sys_port_trace_syscall_exit(K_SYSCALL_KSCAN_CONFIG, kscan_config, dev, callback, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_kscan_enable_callback(const struct device * dev);

__pinned_func
static inline int kscan_enable_callback(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_KSCAN_ENABLE_CALLBACK);
	}
#endif
	compiler_barrier();
	return z_impl_kscan_enable_callback(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define kscan_enable_callback(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_KSCAN_ENABLE_CALLBACK, kscan_enable_callback, dev); 	syscall__retval = kscan_enable_callback(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_KSCAN_ENABLE_CALLBACK, kscan_enable_callback, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_kscan_disable_callback(const struct device * dev);

__pinned_func
static inline int kscan_disable_callback(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_KSCAN_DISABLE_CALLBACK);
	}
#endif
	compiler_barrier();
	return z_impl_kscan_disable_callback(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define kscan_disable_callback(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_KSCAN_DISABLE_CALLBACK, kscan_disable_callback, dev); 	syscall__retval = kscan_disable_callback(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_KSCAN_DISABLE_CALLBACK, kscan_disable_callback, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
