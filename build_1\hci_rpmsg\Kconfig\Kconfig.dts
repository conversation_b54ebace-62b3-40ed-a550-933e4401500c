# Generated devicetree Kconfig
#
# SPDX-License-Identifier: Apache-2.0

DT_COMPAT_ADAFRUIT_FEATHER_HEADER := adafruit-feather-header

config DT_HAS_ADAFRUIT_FEATHER_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADAFRUIT_FEATHER_HEADER))

DT_COMPAT_ADI_ADIN1110 := adi,adin1110

config DT_HAS_ADI_ADIN1110_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADIN1110))

DT_COMPAT_ADI_ADIN2111 := adi,adin2111

config DT_HAS_ADI_ADIN2111_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADIN2111))

DT_COMPAT_ADI_ADIN2111_MDIO := adi,adin2111-mdio

config DT_HAS_ADI_ADIN2111_MDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADIN2111_MDIO))

DT_COMPAT_ADI_ADIN2111_PHY := adi,adin2111-phy

config DT_HAS_ADI_ADIN2111_PHY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADIN2111_PHY))

DT_COMPAT_ADI_ADP5360 := adi,adp5360

config DT_HAS_ADI_ADP5360_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADP5360))

DT_COMPAT_ADI_ADP5360_REGULATOR := adi,adp5360-regulator

config DT_HAS_ADI_ADP5360_REGULATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADP5360_REGULATOR))

DT_COMPAT_ADI_ADT7310 := adi,adt7310

config DT_HAS_ADI_ADT7310_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADT7310))

DT_COMPAT_ADI_ADT7420 := adi,adt7420

config DT_HAS_ADI_ADT7420_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADT7420))

DT_COMPAT_ADI_ADXL345 := adi,adxl345

config DT_HAS_ADI_ADXL345_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADXL345))

DT_COMPAT_ADI_ADXL362 := adi,adxl362

config DT_HAS_ADI_ADXL362_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADXL362))

DT_COMPAT_ADI_ADXL372 := adi,adxl372

config DT_HAS_ADI_ADXL372_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ADI_ADXL372))

DT_COMPAT_ALTR_JTAG_UART := altr,jtag-uart

config DT_HAS_ALTR_JTAG_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ALTR_JTAG_UART))

DT_COMPAT_ALTR_MSGDMA := altr,msgdma

config DT_HAS_ALTR_MSGDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ALTR_MSGDMA))

DT_COMPAT_ALTR_NIOS2_I2C := altr,nios2-i2c

config DT_HAS_ALTR_NIOS2_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ALTR_NIOS2_I2C))

DT_COMPAT_ALTR_NIOS2_QSPI := altr,nios2-qspi

config DT_HAS_ALTR_NIOS2_QSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ALTR_NIOS2_QSPI))

DT_COMPAT_ALTR_NIOS2_QSPI_NOR := altr,nios2-qspi-nor

config DT_HAS_ALTR_NIOS2_QSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ALTR_NIOS2_QSPI_NOR))

DT_COMPAT_ALTR_NIOS2F := altr,nios2f

config DT_HAS_ALTR_NIOS2F_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ALTR_NIOS2F))

DT_COMPAT_ALTR_UART := altr,uart

config DT_HAS_ALTR_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ALTR_UART))

DT_COMPAT_AMBIQ_APOLLO4_PINCTRL := ambiq,apollo4-pinctrl

config DT_HAS_AMBIQ_APOLLO4_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_APOLLO4_PINCTRL))

DT_COMPAT_AMBIQ_COUNTER := ambiq,counter

config DT_HAS_AMBIQ_COUNTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_COUNTER))

DT_COMPAT_AMBIQ_I2C := ambiq,i2c

config DT_HAS_AMBIQ_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_I2C))

DT_COMPAT_AMBIQ_PWRCTRL := ambiq,pwrctrl

config DT_HAS_AMBIQ_PWRCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_PWRCTRL))

DT_COMPAT_AMBIQ_SPI := ambiq,spi

config DT_HAS_AMBIQ_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_SPI))

DT_COMPAT_AMBIQ_STIMER := ambiq,stimer

config DT_HAS_AMBIQ_STIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_STIMER))

DT_COMPAT_AMBIQ_UART := ambiq,uart

config DT_HAS_AMBIQ_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_UART))

DT_COMPAT_AMBIQ_WATCHDOG := ambiq,watchdog

config DT_HAS_AMBIQ_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMBIQ_WATCHDOG))

DT_COMPAT_AMS_AS5600 := ams,as5600

config DT_HAS_AMS_AS5600_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_AS5600))

DT_COMPAT_AMS_AS6212 := ams,as6212

config DT_HAS_AMS_AS6212_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_AS6212))

DT_COMPAT_AMS_CCS811 := ams,ccs811

config DT_HAS_AMS_CCS811_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_CCS811))

DT_COMPAT_AMS_ENS210 := ams,ens210

config DT_HAS_AMS_ENS210_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_ENS210))

DT_COMPAT_AMS_IAQCORE := ams,iaqcore

config DT_HAS_AMS_IAQCORE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_IAQCORE))

DT_COMPAT_AMS_TCS3400 := ams,tcs3400

config DT_HAS_AMS_TCS3400_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_TCS3400))

DT_COMPAT_AMS_TMD2620 := ams,tmd2620

config DT_HAS_AMS_TMD2620_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AMS_TMD2620))

DT_COMPAT_ANDESTECH_ATCGPIO100 := andestech,atcgpio100

config DT_HAS_ANDESTECH_ATCGPIO100_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_ATCGPIO100))

DT_COMPAT_ANDESTECH_ATCIIC100 := andestech,atciic100

config DT_HAS_ANDESTECH_ATCIIC100_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_ATCIIC100))

DT_COMPAT_ANDESTECH_ATCPIT100 := andestech,atcpit100

config DT_HAS_ANDESTECH_ATCPIT100_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_ATCPIT100))

DT_COMPAT_ANDESTECH_ATCSPI200 := andestech,atcspi200

config DT_HAS_ANDESTECH_ATCSPI200_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_ATCSPI200))

DT_COMPAT_ANDESTECH_ATCWDT200 := andestech,atcwdt200

config DT_HAS_ANDESTECH_ATCWDT200_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_ATCWDT200))

DT_COMPAT_ANDESTECH_MACHINE_TIMER := andestech,machine-timer

config DT_HAS_ANDESTECH_MACHINE_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_MACHINE_TIMER))

DT_COMPAT_ANDESTECH_PLIC_SW := andestech,plic-sw

config DT_HAS_ANDESTECH_PLIC_SW_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_PLIC_SW))

DT_COMPAT_ANDESTECH_QSPI_NOR := andestech,qspi-nor

config DT_HAS_ANDESTECH_QSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ANDESTECH_QSPI_NOR))

DT_COMPAT_AOSONG_DHT := aosong,dht

config DT_HAS_AOSONG_DHT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AOSONG_DHT))

DT_COMPAT_APA_APA102 := apa,apa102

config DT_HAS_APA_APA102_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_APA_APA102))

DT_COMPAT_APTINA_MT9M114 := aptina,mt9m114

config DT_HAS_APTINA_MT9M114_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_APTINA_MT9M114))

DT_COMPAT_ARC_DCCM := arc,dccm

config DT_HAS_ARC_DCCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARC_DCCM))

DT_COMPAT_ARC_ICCM := arc,iccm

config DT_HAS_ARC_ICCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARC_ICCM))

DT_COMPAT_ARC_XCCM := arc,xccm

config DT_HAS_ARC_XCCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARC_XCCM))

DT_COMPAT_ARC_YCCM := arc,yccm

config DT_HAS_ARC_YCCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARC_YCCM))

DT_COMPAT_ARDUINO_UNO_ADC := arduino,uno-adc

config DT_HAS_ARDUINO_UNO_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARDUINO_UNO_ADC))

DT_COMPAT_ARDUINO_HEADER_R3 := arduino-header-r3

config DT_HAS_ARDUINO_HEADER_R3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARDUINO_HEADER_R3))

DT_COMPAT_ARDUINO_MKR_HEADER := arduino-mkr-header

config DT_HAS_ARDUINO_MKR_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARDUINO_MKR_HEADER))

DT_COMPAT_ARDUINO_NANO_HEADER_R3 := arduino-nano-header-r3

config DT_HAS_ARDUINO_NANO_HEADER_R3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARDUINO_NANO_HEADER_R3))

DT_COMPAT_ARM_ARMV6M_MPU := arm,armv6m-mpu

config DT_HAS_ARM_ARMV6M_MPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV6M_MPU))

DT_COMPAT_ARM_ARMV6M_SYSTICK := arm,armv6m-systick

config DT_HAS_ARM_ARMV6M_SYSTICK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV6M_SYSTICK))

DT_COMPAT_ARM_ARMV7M_ITM := arm,armv7m-itm

config DT_HAS_ARM_ARMV7M_ITM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV7M_ITM))

DT_COMPAT_ARM_ARMV7M_MPU := arm,armv7m-mpu

config DT_HAS_ARM_ARMV7M_MPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV7M_MPU))

DT_COMPAT_ARM_ARMV7M_SYSTICK := arm,armv7m-systick

config DT_HAS_ARM_ARMV7M_SYSTICK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV7M_SYSTICK))

DT_COMPAT_ARM_ARMV8_TIMER := arm,armv8-timer

config DT_HAS_ARM_ARMV8_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV8_TIMER))

DT_COMPAT_ARM_ARMV8_1M_MPU := arm,armv8.1m-mpu

config DT_HAS_ARM_ARMV8_1M_MPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV8_1M_MPU))

DT_COMPAT_ARM_ARMV8_1M_SYSTICK := arm,armv8.1m-systick

config DT_HAS_ARM_ARMV8_1M_SYSTICK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV8_1M_SYSTICK))

DT_COMPAT_ARM_ARMV8M_ITM := arm,armv8m-itm

config DT_HAS_ARM_ARMV8M_ITM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV8M_ITM))

DT_COMPAT_ARM_ARMV8M_MPU := arm,armv8m-mpu

config DT_HAS_ARM_ARMV8M_MPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV8M_MPU))

DT_COMPAT_ARM_ARMV8M_SYSTICK := arm,armv8m-systick

config DT_HAS_ARM_ARMV8M_SYSTICK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ARMV8M_SYSTICK))

DT_COMPAT_ARM_BEETLE_SYSCON := arm,beetle-syscon

config DT_HAS_ARM_BEETLE_SYSCON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_BEETLE_SYSCON))

DT_COMPAT_ARM_CMSDK_DTIMER := arm,cmsdk-dtimer

config DT_HAS_ARM_CMSDK_DTIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CMSDK_DTIMER))

DT_COMPAT_ARM_CMSDK_GPIO := arm,cmsdk-gpio

config DT_HAS_ARM_CMSDK_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CMSDK_GPIO))

DT_COMPAT_ARM_CMSDK_TIMER := arm,cmsdk-timer

config DT_HAS_ARM_CMSDK_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CMSDK_TIMER))

DT_COMPAT_ARM_CMSDK_UART := arm,cmsdk-uart

config DT_HAS_ARM_CMSDK_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CMSDK_UART))

DT_COMPAT_ARM_CMSDK_WATCHDOG := arm,cmsdk-watchdog

config DT_HAS_ARM_CMSDK_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CMSDK_WATCHDOG))

DT_COMPAT_ARM_CORTEX_A53 := arm,cortex-a53

config DT_HAS_ARM_CORTEX_A53_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_A53))

DT_COMPAT_ARM_CORTEX_A55 := arm,cortex-a55

config DT_HAS_ARM_CORTEX_A55_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_A55))

DT_COMPAT_ARM_CORTEX_A72 := arm,cortex-a72

config DT_HAS_ARM_CORTEX_A72_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_A72))

DT_COMPAT_ARM_CORTEX_A76 := arm,cortex-a76

config DT_HAS_ARM_CORTEX_A76_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_A76))

DT_COMPAT_ARM_CORTEX_M0 := arm,cortex-m0

config DT_HAS_ARM_CORTEX_M0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M0))

DT_COMPAT_ARM_CORTEX_M0_ := arm,cortex-m0+

config DT_HAS_ARM_CORTEX_M0__ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M0_))

DT_COMPAT_ARM_CORTEX_M1 := arm,cortex-m1

config DT_HAS_ARM_CORTEX_M1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M1))

DT_COMPAT_ARM_CORTEX_M23 := arm,cortex-m23

config DT_HAS_ARM_CORTEX_M23_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M23))

DT_COMPAT_ARM_CORTEX_M3 := arm,cortex-m3

config DT_HAS_ARM_CORTEX_M3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M3))

DT_COMPAT_ARM_CORTEX_M33 := arm,cortex-m33

config DT_HAS_ARM_CORTEX_M33_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M33))

DT_COMPAT_ARM_CORTEX_M33F := arm,cortex-m33f

config DT_HAS_ARM_CORTEX_M33F_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M33F))

DT_COMPAT_ARM_CORTEX_M4 := arm,cortex-m4

config DT_HAS_ARM_CORTEX_M4_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M4))

DT_COMPAT_ARM_CORTEX_M4F := arm,cortex-m4f

config DT_HAS_ARM_CORTEX_M4F_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M4F))

DT_COMPAT_ARM_CORTEX_M7 := arm,cortex-m7

config DT_HAS_ARM_CORTEX_M7_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_M7))

DT_COMPAT_ARM_CORTEX_R4 := arm,cortex-r4

config DT_HAS_ARM_CORTEX_R4_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_R4))

DT_COMPAT_ARM_CORTEX_R4F := arm,cortex-r4f

config DT_HAS_ARM_CORTEX_R4F_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_R4F))

DT_COMPAT_ARM_CORTEX_R5 := arm,cortex-r5

config DT_HAS_ARM_CORTEX_R5_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_R5))

DT_COMPAT_ARM_CORTEX_R52 := arm,cortex-r52

config DT_HAS_ARM_CORTEX_R52_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_R52))

DT_COMPAT_ARM_CORTEX_R5F := arm,cortex-r5f

config DT_HAS_ARM_CORTEX_R5F_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_R5F))

DT_COMPAT_ARM_CORTEX_R7 := arm,cortex-r7

config DT_HAS_ARM_CORTEX_R7_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_R7))

DT_COMPAT_ARM_CORTEX_R82 := arm,cortex-r82

config DT_HAS_ARM_CORTEX_R82_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CORTEX_R82))

DT_COMPAT_ARM_CRYPTOCELL_310 := arm,cryptocell-310

config DT_HAS_ARM_CRYPTOCELL_310_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CRYPTOCELL_310))

DT_COMPAT_ARM_CRYPTOCELL_312 := arm,cryptocell-312

config DT_HAS_ARM_CRYPTOCELL_312_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_CRYPTOCELL_312))

DT_COMPAT_ARM_DMA_PL330 := arm,dma-pl330

config DT_HAS_ARM_DMA_PL330_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_DMA_PL330))

DT_COMPAT_ARM_DTCM := arm,dtcm

config DT_HAS_ARM_DTCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_DTCM))

DT_COMPAT_ARM_ETHOS_U := arm,ethos-u

config DT_HAS_ARM_ETHOS_U_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ETHOS_U))

DT_COMPAT_ARM_GIC := arm,gic

config DT_HAS_ARM_GIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_GIC))

DT_COMPAT_ARM_GIC_V1 := arm,gic-v1

config DT_HAS_ARM_GIC_V1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_GIC_V1))

DT_COMPAT_ARM_GIC_V2 := arm,gic-v2

config DT_HAS_ARM_GIC_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_GIC_V2))

DT_COMPAT_ARM_GIC_V3 := arm,gic-v3

config DT_HAS_ARM_GIC_V3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_GIC_V3))

DT_COMPAT_ARM_GIC_V3_ITS := arm,gic-v3-its

config DT_HAS_ARM_GIC_V3_ITS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_GIC_V3_ITS))

DT_COMPAT_ARM_ITCM := arm,itcm

config DT_HAS_ARM_ITCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_ITCM))

DT_COMPAT_ARM_MHU := arm,mhu

config DT_HAS_ARM_MHU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_MHU))

DT_COMPAT_ARM_MPS2_FPGAIO_GPIO := arm,mps2-fpgaio-gpio

config DT_HAS_ARM_MPS2_FPGAIO_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_MPS2_FPGAIO_GPIO))

DT_COMPAT_ARM_MPS3_FPGAIO_GPIO := arm,mps3-fpgaio-gpio

config DT_HAS_ARM_MPS3_FPGAIO_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_MPS3_FPGAIO_GPIO))

DT_COMPAT_ARM_PL011 := arm,pl011

config DT_HAS_ARM_PL011_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_PL011))

DT_COMPAT_ARM_PL022 := arm,pl022

config DT_HAS_ARM_PL022_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_PL022))

DT_COMPAT_ARM_PSCI_0_2 := arm,psci-0.2

config DT_HAS_ARM_PSCI_0_2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_PSCI_0_2))

DT_COMPAT_ARM_PSCI_1_1 := arm,psci-1.1

config DT_HAS_ARM_PSCI_1_1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_PSCI_1_1))

DT_COMPAT_ARM_SBSA_UART := arm,sbsa-uart

config DT_HAS_ARM_SBSA_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_SBSA_UART))

DT_COMPAT_ARM_SCC := arm,scc

config DT_HAS_ARM_SCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_SCC))

DT_COMPAT_ARM_V6M_NVIC := arm,v6m-nvic

config DT_HAS_ARM_V6M_NVIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_V6M_NVIC))

DT_COMPAT_ARM_V7M_NVIC := arm,v7m-nvic

config DT_HAS_ARM_V7M_NVIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_V7M_NVIC))

DT_COMPAT_ARM_V8_1M_NVIC := arm,v8.1m-nvic

config DT_HAS_ARM_V8_1M_NVIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_V8_1M_NVIC))

DT_COMPAT_ARM_V8M_NVIC := arm,v8m-nvic

config DT_HAS_ARM_V8M_NVIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_V8M_NVIC))

DT_COMPAT_ARM_VERSATILE_I2C := arm,versatile-i2c

config DT_HAS_ARM_VERSATILE_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ARM_VERSATILE_I2C))

DT_COMPAT_ASAHI_KASEI_AK8975 := asahi-kasei,ak8975

config DT_HAS_ASAHI_KASEI_AK8975_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ASAHI_KASEI_AK8975))

DT_COMPAT_ASAHI_KASEI_AKM09918C := asahi-kasei,akm09918c

config DT_HAS_ASAHI_KASEI_AKM09918C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ASAHI_KASEI_AKM09918C))

DT_COMPAT_ASMEDIA_ASM2364 := asmedia,asm2364

config DT_HAS_ASMEDIA_ASM2364_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ASMEDIA_ASM2364))

DT_COMPAT_ASPEED_AST10X0_CLOCK := aspeed,ast10x0-clock

config DT_HAS_ASPEED_AST10X0_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ASPEED_AST10X0_CLOCK))

DT_COMPAT_ASPEED_AST10X0_RESET := aspeed,ast10x0-reset

config DT_HAS_ASPEED_AST10X0_RESET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ASPEED_AST10X0_RESET))

DT_COMPAT_ATMEL_AT24 := atmel,at24

config DT_HAS_ATMEL_AT24_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_AT24))

DT_COMPAT_ATMEL_AT24MAC402 := atmel,at24mac402

config DT_HAS_ATMEL_AT24MAC402_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_AT24MAC402))

DT_COMPAT_ATMEL_AT25 := atmel,at25

config DT_HAS_ATMEL_AT25_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_AT25))

DT_COMPAT_ATMEL_AT45 := atmel,at45

config DT_HAS_ATMEL_AT45_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_AT45))

DT_COMPAT_ATMEL_ATAES132A := atmel,ataes132a

config DT_HAS_ATMEL_ATAES132A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_ATAES132A))

DT_COMPAT_ATMEL_RF2XX := atmel,rf2xx

config DT_HAS_ATMEL_RF2XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_RF2XX))

DT_COMPAT_ATMEL_SAM_ADC := atmel,sam-adc

config DT_HAS_ATMEL_SAM_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_ADC))

DT_COMPAT_ATMEL_SAM_AFEC := atmel,sam-afec

config DT_HAS_ATMEL_SAM_AFEC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_AFEC))

DT_COMPAT_ATMEL_SAM_CAN := atmel,sam-can

config DT_HAS_ATMEL_SAM_CAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_CAN))

DT_COMPAT_ATMEL_SAM_DAC := atmel,sam-dac

config DT_HAS_ATMEL_SAM_DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_DAC))

DT_COMPAT_ATMEL_SAM_FLASH_CONTROLLER := atmel,sam-flash-controller

config DT_HAS_ATMEL_SAM_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_FLASH_CONTROLLER))

DT_COMPAT_ATMEL_SAM_GMAC := atmel,sam-gmac

config DT_HAS_ATMEL_SAM_GMAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_GMAC))

DT_COMPAT_ATMEL_SAM_GPIO := atmel,sam-gpio

config DT_HAS_ATMEL_SAM_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_GPIO))

DT_COMPAT_ATMEL_SAM_I2C_TWI := atmel,sam-i2c-twi

config DT_HAS_ATMEL_SAM_I2C_TWI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_I2C_TWI))

DT_COMPAT_ATMEL_SAM_I2C_TWIHS := atmel,sam-i2c-twihs

config DT_HAS_ATMEL_SAM_I2C_TWIHS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_I2C_TWIHS))

DT_COMPAT_ATMEL_SAM_I2C_TWIM := atmel,sam-i2c-twim

config DT_HAS_ATMEL_SAM_I2C_TWIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_I2C_TWIM))

DT_COMPAT_ATMEL_SAM_MDIO := atmel,sam-mdio

config DT_HAS_ATMEL_SAM_MDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_MDIO))

DT_COMPAT_ATMEL_SAM_PINCTRL := atmel,sam-pinctrl

config DT_HAS_ATMEL_SAM_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_PINCTRL))

DT_COMPAT_ATMEL_SAM_PMC := atmel,sam-pmc

config DT_HAS_ATMEL_SAM_PMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_PMC))

DT_COMPAT_ATMEL_SAM_PWM := atmel,sam-pwm

config DT_HAS_ATMEL_SAM_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_PWM))

DT_COMPAT_ATMEL_SAM_RSTC := atmel,sam-rstc

config DT_HAS_ATMEL_SAM_RSTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_RSTC))

DT_COMPAT_ATMEL_SAM_SMC := atmel,sam-smc

config DT_HAS_ATMEL_SAM_SMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_SMC))

DT_COMPAT_ATMEL_SAM_SPI := atmel,sam-spi

config DT_HAS_ATMEL_SAM_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_SPI))

DT_COMPAT_ATMEL_SAM_SSC := atmel,sam-ssc

config DT_HAS_ATMEL_SAM_SSC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_SSC))

DT_COMPAT_ATMEL_SAM_TC := atmel,sam-tc

config DT_HAS_ATMEL_SAM_TC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_TC))

DT_COMPAT_ATMEL_SAM_TC_QDEC := atmel,sam-tc-qdec

config DT_HAS_ATMEL_SAM_TC_QDEC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_TC_QDEC))

DT_COMPAT_ATMEL_SAM_TRNG := atmel,sam-trng

config DT_HAS_ATMEL_SAM_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_TRNG))

DT_COMPAT_ATMEL_SAM_UART := atmel,sam-uart

config DT_HAS_ATMEL_SAM_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_UART))

DT_COMPAT_ATMEL_SAM_USART := atmel,sam-usart

config DT_HAS_ATMEL_SAM_USART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_USART))

DT_COMPAT_ATMEL_SAM_USBC := atmel,sam-usbc

config DT_HAS_ATMEL_SAM_USBC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_USBC))

DT_COMPAT_ATMEL_SAM_USBHS := atmel,sam-usbhs

config DT_HAS_ATMEL_SAM_USBHS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_USBHS))

DT_COMPAT_ATMEL_SAM_WATCHDOG := atmel,sam-watchdog

config DT_HAS_ATMEL_SAM_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_WATCHDOG))

DT_COMPAT_ATMEL_SAM_XDMAC := atmel,sam-xdmac

config DT_HAS_ATMEL_SAM_XDMAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM_XDMAC))

DT_COMPAT_ATMEL_SAM0_ADC := atmel,sam0-adc

config DT_HAS_ATMEL_SAM0_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_ADC))

DT_COMPAT_ATMEL_SAM0_CAN := atmel,sam0-can

config DT_HAS_ATMEL_SAM0_CAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_CAN))

DT_COMPAT_ATMEL_SAM0_DAC := atmel,sam0-dac

config DT_HAS_ATMEL_SAM0_DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_DAC))

DT_COMPAT_ATMEL_SAM0_DMAC := atmel,sam0-dmac

config DT_HAS_ATMEL_SAM0_DMAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_DMAC))

DT_COMPAT_ATMEL_SAM0_EIC := atmel,sam0-eic

config DT_HAS_ATMEL_SAM0_EIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_EIC))

DT_COMPAT_ATMEL_SAM0_GMAC := atmel,sam0-gmac

config DT_HAS_ATMEL_SAM0_GMAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_GMAC))

DT_COMPAT_ATMEL_SAM0_GPIO := atmel,sam0-gpio

config DT_HAS_ATMEL_SAM0_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_GPIO))

DT_COMPAT_ATMEL_SAM0_I2C := atmel,sam0-i2c

config DT_HAS_ATMEL_SAM0_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_I2C))

DT_COMPAT_ATMEL_SAM0_ID := atmel,sam0-id

config DT_HAS_ATMEL_SAM0_ID_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_ID))

DT_COMPAT_ATMEL_SAM0_NVMCTRL := atmel,sam0-nvmctrl

config DT_HAS_ATMEL_SAM0_NVMCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_NVMCTRL))

DT_COMPAT_ATMEL_SAM0_PINCTRL := atmel,sam0-pinctrl

config DT_HAS_ATMEL_SAM0_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_PINCTRL))

DT_COMPAT_ATMEL_SAM0_PINMUX := atmel,sam0-pinmux

config DT_HAS_ATMEL_SAM0_PINMUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_PINMUX))

DT_COMPAT_ATMEL_SAM0_RTC := atmel,sam0-rtc

config DT_HAS_ATMEL_SAM0_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_RTC))

DT_COMPAT_ATMEL_SAM0_SERCOM := atmel,sam0-sercom

config DT_HAS_ATMEL_SAM0_SERCOM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_SERCOM))

DT_COMPAT_ATMEL_SAM0_SPI := atmel,sam0-spi

config DT_HAS_ATMEL_SAM0_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_SPI))

DT_COMPAT_ATMEL_SAM0_TC32 := atmel,sam0-tc32

config DT_HAS_ATMEL_SAM0_TC32_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_TC32))

DT_COMPAT_ATMEL_SAM0_TCC_PWM := atmel,sam0-tcc-pwm

config DT_HAS_ATMEL_SAM0_TCC_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_TCC_PWM))

DT_COMPAT_ATMEL_SAM0_UART := atmel,sam0-uart

config DT_HAS_ATMEL_SAM0_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_UART))

DT_COMPAT_ATMEL_SAM0_USB := atmel,sam0-usb

config DT_HAS_ATMEL_SAM0_USB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_USB))

DT_COMPAT_ATMEL_SAM0_WATCHDOG := atmel,sam0-watchdog

config DT_HAS_ATMEL_SAM0_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM0_WATCHDOG))

DT_COMPAT_ATMEL_SAM4L_FLASHCALW_CONTROLLER := atmel,sam4l-flashcalw-controller

config DT_HAS_ATMEL_SAM4L_FLASHCALW_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM4L_FLASHCALW_CONTROLLER))

DT_COMPAT_ATMEL_SAM4L_GPIO := atmel,sam4l-gpio

config DT_HAS_ATMEL_SAM4L_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM4L_GPIO))

DT_COMPAT_ATMEL_SAM4L_UID := atmel,sam4l-uid

config DT_HAS_ATMEL_SAM4L_UID_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAM4L_UID))

DT_COMPAT_ATMEL_SAMC2X_GCLK := atmel,samc2x-gclk

config DT_HAS_ATMEL_SAMC2X_GCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAMC2X_GCLK))

DT_COMPAT_ATMEL_SAMC2X_MCLK := atmel,samc2x-mclk

config DT_HAS_ATMEL_SAMC2X_MCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAMC2X_MCLK))

DT_COMPAT_ATMEL_SAMD2X_GCLK := atmel,samd2x-gclk

config DT_HAS_ATMEL_SAMD2X_GCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAMD2X_GCLK))

DT_COMPAT_ATMEL_SAMD2X_PM := atmel,samd2x-pm

config DT_HAS_ATMEL_SAMD2X_PM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAMD2X_PM))

DT_COMPAT_ATMEL_SAMD5X_GCLK := atmel,samd5x-gclk

config DT_HAS_ATMEL_SAMD5X_GCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAMD5X_GCLK))

DT_COMPAT_ATMEL_SAMD5X_MCLK := atmel,samd5x-mclk

config DT_HAS_ATMEL_SAMD5X_MCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAMD5X_MCLK))

DT_COMPAT_ATMEL_SAML2X_GCLK := atmel,saml2x-gclk

config DT_HAS_ATMEL_SAML2X_GCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAML2X_GCLK))

DT_COMPAT_ATMEL_SAML2X_MCLK := atmel,saml2x-mclk

config DT_HAS_ATMEL_SAML2X_MCLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_SAML2X_MCLK))

DT_COMPAT_ATMEL_WINC1500 := atmel,winc1500

config DT_HAS_ATMEL_WINC1500_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_WINC1500))

DT_COMPAT_ATMEL_XPLAINED_HEADER := atmel-xplained-header

config DT_HAS_ATMEL_XPLAINED_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_XPLAINED_HEADER))

DT_COMPAT_ATMEL_XPLAINED_PRO_HEADER := atmel-xplained-pro-header

config DT_HAS_ATMEL_XPLAINED_PRO_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ATMEL_XPLAINED_PRO_HEADER))

DT_COMPAT_AVAGO_APDS9960 := avago,apds9960

config DT_HAS_AVAGO_APDS9960_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_AVAGO_APDS9960))

DT_COMPAT_BOSCH_BMA280 := bosch,bma280

config DT_HAS_BOSCH_BMA280_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMA280))

DT_COMPAT_BOSCH_BMC150_MAGN := bosch,bmc150_magn

config DT_HAS_BOSCH_BMC150_MAGN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMC150_MAGN))

DT_COMPAT_BOSCH_BME280 := bosch,bme280

config DT_HAS_BOSCH_BME280_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BME280))

DT_COMPAT_BOSCH_BME680 := bosch,bme680

config DT_HAS_BOSCH_BME680_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BME680))

DT_COMPAT_BOSCH_BMG160 := bosch,bmg160

config DT_HAS_BOSCH_BMG160_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMG160))

DT_COMPAT_BOSCH_BMI08X_ACCEL := bosch,bmi08x-accel

config DT_HAS_BOSCH_BMI08X_ACCEL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMI08X_ACCEL))

DT_COMPAT_BOSCH_BMI08X_GYRO := bosch,bmi08x-gyro

config DT_HAS_BOSCH_BMI08X_GYRO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMI08X_GYRO))

DT_COMPAT_BOSCH_BMI160 := bosch,bmi160

config DT_HAS_BOSCH_BMI160_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMI160))

DT_COMPAT_BOSCH_BMI270 := bosch,bmi270

config DT_HAS_BOSCH_BMI270_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMI270))

DT_COMPAT_BOSCH_BMI323 := bosch,bmi323

config DT_HAS_BOSCH_BMI323_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMI323))

DT_COMPAT_BOSCH_BMM150 := bosch,bmm150

config DT_HAS_BOSCH_BMM150_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMM150))

DT_COMPAT_BOSCH_BMP388 := bosch,bmp388

config DT_HAS_BOSCH_BMP388_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BOSCH_BMP388))

DT_COMPAT_BRCM_IPROC_PAX_DMA_V1 := brcm,iproc-pax-dma-v1

config DT_HAS_BRCM_IPROC_PAX_DMA_V1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BRCM_IPROC_PAX_DMA_V1))

DT_COMPAT_BRCM_IPROC_PAX_DMA_V2 := brcm,iproc-pax-dma-v2

config DT_HAS_BRCM_IPROC_PAX_DMA_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BRCM_IPROC_PAX_DMA_V2))

DT_COMPAT_BRCM_IPROC_PCIE_EP := brcm,iproc-pcie-ep

config DT_HAS_BRCM_IPROC_PCIE_EP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_BRCM_IPROC_PCIE_EP))

DT_COMPAT_CAF_AGGREGATOR := caf,aggregator

config DT_HAS_CAF_AGGREGATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CAF_AGGREGATOR))

DT_COMPAT_CAN_TRANSCEIVER_GPIO := can-transceiver-gpio

config DT_HAS_CAN_TRANSCEIVER_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CAN_TRANSCEIVER_GPIO))

DT_COMPAT_CDNS_I3C := cdns,i3c

config DT_HAS_CDNS_I3C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_I3C))

DT_COMPAT_CDNS_QSPI_NOR := cdns,qspi-nor

config DT_HAS_CDNS_QSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_QSPI_NOR))

DT_COMPAT_CDNS_TENSILICA_XTENSA_LX3 := cdns,tensilica-xtensa-lx3

config DT_HAS_CDNS_TENSILICA_XTENSA_LX3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_TENSILICA_XTENSA_LX3))

DT_COMPAT_CDNS_TENSILICA_XTENSA_LX4 := cdns,tensilica-xtensa-lx4

config DT_HAS_CDNS_TENSILICA_XTENSA_LX4_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_TENSILICA_XTENSA_LX4))

DT_COMPAT_CDNS_TENSILICA_XTENSA_LX6 := cdns,tensilica-xtensa-lx6

config DT_HAS_CDNS_TENSILICA_XTENSA_LX6_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_TENSILICA_XTENSA_LX6))

DT_COMPAT_CDNS_TENSILICA_XTENSA_LX7 := cdns,tensilica-xtensa-lx7

config DT_HAS_CDNS_TENSILICA_XTENSA_LX7_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_TENSILICA_XTENSA_LX7))

DT_COMPAT_CDNS_UART := cdns,uart

config DT_HAS_CDNS_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_UART))

DT_COMPAT_CDNS_XTENSA_CORE_INTC := cdns,xtensa-core-intc

config DT_HAS_CDNS_XTENSA_CORE_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CDNS_XTENSA_CORE_INTC))

DT_COMPAT_CIRRUS_CS47L63 := cirrus,cs47l63

config DT_HAS_CIRRUS_CS47L63_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CIRRUS_CS47L63))

DT_COMPAT_CURRENT_SENSE_AMPLIFIER := current-sense-amplifier

config DT_HAS_CURRENT_SENSE_AMPLIFIER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CURRENT_SENSE_AMPLIFIER))

DT_COMPAT_CURRENT_SENSE_SHUNT := current-sense-shunt

config DT_HAS_CURRENT_SENSE_SHUNT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CURRENT_SENSE_SHUNT))

DT_COMPAT_CYPRESS_CY8C95XX_GPIO := cypress,cy8c95xx-gpio

config DT_HAS_CYPRESS_CY8C95XX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_CY8C95XX_GPIO))

DT_COMPAT_CYPRESS_CY8C95XX_GPIO_PORT := cypress,cy8c95xx-gpio-port

config DT_HAS_CYPRESS_CY8C95XX_GPIO_PORT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_CY8C95XX_GPIO_PORT))

DT_COMPAT_CYPRESS_PSOC6_FLASH_CONTROLLER := cypress,psoc6-flash-controller

config DT_HAS_CYPRESS_PSOC6_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_FLASH_CONTROLLER))

DT_COMPAT_CYPRESS_PSOC6_GPIO := cypress,psoc6-gpio

config DT_HAS_CYPRESS_PSOC6_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_GPIO))

DT_COMPAT_CYPRESS_PSOC6_HSIOM := cypress,psoc6-hsiom

config DT_HAS_CYPRESS_PSOC6_HSIOM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_HSIOM))

DT_COMPAT_CYPRESS_PSOC6_INTMUX := cypress,psoc6-intmux

config DT_HAS_CYPRESS_PSOC6_INTMUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_INTMUX))

DT_COMPAT_CYPRESS_PSOC6_INTMUX_CH := cypress,psoc6-intmux-ch

config DT_HAS_CYPRESS_PSOC6_INTMUX_CH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_INTMUX_CH))

DT_COMPAT_CYPRESS_PSOC6_PINCTRL := cypress,psoc6-pinctrl

config DT_HAS_CYPRESS_PSOC6_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_PINCTRL))

DT_COMPAT_CYPRESS_PSOC6_SPI := cypress,psoc6-spi

config DT_HAS_CYPRESS_PSOC6_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_SPI))

DT_COMPAT_CYPRESS_PSOC6_UART := cypress,psoc6-uart

config DT_HAS_CYPRESS_PSOC6_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_UART))

DT_COMPAT_CYPRESS_PSOC6_UID := cypress,psoc6-uid

config DT_HAS_CYPRESS_PSOC6_UID_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_CYPRESS_PSOC6_UID))

DT_COMPAT_DECAWAVE_DW1000 := decawave,dw1000

config DT_HAS_DECAWAVE_DW1000_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_DECAWAVE_DW1000))

DT_COMPAT_DFROBOT_A01NYUB := dfrobot,a01nyub

config DT_HAS_DFROBOT_A01NYUB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_DFROBOT_A01NYUB))

DT_COMPAT_DIGILENT_PMOD := digilent,pmod

config DT_HAS_DIGILENT_PMOD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_DIGILENT_PMOD))

DT_COMPAT_DIODES_PI3USB9201 := diodes,pi3usb9201

config DT_HAS_DIODES_PI3USB9201_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_DIODES_PI3USB9201))

DT_COMPAT_EFINIX_SAPPHIRE_GPIO := efinix,sapphire-gpio

config DT_HAS_EFINIX_SAPPHIRE_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_EFINIX_SAPPHIRE_GPIO))

DT_COMPAT_EFINIX_SAPPHIRE_TIMER0 := efinix,sapphire-timer0

config DT_HAS_EFINIX_SAPPHIRE_TIMER0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_EFINIX_SAPPHIRE_TIMER0))

DT_COMPAT_EFINIX_SAPPHIRE_UART0 := efinix,sapphire-uart0

config DT_HAS_EFINIX_SAPPHIRE_UART0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_EFINIX_SAPPHIRE_UART0))

DT_COMPAT_EPCOS_B57861S0103A039 := epcos,b57861s0103a039

config DT_HAS_EPCOS_B57861S0103A039_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_EPCOS_B57861S0103A039))

DT_COMPAT_ESPRESSIF_ESP_AT := espressif,esp-at

config DT_HAS_ESPRESSIF_ESP_AT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP_AT))

DT_COMPAT_ESPRESSIF_ESP32_ADC := espressif,esp32-adc

config DT_HAS_ESPRESSIF_ESP32_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_ADC))

DT_COMPAT_ESPRESSIF_ESP32_DAC := espressif,esp32-dac

config DT_HAS_ESPRESSIF_ESP32_DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_DAC))

DT_COMPAT_ESPRESSIF_ESP32_ETH := espressif,esp32-eth

config DT_HAS_ESPRESSIF_ESP32_ETH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_ETH))

DT_COMPAT_ESPRESSIF_ESP32_FLASH_CONTROLLER := espressif,esp32-flash-controller

config DT_HAS_ESPRESSIF_ESP32_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_FLASH_CONTROLLER))

DT_COMPAT_ESPRESSIF_ESP32_GDMA := espressif,esp32-gdma

config DT_HAS_ESPRESSIF_ESP32_GDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_GDMA))

DT_COMPAT_ESPRESSIF_ESP32_GPIO := espressif,esp32-gpio

config DT_HAS_ESPRESSIF_ESP32_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_GPIO))

DT_COMPAT_ESPRESSIF_ESP32_I2C := espressif,esp32-i2c

config DT_HAS_ESPRESSIF_ESP32_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_I2C))

DT_COMPAT_ESPRESSIF_ESP32_INTC := espressif,esp32-intc

config DT_HAS_ESPRESSIF_ESP32_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_INTC))

DT_COMPAT_ESPRESSIF_ESP32_IPM := espressif,esp32-ipm

config DT_HAS_ESPRESSIF_ESP32_IPM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_IPM))

DT_COMPAT_ESPRESSIF_ESP32_LEDC := espressif,esp32-ledc

config DT_HAS_ESPRESSIF_ESP32_LEDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_LEDC))

DT_COMPAT_ESPRESSIF_ESP32_MCPWM := espressif,esp32-mcpwm

config DT_HAS_ESPRESSIF_ESP32_MCPWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_MCPWM))

DT_COMPAT_ESPRESSIF_ESP32_MDIO := espressif,esp32-mdio

config DT_HAS_ESPRESSIF_ESP32_MDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_MDIO))

DT_COMPAT_ESPRESSIF_ESP32_PCNT := espressif,esp32-pcnt

config DT_HAS_ESPRESSIF_ESP32_PCNT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_PCNT))

DT_COMPAT_ESPRESSIF_ESP32_PINCTRL := espressif,esp32-pinctrl

config DT_HAS_ESPRESSIF_ESP32_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_PINCTRL))

DT_COMPAT_ESPRESSIF_ESP32_RTC := espressif,esp32-rtc

config DT_HAS_ESPRESSIF_ESP32_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_RTC))

DT_COMPAT_ESPRESSIF_ESP32_RTC_TIMER := espressif,esp32-rtc-timer

config DT_HAS_ESPRESSIF_ESP32_RTC_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_RTC_TIMER))

DT_COMPAT_ESPRESSIF_ESP32_SPI := espressif,esp32-spi

config DT_HAS_ESPRESSIF_ESP32_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_SPI))

DT_COMPAT_ESPRESSIF_ESP32_SYSTIMER := espressif,esp32-systimer

config DT_HAS_ESPRESSIF_ESP32_SYSTIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_SYSTIMER))

DT_COMPAT_ESPRESSIF_ESP32_TEMP := espressif,esp32-temp

config DT_HAS_ESPRESSIF_ESP32_TEMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_TEMP))

DT_COMPAT_ESPRESSIF_ESP32_TIMER := espressif,esp32-timer

config DT_HAS_ESPRESSIF_ESP32_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_TIMER))

DT_COMPAT_ESPRESSIF_ESP32_TRNG := espressif,esp32-trng

config DT_HAS_ESPRESSIF_ESP32_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_TRNG))

DT_COMPAT_ESPRESSIF_ESP32_TWAI := espressif,esp32-twai

config DT_HAS_ESPRESSIF_ESP32_TWAI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_TWAI))

DT_COMPAT_ESPRESSIF_ESP32_UART := espressif,esp32-uart

config DT_HAS_ESPRESSIF_ESP32_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_UART))

DT_COMPAT_ESPRESSIF_ESP32_USB_SERIAL := espressif,esp32-usb-serial

config DT_HAS_ESPRESSIF_ESP32_USB_SERIAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_USB_SERIAL))

DT_COMPAT_ESPRESSIF_ESP32_WATCHDOG := espressif,esp32-watchdog

config DT_HAS_ESPRESSIF_ESP32_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_WATCHDOG))

DT_COMPAT_ESPRESSIF_ESP32_WIFI := espressif,esp32-wifi

config DT_HAS_ESPRESSIF_ESP32_WIFI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_ESP32_WIFI))

DT_COMPAT_ESPRESSIF_RISCV := espressif,riscv

config DT_HAS_ESPRESSIF_RISCV_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ESPRESSIF_RISCV))

DT_COMPAT_ETHERNET_PHY := ethernet-phy

config DT_HAS_ETHERNET_PHY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ETHERNET_PHY))

DT_COMPAT_FCS_FXL6408 := fcs,fxl6408

config DT_HAS_FCS_FXL6408_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FCS_FXL6408))

DT_COMPAT_FIXED_CLOCK := fixed-clock

config DT_HAS_FIXED_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FIXED_CLOCK))

DT_COMPAT_FIXED_FACTOR_CLOCK := fixed-factor-clock

config DT_HAS_FIXED_FACTOR_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FIXED_FACTOR_CLOCK))

DT_COMPAT_FIXED_PARTITIONS := fixed-partitions

config DT_HAS_FIXED_PARTITIONS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FIXED_PARTITIONS))

DT_COMPAT_FOCALTECH_FT5336 := focaltech,ft5336

config DT_HAS_FOCALTECH_FT5336_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FOCALTECH_FT5336))

DT_COMPAT_FSL_IMX21_I2C := fsl,imx21-i2c

config DT_HAS_FSL_IMX21_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FSL_IMX21_I2C))

DT_COMPAT_FSL_IMX27_PWM := fsl,imx27-pwm

config DT_HAS_FSL_IMX27_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FSL_IMX27_PWM))

DT_COMPAT_FTDI_FT800 := ftdi,ft800

config DT_HAS_FTDI_FT800_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FTDI_FT800))

DT_COMPAT_FUJITSU_MB85RCXX := fujitsu,mb85rcxx

config DT_HAS_FUJITSU_MB85RCXX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_FUJITSU_MB85RCXX))

DT_COMPAT_GAISLER_APBUART := gaisler,apbuart

config DT_HAS_GAISLER_APBUART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GAISLER_APBUART))

DT_COMPAT_GAISLER_GPTIMER := gaisler,gptimer

config DT_HAS_GAISLER_GPTIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GAISLER_GPTIMER))

DT_COMPAT_GAISLER_IRQMP := gaisler,irqmp

config DT_HAS_GAISLER_IRQMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GAISLER_IRQMP))

DT_COMPAT_GAISLER_LEON3 := gaisler,leon3

config DT_HAS_GAISLER_LEON3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GAISLER_LEON3))

DT_COMPAT_GD_GD32_ADC := gd,gd32-adc

config DT_HAS_GD_GD32_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_ADC))

DT_COMPAT_GD_GD32_AFIO := gd,gd32-afio

config DT_HAS_GD_GD32_AFIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_AFIO))

DT_COMPAT_GD_GD32_CCTL := gd,gd32-cctl

config DT_HAS_GD_GD32_CCTL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_CCTL))

DT_COMPAT_GD_GD32_DAC := gd,gd32-dac

config DT_HAS_GD_GD32_DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_DAC))

DT_COMPAT_GD_GD32_DMA := gd,gd32-dma

config DT_HAS_GD_GD32_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_DMA))

DT_COMPAT_GD_GD32_DMA_V1 := gd,gd32-dma-v1

config DT_HAS_GD_GD32_DMA_V1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_DMA_V1))

DT_COMPAT_GD_GD32_EXTI := gd,gd32-exti

config DT_HAS_GD_GD32_EXTI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_EXTI))

DT_COMPAT_GD_GD32_FLASH_CONTROLLER := gd,gd32-flash-controller

config DT_HAS_GD_GD32_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_FLASH_CONTROLLER))

DT_COMPAT_GD_GD32_FWDGT := gd,gd32-fwdgt

config DT_HAS_GD_GD32_FWDGT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_FWDGT))

DT_COMPAT_GD_GD32_GPIO := gd,gd32-gpio

config DT_HAS_GD_GD32_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_GPIO))

DT_COMPAT_GD_GD32_I2C := gd,gd32-i2c

config DT_HAS_GD_GD32_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_I2C))

DT_COMPAT_GD_GD32_NV_FLASH_V1 := gd,gd32-nv-flash-v1

config DT_HAS_GD_GD32_NV_FLASH_V1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_NV_FLASH_V1))

DT_COMPAT_GD_GD32_NV_FLASH_V2 := gd,gd32-nv-flash-v2

config DT_HAS_GD_GD32_NV_FLASH_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_NV_FLASH_V2))

DT_COMPAT_GD_GD32_NV_FLASH_V3 := gd,gd32-nv-flash-v3

config DT_HAS_GD_GD32_NV_FLASH_V3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_NV_FLASH_V3))

DT_COMPAT_GD_GD32_PINCTRL_AF := gd,gd32-pinctrl-af

config DT_HAS_GD_GD32_PINCTRL_AF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_PINCTRL_AF))

DT_COMPAT_GD_GD32_PINCTRL_AFIO := gd,gd32-pinctrl-afio

config DT_HAS_GD_GD32_PINCTRL_AFIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_PINCTRL_AFIO))

DT_COMPAT_GD_GD32_PWM := gd,gd32-pwm

config DT_HAS_GD_GD32_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_PWM))

DT_COMPAT_GD_GD32_RCTL := gd,gd32-rctl

config DT_HAS_GD_GD32_RCTL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_RCTL))

DT_COMPAT_GD_GD32_RCU := gd,gd32-rcu

config DT_HAS_GD_GD32_RCU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_RCU))

DT_COMPAT_GD_GD32_SPI := gd,gd32-spi

config DT_HAS_GD_GD32_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_SPI))

DT_COMPAT_GD_GD32_SYSCFG := gd,gd32-syscfg

config DT_HAS_GD_GD32_SYSCFG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_SYSCFG))

DT_COMPAT_GD_GD32_TIMER := gd,gd32-timer

config DT_HAS_GD_GD32_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_TIMER))

DT_COMPAT_GD_GD32_USART := gd,gd32-usart

config DT_HAS_GD_GD32_USART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_USART))

DT_COMPAT_GD_GD32_WWDGT := gd,gd32-wwdgt

config DT_HAS_GD_GD32_WWDGT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GD_GD32_WWDGT))

DT_COMPAT_GENERIC_FEM_TWO_CTRL_PINS := generic-fem-two-ctrl-pins

config DT_HAS_GENERIC_FEM_TWO_CTRL_PINS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GENERIC_FEM_TWO_CTRL_PINS))

DT_COMPAT_GENERIC_RADIO_COEX_ONE_WIRE := generic-radio-coex-one-wire

config DT_HAS_GENERIC_RADIO_COEX_ONE_WIRE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GENERIC_RADIO_COEX_ONE_WIRE))

DT_COMPAT_GENERIC_RADIO_COEX_THREE_WIRE := generic-radio-coex-three-wire

config DT_HAS_GENERIC_RADIO_COEX_THREE_WIRE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GENERIC_RADIO_COEX_THREE_WIRE))

DT_COMPAT_GOODIX_GT911 := goodix,gt911

config DT_HAS_GOODIX_GT911_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GOODIX_GT911))

DT_COMPAT_GPIO_I2C := gpio-i2c

config DT_HAS_GPIO_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GPIO_I2C))

DT_COMPAT_GPIO_KEYS := gpio-keys

config DT_HAS_GPIO_KEYS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GPIO_KEYS))

DT_COMPAT_GPIO_LEDS := gpio-leds

config DT_HAS_GPIO_LEDS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GPIO_LEDS))

DT_COMPAT_GPIO_QDEC := gpio-qdec

config DT_HAS_GPIO_QDEC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GPIO_QDEC))

DT_COMPAT_GPIO_RADIO_COEX := gpio-radio-coex

config DT_HAS_GPIO_RADIO_COEX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GPIO_RADIO_COEX))

DT_COMPAT_GREELED_LPD8803 := greeled,lpd8803

config DT_HAS_GREELED_LPD8803_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GREELED_LPD8803))

DT_COMPAT_GREELED_LPD8806 := greeled,lpd8806

config DT_HAS_GREELED_LPD8806_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GREELED_LPD8806))

DT_COMPAT_GROVE_HEADER := grove-header

config DT_HAS_GROVE_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_GROVE_HEADER))

DT_COMPAT_HAMAMATSU_S11059 := hamamatsu,s11059

config DT_HAS_HAMAMATSU_S11059_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HAMAMATSU_S11059))

DT_COMPAT_HIMAX_HX8394 := himax,hx8394

config DT_HAS_HIMAX_HX8394_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HIMAX_HX8394))

DT_COMPAT_HIT_HD44780 := hit,hd44780

config DT_HAS_HIT_HD44780_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HIT_HD44780))

DT_COMPAT_HOLTEK_HT16K33 := holtek,ht16k33

config DT_HAS_HOLTEK_HT16K33_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HOLTEK_HT16K33))

DT_COMPAT_HOLTEK_HT16K33_KEYSCAN := holtek,ht16k33-keyscan

config DT_HAS_HOLTEK_HT16K33_KEYSCAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HOLTEK_HT16K33_KEYSCAN))

DT_COMPAT_HONEYWELL_HMC5883L := honeywell,hmc5883l

config DT_HAS_HONEYWELL_HMC5883L_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HONEYWELL_HMC5883L))

DT_COMPAT_HONEYWELL_MPR := honeywell,mpr

config DT_HAS_HONEYWELL_MPR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HONEYWELL_MPR))

DT_COMPAT_HONEYWELL_SM351LT := honeywell,sm351lt

config DT_HAS_HONEYWELL_SM351LT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HONEYWELL_SM351LT))

DT_COMPAT_HOPERF_HP206C := hoperf,hp206c

config DT_HAS_HOPERF_HP206C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HOPERF_HP206C))

DT_COMPAT_HOPERF_TH02 := hoperf,th02

config DT_HAS_HOPERF_TH02_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HOPERF_TH02))

DT_COMPAT_HYNITRON_CST816S := hynitron,cst816s

config DT_HAS_HYNITRON_CST816S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HYNITRON_CST816S))

DT_COMPAT_HZGROW_R502A := hzgrow,r502a

config DT_HAS_HZGROW_R502A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_HZGROW_R502A))

DT_COMPAT_ILITEK_ILI9340 := ilitek,ili9340

config DT_HAS_ILITEK_ILI9340_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ILITEK_ILI9340))

DT_COMPAT_ILITEK_ILI9341 := ilitek,ili9341

config DT_HAS_ILITEK_ILI9341_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ILITEK_ILI9341))

DT_COMPAT_ILITEK_ILI9342C := ilitek,ili9342c

config DT_HAS_ILITEK_ILI9342C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ILITEK_ILI9342C))

DT_COMPAT_ILITEK_ILI9488 := ilitek,ili9488

config DT_HAS_ILITEK_ILI9488_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ILITEK_ILI9488))

DT_COMPAT_INFINEON_CAT1_ADC := infineon,cat1-adc

config DT_HAS_INFINEON_CAT1_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_ADC))

DT_COMPAT_INFINEON_CAT1_BLESS_HCI := infineon,cat1-bless-hci

config DT_HAS_INFINEON_CAT1_BLESS_HCI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_BLESS_HCI))

DT_COMPAT_INFINEON_CAT1_COUNTER := infineon,cat1-counter

config DT_HAS_INFINEON_CAT1_COUNTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_COUNTER))

DT_COMPAT_INFINEON_CAT1_FLASH_CONTROLLER := infineon,cat1-flash-controller

config DT_HAS_INFINEON_CAT1_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_FLASH_CONTROLLER))

DT_COMPAT_INFINEON_CAT1_GPIO := infineon,cat1-gpio

config DT_HAS_INFINEON_CAT1_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_GPIO))

DT_COMPAT_INFINEON_CAT1_I2C := infineon,cat1-i2c

config DT_HAS_INFINEON_CAT1_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_I2C))

DT_COMPAT_INFINEON_CAT1_PINCTRL := infineon,cat1-pinctrl

config DT_HAS_INFINEON_CAT1_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_PINCTRL))

DT_COMPAT_INFINEON_CAT1_SCB := infineon,cat1-scb

config DT_HAS_INFINEON_CAT1_SCB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_SCB))

DT_COMPAT_INFINEON_CAT1_UART := infineon,cat1-uart

config DT_HAS_INFINEON_CAT1_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_UART))

DT_COMPAT_INFINEON_CAT1_WATCHDOG := infineon,cat1-watchdog

config DT_HAS_INFINEON_CAT1_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CAT1_WATCHDOG))

DT_COMPAT_INFINEON_CYW43XXX_BT_HCI := infineon,cyw43xxx-bt-hci

config DT_HAS_INFINEON_CYW43XXX_BT_HCI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_CYW43XXX_BT_HCI))

DT_COMPAT_INFINEON_DPS310 := infineon,dps310

config DT_HAS_INFINEON_DPS310_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_DPS310))

DT_COMPAT_INFINEON_XMC4XXX_ADC := infineon,xmc4xxx-adc

config DT_HAS_INFINEON_XMC4XXX_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_ADC))

DT_COMPAT_INFINEON_XMC4XXX_CCU4_PWM := infineon,xmc4xxx-ccu4-pwm

config DT_HAS_INFINEON_XMC4XXX_CCU4_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_CCU4_PWM))

DT_COMPAT_INFINEON_XMC4XXX_CCU8_PWM := infineon,xmc4xxx-ccu8-pwm

config DT_HAS_INFINEON_XMC4XXX_CCU8_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_CCU8_PWM))

DT_COMPAT_INFINEON_XMC4XXX_DMA := infineon,xmc4xxx-dma

config DT_HAS_INFINEON_XMC4XXX_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_DMA))

DT_COMPAT_INFINEON_XMC4XXX_FLASH_CONTROLLER := infineon,xmc4xxx-flash-controller

config DT_HAS_INFINEON_XMC4XXX_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_FLASH_CONTROLLER))

DT_COMPAT_INFINEON_XMC4XXX_GPIO := infineon,xmc4xxx-gpio

config DT_HAS_INFINEON_XMC4XXX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_GPIO))

DT_COMPAT_INFINEON_XMC4XXX_I2C := infineon,xmc4xxx-i2c

config DT_HAS_INFINEON_XMC4XXX_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_I2C))

DT_COMPAT_INFINEON_XMC4XXX_INTC := infineon,xmc4xxx-intc

config DT_HAS_INFINEON_XMC4XXX_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_INTC))

DT_COMPAT_INFINEON_XMC4XXX_NV_FLASH := infineon,xmc4xxx-nv-flash

config DT_HAS_INFINEON_XMC4XXX_NV_FLASH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_NV_FLASH))

DT_COMPAT_INFINEON_XMC4XXX_PINCTRL := infineon,xmc4xxx-pinctrl

config DT_HAS_INFINEON_XMC4XXX_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_PINCTRL))

DT_COMPAT_INFINEON_XMC4XXX_SPI := infineon,xmc4xxx-spi

config DT_HAS_INFINEON_XMC4XXX_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_SPI))

DT_COMPAT_INFINEON_XMC4XXX_TEMP := infineon,xmc4xxx-temp

config DT_HAS_INFINEON_XMC4XXX_TEMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_TEMP))

DT_COMPAT_INFINEON_XMC4XXX_UART := infineon,xmc4xxx-uart

config DT_HAS_INFINEON_XMC4XXX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INFINEON_XMC4XXX_UART))

DT_COMPAT_INTEL_ACE_ART_COUNTER := intel,ace-art-counter

config DT_HAS_INTEL_ACE_ART_COUNTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ACE_ART_COUNTER))

DT_COMPAT_INTEL_ACE_INTC := intel,ace-intc

config DT_HAS_INTEL_ACE_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ACE_INTC))

DT_COMPAT_INTEL_ACE_RTC_COUNTER := intel,ace-rtc-counter

config DT_HAS_INTEL_ACE_RTC_COUNTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ACE_RTC_COUNTER))

DT_COMPAT_INTEL_ACE_TIMESTAMP := intel,ace-timestamp

config DT_HAS_INTEL_ACE_TIMESTAMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ACE_TIMESTAMP))

DT_COMPAT_INTEL_ADSP_COMMUNICATION_WIDGET := intel,adsp-communication-widget

config DT_HAS_INTEL_ADSP_COMMUNICATION_WIDGET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_COMMUNICATION_WIDGET))

DT_COMPAT_INTEL_ADSP_DFPMCCH := intel,adsp-dfpmcch

config DT_HAS_INTEL_ADSP_DFPMCCH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_DFPMCCH))

DT_COMPAT_INTEL_ADSP_DFPMCCU := intel,adsp-dfpmccu

config DT_HAS_INTEL_ADSP_DFPMCCU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_DFPMCCU))

DT_COMPAT_INTEL_ADSP_DMIC_VSS := intel,adsp-dmic-vss

config DT_HAS_INTEL_ADSP_DMIC_VSS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_DMIC_VSS))

DT_COMPAT_INTEL_ADSP_GPDMA := intel,adsp-gpdma

config DT_HAS_INTEL_ADSP_GPDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_GPDMA))

DT_COMPAT_INTEL_ADSP_HDA_DMIC_CAP := intel,adsp-hda-dmic-cap

config DT_HAS_INTEL_ADSP_HDA_DMIC_CAP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_HDA_DMIC_CAP))

DT_COMPAT_INTEL_ADSP_HDA_HOST_IN := intel,adsp-hda-host-in

config DT_HAS_INTEL_ADSP_HDA_HOST_IN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_HDA_HOST_IN))

DT_COMPAT_INTEL_ADSP_HDA_HOST_OUT := intel,adsp-hda-host-out

config DT_HAS_INTEL_ADSP_HDA_HOST_OUT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_HDA_HOST_OUT))

DT_COMPAT_INTEL_ADSP_HDA_LINK_IN := intel,adsp-hda-link-in

config DT_HAS_INTEL_ADSP_HDA_LINK_IN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_HDA_LINK_IN))

DT_COMPAT_INTEL_ADSP_HDA_LINK_OUT := intel,adsp-hda-link-out

config DT_HAS_INTEL_ADSP_HDA_LINK_OUT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_HDA_LINK_OUT))

DT_COMPAT_INTEL_ADSP_HDA_SSP_CAP := intel,adsp-hda-ssp-cap

config DT_HAS_INTEL_ADSP_HDA_SSP_CAP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_HDA_SSP_CAP))

DT_COMPAT_INTEL_ADSP_HOST_IPC := intel,adsp-host-ipc

config DT_HAS_INTEL_ADSP_HOST_IPC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_HOST_IPC))

DT_COMPAT_INTEL_ADSP_IDC := intel,adsp-idc

config DT_HAS_INTEL_ADSP_IDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_IDC))

DT_COMPAT_INTEL_ADSP_IMR := intel,adsp-imr

config DT_HAS_INTEL_ADSP_IMR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_IMR))

DT_COMPAT_INTEL_ADSP_MAILBOX := intel,adsp-mailbox

config DT_HAS_INTEL_ADSP_MAILBOX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_MAILBOX))

DT_COMPAT_INTEL_ADSP_MEM_WINDOW := intel,adsp-mem-window

config DT_HAS_INTEL_ADSP_MEM_WINDOW_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_MEM_WINDOW))

DT_COMPAT_INTEL_ADSP_MTL_TLB := intel,adsp-mtl-tlb

config DT_HAS_INTEL_ADSP_MTL_TLB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_MTL_TLB))

DT_COMPAT_INTEL_ADSP_POWER_DOMAIN := intel,adsp-power-domain

config DT_HAS_INTEL_ADSP_POWER_DOMAIN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_POWER_DOMAIN))

DT_COMPAT_INTEL_ADSP_SHA := intel,adsp-sha

config DT_HAS_INTEL_ADSP_SHA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_SHA))

DT_COMPAT_INTEL_ADSP_SHIM_CLKCTL := intel,adsp-shim-clkctl

config DT_HAS_INTEL_ADSP_SHIM_CLKCTL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_SHIM_CLKCTL))

DT_COMPAT_INTEL_ADSP_TIMER := intel,adsp-timer

config DT_HAS_INTEL_ADSP_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_TIMER))

DT_COMPAT_INTEL_ADSP_TLB := intel,adsp-tlb

config DT_HAS_INTEL_ADSP_TLB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_TLB))

DT_COMPAT_INTEL_ADSP_WATCHDOG := intel,adsp-watchdog

config DT_HAS_INTEL_ADSP_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ADSP_WATCHDOG))

DT_COMPAT_INTEL_AGILEX_CLOCK := intel,agilex-clock

config DT_HAS_INTEL_AGILEX_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_AGILEX_CLOCK))

DT_COMPAT_INTEL_AGILEX_SOCFPGA_SIP_SMC := intel,agilex-socfpga-sip-smc

config DT_HAS_INTEL_AGILEX_SOCFPGA_SIP_SMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_AGILEX_SOCFPGA_SIP_SMC))

DT_COMPAT_INTEL_AGILEX5_CLOCK := intel,agilex5-clock

config DT_HAS_INTEL_AGILEX5_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_AGILEX5_CLOCK))

DT_COMPAT_INTEL_ALDER_LAKE := intel,alder-lake

config DT_HAS_INTEL_ALDER_LAKE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ALDER_LAKE))

DT_COMPAT_INTEL_ALH_DAI := intel,alh-dai

config DT_HAS_INTEL_ALH_DAI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ALH_DAI))

DT_COMPAT_INTEL_APOLLO_LAKE := intel,apollo_lake

config DT_HAS_INTEL_APOLLO_LAKE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_APOLLO_LAKE))

DT_COMPAT_INTEL_ATOM := intel,atom

config DT_HAS_INTEL_ATOM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ATOM))

DT_COMPAT_INTEL_BLINKY_PWM := intel,blinky-pwm

config DT_HAS_INTEL_BLINKY_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_BLINKY_PWM))

DT_COMPAT_INTEL_CAVS_I2S := intel,cavs-i2s

config DT_HAS_INTEL_CAVS_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_CAVS_I2S))

DT_COMPAT_INTEL_CAVS_INTC := intel,cavs-intc

config DT_HAS_INTEL_CAVS_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_CAVS_INTC))

DT_COMPAT_INTEL_DAI_DMIC := intel,dai-dmic

config DT_HAS_INTEL_DAI_DMIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_DAI_DMIC))

DT_COMPAT_INTEL_E1000 := intel,e1000

config DT_HAS_INTEL_E1000_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_E1000))

DT_COMPAT_INTEL_ELKHART_LAKE := intel,elkhart_lake

config DT_HAS_INTEL_ELKHART_LAKE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ELKHART_LAKE))

DT_COMPAT_INTEL_GNA := intel,gna

config DT_HAS_INTEL_GNA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_GNA))

DT_COMPAT_INTEL_GPIO := intel,gpio

config DT_HAS_INTEL_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_GPIO))

DT_COMPAT_INTEL_HDA_DAI := intel,hda-dai

config DT_HAS_INTEL_HDA_DAI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_HDA_DAI))

DT_COMPAT_INTEL_HPET := intel,hpet

config DT_HAS_INTEL_HPET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_HPET))

DT_COMPAT_INTEL_IBECC := intel,ibecc

config DT_HAS_INTEL_IBECC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_IBECC))

DT_COMPAT_INTEL_IOAPIC := intel,ioapic

config DT_HAS_INTEL_IOAPIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_IOAPIC))

DT_COMPAT_INTEL_ISH := intel,ish

config DT_HAS_INTEL_ISH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_ISH))

DT_COMPAT_INTEL_LAKEMONT := intel,lakemont

config DT_HAS_INTEL_LAKEMONT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_LAKEMONT))

DT_COMPAT_INTEL_LOAPIC := intel,loapic

config DT_HAS_INTEL_LOAPIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_LOAPIC))

DT_COMPAT_INTEL_LPSS := intel,lpss

config DT_HAS_INTEL_LPSS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_LPSS))

DT_COMPAT_INTEL_MULTIBOOT_FRAMEBUFFER := intel,multiboot-framebuffer

config DT_HAS_INTEL_MULTIBOOT_FRAMEBUFFER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_MULTIBOOT_FRAMEBUFFER))

DT_COMPAT_INTEL_NIOSV := intel,niosv

config DT_HAS_INTEL_NIOSV_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_NIOSV))

DT_COMPAT_INTEL_PCH_SMBUS := intel,pch-smbus

config DT_HAS_INTEL_PCH_SMBUS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_PCH_SMBUS))

DT_COMPAT_INTEL_PCIE := intel,pcie

config DT_HAS_INTEL_PCIE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_PCIE))

DT_COMPAT_INTEL_PENWELL_SPI := intel,penwell-spi

config DT_HAS_INTEL_PENWELL_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_PENWELL_SPI))

DT_COMPAT_INTEL_RAPTOR_LAKE := intel,raptor-lake

config DT_HAS_INTEL_RAPTOR_LAKE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_RAPTOR_LAKE))

DT_COMPAT_INTEL_SEDI_I2C := intel,sedi-i2c

config DT_HAS_INTEL_SEDI_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_SEDI_I2C))

DT_COMPAT_INTEL_SEDI_UART := intel,sedi-uart

config DT_HAS_INTEL_SEDI_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_SEDI_UART))

DT_COMPAT_INTEL_SOCFPGA_RESET := intel,socfpga-reset

config DT_HAS_INTEL_SOCFPGA_RESET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_SOCFPGA_RESET))

DT_COMPAT_INTEL_SSP_DAI := intel,ssp-dai

config DT_HAS_INTEL_SSP_DAI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_SSP_DAI))

DT_COMPAT_INTEL_SSP_SSPBASE := intel,ssp-sspbase

config DT_HAS_INTEL_SSP_SSPBASE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_SSP_SSPBASE))

DT_COMPAT_INTEL_TCO_WDT := intel,tco-wdt

config DT_HAS_INTEL_TCO_WDT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_TCO_WDT))

DT_COMPAT_INTEL_VT_D := intel,vt-d

config DT_HAS_INTEL_VT_D_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_VT_D))

DT_COMPAT_INTEL_X86 := intel,x86

config DT_HAS_INTEL_X86_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INTEL_X86))

DT_COMPAT_INVENSENSE_ICM42605 := invensense,icm42605

config DT_HAS_INVENSENSE_ICM42605_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENSENSE_ICM42605))

DT_COMPAT_INVENSENSE_ICM42670 := invensense,icm42670

config DT_HAS_INVENSENSE_ICM42670_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENSENSE_ICM42670))

DT_COMPAT_INVENSENSE_ICM42688 := invensense,icm42688

config DT_HAS_INVENSENSE_ICM42688_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENSENSE_ICM42688))

DT_COMPAT_INVENSENSE_ICP10125 := invensense,icp10125

config DT_HAS_INVENSENSE_ICP10125_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENSENSE_ICP10125))

DT_COMPAT_INVENSENSE_MPU6050 := invensense,mpu6050

config DT_HAS_INVENSENSE_MPU6050_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENSENSE_MPU6050))

DT_COMPAT_INVENSENSE_MPU9250 := invensense,mpu9250

config DT_HAS_INVENSENSE_MPU9250_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENSENSE_MPU9250))

DT_COMPAT_INVENTEK_ESWIFI := inventek,eswifi

config DT_HAS_INVENTEK_ESWIFI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENTEK_ESWIFI))

DT_COMPAT_INVENTEK_ESWIFI_UART := inventek,eswifi-uart

config DT_HAS_INVENTEK_ESWIFI_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_INVENTEK_ESWIFI_UART))

DT_COMPAT_ISENTEK_IST8310 := isentek,ist8310

config DT_HAS_ISENTEK_IST8310_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ISENTEK_IST8310))

DT_COMPAT_ISIL_ISL29035 := isil,isl29035

config DT_HAS_ISIL_ISL29035_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ISIL_ISL29035))

DT_COMPAT_ISSI_IS31FL3216A := issi,is31fl3216a

config DT_HAS_ISSI_IS31FL3216A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ISSI_IS31FL3216A))

DT_COMPAT_ISSI_IS31FL3733 := issi,is31fl3733

config DT_HAS_ISSI_IS31FL3733_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ISSI_IS31FL3733))

DT_COMPAT_ITE_ENHANCE_I2C := ite,enhance-i2c

config DT_HAS_ITE_ENHANCE_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_ENHANCE_I2C))

DT_COMPAT_ITE_IT82XX2_USB := ite,it82xx2-usb

config DT_HAS_ITE_IT82XX2_USB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT82XX2_USB))

DT_COMPAT_ITE_IT8XXX2_ADC := ite,it8xxx2-adc

config DT_HAS_ITE_IT8XXX2_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_ADC))

DT_COMPAT_ITE_IT8XXX2_BBRAM := ite,it8xxx2-bbram

config DT_HAS_ITE_IT8XXX2_BBRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_BBRAM))

DT_COMPAT_ITE_IT8XXX2_ESPI := ite,it8xxx2-espi

config DT_HAS_ITE_IT8XXX2_ESPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_ESPI))

DT_COMPAT_ITE_IT8XXX2_FLASH_CONTROLLER := ite,it8xxx2-flash-controller

config DT_HAS_ITE_IT8XXX2_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_FLASH_CONTROLLER))

DT_COMPAT_ITE_IT8XXX2_GPIO := ite,it8xxx2-gpio

config DT_HAS_ITE_IT8XXX2_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_GPIO))

DT_COMPAT_ITE_IT8XXX2_GPIO_V2 := ite,it8xxx2-gpio-v2

config DT_HAS_ITE_IT8XXX2_GPIO_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_GPIO_V2))

DT_COMPAT_ITE_IT8XXX2_GPIOKSCAN := ite,it8xxx2-gpiokscan

config DT_HAS_ITE_IT8XXX2_GPIOKSCAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_GPIOKSCAN))

DT_COMPAT_ITE_IT8XXX2_I2C := ite,it8xxx2-i2c

config DT_HAS_ITE_IT8XXX2_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_I2C))

DT_COMPAT_ITE_IT8XXX2_ILM := ite,it8xxx2-ilm

config DT_HAS_ITE_IT8XXX2_ILM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_ILM))

DT_COMPAT_ITE_IT8XXX2_INTC := ite,it8xxx2-intc

config DT_HAS_ITE_IT8XXX2_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_INTC))

DT_COMPAT_ITE_IT8XXX2_INTC_V2 := ite,it8xxx2-intc-v2

config DT_HAS_ITE_IT8XXX2_INTC_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_INTC_V2))

DT_COMPAT_ITE_IT8XXX2_KSCAN := ite,it8xxx2-kscan

config DT_HAS_ITE_IT8XXX2_KSCAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_KSCAN))

DT_COMPAT_ITE_IT8XXX2_PECI := ite,it8xxx2-peci

config DT_HAS_ITE_IT8XXX2_PECI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_PECI))

DT_COMPAT_ITE_IT8XXX2_PINCTRL := ite,it8xxx2-pinctrl

config DT_HAS_ITE_IT8XXX2_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_PINCTRL))

DT_COMPAT_ITE_IT8XXX2_PINCTRL_FUNC := ite,it8xxx2-pinctrl-func

config DT_HAS_ITE_IT8XXX2_PINCTRL_FUNC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_PINCTRL_FUNC))

DT_COMPAT_ITE_IT8XXX2_PWM := ite,it8xxx2-pwm

config DT_HAS_ITE_IT8XXX2_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_PWM))

DT_COMPAT_ITE_IT8XXX2_PWMPRS := ite,it8xxx2-pwmprs

config DT_HAS_ITE_IT8XXX2_PWMPRS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_PWMPRS))

DT_COMPAT_ITE_IT8XXX2_SHA := ite,it8xxx2-sha

config DT_HAS_ITE_IT8XXX2_SHA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_SHA))

DT_COMPAT_ITE_IT8XXX2_SHI := ite,it8xxx2-shi

config DT_HAS_ITE_IT8XXX2_SHI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_SHI))

DT_COMPAT_ITE_IT8XXX2_SSPI := ite,it8xxx2-sspi

config DT_HAS_ITE_IT8XXX2_SSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_SSPI))

DT_COMPAT_ITE_IT8XXX2_TACH := ite,it8xxx2-tach

config DT_HAS_ITE_IT8XXX2_TACH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_TACH))

DT_COMPAT_ITE_IT8XXX2_TIMER := ite,it8xxx2-timer

config DT_HAS_ITE_IT8XXX2_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_TIMER))

DT_COMPAT_ITE_IT8XXX2_UART := ite,it8xxx2-uart

config DT_HAS_ITE_IT8XXX2_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_UART))

DT_COMPAT_ITE_IT8XXX2_USBPD := ite,it8xxx2-usbpd

config DT_HAS_ITE_IT8XXX2_USBPD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_USBPD))

DT_COMPAT_ITE_IT8XXX2_VCMP := ite,it8xxx2-vcmp

config DT_HAS_ITE_IT8XXX2_VCMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_VCMP))

DT_COMPAT_ITE_IT8XXX2_WATCHDOG := ite,it8xxx2-watchdog

config DT_HAS_ITE_IT8XXX2_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_WATCHDOG))

DT_COMPAT_ITE_IT8XXX2_WUC := ite,it8xxx2-wuc

config DT_HAS_ITE_IT8XXX2_WUC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_WUC))

DT_COMPAT_ITE_IT8XXX2_WUC_MAP := ite,it8xxx2-wuc-map

config DT_HAS_ITE_IT8XXX2_WUC_MAP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_IT8XXX2_WUC_MAP))

DT_COMPAT_ITE_RISCV_ITE := ite,riscv-ite

config DT_HAS_ITE_RISCV_ITE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ITE_RISCV_ITE))

DT_COMPAT_JEDEC_SPI_NOR := jedec,spi-nor

config DT_HAS_JEDEC_SPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_JEDEC_SPI_NOR))

DT_COMPAT_JHD_JHD1313 := jhd,jhd1313

config DT_HAS_JHD_JHD1313_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_JHD_JHD1313))

DT_COMPAT_KVASER_PCICAN := kvaser,pcican

config DT_HAS_KVASER_PCICAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_KVASER_PCICAN))

DT_COMPAT_LATTICE_ICE40_FPGA := lattice,ice40-fpga

config DT_HAS_LATTICE_ICE40_FPGA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LATTICE_ICE40_FPGA))

DT_COMPAT_LINARO_96B_LSCON_1V8 := linaro,96b-lscon-1v8

config DT_HAS_LINARO_96B_LSCON_1V8_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LINARO_96B_LSCON_1V8))

DT_COMPAT_LINARO_96B_LSCON_3V3 := linaro,96b-lscon-3v3

config DT_HAS_LINARO_96B_LSCON_3V3_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LINARO_96B_LSCON_3V3))

DT_COMPAT_LINARO_IVSHMEM_IPM := linaro,ivshmem-ipm

config DT_HAS_LINARO_IVSHMEM_IPM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LINARO_IVSHMEM_IPM))

DT_COMPAT_LITEX_CLK := litex,clk

config DT_HAS_LITEX_CLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_CLK))

DT_COMPAT_LITEX_CLKOUT := litex,clkout

config DT_HAS_LITEX_CLKOUT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_CLKOUT))

DT_COMPAT_LITEX_DNA0 := litex,dna0

config DT_HAS_LITEX_DNA0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_DNA0))

DT_COMPAT_LITEX_ETH0 := litex,eth0

config DT_HAS_LITEX_ETH0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_ETH0))

DT_COMPAT_LITEX_GPIO := litex,gpio

config DT_HAS_LITEX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_GPIO))

DT_COMPAT_LITEX_I2C := litex,i2c

config DT_HAS_LITEX_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_I2C))

DT_COMPAT_LITEX_I2S := litex,i2s

config DT_HAS_LITEX_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_I2S))

DT_COMPAT_LITEX_PRBS := litex,prbs

config DT_HAS_LITEX_PRBS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_PRBS))

DT_COMPAT_LITEX_PWM := litex,pwm

config DT_HAS_LITEX_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_PWM))

DT_COMPAT_LITEX_SPI := litex,spi

config DT_HAS_LITEX_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_SPI))

DT_COMPAT_LITEX_TIMER0 := litex,timer0

config DT_HAS_LITEX_TIMER0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_TIMER0))

DT_COMPAT_LITEX_UART0 := litex,uart0

config DT_HAS_LITEX_UART0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LITEX_UART0))

DT_COMPAT_LLTC_LTC1660 := lltc,ltc1660

config DT_HAS_LLTC_LTC1660_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LLTC_LTC1660))

DT_COMPAT_LLTC_LTC1665 := lltc,ltc1665

config DT_HAS_LLTC_LTC1665_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LLTC_LTC1665))

DT_COMPAT_LM75 := lm75

config DT_HAS_LM75_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LM75))

DT_COMPAT_LM77 := lm77

config DT_HAS_LM77_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LM77))

DT_COMPAT_LOWRISC_MACHINE_TIMER := lowrisc,machine-timer

config DT_HAS_LOWRISC_MACHINE_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LOWRISC_MACHINE_TIMER))

DT_COMPAT_LOWRISC_OPENTITAN_AONTIMER := lowrisc,opentitan-aontimer

config DT_HAS_LOWRISC_OPENTITAN_AONTIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LOWRISC_OPENTITAN_AONTIMER))

DT_COMPAT_LOWRISC_OPENTITAN_PWRMGR := lowrisc,opentitan-pwrmgr

config DT_HAS_LOWRISC_OPENTITAN_PWRMGR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LOWRISC_OPENTITAN_PWRMGR))

DT_COMPAT_LOWRISC_OPENTITAN_SPI := lowrisc,opentitan-spi

config DT_HAS_LOWRISC_OPENTITAN_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LOWRISC_OPENTITAN_SPI))

DT_COMPAT_LOWRISC_OPENTITAN_UART := lowrisc,opentitan-uart

config DT_HAS_LOWRISC_OPENTITAN_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_LOWRISC_OPENTITAN_UART))

DT_COMPAT_MAXIM_DS18B20 := maxim,ds18b20

config DT_HAS_MAXIM_DS18B20_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS18B20))

DT_COMPAT_MAXIM_DS2482_800 := maxim,ds2482-800

config DT_HAS_MAXIM_DS2482_800_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS2482_800))

DT_COMPAT_MAXIM_DS2482_800_CHANNEL := maxim,ds2482-800-channel

config DT_HAS_MAXIM_DS2482_800_CHANNEL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS2482_800_CHANNEL))

DT_COMPAT_MAXIM_DS2484 := maxim,ds2484

config DT_HAS_MAXIM_DS2484_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS2484))

DT_COMPAT_MAXIM_DS2485 := maxim,ds2485

config DT_HAS_MAXIM_DS2485_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS2485))

DT_COMPAT_MAXIM_DS3231 := maxim,ds3231

config DT_HAS_MAXIM_DS3231_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_DS3231))

DT_COMPAT_MAXIM_MAX11253 := maxim,max11253

config DT_HAS_MAXIM_MAX11253_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX11253))

DT_COMPAT_MAXIM_MAX11254 := maxim,max11254

config DT_HAS_MAXIM_MAX11254_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX11254))

DT_COMPAT_MAXIM_MAX17048 := maxim,max17048

config DT_HAS_MAXIM_MAX17048_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX17048))

DT_COMPAT_MAXIM_MAX17055 := maxim,max17055

config DT_HAS_MAXIM_MAX17055_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX17055))

DT_COMPAT_MAXIM_MAX17262 := maxim,max17262

config DT_HAS_MAXIM_MAX17262_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX17262))

DT_COMPAT_MAXIM_MAX30101 := maxim,max30101

config DT_HAS_MAXIM_MAX30101_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX30101))

DT_COMPAT_MAXIM_MAX31790 := maxim,max31790

config DT_HAS_MAXIM_MAX31790_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX31790))

DT_COMPAT_MAXIM_MAX31855 := maxim,max31855

config DT_HAS_MAXIM_MAX31855_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX31855))

DT_COMPAT_MAXIM_MAX31865 := maxim,max31865

config DT_HAS_MAXIM_MAX31865_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX31865))

DT_COMPAT_MAXIM_MAX31875 := maxim,max31875

config DT_HAS_MAXIM_MAX31875_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX31875))

DT_COMPAT_MAXIM_MAX3421E_SPI := maxim,max3421e_spi

config DT_HAS_MAXIM_MAX3421E_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX3421E_SPI))

DT_COMPAT_MAXIM_MAX44009 := maxim,max44009

config DT_HAS_MAXIM_MAX44009_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX44009))

DT_COMPAT_MAXIM_MAX6675 := maxim,max6675

config DT_HAS_MAXIM_MAX6675_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX6675))

DT_COMPAT_MAXIM_MAX7219 := maxim,max7219

config DT_HAS_MAXIM_MAX7219_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MAXIM_MAX7219))

DT_COMPAT_MEAS_MS5607 := meas,ms5607

config DT_HAS_MEAS_MS5607_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MEAS_MS5607))

DT_COMPAT_MEAS_MS5837 := meas,ms5837

config DT_HAS_MEAS_MS5837_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MEAS_MS5837))

DT_COMPAT_MICROBIT_EDGE_CONNECTOR := microbit,edge-connector

config DT_HAS_MICROBIT_EDGE_CONNECTOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROBIT_EDGE_CONNECTOR))

DT_COMPAT_MICROCHIP_CAP1203 := microchip,cap1203

config DT_HAS_MICROCHIP_CAP1203_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_CAP1203))

DT_COMPAT_MICROCHIP_COREUART := microchip,coreuart

config DT_HAS_MICROCHIP_COREUART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_COREUART))

DT_COMPAT_MICROCHIP_ENC28J60 := microchip,enc28j60

config DT_HAS_MICROCHIP_ENC28J60_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_ENC28J60))

DT_COMPAT_MICROCHIP_ENC424J600 := microchip,enc424j600

config DT_HAS_MICROCHIP_ENC424J600_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_ENC424J600))

DT_COMPAT_MICROCHIP_KSZ8794 := microchip,ksz8794

config DT_HAS_MICROCHIP_KSZ8794_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_KSZ8794))

DT_COMPAT_MICROCHIP_KSZ8863 := microchip,ksz8863

config DT_HAS_MICROCHIP_KSZ8863_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_KSZ8863))

DT_COMPAT_MICROCHIP_MCP230XX := microchip,mcp230xx

config DT_HAS_MICROCHIP_MCP230XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP230XX))

DT_COMPAT_MICROCHIP_MCP23S17 := microchip,mcp23s17

config DT_HAS_MICROCHIP_MCP23S17_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP23S17))

DT_COMPAT_MICROCHIP_MCP23SXX := microchip,mcp23sxx

config DT_HAS_MICROCHIP_MCP23SXX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP23SXX))

DT_COMPAT_MICROCHIP_MCP2515 := microchip,mcp2515

config DT_HAS_MICROCHIP_MCP2515_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP2515))

DT_COMPAT_MICROCHIP_MCP3204 := microchip,mcp3204

config DT_HAS_MICROCHIP_MCP3204_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP3204))

DT_COMPAT_MICROCHIP_MCP3208 := microchip,mcp3208

config DT_HAS_MICROCHIP_MCP3208_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP3208))

DT_COMPAT_MICROCHIP_MCP4725 := microchip,mcp4725

config DT_HAS_MICROCHIP_MCP4725_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP4725))

DT_COMPAT_MICROCHIP_MCP4728 := microchip,mcp4728

config DT_HAS_MICROCHIP_MCP4728_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP4728))

DT_COMPAT_MICROCHIP_MCP7940N := microchip,mcp7940n

config DT_HAS_MICROCHIP_MCP7940N_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP7940N))

DT_COMPAT_MICROCHIP_MCP9600 := microchip,mcp9600

config DT_HAS_MICROCHIP_MCP9600_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP9600))

DT_COMPAT_MICROCHIP_MCP970X := microchip,mcp970x

config DT_HAS_MICROCHIP_MCP970X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP970X))

DT_COMPAT_MICROCHIP_MCP9808 := microchip,mcp9808

config DT_HAS_MICROCHIP_MCP9808_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MCP9808))

DT_COMPAT_MICROCHIP_MPFS_GPIO := microchip,mpfs-gpio

config DT_HAS_MICROCHIP_MPFS_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MPFS_GPIO))

DT_COMPAT_MICROCHIP_MPFS_I2C := microchip,mpfs-i2c

config DT_HAS_MICROCHIP_MPFS_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MPFS_I2C))

DT_COMPAT_MICROCHIP_MPFS_QSPI := microchip,mpfs-qspi

config DT_HAS_MICROCHIP_MPFS_QSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_MPFS_QSPI))

DT_COMPAT_MICROCHIP_TCN75A := microchip,tcn75a

config DT_HAS_MICROCHIP_TCN75A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_TCN75A))

DT_COMPAT_MICROCHIP_XEC_ADC := microchip,xec-adc

config DT_HAS_MICROCHIP_XEC_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ADC))

DT_COMPAT_MICROCHIP_XEC_BBLED := microchip,xec-bbled

config DT_HAS_MICROCHIP_XEC_BBLED_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_BBLED))

DT_COMPAT_MICROCHIP_XEC_BBRAM := microchip,xec-bbram

config DT_HAS_MICROCHIP_XEC_BBRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_BBRAM))

DT_COMPAT_MICROCHIP_XEC_DMAC := microchip,xec-dmac

config DT_HAS_MICROCHIP_XEC_DMAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_DMAC))

DT_COMPAT_MICROCHIP_XEC_ECIA := microchip,xec-ecia

config DT_HAS_MICROCHIP_XEC_ECIA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ECIA))

DT_COMPAT_MICROCHIP_XEC_ECIA_GIRQ := microchip,xec-ecia-girq

config DT_HAS_MICROCHIP_XEC_ECIA_GIRQ_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ECIA_GIRQ))

DT_COMPAT_MICROCHIP_XEC_ECS := microchip,xec-ecs

config DT_HAS_MICROCHIP_XEC_ECS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ECS))

DT_COMPAT_MICROCHIP_XEC_EEPROM := microchip,xec-eeprom

config DT_HAS_MICROCHIP_XEC_EEPROM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_EEPROM))

DT_COMPAT_MICROCHIP_XEC_ESPI := microchip,xec-espi

config DT_HAS_MICROCHIP_XEC_ESPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ESPI))

DT_COMPAT_MICROCHIP_XEC_ESPI_HOST_DEV := microchip,xec-espi-host-dev

config DT_HAS_MICROCHIP_XEC_ESPI_HOST_DEV_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ESPI_HOST_DEV))

DT_COMPAT_MICROCHIP_XEC_ESPI_SAF := microchip,xec-espi-saf

config DT_HAS_MICROCHIP_XEC_ESPI_SAF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ESPI_SAF))

DT_COMPAT_MICROCHIP_XEC_ESPI_SAF_V2 := microchip,xec-espi-saf-v2

config DT_HAS_MICROCHIP_XEC_ESPI_SAF_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ESPI_SAF_V2))

DT_COMPAT_MICROCHIP_XEC_ESPI_V2 := microchip,xec-espi-v2

config DT_HAS_MICROCHIP_XEC_ESPI_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ESPI_V2))

DT_COMPAT_MICROCHIP_XEC_ESPI_VW_ROUTING := microchip,xec-espi-vw-routing

config DT_HAS_MICROCHIP_XEC_ESPI_VW_ROUTING_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_ESPI_VW_ROUTING))

DT_COMPAT_MICROCHIP_XEC_GPIO := microchip,xec-gpio

config DT_HAS_MICROCHIP_XEC_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_GPIO))

DT_COMPAT_MICROCHIP_XEC_GPIO_V2 := microchip,xec-gpio-v2

config DT_HAS_MICROCHIP_XEC_GPIO_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_GPIO_V2))

DT_COMPAT_MICROCHIP_XEC_I2C := microchip,xec-i2c

config DT_HAS_MICROCHIP_XEC_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_I2C))

DT_COMPAT_MICROCHIP_XEC_I2C_V2 := microchip,xec-i2c-v2

config DT_HAS_MICROCHIP_XEC_I2C_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_I2C_V2))

DT_COMPAT_MICROCHIP_XEC_KSCAN := microchip,xec-kscan

config DT_HAS_MICROCHIP_XEC_KSCAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_KSCAN))

DT_COMPAT_MICROCHIP_XEC_PCR := microchip,xec-pcr

config DT_HAS_MICROCHIP_XEC_PCR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_PCR))

DT_COMPAT_MICROCHIP_XEC_PECI := microchip,xec-peci

config DT_HAS_MICROCHIP_XEC_PECI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_PECI))

DT_COMPAT_MICROCHIP_XEC_PINCTRL := microchip,xec-pinctrl

config DT_HAS_MICROCHIP_XEC_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_PINCTRL))

DT_COMPAT_MICROCHIP_XEC_PS2 := microchip,xec-ps2

config DT_HAS_MICROCHIP_XEC_PS2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_PS2))

DT_COMPAT_MICROCHIP_XEC_PWM := microchip,xec-pwm

config DT_HAS_MICROCHIP_XEC_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_PWM))

DT_COMPAT_MICROCHIP_XEC_PWMBBLED := microchip,xec-pwmbbled

config DT_HAS_MICROCHIP_XEC_PWMBBLED_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_PWMBBLED))

DT_COMPAT_MICROCHIP_XEC_QMSPI := microchip,xec-qmspi

config DT_HAS_MICROCHIP_XEC_QMSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_QMSPI))

DT_COMPAT_MICROCHIP_XEC_QMSPI_LDMA := microchip,xec-qmspi-ldma

config DT_HAS_MICROCHIP_XEC_QMSPI_LDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_QMSPI_LDMA))

DT_COMPAT_MICROCHIP_XEC_RTOS_TIMER := microchip,xec-rtos-timer

config DT_HAS_MICROCHIP_XEC_RTOS_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_RTOS_TIMER))

DT_COMPAT_MICROCHIP_XEC_SYMCR := microchip,xec-symcr

config DT_HAS_MICROCHIP_XEC_SYMCR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_SYMCR))

DT_COMPAT_MICROCHIP_XEC_TACH := microchip,xec-tach

config DT_HAS_MICROCHIP_XEC_TACH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_TACH))

DT_COMPAT_MICROCHIP_XEC_TIMER := microchip,xec-timer

config DT_HAS_MICROCHIP_XEC_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_TIMER))

DT_COMPAT_MICROCHIP_XEC_UART := microchip,xec-uart

config DT_HAS_MICROCHIP_XEC_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_UART))

DT_COMPAT_MICROCHIP_XEC_WATCHDOG := microchip,xec-watchdog

config DT_HAS_MICROCHIP_XEC_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICROCHIP_XEC_WATCHDOG))

DT_COMPAT_MICRON_MT25QU02G := micron,mt25qu02g

config DT_HAS_MICRON_MT25QU02G_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MICRON_MT25QU02G))

DT_COMPAT_MIKRO_BUS := mikro-bus

config DT_HAS_MIKRO_BUS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MIKRO_BUS))

DT_COMPAT_MMIO_SRAM := mmio-sram

config DT_HAS_MMIO_SRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MMIO_SRAM))

DT_COMPAT_MOTOROLA_MC146818 := motorola,mc146818

config DT_HAS_MOTOROLA_MC146818_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MOTOROLA_MC146818))

DT_COMPAT_MTI_CPU_INTC := mti,cpu-intc

config DT_HAS_MTI_CPU_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_MTI_CPU_INTC))

DT_COMPAT_NEORV32_CPU := neorv32-cpu

config DT_HAS_NEORV32_CPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NEORV32_CPU))

DT_COMPAT_NEORV32_GPIO := neorv32-gpio

config DT_HAS_NEORV32_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NEORV32_GPIO))

DT_COMPAT_NEORV32_MACHINE_TIMER := neorv32-machine-timer

config DT_HAS_NEORV32_MACHINE_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NEORV32_MACHINE_TIMER))

DT_COMPAT_NEORV32_TRNG := neorv32-trng

config DT_HAS_NEORV32_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NEORV32_TRNG))

DT_COMPAT_NEORV32_UART := neorv32-uart

config DT_HAS_NEORV32_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NEORV32_UART))

DT_COMPAT_NIOSV_MACHINE_TIMER := niosv-machine-timer

config DT_HAS_NIOSV_MACHINE_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NIOSV_MACHINE_TIMER))

DT_COMPAT_NORDIC_MBOX_NRF_IPC := nordic,mbox-nrf-ipc

config DT_HAS_NORDIC_MBOX_NRF_IPC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_MBOX_NRF_IPC))

DT_COMPAT_NORDIC_NPM1100 := nordic,npm1100

config DT_HAS_NORDIC_NPM1100_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM1100))

DT_COMPAT_NORDIC_NPM1300 := nordic,npm1300

config DT_HAS_NORDIC_NPM1300_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM1300))

DT_COMPAT_NORDIC_NPM1300_CHARGER := nordic,npm1300-charger

config DT_HAS_NORDIC_NPM1300_CHARGER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM1300_CHARGER))

DT_COMPAT_NORDIC_NPM1300_GPIO := nordic,npm1300-gpio

config DT_HAS_NORDIC_NPM1300_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM1300_GPIO))

DT_COMPAT_NORDIC_NPM1300_LED := nordic,npm1300-led

config DT_HAS_NORDIC_NPM1300_LED_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM1300_LED))

DT_COMPAT_NORDIC_NPM1300_REGULATOR := nordic,npm1300-regulator

config DT_HAS_NORDIC_NPM1300_REGULATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM1300_REGULATOR))

DT_COMPAT_NORDIC_NPM1300_WDT := nordic,npm1300-wdt

config DT_HAS_NORDIC_NPM1300_WDT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM1300_WDT))

DT_COMPAT_NORDIC_NPM6001 := nordic,npm6001

config DT_HAS_NORDIC_NPM6001_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM6001))

DT_COMPAT_NORDIC_NPM6001_GPIO := nordic,npm6001-gpio

config DT_HAS_NORDIC_NPM6001_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM6001_GPIO))

DT_COMPAT_NORDIC_NPM6001_REGULATOR := nordic,npm6001-regulator

config DT_HAS_NORDIC_NPM6001_REGULATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM6001_REGULATOR))

DT_COMPAT_NORDIC_NPM6001_WDT := nordic,npm6001-wdt

config DT_HAS_NORDIC_NPM6001_WDT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NPM6001_WDT))

DT_COMPAT_NORDIC_NRF_ACL := nordic,nrf-acl

config DT_HAS_NORDIC_NRF_ACL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_ACL))

DT_COMPAT_NORDIC_NRF_ADC := nordic,nrf-adc

config DT_HAS_NORDIC_NRF_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_ADC))

DT_COMPAT_NORDIC_NRF_BPROT := nordic,nrf-bprot

config DT_HAS_NORDIC_NRF_BPROT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_BPROT))

DT_COMPAT_NORDIC_NRF_CC310 := nordic,nrf-cc310

config DT_HAS_NORDIC_NRF_CC310_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_CC310))

DT_COMPAT_NORDIC_NRF_CC312 := nordic,nrf-cc312

config DT_HAS_NORDIC_NRF_CC312_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_CC312))

DT_COMPAT_NORDIC_NRF_CCM := nordic,nrf-ccm

config DT_HAS_NORDIC_NRF_CCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_CCM))

DT_COMPAT_NORDIC_NRF_CLOCK := nordic,nrf-clock

config DT_HAS_NORDIC_NRF_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_CLOCK))

DT_COMPAT_NORDIC_NRF_COMP := nordic,nrf-comp

config DT_HAS_NORDIC_NRF_COMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_COMP))

DT_COMPAT_NORDIC_NRF_CTRLAPPERI := nordic,nrf-ctrlapperi

config DT_HAS_NORDIC_NRF_CTRLAPPERI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_CTRLAPPERI))

DT_COMPAT_NORDIC_NRF_DCNF := nordic,nrf-dcnf

config DT_HAS_NORDIC_NRF_DCNF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_DCNF))

DT_COMPAT_NORDIC_NRF_DPPIC := nordic,nrf-dppic

config DT_HAS_NORDIC_NRF_DPPIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_DPPIC))

DT_COMPAT_NORDIC_NRF_ECB := nordic,nrf-ecb

config DT_HAS_NORDIC_NRF_ECB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_ECB))

DT_COMPAT_NORDIC_NRF_EGU := nordic,nrf-egu

config DT_HAS_NORDIC_NRF_EGU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_EGU))

DT_COMPAT_NORDIC_NRF_FICR := nordic,nrf-ficr

config DT_HAS_NORDIC_NRF_FICR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_FICR))

DT_COMPAT_NORDIC_NRF_GPIO := nordic,nrf-gpio

config DT_HAS_NORDIC_NRF_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_GPIO))

DT_COMPAT_NORDIC_NRF_GPIO_FORWARDER := nordic,nrf-gpio-forwarder

config DT_HAS_NORDIC_NRF_GPIO_FORWARDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_GPIO_FORWARDER))

DT_COMPAT_NORDIC_NRF_GPIOTE := nordic,nrf-gpiote

config DT_HAS_NORDIC_NRF_GPIOTE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_GPIOTE))

DT_COMPAT_NORDIC_NRF_GPREGRET := nordic,nrf-gpregret

config DT_HAS_NORDIC_NRF_GPREGRET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_GPREGRET))

DT_COMPAT_NORDIC_NRF_I2S := nordic,nrf-i2s

config DT_HAS_NORDIC_NRF_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_I2S))

DT_COMPAT_NORDIC_NRF_IEEE802154 := nordic,nrf-ieee802154

config DT_HAS_NORDIC_NRF_IEEE802154_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_IEEE802154))

DT_COMPAT_NORDIC_NRF_IPC := nordic,nrf-ipc

config DT_HAS_NORDIC_NRF_IPC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_IPC))

DT_COMPAT_NORDIC_NRF_IPC_UART := nordic,nrf-ipc-uart

config DT_HAS_NORDIC_NRF_IPC_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_IPC_UART))

DT_COMPAT_NORDIC_NRF_KMU := nordic,nrf-kmu

config DT_HAS_NORDIC_NRF_KMU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_KMU))

DT_COMPAT_NORDIC_NRF_LED_MATRIX := nordic,nrf-led-matrix

config DT_HAS_NORDIC_NRF_LED_MATRIX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_LED_MATRIX))

DT_COMPAT_NORDIC_NRF_LPCOMP := nordic,nrf-lpcomp

config DT_HAS_NORDIC_NRF_LPCOMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_LPCOMP))

DT_COMPAT_NORDIC_NRF_MPU := nordic,nrf-mpu

config DT_HAS_NORDIC_NRF_MPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_MPU))

DT_COMPAT_NORDIC_NRF_MUTEX := nordic,nrf-mutex

config DT_HAS_NORDIC_NRF_MUTEX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_MUTEX))

DT_COMPAT_NORDIC_NRF_MWU := nordic,nrf-mwu

config DT_HAS_NORDIC_NRF_MWU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_MWU))

DT_COMPAT_NORDIC_NRF_NFCT := nordic,nrf-nfct

config DT_HAS_NORDIC_NRF_NFCT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_NFCT))

DT_COMPAT_NORDIC_NRF_OSCILLATORS := nordic,nrf-oscillators

config DT_HAS_NORDIC_NRF_OSCILLATORS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_OSCILLATORS))

DT_COMPAT_NORDIC_NRF_PDM := nordic,nrf-pdm

config DT_HAS_NORDIC_NRF_PDM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_PDM))

DT_COMPAT_NORDIC_NRF_PINCTRL := nordic,nrf-pinctrl

config DT_HAS_NORDIC_NRF_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_PINCTRL))

DT_COMPAT_NORDIC_NRF_POWER := nordic,nrf-power

config DT_HAS_NORDIC_NRF_POWER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_POWER))

DT_COMPAT_NORDIC_NRF_PPI := nordic,nrf-ppi

config DT_HAS_NORDIC_NRF_PPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_PPI))

DT_COMPAT_NORDIC_NRF_PWM := nordic,nrf-pwm

config DT_HAS_NORDIC_NRF_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_PWM))

DT_COMPAT_NORDIC_NRF_QDEC := nordic,nrf-qdec

config DT_HAS_NORDIC_NRF_QDEC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_QDEC))

DT_COMPAT_NORDIC_NRF_QSPI := nordic,nrf-qspi

config DT_HAS_NORDIC_NRF_QSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_QSPI))

DT_COMPAT_NORDIC_NRF_RADIO := nordic,nrf-radio

config DT_HAS_NORDIC_NRF_RADIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_RADIO))

DT_COMPAT_NORDIC_NRF_REGULATORS := nordic,nrf-regulators

config DT_HAS_NORDIC_NRF_REGULATORS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_REGULATORS))

DT_COMPAT_NORDIC_NRF_RESET := nordic,nrf-reset

config DT_HAS_NORDIC_NRF_RESET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_RESET))

DT_COMPAT_NORDIC_NRF_RNG := nordic,nrf-rng

config DT_HAS_NORDIC_NRF_RNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_RNG))

DT_COMPAT_NORDIC_NRF_RTC := nordic,nrf-rtc

config DT_HAS_NORDIC_NRF_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_RTC))

DT_COMPAT_NORDIC_NRF_SAADC := nordic,nrf-saadc

config DT_HAS_NORDIC_NRF_SAADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SAADC))

DT_COMPAT_NORDIC_NRF_SPI := nordic,nrf-spi

config DT_HAS_NORDIC_NRF_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SPI))

DT_COMPAT_NORDIC_NRF_SPIM := nordic,nrf-spim

config DT_HAS_NORDIC_NRF_SPIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SPIM))

DT_COMPAT_NORDIC_NRF_SPIS := nordic,nrf-spis

config DT_HAS_NORDIC_NRF_SPIS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SPIS))

DT_COMPAT_NORDIC_NRF_SPU := nordic,nrf-spu

config DT_HAS_NORDIC_NRF_SPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SPU))

DT_COMPAT_NORDIC_NRF_SW_LPUART := nordic,nrf-sw-lpuart

config DT_HAS_NORDIC_NRF_SW_LPUART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SW_LPUART))

DT_COMPAT_NORDIC_NRF_SW_PWM := nordic,nrf-sw-pwm

config DT_HAS_NORDIC_NRF_SW_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SW_PWM))

DT_COMPAT_NORDIC_NRF_SWI := nordic,nrf-swi

config DT_HAS_NORDIC_NRF_SWI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_SWI))

DT_COMPAT_NORDIC_NRF_TEMP := nordic,nrf-temp

config DT_HAS_NORDIC_NRF_TEMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_TEMP))

DT_COMPAT_NORDIC_NRF_TIMER := nordic,nrf-timer

config DT_HAS_NORDIC_NRF_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_TIMER))

DT_COMPAT_NORDIC_NRF_TWI := nordic,nrf-twi

config DT_HAS_NORDIC_NRF_TWI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_TWI))

DT_COMPAT_NORDIC_NRF_TWIM := nordic,nrf-twim

config DT_HAS_NORDIC_NRF_TWIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_TWIM))

DT_COMPAT_NORDIC_NRF_TWIS := nordic,nrf-twis

config DT_HAS_NORDIC_NRF_TWIS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_TWIS))

DT_COMPAT_NORDIC_NRF_UART := nordic,nrf-uart

config DT_HAS_NORDIC_NRF_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_UART))

DT_COMPAT_NORDIC_NRF_UARTE := nordic,nrf-uarte

config DT_HAS_NORDIC_NRF_UARTE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_UARTE))

DT_COMPAT_NORDIC_NRF_UICR := nordic,nrf-uicr

config DT_HAS_NORDIC_NRF_UICR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_UICR))

DT_COMPAT_NORDIC_NRF_USBD := nordic,nrf-usbd

config DT_HAS_NORDIC_NRF_USBD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_USBD))

DT_COMPAT_NORDIC_NRF_USBREG := nordic,nrf-usbreg

config DT_HAS_NORDIC_NRF_USBREG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_USBREG))

DT_COMPAT_NORDIC_NRF_VMC := nordic,nrf-vmc

config DT_HAS_NORDIC_NRF_VMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_VMC))

DT_COMPAT_NORDIC_NRF_WDT := nordic,nrf-wdt

config DT_HAS_NORDIC_NRF_WDT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF_WDT))

DT_COMPAT_NORDIC_NRF21540_FEM := nordic,nrf21540-fem

config DT_HAS_NORDIC_NRF21540_FEM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF21540_FEM))

DT_COMPAT_NORDIC_NRF21540_FEM_SPI := nordic,nrf21540-fem-spi

config DT_HAS_NORDIC_NRF21540_FEM_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF21540_FEM_SPI))

DT_COMPAT_NORDIC_NRF51_FLASH_CONTROLLER := nordic,nrf51-flash-controller

config DT_HAS_NORDIC_NRF51_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF51_FLASH_CONTROLLER))

DT_COMPAT_NORDIC_NRF52_FLASH_CONTROLLER := nordic,nrf52-flash-controller

config DT_HAS_NORDIC_NRF52_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF52_FLASH_CONTROLLER))

DT_COMPAT_NORDIC_NRF53_FLASH_CONTROLLER := nordic,nrf53-flash-controller

config DT_HAS_NORDIC_NRF53_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF53_FLASH_CONTROLLER))

DT_COMPAT_NORDIC_NRF700X_COEX := nordic,nrf700x-coex

config DT_HAS_NORDIC_NRF700X_COEX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF700X_COEX))

DT_COMPAT_NORDIC_NRF700X_QSPI := nordic,nrf700x-qspi

config DT_HAS_NORDIC_NRF700X_QSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF700X_QSPI))

DT_COMPAT_NORDIC_NRF700X_SPI := nordic,nrf700x-spi

config DT_HAS_NORDIC_NRF700X_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF700X_SPI))

DT_COMPAT_NORDIC_NRF700X_TX_POWER_CEILING := nordic,nrf700x-tx-power-ceiling

config DT_HAS_NORDIC_NRF700X_TX_POWER_CEILING_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF700X_TX_POWER_CEILING))

DT_COMPAT_NORDIC_NRF91_FLASH_CONTROLLER := nordic,nrf91-flash-controller

config DT_HAS_NORDIC_NRF91_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_NRF91_FLASH_CONTROLLER))

DT_COMPAT_NORDIC_QSPI_NOR := nordic,qspi-nor

config DT_HAS_NORDIC_QSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_QSPI_NOR))

DT_COMPAT_NORDIC_SENSOR_SIM := nordic,sensor-sim

config DT_HAS_NORDIC_SENSOR_SIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_SENSOR_SIM))

DT_COMPAT_NORDIC_SENSOR_STUB := nordic,sensor-stub

config DT_HAS_NORDIC_SENSOR_STUB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_SENSOR_STUB))

DT_COMPAT_NORDIC_THINGY53_EDGE_CONNECTOR := nordic-thingy53-edge-connector

config DT_HAS_NORDIC_THINGY53_EDGE_CONNECTOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORDIC_THINGY53_EDGE_CONNECTOR))

DT_COMPAT_NORITAKE_ITRON := noritake,itron

config DT_HAS_NORITAKE_ITRON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NORITAKE_ITRON))

DT_COMPAT_NS16550 := ns16550

config DT_HAS_NS16550_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NS16550))

DT_COMPAT_NTC_THERMISTOR_GENERIC := ntc-thermistor-generic

config DT_HAS_NTC_THERMISTOR_GENERIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NTC_THERMISTOR_GENERIC))

DT_COMPAT_NUCLEI_BUMBLEBEE := nuclei,bumblebee

config DT_HAS_NUCLEI_BUMBLEBEE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUCLEI_BUMBLEBEE))

DT_COMPAT_NUCLEI_ECLIC := nuclei,eclic

config DT_HAS_NUCLEI_ECLIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUCLEI_ECLIC))

DT_COMPAT_NUCLEI_SYSTIMER := nuclei,systimer

config DT_HAS_NUCLEI_SYSTIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUCLEI_SYSTIMER))

DT_COMPAT_NUVOTON_ADC_CMP := nuvoton,adc-cmp

config DT_HAS_NUVOTON_ADC_CMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_ADC_CMP))

DT_COMPAT_NUVOTON_NCT38XX_GPIO := nuvoton,nct38xx-gpio

config DT_HAS_NUVOTON_NCT38XX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NCT38XX_GPIO))

DT_COMPAT_NUVOTON_NCT38XX_GPIO_ALERT := nuvoton,nct38xx-gpio-alert

config DT_HAS_NUVOTON_NCT38XX_GPIO_ALERT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NCT38XX_GPIO_ALERT))

DT_COMPAT_NUVOTON_NCT38XX_GPIO_PORT := nuvoton,nct38xx-gpio-port

config DT_HAS_NUVOTON_NCT38XX_GPIO_PORT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NCT38XX_GPIO_PORT))

DT_COMPAT_NUVOTON_NPCX_ADC := nuvoton,npcx-adc

config DT_HAS_NUVOTON_NPCX_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_ADC))

DT_COMPAT_NUVOTON_NPCX_BBRAM := nuvoton,npcx-bbram

config DT_HAS_NUVOTON_NPCX_BBRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_BBRAM))

DT_COMPAT_NUVOTON_NPCX_BOOTER_VARIANT := nuvoton,npcx-booter-variant

config DT_HAS_NUVOTON_NPCX_BOOTER_VARIANT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_BOOTER_VARIANT))

DT_COMPAT_NUVOTON_NPCX_ESPI := nuvoton,npcx-espi

config DT_HAS_NUVOTON_NPCX_ESPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_ESPI))

DT_COMPAT_NUVOTON_NPCX_ESPI_VW_CONF := nuvoton,npcx-espi-vw-conf

config DT_HAS_NUVOTON_NPCX_ESPI_VW_CONF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_ESPI_VW_CONF))

DT_COMPAT_NUVOTON_NPCX_FIU_NOR := nuvoton,npcx-fiu-nor

config DT_HAS_NUVOTON_NPCX_FIU_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_FIU_NOR))

DT_COMPAT_NUVOTON_NPCX_FIU_QSPI := nuvoton,npcx-fiu-qspi

config DT_HAS_NUVOTON_NPCX_FIU_QSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_FIU_QSPI))

DT_COMPAT_NUVOTON_NPCX_GPIO := nuvoton,npcx-gpio

config DT_HAS_NUVOTON_NPCX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_GPIO))

DT_COMPAT_NUVOTON_NPCX_HOST_SUB := nuvoton,npcx-host-sub

config DT_HAS_NUVOTON_NPCX_HOST_SUB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_HOST_SUB))

DT_COMPAT_NUVOTON_NPCX_HOST_UART := nuvoton,npcx-host-uart

config DT_HAS_NUVOTON_NPCX_HOST_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_HOST_UART))

DT_COMPAT_NUVOTON_NPCX_I2C_CTRL := nuvoton,npcx-i2c-ctrl

config DT_HAS_NUVOTON_NPCX_I2C_CTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_I2C_CTRL))

DT_COMPAT_NUVOTON_NPCX_I2C_PORT := nuvoton,npcx-i2c-port

config DT_HAS_NUVOTON_NPCX_I2C_PORT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_I2C_PORT))

DT_COMPAT_NUVOTON_NPCX_ITIM_TIMER := nuvoton,npcx-itim-timer

config DT_HAS_NUVOTON_NPCX_ITIM_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_ITIM_TIMER))

DT_COMPAT_NUVOTON_NPCX_KBD := nuvoton,npcx-kbd

config DT_HAS_NUVOTON_NPCX_KBD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_KBD))

DT_COMPAT_NUVOTON_NPCX_LEAKAGE_IO := nuvoton,npcx-leakage-io

config DT_HAS_NUVOTON_NPCX_LEAKAGE_IO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_LEAKAGE_IO))

DT_COMPAT_NUVOTON_NPCX_LVOLCTRL_CONF := nuvoton,npcx-lvolctrl-conf

config DT_HAS_NUVOTON_NPCX_LVOLCTRL_CONF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_LVOLCTRL_CONF))

DT_COMPAT_NUVOTON_NPCX_MIWU := nuvoton,npcx-miwu

config DT_HAS_NUVOTON_NPCX_MIWU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_MIWU))

DT_COMPAT_NUVOTON_NPCX_MIWU_INT_MAP := nuvoton,npcx-miwu-int-map

config DT_HAS_NUVOTON_NPCX_MIWU_INT_MAP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_MIWU_INT_MAP))

DT_COMPAT_NUVOTON_NPCX_MIWU_WUI_MAP := nuvoton,npcx-miwu-wui-map

config DT_HAS_NUVOTON_NPCX_MIWU_WUI_MAP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_MIWU_WUI_MAP))

DT_COMPAT_NUVOTON_NPCX_PCC := nuvoton,npcx-pcc

config DT_HAS_NUVOTON_NPCX_PCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_PCC))

DT_COMPAT_NUVOTON_NPCX_PECI := nuvoton,npcx-peci

config DT_HAS_NUVOTON_NPCX_PECI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_PECI))

DT_COMPAT_NUVOTON_NPCX_PINCTRL := nuvoton,npcx-pinctrl

config DT_HAS_NUVOTON_NPCX_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_PINCTRL))

DT_COMPAT_NUVOTON_NPCX_PINCTRL_CONF := nuvoton,npcx-pinctrl-conf

config DT_HAS_NUVOTON_NPCX_PINCTRL_CONF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_PINCTRL_CONF))

DT_COMPAT_NUVOTON_NPCX_PINCTRL_DEF := nuvoton,npcx-pinctrl-def

config DT_HAS_NUVOTON_NPCX_PINCTRL_DEF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_PINCTRL_DEF))

DT_COMPAT_NUVOTON_NPCX_POWER_PSL := nuvoton,npcx-power-psl

config DT_HAS_NUVOTON_NPCX_POWER_PSL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_POWER_PSL))

DT_COMPAT_NUVOTON_NPCX_PS2_CHANNEL := nuvoton,npcx-ps2-channel

config DT_HAS_NUVOTON_NPCX_PS2_CHANNEL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_PS2_CHANNEL))

DT_COMPAT_NUVOTON_NPCX_PS2_CTRL := nuvoton,npcx-ps2-ctrl

config DT_HAS_NUVOTON_NPCX_PS2_CTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_PS2_CTRL))

DT_COMPAT_NUVOTON_NPCX_PWM := nuvoton,npcx-pwm

config DT_HAS_NUVOTON_NPCX_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_PWM))

DT_COMPAT_NUVOTON_NPCX_SCFG := nuvoton,npcx-scfg

config DT_HAS_NUVOTON_NPCX_SCFG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_SCFG))

DT_COMPAT_NUVOTON_NPCX_SHA := nuvoton,npcx-sha

config DT_HAS_NUVOTON_NPCX_SHA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_SHA))

DT_COMPAT_NUVOTON_NPCX_SHI := nuvoton,npcx-shi

config DT_HAS_NUVOTON_NPCX_SHI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_SHI))

DT_COMPAT_NUVOTON_NPCX_SOC_ID := nuvoton,npcx-soc-id

config DT_HAS_NUVOTON_NPCX_SOC_ID_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_SOC_ID))

DT_COMPAT_NUVOTON_NPCX_TACH := nuvoton,npcx-tach

config DT_HAS_NUVOTON_NPCX_TACH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_TACH))

DT_COMPAT_NUVOTON_NPCX_UART := nuvoton,npcx-uart

config DT_HAS_NUVOTON_NPCX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_UART))

DT_COMPAT_NUVOTON_NPCX_WATCHDOG := nuvoton,npcx-watchdog

config DT_HAS_NUVOTON_NPCX_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NPCX_WATCHDOG))

DT_COMPAT_NUVOTON_NUMAKER_FMC := nuvoton,numaker-fmc

config DT_HAS_NUVOTON_NUMAKER_FMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_FMC))

DT_COMPAT_NUVOTON_NUMAKER_GPIO := nuvoton,numaker-gpio

config DT_HAS_NUVOTON_NUMAKER_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_GPIO))

DT_COMPAT_NUVOTON_NUMAKER_PCC := nuvoton,numaker-pcc

config DT_HAS_NUVOTON_NUMAKER_PCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_PCC))

DT_COMPAT_NUVOTON_NUMAKER_PINCTRL := nuvoton,numaker-pinctrl

config DT_HAS_NUVOTON_NUMAKER_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_PINCTRL))

DT_COMPAT_NUVOTON_NUMAKER_PWM := nuvoton,numaker-pwm

config DT_HAS_NUVOTON_NUMAKER_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_PWM))

DT_COMPAT_NUVOTON_NUMAKER_RST := nuvoton,numaker-rst

config DT_HAS_NUVOTON_NUMAKER_RST_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_RST))

DT_COMPAT_NUVOTON_NUMAKER_SCC := nuvoton,numaker-scc

config DT_HAS_NUVOTON_NUMAKER_SCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_SCC))

DT_COMPAT_NUVOTON_NUMAKER_SPI := nuvoton,numaker-spi

config DT_HAS_NUVOTON_NUMAKER_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_SPI))

DT_COMPAT_NUVOTON_NUMAKER_UART := nuvoton,numaker-uart

config DT_HAS_NUVOTON_NUMAKER_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMAKER_UART))

DT_COMPAT_NUVOTON_NUMICRO_GPIO := nuvoton,numicro-gpio

config DT_HAS_NUVOTON_NUMICRO_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMICRO_GPIO))

DT_COMPAT_NUVOTON_NUMICRO_PINCTRL := nuvoton,numicro-pinctrl

config DT_HAS_NUVOTON_NUMICRO_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMICRO_PINCTRL))

DT_COMPAT_NUVOTON_NUMICRO_UART := nuvoton,numicro-uart

config DT_HAS_NUVOTON_NUMICRO_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NUVOTON_NUMICRO_UART))

DT_COMPAT_NVME_CONTROLLER := nvme-controller

config DT_HAS_NVME_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NVME_CONTROLLER))

DT_COMPAT_NXP_CSS_V2 := nxp,css-v2

config DT_HAS_NXP_CSS_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_CSS_V2))

DT_COMPAT_NXP_CTIMER_PWM := nxp,ctimer-pwm

config DT_HAS_NXP_CTIMER_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_CTIMER_PWM))

DT_COMPAT_NXP_DCNANO_LCDIF := nxp,dcnano-lcdif

config DT_HAS_NXP_DCNANO_LCDIF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_DCNANO_LCDIF))

DT_COMPAT_NXP_FLEXCAN := nxp,flexcan

config DT_HAS_NXP_FLEXCAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FLEXCAN))

DT_COMPAT_NXP_FLEXCAN_FD := nxp,flexcan-fd

config DT_HAS_NXP_FLEXCAN_FD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FLEXCAN_FD))

DT_COMPAT_NXP_FLEXPWM := nxp,flexpwm

config DT_HAS_NXP_FLEXPWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FLEXPWM))

DT_COMPAT_NXP_FS26_WDOG := nxp,fs26-wdog

config DT_HAS_NXP_FS26_WDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FS26_WDOG))

DT_COMPAT_NXP_FXAS21002 := nxp,fxas21002

config DT_HAS_NXP_FXAS21002_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FXAS21002))

DT_COMPAT_NXP_FXOS8700 := nxp,fxos8700

config DT_HAS_NXP_FXOS8700_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_FXOS8700))

DT_COMPAT_NXP_GPT_HW_TIMER := nxp,gpt-hw-timer

config DT_HAS_NXP_GPT_HW_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_GPT_HW_TIMER))

DT_COMPAT_NXP_IAP_FMC11 := nxp,iap-fmc11

config DT_HAS_NXP_IAP_FMC11_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IAP_FMC11))

DT_COMPAT_NXP_IAP_FMC54 := nxp,iap-fmc54

config DT_HAS_NXP_IAP_FMC54_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IAP_FMC54))

DT_COMPAT_NXP_IAP_FMC55 := nxp,iap-fmc55

config DT_HAS_NXP_IAP_FMC55_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IAP_FMC55))

DT_COMPAT_NXP_IAP_FMC553 := nxp,iap-fmc553

config DT_HAS_NXP_IAP_FMC553_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IAP_FMC553))

DT_COMPAT_NXP_IMX_ANATOP := nxp,imx-anatop

config DT_HAS_NXP_IMX_ANATOP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_ANATOP))

DT_COMPAT_NXP_IMX_CAAM := nxp,imx-caam

config DT_HAS_NXP_IMX_CAAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_CAAM))

DT_COMPAT_NXP_IMX_CCM := nxp,imx-ccm

config DT_HAS_NXP_IMX_CCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_CCM))

DT_COMPAT_NXP_IMX_CCM_REV2 := nxp,imx-ccm-rev2

config DT_HAS_NXP_IMX_CCM_REV2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_CCM_REV2))

DT_COMPAT_NXP_IMX_CSI := nxp,imx-csi

config DT_HAS_NXP_IMX_CSI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_CSI))

DT_COMPAT_NXP_IMX_DTCM := nxp,imx-dtcm

config DT_HAS_NXP_IMX_DTCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_DTCM))

DT_COMPAT_NXP_IMX_ELCDIF := nxp,imx-elcdif

config DT_HAS_NXP_IMX_ELCDIF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_ELCDIF))

DT_COMPAT_NXP_IMX_EPIT := nxp,imx-epit

config DT_HAS_NXP_IMX_EPIT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_EPIT))

DT_COMPAT_NXP_IMX_FLEXSPI := nxp,imx-flexspi

config DT_HAS_NXP_IMX_FLEXSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_FLEXSPI))

DT_COMPAT_NXP_IMX_FLEXSPI_APS6408L := nxp,imx-flexspi-aps6408l

config DT_HAS_NXP_IMX_FLEXSPI_APS6408L_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_FLEXSPI_APS6408L))

DT_COMPAT_NXP_IMX_FLEXSPI_HYPERFLASH := nxp,imx-flexspi-hyperflash

config DT_HAS_NXP_IMX_FLEXSPI_HYPERFLASH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_FLEXSPI_HYPERFLASH))

DT_COMPAT_NXP_IMX_FLEXSPI_MX25UM51345G := nxp,imx-flexspi-mx25um51345g

config DT_HAS_NXP_IMX_FLEXSPI_MX25UM51345G_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_FLEXSPI_MX25UM51345G))

DT_COMPAT_NXP_IMX_FLEXSPI_NOR := nxp,imx-flexspi-nor

config DT_HAS_NXP_IMX_FLEXSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_FLEXSPI_NOR))

DT_COMPAT_NXP_IMX_FLEXSPI_S27KS0641 := nxp,imx-flexspi-s27ks0641

config DT_HAS_NXP_IMX_FLEXSPI_S27KS0641_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_FLEXSPI_S27KS0641))

DT_COMPAT_NXP_IMX_FLEXSPI_W956A8MBYA := nxp,imx-flexspi-w956a8mbya

config DT_HAS_NXP_IMX_FLEXSPI_W956A8MBYA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_FLEXSPI_W956A8MBYA))

DT_COMPAT_NXP_IMX_GPIO := nxp,imx-gpio

config DT_HAS_NXP_IMX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_GPIO))

DT_COMPAT_NXP_IMX_GPR := nxp,imx-gpr

config DT_HAS_NXP_IMX_GPR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_GPR))

DT_COMPAT_NXP_IMX_GPT := nxp,imx-gpt

config DT_HAS_NXP_IMX_GPT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_GPT))

DT_COMPAT_NXP_IMX_IOMUXC := nxp,imx-iomuxc

config DT_HAS_NXP_IMX_IOMUXC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_IOMUXC))

DT_COMPAT_NXP_IMX_ITCM := nxp,imx-itcm

config DT_HAS_NXP_IMX_ITCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_ITCM))

DT_COMPAT_NXP_IMX_IUART := nxp,imx-iuart

config DT_HAS_NXP_IMX_IUART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_IUART))

DT_COMPAT_NXP_IMX_LPI2C := nxp,imx-lpi2c

config DT_HAS_NXP_IMX_LPI2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_LPI2C))

DT_COMPAT_NXP_IMX_LPSPI := nxp,imx-lpspi

config DT_HAS_NXP_IMX_LPSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_LPSPI))

DT_COMPAT_NXP_IMX_MIPI_DSI := nxp,imx-mipi-dsi

config DT_HAS_NXP_IMX_MIPI_DSI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_MIPI_DSI))

DT_COMPAT_NXP_IMX_MU := nxp,imx-mu

config DT_HAS_NXP_IMX_MU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_MU))

DT_COMPAT_NXP_IMX_MU_REV2 := nxp,imx-mu-rev2

config DT_HAS_NXP_IMX_MU_REV2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_MU_REV2))

DT_COMPAT_NXP_IMX_PWM := nxp,imx-pwm

config DT_HAS_NXP_IMX_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_PWM))

DT_COMPAT_NXP_IMX_QTMR := nxp,imx-qtmr

config DT_HAS_NXP_IMX_QTMR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_QTMR))

DT_COMPAT_NXP_IMX_SEMC := nxp,imx-semc

config DT_HAS_NXP_IMX_SEMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_SEMC))

DT_COMPAT_NXP_IMX_SNVS_RTC := nxp,imx-snvs-rtc

config DT_HAS_NXP_IMX_SNVS_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_SNVS_RTC))

DT_COMPAT_NXP_IMX_TMR := nxp,imx-tmr

config DT_HAS_NXP_IMX_TMR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_TMR))

DT_COMPAT_NXP_IMX_UART := nxp,imx-uart

config DT_HAS_NXP_IMX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_UART))

DT_COMPAT_NXP_IMX_USDHC := nxp,imx-usdhc

config DT_HAS_NXP_IMX_USDHC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_USDHC))

DT_COMPAT_NXP_IMX_WDOG := nxp,imx-wdog

config DT_HAS_NXP_IMX_WDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX_WDOG))

DT_COMPAT_NXP_IMX7D_PINCTRL := nxp,imx7d-pinctrl

config DT_HAS_NXP_IMX7D_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX7D_PINCTRL))

DT_COMPAT_NXP_IMX8M_PINCTRL := nxp,imx8m-pinctrl

config DT_HAS_NXP_IMX8M_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX8M_PINCTRL))

DT_COMPAT_NXP_IMX8MP_PINCTRL := nxp,imx8mp-pinctrl

config DT_HAS_NXP_IMX8MP_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX8MP_PINCTRL))

DT_COMPAT_NXP_IMX93_PINCTRL := nxp,imx93-pinctrl

config DT_HAS_NXP_IMX93_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IMX93_PINCTRL))

DT_COMPAT_NXP_IRQSTEER_INTC := nxp,irqsteer-intc

config DT_HAS_NXP_IRQSTEER_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_IRQSTEER_INTC))

DT_COMPAT_NXP_KINETIS_ACMP := nxp,kinetis-acmp

config DT_HAS_NXP_KINETIS_ACMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_ACMP))

DT_COMPAT_NXP_KINETIS_ADC12 := nxp,kinetis-adc12

config DT_HAS_NXP_KINETIS_ADC12_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_ADC12))

DT_COMPAT_NXP_KINETIS_ADC16 := nxp,kinetis-adc16

config DT_HAS_NXP_KINETIS_ADC16_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_ADC16))

DT_COMPAT_NXP_KINETIS_DAC := nxp,kinetis-dac

config DT_HAS_NXP_KINETIS_DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_DAC))

DT_COMPAT_NXP_KINETIS_DAC32 := nxp,kinetis-dac32

config DT_HAS_NXP_KINETIS_DAC32_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_DAC32))

DT_COMPAT_NXP_KINETIS_DSPI := nxp,kinetis-dspi

config DT_HAS_NXP_KINETIS_DSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_DSPI))

DT_COMPAT_NXP_KINETIS_ETHERNET := nxp,kinetis-ethernet

config DT_HAS_NXP_KINETIS_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_ETHERNET))

DT_COMPAT_NXP_KINETIS_FTFA := nxp,kinetis-ftfa

config DT_HAS_NXP_KINETIS_FTFA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_FTFA))

DT_COMPAT_NXP_KINETIS_FTFE := nxp,kinetis-ftfe

config DT_HAS_NXP_KINETIS_FTFE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_FTFE))

DT_COMPAT_NXP_KINETIS_FTFL := nxp,kinetis-ftfl

config DT_HAS_NXP_KINETIS_FTFL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_FTFL))

DT_COMPAT_NXP_KINETIS_FTM := nxp,kinetis-ftm

config DT_HAS_NXP_KINETIS_FTM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_FTM))

DT_COMPAT_NXP_KINETIS_FTM_PWM := nxp,kinetis-ftm-pwm

config DT_HAS_NXP_KINETIS_FTM_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_FTM_PWM))

DT_COMPAT_NXP_KINETIS_GPIO := nxp,kinetis-gpio

config DT_HAS_NXP_KINETIS_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_GPIO))

DT_COMPAT_NXP_KINETIS_I2C := nxp,kinetis-i2c

config DT_HAS_NXP_KINETIS_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_I2C))

DT_COMPAT_NXP_KINETIS_KE1XF_SIM := nxp,kinetis-ke1xf-sim

config DT_HAS_NXP_KINETIS_KE1XF_SIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_KE1XF_SIM))

DT_COMPAT_NXP_KINETIS_LPSCI := nxp,kinetis-lpsci

config DT_HAS_NXP_KINETIS_LPSCI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_LPSCI))

DT_COMPAT_NXP_KINETIS_LPTMR := nxp,kinetis-lptmr

config DT_HAS_NXP_KINETIS_LPTMR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_LPTMR))

DT_COMPAT_NXP_KINETIS_LPUART := nxp,kinetis-lpuart

config DT_HAS_NXP_KINETIS_LPUART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_LPUART))

DT_COMPAT_NXP_KINETIS_MCG := nxp,kinetis-mcg

config DT_HAS_NXP_KINETIS_MCG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_MCG))

DT_COMPAT_NXP_KINETIS_PCC := nxp,kinetis-pcc

config DT_HAS_NXP_KINETIS_PCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_PCC))

DT_COMPAT_NXP_KINETIS_PINCTRL := nxp,kinetis-pinctrl

config DT_HAS_NXP_KINETIS_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_PINCTRL))

DT_COMPAT_NXP_KINETIS_PINMUX := nxp,kinetis-pinmux

config DT_HAS_NXP_KINETIS_PINMUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_PINMUX))

DT_COMPAT_NXP_KINETIS_PIT := nxp,kinetis-pit

config DT_HAS_NXP_KINETIS_PIT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_PIT))

DT_COMPAT_NXP_KINETIS_PTP := nxp,kinetis-ptp

config DT_HAS_NXP_KINETIS_PTP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_PTP))

DT_COMPAT_NXP_KINETIS_PWT := nxp,kinetis-pwt

config DT_HAS_NXP_KINETIS_PWT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_PWT))

DT_COMPAT_NXP_KINETIS_RNGA := nxp,kinetis-rnga

config DT_HAS_NXP_KINETIS_RNGA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_RNGA))

DT_COMPAT_NXP_KINETIS_RTC := nxp,kinetis-rtc

config DT_HAS_NXP_KINETIS_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_RTC))

DT_COMPAT_NXP_KINETIS_SCG := nxp,kinetis-scg

config DT_HAS_NXP_KINETIS_SCG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_SCG))

DT_COMPAT_NXP_KINETIS_SIM := nxp,kinetis-sim

config DT_HAS_NXP_KINETIS_SIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_SIM))

DT_COMPAT_NXP_KINETIS_TEMPERATURE := nxp,kinetis-temperature

config DT_HAS_NXP_KINETIS_TEMPERATURE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_TEMPERATURE))

DT_COMPAT_NXP_KINETIS_TPM := nxp,kinetis-tpm

config DT_HAS_NXP_KINETIS_TPM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_TPM))

DT_COMPAT_NXP_KINETIS_TRNG := nxp,kinetis-trng

config DT_HAS_NXP_KINETIS_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_TRNG))

DT_COMPAT_NXP_KINETIS_UART := nxp,kinetis-uart

config DT_HAS_NXP_KINETIS_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_UART))

DT_COMPAT_NXP_KINETIS_USBD := nxp,kinetis-usbd

config DT_HAS_NXP_KINETIS_USBD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_USBD))

DT_COMPAT_NXP_KINETIS_WDOG := nxp,kinetis-wdog

config DT_HAS_NXP_KINETIS_WDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_WDOG))

DT_COMPAT_NXP_KINETIS_WDOG32 := nxp,kinetis-wdog32

config DT_HAS_NXP_KINETIS_WDOG32_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KINETIS_WDOG32))

DT_COMPAT_NXP_KW41Z_IEEE802154 := nxp,kw41z-ieee802154

config DT_HAS_NXP_KW41Z_IEEE802154_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_KW41Z_IEEE802154))

DT_COMPAT_NXP_LPC_CTIMER := nxp,lpc-ctimer

config DT_HAS_NXP_LPC_CTIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_CTIMER))

DT_COMPAT_NXP_LPC_DMA := nxp,lpc-dma

config DT_HAS_NXP_LPC_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_DMA))

DT_COMPAT_NXP_LPC_FLEXCOMM := nxp,lpc-flexcomm

config DT_HAS_NXP_LPC_FLEXCOMM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_FLEXCOMM))

DT_COMPAT_NXP_LPC_GPIO := nxp,lpc-gpio

config DT_HAS_NXP_LPC_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_GPIO))

DT_COMPAT_NXP_LPC_I2C := nxp,lpc-i2c

config DT_HAS_NXP_LPC_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_I2C))

DT_COMPAT_NXP_LPC_I2S := nxp,lpc-i2s

config DT_HAS_NXP_LPC_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_I2S))

DT_COMPAT_NXP_LPC_IOCON := nxp,lpc-iocon

config DT_HAS_NXP_LPC_IOCON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_IOCON))

DT_COMPAT_NXP_LPC_IOCON_PINCTRL := nxp,lpc-iocon-pinctrl

config DT_HAS_NXP_LPC_IOCON_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_IOCON_PINCTRL))

DT_COMPAT_NXP_LPC_IOCON_PIO := nxp,lpc-iocon-pio

config DT_HAS_NXP_LPC_IOCON_PIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_IOCON_PIO))

DT_COMPAT_NXP_LPC_LPADC := nxp,lpc-lpadc

config DT_HAS_NXP_LPC_LPADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_LPADC))

DT_COMPAT_NXP_LPC_MAILBOX := nxp,lpc-mailbox

config DT_HAS_NXP_LPC_MAILBOX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_MAILBOX))

DT_COMPAT_NXP_LPC_MCAN := nxp,lpc-mcan

config DT_HAS_NXP_LPC_MCAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_MCAN))

DT_COMPAT_NXP_LPC_RNG := nxp,lpc-rng

config DT_HAS_NXP_LPC_RNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_RNG))

DT_COMPAT_NXP_LPC_RTC := nxp,lpc-rtc

config DT_HAS_NXP_LPC_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_RTC))

DT_COMPAT_NXP_LPC_SDIF := nxp,lpc-sdif

config DT_HAS_NXP_LPC_SDIF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_SDIF))

DT_COMPAT_NXP_LPC_SPI := nxp,lpc-spi

config DT_HAS_NXP_LPC_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_SPI))

DT_COMPAT_NXP_LPC_SYSCON := nxp,lpc-syscon

config DT_HAS_NXP_LPC_SYSCON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_SYSCON))

DT_COMPAT_NXP_LPC_UID := nxp,lpc-uid

config DT_HAS_NXP_LPC_UID_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_UID))

DT_COMPAT_NXP_LPC_USART := nxp,lpc-usart

config DT_HAS_NXP_LPC_USART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_USART))

DT_COMPAT_NXP_LPC_WWDT := nxp,lpc-wwdt

config DT_HAS_NXP_LPC_WWDT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC_WWDT))

DT_COMPAT_NXP_LPC11U6X_EEPROM := nxp,lpc11u6x-eeprom

config DT_HAS_NXP_LPC11U6X_EEPROM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC11U6X_EEPROM))

DT_COMPAT_NXP_LPC11U6X_GPIO := nxp,lpc11u6x-gpio

config DT_HAS_NXP_LPC11U6X_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC11U6X_GPIO))

DT_COMPAT_NXP_LPC11U6X_I2C := nxp,lpc11u6x-i2c

config DT_HAS_NXP_LPC11U6X_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC11U6X_I2C))

DT_COMPAT_NXP_LPC11U6X_PINCTRL := nxp,lpc11u6x-pinctrl

config DT_HAS_NXP_LPC11U6X_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC11U6X_PINCTRL))

DT_COMPAT_NXP_LPC11U6X_SYSCON := nxp,lpc11u6x-syscon

config DT_HAS_NXP_LPC11U6X_SYSCON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC11U6X_SYSCON))

DT_COMPAT_NXP_LPC11U6X_UART := nxp,lpc11u6x-uart

config DT_HAS_NXP_LPC11U6X_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_LPC11U6X_UART))

DT_COMPAT_NXP_MCR20A := nxp,mcr20a

config DT_HAS_NXP_MCR20A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCR20A))

DT_COMPAT_NXP_MCUX_12B1MSPS_SAR := nxp,mcux-12b1msps-sar

config DT_HAS_NXP_MCUX_12B1MSPS_SAR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_12B1MSPS_SAR))

DT_COMPAT_NXP_MCUX_DCP := nxp,mcux-dcp

config DT_HAS_NXP_MCUX_DCP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_DCP))

DT_COMPAT_NXP_MCUX_EDMA := nxp,mcux-edma

config DT_HAS_NXP_MCUX_EDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_EDMA))

DT_COMPAT_NXP_MCUX_I2S := nxp,mcux-i2s

config DT_HAS_NXP_MCUX_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_I2S))

DT_COMPAT_NXP_MCUX_I3C := nxp,mcux-i3c

config DT_HAS_NXP_MCUX_I3C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_I3C))

DT_COMPAT_NXP_MCUX_QDEC := nxp,mcux-qdec

config DT_HAS_NXP_MCUX_QDEC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_QDEC))

DT_COMPAT_NXP_MCUX_RT_PINCTRL := nxp,mcux-rt-pinctrl

config DT_HAS_NXP_MCUX_RT_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_RT_PINCTRL))

DT_COMPAT_NXP_MCUX_RT11XX_PINCTRL := nxp,mcux-rt11xx-pinctrl

config DT_HAS_NXP_MCUX_RT11XX_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_RT11XX_PINCTRL))

DT_COMPAT_NXP_MCUX_USBD := nxp,mcux-usbd

config DT_HAS_NXP_MCUX_USBD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_USBD))

DT_COMPAT_NXP_MCUX_XBAR := nxp,mcux-xbar

config DT_HAS_NXP_MCUX_XBAR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MCUX_XBAR))

DT_COMPAT_NXP_MIPI_DSI_2L := nxp,mipi-dsi-2l

config DT_HAS_NXP_MIPI_DSI_2L_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_MIPI_DSI_2L))

DT_COMPAT_NXP_OS_TIMER := nxp,os-timer

config DT_HAS_NXP_OS_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_OS_TIMER))

DT_COMPAT_NXP_PCA9420 := nxp,pca9420

config DT_HAS_NXP_PCA9420_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCA9420))

DT_COMPAT_NXP_PCA95XX := nxp,pca95xx

config DT_HAS_NXP_PCA95XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCA95XX))

DT_COMPAT_NXP_PCA9633 := nxp,pca9633

config DT_HAS_NXP_PCA9633_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCA9633))

DT_COMPAT_NXP_PCA9685_PWM := nxp,pca9685-pwm

config DT_HAS_NXP_PCA9685_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCA9685_PWM))

DT_COMPAT_NXP_PCAL6408A := nxp,pcal6408a

config DT_HAS_NXP_PCAL6408A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCAL6408A))

DT_COMPAT_NXP_PCAL6416A := nxp,pcal6416a

config DT_HAS_NXP_PCAL6416A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCAL6416A))

DT_COMPAT_NXP_PCF8523 := nxp,pcf8523

config DT_HAS_NXP_PCF8523_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCF8523))

DT_COMPAT_NXP_PCF8563 := nxp,pcf8563

config DT_HAS_NXP_PCF8563_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCF8563))

DT_COMPAT_NXP_PCF8574 := nxp,pcf8574

config DT_HAS_NXP_PCF8574_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PCF8574))

DT_COMPAT_NXP_PDCFG_POWER := nxp,pdcfg-power

config DT_HAS_NXP_PDCFG_POWER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PDCFG_POWER))

DT_COMPAT_NXP_PINT := nxp,pint

config DT_HAS_NXP_PINT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PINT))

DT_COMPAT_NXP_PXP := nxp,pxp

config DT_HAS_NXP_PXP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_PXP))

DT_COMPAT_NXP_RT_IOCON_PINCTRL := nxp,rt-iocon-pinctrl

config DT_HAS_NXP_RT_IOCON_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_RT_IOCON_PINCTRL))

DT_COMPAT_NXP_S32_ADC_SAR := nxp,s32-adc-sar

config DT_HAS_NXP_S32_ADC_SAR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_ADC_SAR))

DT_COMPAT_NXP_S32_CANXL := nxp,s32-canxl

config DT_HAS_NXP_S32_CANXL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_CANXL))

DT_COMPAT_NXP_S32_CLOCK := nxp,s32-clock

config DT_HAS_NXP_S32_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_CLOCK))

DT_COMPAT_NXP_S32_GMAC := nxp,s32-gmac

config DT_HAS_NXP_S32_GMAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_GMAC))

DT_COMPAT_NXP_S32_GPIO := nxp,s32-gpio

config DT_HAS_NXP_S32_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_GPIO))

DT_COMPAT_NXP_S32_LINFLEXD := nxp,s32-linflexd

config DT_HAS_NXP_S32_LINFLEXD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_LINFLEXD))

DT_COMPAT_NXP_S32_MRU := nxp,s32-mru

config DT_HAS_NXP_S32_MRU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_MRU))

DT_COMPAT_NXP_S32_NETC_EMDIO := nxp,s32-netc-emdio

config DT_HAS_NXP_S32_NETC_EMDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_NETC_EMDIO))

DT_COMPAT_NXP_S32_NETC_PSI := nxp,s32-netc-psi

config DT_HAS_NXP_S32_NETC_PSI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_NETC_PSI))

DT_COMPAT_NXP_S32_NETC_VSI := nxp,s32-netc-vsi

config DT_HAS_NXP_S32_NETC_VSI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_NETC_VSI))

DT_COMPAT_NXP_S32_QSPI := nxp,s32-qspi

config DT_HAS_NXP_S32_QSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_QSPI))

DT_COMPAT_NXP_S32_QSPI_DEVICE := nxp,s32-qspi-device

config DT_HAS_NXP_S32_QSPI_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_QSPI_DEVICE))

DT_COMPAT_NXP_S32_QSPI_NOR := nxp,s32-qspi-nor

config DT_HAS_NXP_S32_QSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_QSPI_NOR))

DT_COMPAT_NXP_S32_SIUL2_EIRQ := nxp,s32-siul2-eirq

config DT_HAS_NXP_S32_SIUL2_EIRQ_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_SIUL2_EIRQ))

DT_COMPAT_NXP_S32_SPI := nxp,s32-spi

config DT_HAS_NXP_S32_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_SPI))

DT_COMPAT_NXP_S32_SWT := nxp,s32-swt

config DT_HAS_NXP_S32_SWT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_SWT))

DT_COMPAT_NXP_S32_SYS_TIMER := nxp,s32-sys-timer

config DT_HAS_NXP_S32_SYS_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32_SYS_TIMER))

DT_COMPAT_NXP_S32K3_PINCTRL := nxp,s32k3-pinctrl

config DT_HAS_NXP_S32K3_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32K3_PINCTRL))

DT_COMPAT_NXP_S32ZE_PINCTRL := nxp,s32ze-pinctrl

config DT_HAS_NXP_S32ZE_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_S32ZE_PINCTRL))

DT_COMPAT_NXP_SC18IM704 := nxp,sc18im704

config DT_HAS_NXP_SC18IM704_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_SC18IM704))

DT_COMPAT_NXP_SC18IM704_GPIO := nxp,sc18im704-gpio

config DT_HAS_NXP_SC18IM704_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_SC18IM704_GPIO))

DT_COMPAT_NXP_SC18IM704_I2C := nxp,sc18im704-i2c

config DT_HAS_NXP_SC18IM704_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_SC18IM704_I2C))

DT_COMPAT_NXP_SCTIMER_PWM := nxp,sctimer-pwm

config DT_HAS_NXP_SCTIMER_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_SCTIMER_PWM))

DT_COMPAT_NXP_TEMPMON := nxp,tempmon

config DT_HAS_NXP_TEMPMON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_TEMPMON))

DT_COMPAT_NXP_VF610_ADC := nxp,vf610-adc

config DT_HAS_NXP_VF610_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_NXP_VF610_ADC))

DT_COMPAT_ONNN_NCP5623 := onnn,ncp5623

config DT_HAS_ONNN_NCP5623_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ONNN_NCP5623))

DT_COMPAT_OPENCORES_SPI_SIMPLE := opencores,spi-simple

config DT_HAS_OPENCORES_SPI_SIMPLE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENCORES_SPI_SIMPLE))

DT_COMPAT_OPENISA_RV32M1_EVENT_UNIT := openisa,rv32m1-event-unit

config DT_HAS_OPENISA_RV32M1_EVENT_UNIT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_EVENT_UNIT))

DT_COMPAT_OPENISA_RV32M1_FTFE := openisa,rv32m1-ftfe

config DT_HAS_OPENISA_RV32M1_FTFE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_FTFE))

DT_COMPAT_OPENISA_RV32M1_GENFSK := openisa,rv32m1-genfsk

config DT_HAS_OPENISA_RV32M1_GENFSK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_GENFSK))

DT_COMPAT_OPENISA_RV32M1_GPIO := openisa,rv32m1-gpio

config DT_HAS_OPENISA_RV32M1_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_GPIO))

DT_COMPAT_OPENISA_RV32M1_INTMUX := openisa,rv32m1-intmux

config DT_HAS_OPENISA_RV32M1_INTMUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_INTMUX))

DT_COMPAT_OPENISA_RV32M1_INTMUX_CH := openisa,rv32m1-intmux-ch

config DT_HAS_OPENISA_RV32M1_INTMUX_CH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_INTMUX_CH))

DT_COMPAT_OPENISA_RV32M1_LPI2C := openisa,rv32m1-lpi2c

config DT_HAS_OPENISA_RV32M1_LPI2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_LPI2C))

DT_COMPAT_OPENISA_RV32M1_LPSPI := openisa,rv32m1-lpspi

config DT_HAS_OPENISA_RV32M1_LPSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_LPSPI))

DT_COMPAT_OPENISA_RV32M1_LPTMR := openisa,rv32m1-lptmr

config DT_HAS_OPENISA_RV32M1_LPTMR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_LPTMR))

DT_COMPAT_OPENISA_RV32M1_LPUART := openisa,rv32m1-lpuart

config DT_HAS_OPENISA_RV32M1_LPUART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_LPUART))

DT_COMPAT_OPENISA_RV32M1_PCC := openisa,rv32m1-pcc

config DT_HAS_OPENISA_RV32M1_PCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_PCC))

DT_COMPAT_OPENISA_RV32M1_PINCTRL := openisa,rv32m1-pinctrl

config DT_HAS_OPENISA_RV32M1_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_PINCTRL))

DT_COMPAT_OPENISA_RV32M1_PINMUX := openisa,rv32m1-pinmux

config DT_HAS_OPENISA_RV32M1_PINMUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_PINMUX))

DT_COMPAT_OPENISA_RV32M1_TPM := openisa,rv32m1-tpm

config DT_HAS_OPENISA_RV32M1_TPM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_TPM))

DT_COMPAT_OPENISA_RV32M1_TRNG := openisa,rv32m1-trng

config DT_HAS_OPENISA_RV32M1_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENISA_RV32M1_TRNG))

DT_COMPAT_OPENTHREAD_CONFIG := openthread,config

config DT_HAS_OPENTHREAD_CONFIG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OPENTHREAD_CONFIG))

DT_COMPAT_ORISETECH_OTM8009A := orisetech,otm8009a

config DT_HAS_ORISETECH_OTM8009A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ORISETECH_OTM8009A))

DT_COMPAT_OVTI_OV2640 := ovti,ov2640

config DT_HAS_OVTI_OV2640_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OVTI_OV2640))

DT_COMPAT_OVTI_OV7725 := ovti,ov7725

config DT_HAS_OVTI_OV7725_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_OVTI_OV7725))

DT_COMPAT_PANASONIC_AMG88XX := panasonic,amg88xx

config DT_HAS_PANASONIC_AMG88XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PANASONIC_AMG88XX))

DT_COMPAT_PANASONIC_REDUCED_ARDUINO_HEADER := panasonic,reduced-arduino-header

config DT_HAS_PANASONIC_REDUCED_ARDUINO_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PANASONIC_REDUCED_ARDUINO_HEADER))

DT_COMPAT_PARTICLE_GEN3_HEADER := particle-gen3-header

config DT_HAS_PARTICLE_GEN3_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PARTICLE_GEN3_HEADER))

DT_COMPAT_PCI_HOST_ECAM_GENERIC := pci-host-ecam-generic

config DT_HAS_PCI_HOST_ECAM_GENERIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PCI_HOST_ECAM_GENERIC))

DT_COMPAT_PIXART_PAW3212 := pixart,paw3212

config DT_HAS_PIXART_PAW3212_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PIXART_PAW3212))

DT_COMPAT_PIXART_PMW3360 := pixart,pmw3360

config DT_HAS_PIXART_PMW3360_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PIXART_PMW3360))

DT_COMPAT_PLANTOWER_PMS7003 := plantower,pms7003

config DT_HAS_PLANTOWER_PMS7003_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PLANTOWER_PMS7003))

DT_COMPAT_POWER_DOMAIN := power-domain

config DT_HAS_POWER_DOMAIN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_POWER_DOMAIN))

DT_COMPAT_POWER_DOMAIN_GPIO := power-domain-gpio

config DT_HAS_POWER_DOMAIN_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_POWER_DOMAIN_GPIO))

DT_COMPAT_PTC_PT6314 := ptc,pt6314

config DT_HAS_PTC_PT6314_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PTC_PT6314))

DT_COMPAT_PWM_LEDS := pwm-leds

config DT_HAS_PWM_LEDS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_PWM_LEDS))

DT_COMPAT_QEMU_IVSHMEM := qemu,ivshmem

config DT_HAS_QEMU_IVSHMEM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QEMU_IVSHMEM))

DT_COMPAT_QEMU_NIOS2_ZEPHYR := qemu,nios2-zephyr

config DT_HAS_QEMU_NIOS2_ZEPHYR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QEMU_NIOS2_ZEPHYR))

DT_COMPAT_QUECTEL_BG95 := quectel,bg95

config DT_HAS_QUECTEL_BG95_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QUECTEL_BG95))

DT_COMPAT_QUECTEL_BG9X := quectel,bg9x

config DT_HAS_QUECTEL_BG9X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QUECTEL_BG9X))

DT_COMPAT_QUICKLOGIC_EOS_S3_GPIO := quicklogic,eos-s3-gpio

config DT_HAS_QUICKLOGIC_EOS_S3_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QUICKLOGIC_EOS_S3_GPIO))

DT_COMPAT_QUICKLOGIC_EOS_S3_PINCTRL := quicklogic,eos-s3-pinctrl

config DT_HAS_QUICKLOGIC_EOS_S3_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QUICKLOGIC_EOS_S3_PINCTRL))

DT_COMPAT_QUICKLOGIC_USBSERIALPORT_S3B := quicklogic,usbserialport-s3b

config DT_HAS_QUICKLOGIC_USBSERIALPORT_S3B_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_QUICKLOGIC_USBSERIALPORT_S3B))

DT_COMPAT_RASPBERRYPI_CORE_SUPPLY_REGULATOR := raspberrypi,core-supply-regulator

config DT_HAS_RASPBERRYPI_CORE_SUPPLY_REGULATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_CORE_SUPPLY_REGULATOR))

DT_COMPAT_RASPBERRYPI_PICO_ADC := raspberrypi,pico-adc

config DT_HAS_RASPBERRYPI_PICO_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_ADC))

DT_COMPAT_RASPBERRYPI_PICO_DMA := raspberrypi,pico-dma

config DT_HAS_RASPBERRYPI_PICO_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_DMA))

DT_COMPAT_RASPBERRYPI_PICO_FLASH_CONTROLLER := raspberrypi,pico-flash-controller

config DT_HAS_RASPBERRYPI_PICO_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_FLASH_CONTROLLER))

DT_COMPAT_RASPBERRYPI_PICO_GPIO := raspberrypi,pico-gpio

config DT_HAS_RASPBERRYPI_PICO_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_GPIO))

DT_COMPAT_RASPBERRYPI_PICO_HEADER := raspberrypi,pico-header

config DT_HAS_RASPBERRYPI_PICO_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_HEADER))

DT_COMPAT_RASPBERRYPI_PICO_PINCTRL := raspberrypi,pico-pinctrl

config DT_HAS_RASPBERRYPI_PICO_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_PINCTRL))

DT_COMPAT_RASPBERRYPI_PICO_PIO := raspberrypi,pico-pio

config DT_HAS_RASPBERRYPI_PICO_PIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_PIO))

DT_COMPAT_RASPBERRYPI_PICO_PIO_DEVICE := raspberrypi,pico-pio-device

config DT_HAS_RASPBERRYPI_PICO_PIO_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_PIO_DEVICE))

DT_COMPAT_RASPBERRYPI_PICO_PWM := raspberrypi,pico-pwm

config DT_HAS_RASPBERRYPI_PICO_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_PWM))

DT_COMPAT_RASPBERRYPI_PICO_RESET := raspberrypi,pico-reset

config DT_HAS_RASPBERRYPI_PICO_RESET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_RESET))

DT_COMPAT_RASPBERRYPI_PICO_SPI := raspberrypi,pico-spi

config DT_HAS_RASPBERRYPI_PICO_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_SPI))

DT_COMPAT_RASPBERRYPI_PICO_SPI_PIO := raspberrypi,pico-spi-pio

config DT_HAS_RASPBERRYPI_PICO_SPI_PIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_SPI_PIO))

DT_COMPAT_RASPBERRYPI_PICO_TEMP := raspberrypi,pico-temp

config DT_HAS_RASPBERRYPI_PICO_TEMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_TEMP))

DT_COMPAT_RASPBERRYPI_PICO_UART := raspberrypi,pico-uart

config DT_HAS_RASPBERRYPI_PICO_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_UART))

DT_COMPAT_RASPBERRYPI_PICO_UART_PIO := raspberrypi,pico-uart-pio

config DT_HAS_RASPBERRYPI_PICO_UART_PIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_UART_PIO))

DT_COMPAT_RASPBERRYPI_PICO_USBD := raspberrypi,pico-usbd

config DT_HAS_RASPBERRYPI_PICO_USBD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_USBD))

DT_COMPAT_RASPBERRYPI_PICO_WATCHDOG := raspberrypi,pico-watchdog

config DT_HAS_RASPBERRYPI_PICO_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_PICO_WATCHDOG))

DT_COMPAT_RASPBERRYPI_40PINS_HEADER := raspberrypi-40pins-header

config DT_HAS_RASPBERRYPI_40PINS_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RASPBERRYPI_40PINS_HEADER))

DT_COMPAT_RAYDIUM_RM67162 := raydium,rm67162

config DT_HAS_RAYDIUM_RM67162_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RAYDIUM_RM67162))

DT_COMPAT_RAYDIUM_RM68200 := raydium,rm68200

config DT_HAS_RAYDIUM_RM68200_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RAYDIUM_RM68200))

DT_COMPAT_REGULATOR_FIXED := regulator-fixed

config DT_HAS_REGULATOR_FIXED_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_REGULATOR_FIXED))

DT_COMPAT_REGULATOR_GPIO := regulator-gpio

config DT_HAS_REGULATOR_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_REGULATOR_GPIO))

DT_COMPAT_RENESAS_PWM_RCAR := renesas,pwm-rcar

config DT_HAS_RENESAS_PWM_RCAR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_PWM_RCAR))

DT_COMPAT_RENESAS_R8A7795_CPG_MSSR := renesas,r8a7795-cpg-mssr

config DT_HAS_RENESAS_R8A7795_CPG_MSSR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_R8A7795_CPG_MSSR))

DT_COMPAT_RENESAS_RCAR_CAN := renesas,rcar-can

config DT_HAS_RENESAS_RCAR_CAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RCAR_CAN))

DT_COMPAT_RENESAS_RCAR_CMT := renesas,rcar-cmt

config DT_HAS_RENESAS_RCAR_CMT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RCAR_CMT))

DT_COMPAT_RENESAS_RCAR_GPIO := renesas,rcar-gpio

config DT_HAS_RENESAS_RCAR_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RCAR_GPIO))

DT_COMPAT_RENESAS_RCAR_I2C := renesas,rcar-i2c

config DT_HAS_RENESAS_RCAR_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RCAR_I2C))

DT_COMPAT_RENESAS_RCAR_PFC := renesas,rcar-pfc

config DT_HAS_RENESAS_RCAR_PFC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RCAR_PFC))

DT_COMPAT_RENESAS_RCAR_SCIF := renesas,rcar-scif

config DT_HAS_RENESAS_RCAR_SCIF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_RCAR_SCIF))

DT_COMPAT_RENESAS_SMARTBOND_ADC := renesas,smartbond-adc

config DT_HAS_RENESAS_SMARTBOND_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_ADC))

DT_COMPAT_RENESAS_SMARTBOND_FLASH_CONTROLLER := renesas,smartbond-flash-controller

config DT_HAS_RENESAS_SMARTBOND_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_FLASH_CONTROLLER))

DT_COMPAT_RENESAS_SMARTBOND_GPIO := renesas,smartbond-gpio

config DT_HAS_RENESAS_SMARTBOND_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_GPIO))

DT_COMPAT_RENESAS_SMARTBOND_I2C := renesas,smartbond-i2c

config DT_HAS_RENESAS_SMARTBOND_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_I2C))

DT_COMPAT_RENESAS_SMARTBOND_LP_CLK := renesas,smartbond-lp-clk

config DT_HAS_RENESAS_SMARTBOND_LP_CLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_LP_CLK))

DT_COMPAT_RENESAS_SMARTBOND_LP_OSC := renesas,smartbond-lp-osc

config DT_HAS_RENESAS_SMARTBOND_LP_OSC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_LP_OSC))

DT_COMPAT_RENESAS_SMARTBOND_PINCTRL := renesas,smartbond-pinctrl

config DT_HAS_RENESAS_SMARTBOND_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_PINCTRL))

DT_COMPAT_RENESAS_SMARTBOND_SDADC := renesas,smartbond-sdadc

config DT_HAS_RENESAS_SMARTBOND_SDADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_SDADC))

DT_COMPAT_RENESAS_SMARTBOND_SPI := renesas,smartbond-spi

config DT_HAS_RENESAS_SMARTBOND_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_SPI))

DT_COMPAT_RENESAS_SMARTBOND_SYS_CLK := renesas,smartbond-sys-clk

config DT_HAS_RENESAS_SMARTBOND_SYS_CLK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_SYS_CLK))

DT_COMPAT_RENESAS_SMARTBOND_TIMER := renesas,smartbond-timer

config DT_HAS_RENESAS_SMARTBOND_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_TIMER))

DT_COMPAT_RENESAS_SMARTBOND_TRNG := renesas,smartbond-trng

config DT_HAS_RENESAS_SMARTBOND_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_TRNG))

DT_COMPAT_RENESAS_SMARTBOND_UART := renesas,smartbond-uart

config DT_HAS_RENESAS_SMARTBOND_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_UART))

DT_COMPAT_RENESAS_SMARTBOND_USBD := renesas,smartbond-usbd

config DT_HAS_RENESAS_SMARTBOND_USBD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_USBD))

DT_COMPAT_RENESAS_SMARTBOND_WATCHDOG := renesas,smartbond-watchdog

config DT_HAS_RENESAS_SMARTBOND_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RENESAS_SMARTBOND_WATCHDOG))

DT_COMPAT_RICHTEK_RT1718S := richtek,rt1718s

config DT_HAS_RICHTEK_RT1718S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RICHTEK_RT1718S))

DT_COMPAT_RICHTEK_RT1718S_GPIO_PORT := richtek,rt1718s-gpio-port

config DT_HAS_RICHTEK_RT1718S_GPIO_PORT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RICHTEK_RT1718S_GPIO_PORT))

DT_COMPAT_RISCV_CPU_INTC := riscv,cpu-intc

config DT_HAS_RISCV_CPU_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_RISCV_CPU_INTC))

DT_COMPAT_ROCKTECH_RK043FN02H_CT := rocktech,rk043fn02h-ct

config DT_HAS_ROCKTECH_RK043FN02H_CT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ROCKTECH_RK043FN02H_CT))

DT_COMPAT_ROHM_BD8LB600FS := rohm,bd8lb600fs

config DT_HAS_ROHM_BD8LB600FS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ROHM_BD8LB600FS))

DT_COMPAT_ROHM_BH1749 := rohm,bh1749

config DT_HAS_ROHM_BH1749_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ROHM_BH1749))

DT_COMPAT_ROHM_BH1750 := rohm,bh1750

config DT_HAS_ROHM_BH1750_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ROHM_BH1750))

DT_COMPAT_SAMPLE_CONTROLLER := sample_controller

config DT_HAS_SAMPLE_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SAMPLE_CONTROLLER))

DT_COMPAT_SBS_DEFAULT_SBS_GAUGE := sbs,default-sbs-gauge

config DT_HAS_SBS_DEFAULT_SBS_GAUGE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SBS_DEFAULT_SBS_GAUGE))

DT_COMPAT_SBS_SBS_GAUGE := sbs,sbs-gauge

config DT_HAS_SBS_SBS_GAUGE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SBS_SBS_GAUGE))

DT_COMPAT_SBS_SBS_GAUGE_NEW_API := sbs,sbs-gauge-new-api

config DT_HAS_SBS_SBS_GAUGE_NEW_API_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SBS_SBS_GAUGE_NEW_API))

DT_COMPAT_SDC_RADIO_COEX_ONE_WIRE := sdc-radio-coex-one-wire

config DT_HAS_SDC_RADIO_COEX_ONE_WIRE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SDC_RADIO_COEX_ONE_WIRE))

DT_COMPAT_SEEED_GROVE_LCD_RGB := seeed,grove-lcd-rgb

config DT_HAS_SEEED_GROVE_LCD_RGB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEEED_GROVE_LCD_RGB))

DT_COMPAT_SEEED_GROVE_LIGHT := seeed,grove-light

config DT_HAS_SEEED_GROVE_LIGHT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEEED_GROVE_LIGHT))

DT_COMPAT_SEEED_GROVE_TEMPERATURE := seeed,grove-temperature

config DT_HAS_SEEED_GROVE_TEMPERATURE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEEED_GROVE_TEMPERATURE))

DT_COMPAT_SEEED_XIAO_GPIO := seeed,xiao-gpio

config DT_HAS_SEEED_XIAO_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEEED_XIAO_GPIO))

DT_COMPAT_SEGGER_ETH_RTT := segger,eth-rtt

config DT_HAS_SEGGER_ETH_RTT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEGGER_ETH_RTT))

DT_COMPAT_SEGGER_RTT_UART := segger,rtt-uart

config DT_HAS_SEGGER_RTT_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEGGER_RTT_UART))

DT_COMPAT_SEMTECH_SX1261 := semtech,sx1261

config DT_HAS_SEMTECH_SX1261_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEMTECH_SX1261))

DT_COMPAT_SEMTECH_SX1262 := semtech,sx1262

config DT_HAS_SEMTECH_SX1262_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEMTECH_SX1262))

DT_COMPAT_SEMTECH_SX1272 := semtech,sx1272

config DT_HAS_SEMTECH_SX1272_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEMTECH_SX1272))

DT_COMPAT_SEMTECH_SX1276 := semtech,sx1276

config DT_HAS_SEMTECH_SX1276_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEMTECH_SX1276))

DT_COMPAT_SEMTECH_SX1509B := semtech,sx1509b

config DT_HAS_SEMTECH_SX1509B_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEMTECH_SX1509B))

DT_COMPAT_SEMTECH_SX9500 := semtech,sx9500

config DT_HAS_SEMTECH_SX9500_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SEMTECH_SX9500))

DT_COMPAT_SENSIRION_SGP40 := sensirion,sgp40

config DT_HAS_SENSIRION_SGP40_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSIRION_SGP40))

DT_COMPAT_SENSIRION_SHT3XD := sensirion,sht3xd

config DT_HAS_SENSIRION_SHT3XD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSIRION_SHT3XD))

DT_COMPAT_SENSIRION_SHT4X := sensirion,sht4x

config DT_HAS_SENSIRION_SHT4X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSIRION_SHT4X))

DT_COMPAT_SENSIRION_SHTCX := sensirion,shtcx

config DT_HAS_SENSIRION_SHTCX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SENSIRION_SHTCX))

DT_COMPAT_SHARED_IRQ := shared-irq

config DT_HAS_SHARED_IRQ_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SHARED_IRQ))

DT_COMPAT_SHARP_LS0XX := sharp,ls0xx

config DT_HAS_SHARP_LS0XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SHARP_LS0XX))

DT_COMPAT_SIEMENS_IVSHMEM_ETH := siemens,ivshmem-eth

config DT_HAS_SIEMENS_IVSHMEM_ETH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIEMENS_IVSHMEM_ETH))

DT_COMPAT_SIFIVE_CLINT0 := sifive,clint0

config DT_HAS_SIFIVE_CLINT0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_CLINT0))

DT_COMPAT_SIFIVE_DTIM0 := sifive,dtim0

config DT_HAS_SIFIVE_DTIM0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_DTIM0))

DT_COMPAT_SIFIVE_E24 := sifive,e24

config DT_HAS_SIFIVE_E24_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_E24))

DT_COMPAT_SIFIVE_E31 := sifive,e31

config DT_HAS_SIFIVE_E31_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_E31))

DT_COMPAT_SIFIVE_E51 := sifive,e51

config DT_HAS_SIFIVE_E51_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_E51))

DT_COMPAT_SIFIVE_FU740_C000_DDR := sifive,fu740-c000-ddr

config DT_HAS_SIFIVE_FU740_C000_DDR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_FU740_C000_DDR))

DT_COMPAT_SIFIVE_GPIO0 := sifive,gpio0

config DT_HAS_SIFIVE_GPIO0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_GPIO0))

DT_COMPAT_SIFIVE_I2C0 := sifive,i2c0

config DT_HAS_SIFIVE_I2C0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_I2C0))

DT_COMPAT_SIFIVE_PINCTRL := sifive,pinctrl

config DT_HAS_SIFIVE_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_PINCTRL))

DT_COMPAT_SIFIVE_PLIC_1_0_0 := sifive,plic-1.0.0

config DT_HAS_SIFIVE_PLIC_1_0_0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_PLIC_1_0_0))

DT_COMPAT_SIFIVE_PWM0 := sifive,pwm0

config DT_HAS_SIFIVE_PWM0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_PWM0))

DT_COMPAT_SIFIVE_S7 := sifive,s7

config DT_HAS_SIFIVE_S7_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_S7))

DT_COMPAT_SIFIVE_SPI0 := sifive,spi0

config DT_HAS_SIFIVE_SPI0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_SPI0))

DT_COMPAT_SIFIVE_UART0 := sifive,uart0

config DT_HAS_SIFIVE_UART0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_UART0))

DT_COMPAT_SIFIVE_WDT := sifive,wdt

config DT_HAS_SIFIVE_WDT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIFIVE_WDT))

DT_COMPAT_SILABS_GECKO_ADC := silabs,gecko-adc

config DT_HAS_SILABS_GECKO_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_ADC))

DT_COMPAT_SILABS_GECKO_BURTC := silabs,gecko-burtc

config DT_HAS_SILABS_GECKO_BURTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_BURTC))

DT_COMPAT_SILABS_GECKO_ETHERNET := silabs,gecko-ethernet

config DT_HAS_SILABS_GECKO_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_ETHERNET))

DT_COMPAT_SILABS_GECKO_FLASH_CONTROLLER := silabs,gecko-flash-controller

config DT_HAS_SILABS_GECKO_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_FLASH_CONTROLLER))

DT_COMPAT_SILABS_GECKO_GPIO := silabs,gecko-gpio

config DT_HAS_SILABS_GECKO_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_GPIO))

DT_COMPAT_SILABS_GECKO_GPIO_PORT := silabs,gecko-gpio-port

config DT_HAS_SILABS_GECKO_GPIO_PORT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_GPIO_PORT))

DT_COMPAT_SILABS_GECKO_I2C := silabs,gecko-i2c

config DT_HAS_SILABS_GECKO_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_I2C))

DT_COMPAT_SILABS_GECKO_IADC := silabs,gecko-iadc

config DT_HAS_SILABS_GECKO_IADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_IADC))

DT_COMPAT_SILABS_GECKO_LEUART := silabs,gecko-leuart

config DT_HAS_SILABS_GECKO_LEUART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_LEUART))

DT_COMPAT_SILABS_GECKO_PINCTRL := silabs,gecko-pinctrl

config DT_HAS_SILABS_GECKO_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_PINCTRL))

DT_COMPAT_SILABS_GECKO_PWM := silabs,gecko-pwm

config DT_HAS_SILABS_GECKO_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_PWM))

DT_COMPAT_SILABS_GECKO_RTCC := silabs,gecko-rtcc

config DT_HAS_SILABS_GECKO_RTCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_RTCC))

DT_COMPAT_SILABS_GECKO_SEMAILBOX := silabs,gecko-semailbox

config DT_HAS_SILABS_GECKO_SEMAILBOX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_SEMAILBOX))

DT_COMPAT_SILABS_GECKO_SPI_USART := silabs,gecko-spi-usart

config DT_HAS_SILABS_GECKO_SPI_USART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_SPI_USART))

DT_COMPAT_SILABS_GECKO_STIMER := silabs,gecko-stimer

config DT_HAS_SILABS_GECKO_STIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_STIMER))

DT_COMPAT_SILABS_GECKO_TIMER := silabs,gecko-timer

config DT_HAS_SILABS_GECKO_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_TIMER))

DT_COMPAT_SILABS_GECKO_TRNG := silabs,gecko-trng

config DT_HAS_SILABS_GECKO_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_TRNG))

DT_COMPAT_SILABS_GECKO_UART := silabs,gecko-uart

config DT_HAS_SILABS_GECKO_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_UART))

DT_COMPAT_SILABS_GECKO_USART := silabs,gecko-usart

config DT_HAS_SILABS_GECKO_USART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_USART))

DT_COMPAT_SILABS_GECKO_WDOG := silabs,gecko-wdog

config DT_HAS_SILABS_GECKO_WDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_GECKO_WDOG))

DT_COMPAT_SILABS_SI7006 := silabs,si7006

config DT_HAS_SILABS_SI7006_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI7006))

DT_COMPAT_SILABS_SI7055 := silabs,si7055

config DT_HAS_SILABS_SI7055_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI7055))

DT_COMPAT_SILABS_SI7060 := silabs,si7060

config DT_HAS_SILABS_SI7060_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI7060))

DT_COMPAT_SILABS_SI7210 := silabs,si7210

config DT_HAS_SILABS_SI7210_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SILABS_SI7210))

DT_COMPAT_SIMCOM_SIM7080 := simcom,sim7080

config DT_HAS_SIMCOM_SIM7080_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SIMCOM_SIM7080))

DT_COMPAT_SITRONIX_ST7735R := sitronix,st7735r

config DT_HAS_SITRONIX_ST7735R_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SITRONIX_ST7735R))

DT_COMPAT_SITRONIX_ST7789V := sitronix,st7789v

config DT_HAS_SITRONIX_ST7789V_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SITRONIX_ST7789V))

DT_COMPAT_SKYWORKS_SKY13351 := skyworks,sky13351

config DT_HAS_SKYWORKS_SKY13351_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SKYWORKS_SKY13351))

DT_COMPAT_SKYWORKS_SKY66112_11 := skyworks,sky66112-11

config DT_HAS_SKYWORKS_SKY66112_11_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SKYWORKS_SKY66112_11))

DT_COMPAT_SKYWORKS_SKY66114_11 := skyworks,sky66114-11

config DT_HAS_SKYWORKS_SKY66114_11_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SKYWORKS_SKY66114_11))

DT_COMPAT_SKYWORKS_SKY66403_11 := skyworks,sky66403-11

config DT_HAS_SKYWORKS_SKY66403_11_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SKYWORKS_SKY66403_11))

DT_COMPAT_SKYWORKS_SKY66407_11 := skyworks,sky66407-11

config DT_HAS_SKYWORKS_SKY66407_11_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SKYWORKS_SKY66407_11))

DT_COMPAT_SMSC_LAN91C111 := smsc,lan91c111

config DT_HAS_SMSC_LAN91C111_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SMSC_LAN91C111))

DT_COMPAT_SMSC_LAN91C111_MDIO := smsc,lan91c111-mdio

config DT_HAS_SMSC_LAN91C111_MDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SMSC_LAN91C111_MDIO))

DT_COMPAT_SMSC_LAN9220 := smsc,lan9220

config DT_HAS_SMSC_LAN9220_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SMSC_LAN9220))

DT_COMPAT_SNPS_ARC_IOT_SYSCONF := snps,arc-iot-sysconf

config DT_HAS_SNPS_ARC_IOT_SYSCONF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_ARC_IOT_SYSCONF))

DT_COMPAT_SNPS_ARC_TIMER := snps,arc-timer

config DT_HAS_SNPS_ARC_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_ARC_TIMER))

DT_COMPAT_SNPS_ARCEM := snps,arcem

config DT_HAS_SNPS_ARCEM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_ARCEM))

DT_COMPAT_SNPS_ARCHS_ICI := snps,archs-ici

config DT_HAS_SNPS_ARCHS_ICI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_ARCHS_ICI))

DT_COMPAT_SNPS_ARCHS_IDU_INTC := snps,archs-idu-intc

config DT_HAS_SNPS_ARCHS_IDU_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_ARCHS_IDU_INTC))

DT_COMPAT_SNPS_ARCV2_INTC := snps,arcv2-intc

config DT_HAS_SNPS_ARCV2_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_ARCV2_INTC))

DT_COMPAT_SNPS_CREG_GPIO := snps,creg-gpio

config DT_HAS_SNPS_CREG_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_CREG_GPIO))

DT_COMPAT_SNPS_DESIGNWARE_DMA := snps,designware-dma

config DT_HAS_SNPS_DESIGNWARE_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_DMA))

DT_COMPAT_SNPS_DESIGNWARE_ETHERNET := snps,designware-ethernet

config DT_HAS_SNPS_DESIGNWARE_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_ETHERNET))

DT_COMPAT_SNPS_DESIGNWARE_GPIO := snps,designware-gpio

config DT_HAS_SNPS_DESIGNWARE_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_GPIO))

DT_COMPAT_SNPS_DESIGNWARE_I2C := snps,designware-i2c

config DT_HAS_SNPS_DESIGNWARE_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_I2C))

DT_COMPAT_SNPS_DESIGNWARE_INTC := snps,designware-intc

config DT_HAS_SNPS_DESIGNWARE_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_INTC))

DT_COMPAT_SNPS_DESIGNWARE_SPI := snps,designware-spi

config DT_HAS_SNPS_DESIGNWARE_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_SPI))

DT_COMPAT_SNPS_DESIGNWARE_USB := snps,designware-usb

config DT_HAS_SNPS_DESIGNWARE_USB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_USB))

DT_COMPAT_SNPS_DESIGNWARE_WATCHDOG := snps,designware-watchdog

config DT_HAS_SNPS_DESIGNWARE_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DESIGNWARE_WATCHDOG))

DT_COMPAT_SNPS_DW_TIMERS := snps,dw-timers

config DT_HAS_SNPS_DW_TIMERS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DW_TIMERS))

DT_COMPAT_SNPS_DWC2 := snps,dwc2

config DT_HAS_SNPS_DWC2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_DWC2))

DT_COMPAT_SNPS_EMSDP_PINCTRL := snps,emsdp-pinctrl

config DT_HAS_SNPS_EMSDP_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_EMSDP_PINCTRL))

DT_COMPAT_SNPS_ETHERNET_CYCLONEV := snps,ethernet-cyclonev

config DT_HAS_SNPS_ETHERNET_CYCLONEV_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_ETHERNET_CYCLONEV))

DT_COMPAT_SNPS_HOSTLINK_UART := snps,hostlink-uart

config DT_HAS_SNPS_HOSTLINK_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_HOSTLINK_UART))

DT_COMPAT_SNPS_NSIM_UART := snps,nsim-uart

config DT_HAS_SNPS_NSIM_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SNPS_NSIM_UART))

DT_COMPAT_SOC_NV_FLASH := soc-nv-flash

config DT_HAS_SOC_NV_FLASH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SOC_NV_FLASH))

DT_COMPAT_SOLOMON_SSD1306FB := solomon,ssd1306fb

config DT_HAS_SOLOMON_SSD1306FB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SOLOMON_SSD1306FB))

DT_COMPAT_SOLOMON_SSD1608 := solomon,ssd1608

config DT_HAS_SOLOMON_SSD1608_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SOLOMON_SSD1608))

DT_COMPAT_SOLOMON_SSD1673 := solomon,ssd1673

config DT_HAS_SOLOMON_SSD1673_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SOLOMON_SSD1673))

DT_COMPAT_SOLOMON_SSD1675A := solomon,ssd1675a

config DT_HAS_SOLOMON_SSD1675A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SOLOMON_SSD1675A))

DT_COMPAT_SOLOMON_SSD1680 := solomon,ssd1680

config DT_HAS_SOLOMON_SSD1680_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SOLOMON_SSD1680))

DT_COMPAT_SOLOMON_SSD1681 := solomon,ssd1681

config DT_HAS_SOLOMON_SSD1681_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SOLOMON_SSD1681))

DT_COMPAT_SPARKFUN_PRO_MICRO_GPIO := sparkfun,pro-micro-gpio

config DT_HAS_SPARKFUN_PRO_MICRO_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SPARKFUN_PRO_MICRO_GPIO))

DT_COMPAT_SQN_HWSPINLOCK := sqn,hwspinlock

config DT_HAS_SQN_HWSPINLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SQN_HWSPINLOCK))

DT_COMPAT_ST_DSI_LCD_QSH_030 := st,dsi-lcd-qsh-030

config DT_HAS_ST_DSI_LCD_QSH_030_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_DSI_LCD_QSH_030))

DT_COMPAT_ST_HTS221 := st,hts221

config DT_HAS_ST_HTS221_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_HTS221))

DT_COMPAT_ST_I3G4250D := st,i3g4250d

config DT_HAS_ST_I3G4250D_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_I3G4250D))

DT_COMPAT_ST_IIS2DH := st,iis2dh

config DT_HAS_ST_IIS2DH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_IIS2DH))

DT_COMPAT_ST_IIS2DLPC := st,iis2dlpc

config DT_HAS_ST_IIS2DLPC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_IIS2DLPC))

DT_COMPAT_ST_IIS2ICLX := st,iis2iclx

config DT_HAS_ST_IIS2ICLX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_IIS2ICLX))

DT_COMPAT_ST_IIS2MDC := st,iis2mdc

config DT_HAS_ST_IIS2MDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_IIS2MDC))

DT_COMPAT_ST_IIS3DHHC := st,iis3dhhc

config DT_HAS_ST_IIS3DHHC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_IIS3DHHC))

DT_COMPAT_ST_ISM330DHCX := st,ism330dhcx

config DT_HAS_ST_ISM330DHCX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_ISM330DHCX))

DT_COMPAT_ST_LIS2DH := st,lis2dh

config DT_HAS_ST_LIS2DH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS2DH))

DT_COMPAT_ST_LIS2DH12 := st,lis2dh12

config DT_HAS_ST_LIS2DH12_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS2DH12))

DT_COMPAT_ST_LIS2DS12 := st,lis2ds12

config DT_HAS_ST_LIS2DS12_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS2DS12))

DT_COMPAT_ST_LIS2DW12 := st,lis2dw12

config DT_HAS_ST_LIS2DW12_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS2DW12))

DT_COMPAT_ST_LIS2MDL := st,lis2mdl

config DT_HAS_ST_LIS2MDL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS2MDL))

DT_COMPAT_ST_LIS3DH := st,lis3dh

config DT_HAS_ST_LIS3DH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS3DH))

DT_COMPAT_ST_LIS3MDL_MAGN := st,lis3mdl-magn

config DT_HAS_ST_LIS3MDL_MAGN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LIS3MDL_MAGN))

DT_COMPAT_ST_LPS22HB_PRESS := st,lps22hb-press

config DT_HAS_ST_LPS22HB_PRESS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LPS22HB_PRESS))

DT_COMPAT_ST_LPS22HH := st,lps22hh

config DT_HAS_ST_LPS22HH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LPS22HH))

DT_COMPAT_ST_LPS25HB_PRESS := st,lps25hb-press

config DT_HAS_ST_LPS25HB_PRESS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LPS25HB_PRESS))

DT_COMPAT_ST_LSM303AGR_ACCEL := st,lsm303agr-accel

config DT_HAS_ST_LSM303AGR_ACCEL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM303AGR_ACCEL))

DT_COMPAT_ST_LSM303DLHC_ACCEL := st,lsm303dlhc-accel

config DT_HAS_ST_LSM303DLHC_ACCEL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM303DLHC_ACCEL))

DT_COMPAT_ST_LSM303DLHC_MAGN := st,lsm303dlhc-magn

config DT_HAS_ST_LSM303DLHC_MAGN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM303DLHC_MAGN))

DT_COMPAT_ST_LSM6DS0 := st,lsm6ds0

config DT_HAS_ST_LSM6DS0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM6DS0))

DT_COMPAT_ST_LSM6DSL := st,lsm6dsl

config DT_HAS_ST_LSM6DSL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM6DSL))

DT_COMPAT_ST_LSM6DSO := st,lsm6dso

config DT_HAS_ST_LSM6DSO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM6DSO))

DT_COMPAT_ST_LSM6DSO16IS := st,lsm6dso16is

config DT_HAS_ST_LSM6DSO16IS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM6DSO16IS))

DT_COMPAT_ST_LSM6DSO32 := st,lsm6dso32

config DT_HAS_ST_LSM6DSO32_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM6DSO32))

DT_COMPAT_ST_LSM6DSV16X := st,lsm6dsv16x

config DT_HAS_ST_LSM6DSV16X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM6DSV16X))

DT_COMPAT_ST_LSM9DS0_GYRO := st,lsm9ds0-gyro

config DT_HAS_ST_LSM9DS0_GYRO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM9DS0_GYRO))

DT_COMPAT_ST_LSM9DS0_MFD := st,lsm9ds0-mfd

config DT_HAS_ST_LSM9DS0_MFD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_LSM9DS0_MFD))

DT_COMPAT_ST_MPXXDTYY := st,mpxxdtyy

config DT_HAS_ST_MPXXDTYY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_MPXXDTYY))

DT_COMPAT_ST_ST25R3911B := st,st25r3911b

config DT_HAS_ST_ST25R3911B_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_ST25R3911B))

DT_COMPAT_ST_STM32_ADC := st,stm32-adc

config DT_HAS_ST_STM32_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_ADC))

DT_COMPAT_ST_STM32_AES := st,stm32-aes

config DT_HAS_ST_STM32_AES_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_AES))

DT_COMPAT_ST_STM32_BACKUP_SRAM := st,stm32-backup-sram

config DT_HAS_ST_STM32_BACKUP_SRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_BACKUP_SRAM))

DT_COMPAT_ST_STM32_BBRAM := st,stm32-bbram

config DT_HAS_ST_STM32_BBRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_BBRAM))

DT_COMPAT_ST_STM32_BDMA := st,stm32-bdma

config DT_HAS_ST_STM32_BDMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_BDMA))

DT_COMPAT_ST_STM32_BXCAN := st,stm32-bxcan

config DT_HAS_ST_STM32_BXCAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_BXCAN))

DT_COMPAT_ST_STM32_CCM := st,stm32-ccm

config DT_HAS_ST_STM32_CCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_CCM))

DT_COMPAT_ST_STM32_CLOCK_MUX := st,stm32-clock-mux

config DT_HAS_ST_STM32_CLOCK_MUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_CLOCK_MUX))

DT_COMPAT_ST_STM32_COUNTER := st,stm32-counter

config DT_HAS_ST_STM32_COUNTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_COUNTER))

DT_COMPAT_ST_STM32_CRYP := st,stm32-cryp

config DT_HAS_ST_STM32_CRYP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_CRYP))

DT_COMPAT_ST_STM32_DAC := st,stm32-dac

config DT_HAS_ST_STM32_DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_DAC))

DT_COMPAT_ST_STM32_DMA := st,stm32-dma

config DT_HAS_ST_STM32_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_DMA))

DT_COMPAT_ST_STM32_DMA_V1 := st,stm32-dma-v1

config DT_HAS_ST_STM32_DMA_V1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_DMA_V1))

DT_COMPAT_ST_STM32_DMA_V2 := st,stm32-dma-v2

config DT_HAS_ST_STM32_DMA_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_DMA_V2))

DT_COMPAT_ST_STM32_DMA_V2BIS := st,stm32-dma-v2bis

config DT_HAS_ST_STM32_DMA_V2BIS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_DMA_V2BIS))

DT_COMPAT_ST_STM32_DMAMUX := st,stm32-dmamux

config DT_HAS_ST_STM32_DMAMUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_DMAMUX))

DT_COMPAT_ST_STM32_EEPROM := st,stm32-eeprom

config DT_HAS_ST_STM32_EEPROM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_EEPROM))

DT_COMPAT_ST_STM32_ETHERNET := st,stm32-ethernet

config DT_HAS_ST_STM32_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_ETHERNET))

DT_COMPAT_ST_STM32_EXTI := st,stm32-exti

config DT_HAS_ST_STM32_EXTI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_EXTI))

DT_COMPAT_ST_STM32_FDCAN := st,stm32-fdcan

config DT_HAS_ST_STM32_FDCAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_FDCAN))

DT_COMPAT_ST_STM32_FLASH_CONTROLLER := st,stm32-flash-controller

config DT_HAS_ST_STM32_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32_FMC := st,stm32-fmc

config DT_HAS_ST_STM32_FMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_FMC))

DT_COMPAT_ST_STM32_FMC_NOR_PSRAM := st,stm32-fmc-nor-psram

config DT_HAS_ST_STM32_FMC_NOR_PSRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_FMC_NOR_PSRAM))

DT_COMPAT_ST_STM32_FMC_SDRAM := st,stm32-fmc-sdram

config DT_HAS_ST_STM32_FMC_SDRAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_FMC_SDRAM))

DT_COMPAT_ST_STM32_GPIO := st,stm32-gpio

config DT_HAS_ST_STM32_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_GPIO))

DT_COMPAT_ST_STM32_HSE_CLOCK := st,stm32-hse-clock

config DT_HAS_ST_STM32_HSE_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_HSE_CLOCK))

DT_COMPAT_ST_STM32_HSEM_MAILBOX := st,stm32-hsem-mailbox

config DT_HAS_ST_STM32_HSEM_MAILBOX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_HSEM_MAILBOX))

DT_COMPAT_ST_STM32_I2C_V1 := st,stm32-i2c-v1

config DT_HAS_ST_STM32_I2C_V1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_I2C_V1))

DT_COMPAT_ST_STM32_I2C_V2 := st,stm32-i2c-v2

config DT_HAS_ST_STM32_I2C_V2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_I2C_V2))

DT_COMPAT_ST_STM32_I2S := st,stm32-i2s

config DT_HAS_ST_STM32_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_I2S))

DT_COMPAT_ST_STM32_IPCC_MAILBOX := st,stm32-ipcc-mailbox

config DT_HAS_ST_STM32_IPCC_MAILBOX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_IPCC_MAILBOX))

DT_COMPAT_ST_STM32_LPTIM := st,stm32-lptim

config DT_HAS_ST_STM32_LPTIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_LPTIM))

DT_COMPAT_ST_STM32_LPUART := st,stm32-lpuart

config DT_HAS_ST_STM32_LPUART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_LPUART))

DT_COMPAT_ST_STM32_LSE_CLOCK := st,stm32-lse-clock

config DT_HAS_ST_STM32_LSE_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_LSE_CLOCK))

DT_COMPAT_ST_STM32_LTDC := st,stm32-ltdc

config DT_HAS_ST_STM32_LTDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_LTDC))

DT_COMPAT_ST_STM32_MIPI_DSI := st,stm32-mipi-dsi

config DT_HAS_ST_STM32_MIPI_DSI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_MIPI_DSI))

DT_COMPAT_ST_STM32_MSI_CLOCK := st,stm32-msi-clock

config DT_HAS_ST_STM32_MSI_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_MSI_CLOCK))

DT_COMPAT_ST_STM32_NV_FLASH := st,stm32-nv-flash

config DT_HAS_ST_STM32_NV_FLASH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_NV_FLASH))

DT_COMPAT_ST_STM32_OSPI := st,stm32-ospi

config DT_HAS_ST_STM32_OSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_OSPI))

DT_COMPAT_ST_STM32_OSPI_NOR := st,stm32-ospi-nor

config DT_HAS_ST_STM32_OSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_OSPI_NOR))

DT_COMPAT_ST_STM32_OTGFS := st,stm32-otgfs

config DT_HAS_ST_STM32_OTGFS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_OTGFS))

DT_COMPAT_ST_STM32_OTGHS := st,stm32-otghs

config DT_HAS_ST_STM32_OTGHS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_OTGHS))

DT_COMPAT_ST_STM32_PINCTRL := st,stm32-pinctrl

config DT_HAS_ST_STM32_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_PINCTRL))

DT_COMPAT_ST_STM32_PWM := st,stm32-pwm

config DT_HAS_ST_STM32_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_PWM))

DT_COMPAT_ST_STM32_QDEC := st,stm32-qdec

config DT_HAS_ST_STM32_QDEC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_QDEC))

DT_COMPAT_ST_STM32_QSPI := st,stm32-qspi

config DT_HAS_ST_STM32_QSPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_QSPI))

DT_COMPAT_ST_STM32_QSPI_NOR := st,stm32-qspi-nor

config DT_HAS_ST_STM32_QSPI_NOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_QSPI_NOR))

DT_COMPAT_ST_STM32_RCC := st,stm32-rcc

config DT_HAS_ST_STM32_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_RCC))

DT_COMPAT_ST_STM32_RCC_RCTL := st,stm32-rcc-rctl

config DT_HAS_ST_STM32_RCC_RCTL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_RCC_RCTL))

DT_COMPAT_ST_STM32_RNG := st,stm32-rng

config DT_HAS_ST_STM32_RNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_RNG))

DT_COMPAT_ST_STM32_RTC := st,stm32-rtc

config DT_HAS_ST_STM32_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_RTC))

DT_COMPAT_ST_STM32_SDMMC := st,stm32-sdmmc

config DT_HAS_ST_STM32_SDMMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_SDMMC))

DT_COMPAT_ST_STM32_SPI := st,stm32-spi

config DT_HAS_ST_STM32_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_SPI))

DT_COMPAT_ST_STM32_SPI_FIFO := st,stm32-spi-fifo

config DT_HAS_ST_STM32_SPI_FIFO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_SPI_FIFO))

DT_COMPAT_ST_STM32_SPI_HOST_CMD := st,stm32-spi-host-cmd

config DT_HAS_ST_STM32_SPI_HOST_CMD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_SPI_HOST_CMD))

DT_COMPAT_ST_STM32_SPI_SUBGHZ := st,stm32-spi-subghz

config DT_HAS_ST_STM32_SPI_SUBGHZ_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_SPI_SUBGHZ))

DT_COMPAT_ST_STM32_TEMP := st,stm32-temp

config DT_HAS_ST_STM32_TEMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_TEMP))

DT_COMPAT_ST_STM32_TEMP_CAL := st,stm32-temp-cal

config DT_HAS_ST_STM32_TEMP_CAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_TEMP_CAL))

DT_COMPAT_ST_STM32_TIMERS := st,stm32-timers

config DT_HAS_ST_STM32_TIMERS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_TIMERS))

DT_COMPAT_ST_STM32_UART := st,stm32-uart

config DT_HAS_ST_STM32_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_UART))

DT_COMPAT_ST_STM32_UCPD := st,stm32-ucpd

config DT_HAS_ST_STM32_UCPD_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_UCPD))

DT_COMPAT_ST_STM32_USART := st,stm32-usart

config DT_HAS_ST_STM32_USART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_USART))

DT_COMPAT_ST_STM32_USB := st,stm32-usb

config DT_HAS_ST_STM32_USB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_USB))

DT_COMPAT_ST_STM32_USBPHYC := st,stm32-usbphyc

config DT_HAS_ST_STM32_USBPHYC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_USBPHYC))

DT_COMPAT_ST_STM32_VBAT := st,stm32-vbat

config DT_HAS_ST_STM32_VBAT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_VBAT))

DT_COMPAT_ST_STM32_VREF := st,stm32-vref

config DT_HAS_ST_STM32_VREF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_VREF))

DT_COMPAT_ST_STM32_WATCHDOG := st,stm32-watchdog

config DT_HAS_ST_STM32_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_WATCHDOG))

DT_COMPAT_ST_STM32_WINDOW_WATCHDOG := st,stm32-window-watchdog

config DT_HAS_ST_STM32_WINDOW_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32_WINDOW_WATCHDOG))

DT_COMPAT_ST_STM32C0_HSI_CLOCK := st,stm32c0-hsi-clock

config DT_HAS_ST_STM32C0_HSI_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32C0_HSI_CLOCK))

DT_COMPAT_ST_STM32C0_TEMP_CAL := st,stm32c0-temp-cal

config DT_HAS_ST_STM32C0_TEMP_CAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32C0_TEMP_CAL))

DT_COMPAT_ST_STM32F0_PLL_CLOCK := st,stm32f0-pll-clock

config DT_HAS_ST_STM32F0_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F0_PLL_CLOCK))

DT_COMPAT_ST_STM32F0_RCC := st,stm32f0-rcc

config DT_HAS_ST_STM32F0_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F0_RCC))

DT_COMPAT_ST_STM32F1_ADC := st,stm32f1-adc

config DT_HAS_ST_STM32F1_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F1_ADC))

DT_COMPAT_ST_STM32F1_FLASH_CONTROLLER := st,stm32f1-flash-controller

config DT_HAS_ST_STM32F1_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F1_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32F1_PINCTRL := st,stm32f1-pinctrl

config DT_HAS_ST_STM32F1_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F1_PINCTRL))

DT_COMPAT_ST_STM32F1_PLL_CLOCK := st,stm32f1-pll-clock

config DT_HAS_ST_STM32F1_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F1_PLL_CLOCK))

DT_COMPAT_ST_STM32F1_RCC := st,stm32f1-rcc

config DT_HAS_ST_STM32F1_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F1_RCC))

DT_COMPAT_ST_STM32F100_PLL_CLOCK := st,stm32f100-pll-clock

config DT_HAS_ST_STM32F100_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F100_PLL_CLOCK))

DT_COMPAT_ST_STM32F105_PLL_CLOCK := st,stm32f105-pll-clock

config DT_HAS_ST_STM32F105_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F105_PLL_CLOCK))

DT_COMPAT_ST_STM32F105_PLL2_CLOCK := st,stm32f105-pll2-clock

config DT_HAS_ST_STM32F105_PLL2_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F105_PLL2_CLOCK))

DT_COMPAT_ST_STM32F2_FLASH_CONTROLLER := st,stm32f2-flash-controller

config DT_HAS_ST_STM32F2_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F2_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32F2_PLL_CLOCK := st,stm32f2-pll-clock

config DT_HAS_ST_STM32F2_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F2_PLL_CLOCK))

DT_COMPAT_ST_STM32F3_RCC := st,stm32f3-rcc

config DT_HAS_ST_STM32F3_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F3_RCC))

DT_COMPAT_ST_STM32F4_ADC := st,stm32f4-adc

config DT_HAS_ST_STM32F4_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F4_ADC))

DT_COMPAT_ST_STM32F4_FLASH_CONTROLLER := st,stm32f4-flash-controller

config DT_HAS_ST_STM32F4_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F4_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32F4_FSOTG := st,stm32f4-fsotg

config DT_HAS_ST_STM32F4_FSOTG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F4_FSOTG))

DT_COMPAT_ST_STM32F4_PLL_CLOCK := st,stm32f4-pll-clock

config DT_HAS_ST_STM32F4_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F4_PLL_CLOCK))

DT_COMPAT_ST_STM32F4_PLLI2S_CLOCK := st,stm32f4-plli2s-clock

config DT_HAS_ST_STM32F4_PLLI2S_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F4_PLLI2S_CLOCK))

DT_COMPAT_ST_STM32F412_PLLI2S_CLOCK := st,stm32f412-plli2s-clock

config DT_HAS_ST_STM32F412_PLLI2S_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F412_PLLI2S_CLOCK))

DT_COMPAT_ST_STM32F7_FLASH_CONTROLLER := st,stm32f7-flash-controller

config DT_HAS_ST_STM32F7_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F7_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32F7_PLL_CLOCK := st,stm32f7-pll-clock

config DT_HAS_ST_STM32F7_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32F7_PLL_CLOCK))

DT_COMPAT_ST_STM32G0_EXTI := st,stm32g0-exti

config DT_HAS_ST_STM32G0_EXTI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32G0_EXTI))

DT_COMPAT_ST_STM32G0_FLASH_CONTROLLER := st,stm32g0-flash-controller

config DT_HAS_ST_STM32G0_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32G0_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32G0_HSI_CLOCK := st,stm32g0-hsi-clock

config DT_HAS_ST_STM32G0_HSI_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32G0_HSI_CLOCK))

DT_COMPAT_ST_STM32G0_PLL_CLOCK := st,stm32g0-pll-clock

config DT_HAS_ST_STM32G0_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32G0_PLL_CLOCK))

DT_COMPAT_ST_STM32G4_FLASH_CONTROLLER := st,stm32g4-flash-controller

config DT_HAS_ST_STM32G4_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32G4_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32G4_PLL_CLOCK := st,stm32g4-pll-clock

config DT_HAS_ST_STM32G4_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32G4_PLL_CLOCK))

DT_COMPAT_ST_STM32H7_FDCAN := st,stm32h7-fdcan

config DT_HAS_ST_STM32H7_FDCAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7_FDCAN))

DT_COMPAT_ST_STM32H7_FLASH_CONTROLLER := st,stm32h7-flash-controller

config DT_HAS_ST_STM32H7_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32H7_FMC := st,stm32h7-fmc

config DT_HAS_ST_STM32H7_FMC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7_FMC))

DT_COMPAT_ST_STM32H7_HSI_CLOCK := st,stm32h7-hsi-clock

config DT_HAS_ST_STM32H7_HSI_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7_HSI_CLOCK))

DT_COMPAT_ST_STM32H7_PLL_CLOCK := st,stm32h7-pll-clock

config DT_HAS_ST_STM32H7_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7_PLL_CLOCK))

DT_COMPAT_ST_STM32H7_RCC := st,stm32h7-rcc

config DT_HAS_ST_STM32H7_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7_RCC))

DT_COMPAT_ST_STM32H7_SPI := st,stm32h7-spi

config DT_HAS_ST_STM32H7_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32H7_SPI))

DT_COMPAT_ST_STM32L0_MSI_CLOCK := st,stm32l0-msi-clock

config DT_HAS_ST_STM32L0_MSI_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32L0_MSI_CLOCK))

DT_COMPAT_ST_STM32L0_PLL_CLOCK := st,stm32l0-pll-clock

config DT_HAS_ST_STM32L0_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32L0_PLL_CLOCK))

DT_COMPAT_ST_STM32L4_FLASH_CONTROLLER := st,stm32l4-flash-controller

config DT_HAS_ST_STM32L4_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32L4_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32L4_PLL_CLOCK := st,stm32l4-pll-clock

config DT_HAS_ST_STM32L4_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32L4_PLL_CLOCK))

DT_COMPAT_ST_STM32L5_FLASH_CONTROLLER := st,stm32l5-flash-controller

config DT_HAS_ST_STM32L5_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32L5_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32MP1_RCC := st,stm32mp1-rcc

config DT_HAS_ST_STM32MP1_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32MP1_RCC))

DT_COMPAT_ST_STM32U5_DMA := st,stm32u5-dma

config DT_HAS_ST_STM32U5_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32U5_DMA))

DT_COMPAT_ST_STM32U5_MSI_CLOCK := st,stm32u5-msi-clock

config DT_HAS_ST_STM32U5_MSI_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32U5_MSI_CLOCK))

DT_COMPAT_ST_STM32U5_PLL_CLOCK := st,stm32u5-pll-clock

config DT_HAS_ST_STM32U5_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32U5_PLL_CLOCK))

DT_COMPAT_ST_STM32U5_RCC := st,stm32u5-rcc

config DT_HAS_ST_STM32U5_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32U5_RCC))

DT_COMPAT_ST_STM32WB_FLASH_CONTROLLER := st,stm32wb-flash-controller

config DT_HAS_ST_STM32WB_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WB_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32WB_PLL_CLOCK := st,stm32wb-pll-clock

config DT_HAS_ST_STM32WB_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WB_PLL_CLOCK))

DT_COMPAT_ST_STM32WB_RCC := st,stm32wb-rcc

config DT_HAS_ST_STM32WB_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WB_RCC))

DT_COMPAT_ST_STM32WB_RF := st,stm32wb-rf

config DT_HAS_ST_STM32WB_RF_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WB_RF))

DT_COMPAT_ST_STM32WBA_FLASH_CONTROLLER := st,stm32wba-flash-controller

config DT_HAS_ST_STM32WBA_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WBA_FLASH_CONTROLLER))

DT_COMPAT_ST_STM32WBA_HSE_CLOCK := st,stm32wba-hse-clock

config DT_HAS_ST_STM32WBA_HSE_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WBA_HSE_CLOCK))

DT_COMPAT_ST_STM32WBA_PLL_CLOCK := st,stm32wba-pll-clock

config DT_HAS_ST_STM32WBA_PLL_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WBA_PLL_CLOCK))

DT_COMPAT_ST_STM32WBA_RCC := st,stm32wba-rcc

config DT_HAS_ST_STM32WBA_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WBA_RCC))

DT_COMPAT_ST_STM32WL_HSE_CLOCK := st,stm32wl-hse-clock

config DT_HAS_ST_STM32WL_HSE_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WL_HSE_CLOCK))

DT_COMPAT_ST_STM32WL_RCC := st,stm32wl-rcc

config DT_HAS_ST_STM32WL_RCC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WL_RCC))

DT_COMPAT_ST_STM32WL_SUBGHZ_RADIO := st,stm32wl-subghz-radio

config DT_HAS_ST_STM32WL_SUBGHZ_RADIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STM32WL_SUBGHZ_RADIO))

DT_COMPAT_ST_STMPE1600 := st,stmpe1600

config DT_HAS_ST_STMPE1600_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STMPE1600))

DT_COMPAT_ST_STTS751 := st,stts751

config DT_HAS_ST_STTS751_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_STTS751))

DT_COMPAT_ST_VL53L0X := st,vl53l0x

config DT_HAS_ST_VL53L0X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_VL53L0X))

DT_COMPAT_ST_VL53L1X := st,vl53l1x

config DT_HAS_ST_VL53L1X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_VL53L1X))

DT_COMPAT_ST_MORPHO_HEADER := st-morpho-header

config DT_HAS_ST_MORPHO_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ST_MORPHO_HEADER))

DT_COMPAT_STARFIVE_JH7100_CLINT := starfive,jh7100-clint

config DT_HAS_STARFIVE_JH7100_CLINT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_STARFIVE_JH7100_CLINT))

DT_COMPAT_SWERV_PIC := swerv,pic

config DT_HAS_SWERV_PIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SWERV_PIC))

DT_COMPAT_SWIR_HL7800 := swir,hl7800

config DT_HAS_SWIR_HL7800_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SWIR_HL7800))

DT_COMPAT_SYSCON := syscon

config DT_HAS_SYSCON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_SYSCON))

DT_COMPAT_TELINK_B91 := telink,b91

config DT_HAS_TELINK_B91_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91))

DT_COMPAT_TELINK_B91_ADC := telink,b91-adc

config DT_HAS_TELINK_B91_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_ADC))

DT_COMPAT_TELINK_B91_FLASH_CONTROLLER := telink,b91-flash-controller

config DT_HAS_TELINK_B91_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_FLASH_CONTROLLER))

DT_COMPAT_TELINK_B91_GPIO := telink,b91-gpio

config DT_HAS_TELINK_B91_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_GPIO))

DT_COMPAT_TELINK_B91_I2C := telink,b91-i2c

config DT_HAS_TELINK_B91_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_I2C))

DT_COMPAT_TELINK_B91_PINCTRL := telink,b91-pinctrl

config DT_HAS_TELINK_B91_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_PINCTRL))

DT_COMPAT_TELINK_B91_POWER := telink,b91-power

config DT_HAS_TELINK_B91_POWER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_POWER))

DT_COMPAT_TELINK_B91_PWM := telink,b91-pwm

config DT_HAS_TELINK_B91_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_PWM))

DT_COMPAT_TELINK_B91_SPI := telink,b91-spi

config DT_HAS_TELINK_B91_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_SPI))

DT_COMPAT_TELINK_B91_TRNG := telink,b91-trng

config DT_HAS_TELINK_B91_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_TRNG))

DT_COMPAT_TELINK_B91_UART := telink,b91-uart

config DT_HAS_TELINK_B91_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_UART))

DT_COMPAT_TELINK_B91_ZB := telink,b91-zb

config DT_HAS_TELINK_B91_ZB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_B91_ZB))

DT_COMPAT_TELINK_MACHINE_TIMER := telink,machine-timer

config DT_HAS_TELINK_MACHINE_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TELINK_MACHINE_TIMER))

DT_COMPAT_TEST_GPIO_ENABLE_DISABLE_INTERRUPT := test-gpio-enable-disable-interrupt

config DT_HAS_TEST_GPIO_ENABLE_DISABLE_INTERRUPT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TEST_GPIO_ENABLE_DISABLE_INTERRUPT))

DT_COMPAT_TI_ADS1013 := ti,ads1013

config DT_HAS_TI_ADS1013_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS1013))

DT_COMPAT_TI_ADS1014 := ti,ads1014

config DT_HAS_TI_ADS1014_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS1014))

DT_COMPAT_TI_ADS1015 := ti,ads1015

config DT_HAS_TI_ADS1015_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS1015))

DT_COMPAT_TI_ADS1112 := ti,ads1112

config DT_HAS_TI_ADS1112_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS1112))

DT_COMPAT_TI_ADS1113 := ti,ads1113

config DT_HAS_TI_ADS1113_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS1113))

DT_COMPAT_TI_ADS1114 := ti,ads1114

config DT_HAS_TI_ADS1114_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS1114))

DT_COMPAT_TI_ADS1115 := ti,ads1115

config DT_HAS_TI_ADS1115_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS1115))

DT_COMPAT_TI_ADS1119 := ti,ads1119

config DT_HAS_TI_ADS1119_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS1119))

DT_COMPAT_TI_ADS114S08 := ti,ads114s08

config DT_HAS_TI_ADS114S08_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS114S08))

DT_COMPAT_TI_ADS114S0X_GPIO := ti,ads114s0x-gpio

config DT_HAS_TI_ADS114S0X_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS114S0X_GPIO))

DT_COMPAT_TI_ADS7052 := ti,ads7052

config DT_HAS_TI_ADS7052_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_ADS7052))

DT_COMPAT_TI_BOOSTERPACK_HEADER := ti,boosterpack-header

config DT_HAS_TI_BOOSTERPACK_HEADER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_BOOSTERPACK_HEADER))

DT_COMPAT_TI_BQ274XX := ti,bq274xx

config DT_HAS_TI_BQ274XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_BQ274XX))

DT_COMPAT_TI_CC1200 := ti,cc1200

config DT_HAS_TI_CC1200_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC1200))

DT_COMPAT_TI_CC13XX_CC26XX_ADC := ti,cc13xx-cc26xx-adc

config DT_HAS_TI_CC13XX_CC26XX_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_ADC))

DT_COMPAT_TI_CC13XX_CC26XX_FLASH_CONTROLLER := ti,cc13xx-cc26xx-flash-controller

config DT_HAS_TI_CC13XX_CC26XX_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_FLASH_CONTROLLER))

DT_COMPAT_TI_CC13XX_CC26XX_GPIO := ti,cc13xx-cc26xx-gpio

config DT_HAS_TI_CC13XX_CC26XX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_GPIO))

DT_COMPAT_TI_CC13XX_CC26XX_I2C := ti,cc13xx-cc26xx-i2c

config DT_HAS_TI_CC13XX_CC26XX_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_I2C))

DT_COMPAT_TI_CC13XX_CC26XX_IEEE802154 := ti,cc13xx-cc26xx-ieee802154

config DT_HAS_TI_CC13XX_CC26XX_IEEE802154_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_IEEE802154))

DT_COMPAT_TI_CC13XX_CC26XX_IEEE802154_SUBGHZ := ti,cc13xx-cc26xx-ieee802154-subghz

config DT_HAS_TI_CC13XX_CC26XX_IEEE802154_SUBGHZ_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_IEEE802154_SUBGHZ))

DT_COMPAT_TI_CC13XX_CC26XX_PINCTRL := ti,cc13xx-cc26xx-pinctrl

config DT_HAS_TI_CC13XX_CC26XX_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_PINCTRL))

DT_COMPAT_TI_CC13XX_CC26XX_RADIO := ti,cc13xx-cc26xx-radio

config DT_HAS_TI_CC13XX_CC26XX_RADIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_RADIO))

DT_COMPAT_TI_CC13XX_CC26XX_RTC_TIMER := ti,cc13xx-cc26xx-rtc-timer

config DT_HAS_TI_CC13XX_CC26XX_RTC_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_RTC_TIMER))

DT_COMPAT_TI_CC13XX_CC26XX_SPI := ti,cc13xx-cc26xx-spi

config DT_HAS_TI_CC13XX_CC26XX_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_SPI))

DT_COMPAT_TI_CC13XX_CC26XX_TIMER := ti,cc13xx-cc26xx-timer

config DT_HAS_TI_CC13XX_CC26XX_TIMER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_TIMER))

DT_COMPAT_TI_CC13XX_CC26XX_TIMER_PWM := ti,cc13xx-cc26xx-timer-pwm

config DT_HAS_TI_CC13XX_CC26XX_TIMER_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_TIMER_PWM))

DT_COMPAT_TI_CC13XX_CC26XX_TRNG := ti,cc13xx-cc26xx-trng

config DT_HAS_TI_CC13XX_CC26XX_TRNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_TRNG))

DT_COMPAT_TI_CC13XX_CC26XX_UART := ti,cc13xx-cc26xx-uart

config DT_HAS_TI_CC13XX_CC26XX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_UART))

DT_COMPAT_TI_CC13XX_CC26XX_WATCHDOG := ti,cc13xx-cc26xx-watchdog

config DT_HAS_TI_CC13XX_CC26XX_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC13XX_CC26XX_WATCHDOG))

DT_COMPAT_TI_CC2520 := ti,cc2520

config DT_HAS_TI_CC2520_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC2520))

DT_COMPAT_TI_CC32XX_ADC := ti,cc32xx-adc

config DT_HAS_TI_CC32XX_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC32XX_ADC))

DT_COMPAT_TI_CC32XX_GPIO := ti,cc32xx-gpio

config DT_HAS_TI_CC32XX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC32XX_GPIO))

DT_COMPAT_TI_CC32XX_I2C := ti,cc32xx-i2c

config DT_HAS_TI_CC32XX_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC32XX_I2C))

DT_COMPAT_TI_CC32XX_PINCTRL := ti,cc32xx-pinctrl

config DT_HAS_TI_CC32XX_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC32XX_PINCTRL))

DT_COMPAT_TI_CC32XX_UART := ti,cc32xx-uart

config DT_HAS_TI_CC32XX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC32XX_UART))

DT_COMPAT_TI_CC32XX_WATCHDOG := ti,cc32xx-watchdog

config DT_HAS_TI_CC32XX_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_CC32XX_WATCHDOG))

DT_COMPAT_TI_DAC43608 := ti,dac43608

config DT_HAS_TI_DAC43608_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DAC43608))

DT_COMPAT_TI_DAC53608 := ti,dac53608

config DT_HAS_TI_DAC53608_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DAC53608))

DT_COMPAT_TI_DAC60508 := ti,dac60508

config DT_HAS_TI_DAC60508_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DAC60508))

DT_COMPAT_TI_DAC70508 := ti,dac70508

config DT_HAS_TI_DAC70508_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DAC70508))

DT_COMPAT_TI_DAC80508 := ti,dac80508

config DT_HAS_TI_DAC80508_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DAC80508))

DT_COMPAT_TI_DAVINCI_GPIO := ti,davinci-gpio

config DT_HAS_TI_DAVINCI_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DAVINCI_GPIO))

DT_COMPAT_TI_DAVINCI_GPIO_NEXUS := ti,davinci-gpio-nexus

config DT_HAS_TI_DAVINCI_GPIO_NEXUS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_DAVINCI_GPIO_NEXUS))

DT_COMPAT_TI_FDC2X1X := ti,fdc2x1x

config DT_HAS_TI_FDC2X1X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_FDC2X1X))

DT_COMPAT_TI_HDC := ti,hdc

config DT_HAS_TI_HDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_HDC))

DT_COMPAT_TI_HDC2010 := ti,hdc2010

config DT_HAS_TI_HDC2010_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_HDC2010))

DT_COMPAT_TI_HDC2021 := ti,hdc2021

config DT_HAS_TI_HDC2021_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_HDC2021))

DT_COMPAT_TI_HDC2022 := ti,hdc2022

config DT_HAS_TI_HDC2022_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_HDC2022))

DT_COMPAT_TI_HDC2080 := ti,hdc2080

config DT_HAS_TI_HDC2080_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_HDC2080))

DT_COMPAT_TI_HDC20XX := ti,hdc20xx

config DT_HAS_TI_HDC20XX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_HDC20XX))

DT_COMPAT_TI_INA219 := ti,ina219

config DT_HAS_TI_INA219_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_INA219))

DT_COMPAT_TI_INA230 := ti,ina230

config DT_HAS_TI_INA230_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_INA230))

DT_COMPAT_TI_INA237 := ti,ina237

config DT_HAS_TI_INA237_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_INA237))

DT_COMPAT_TI_INA3221 := ti,ina3221

config DT_HAS_TI_INA3221_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_INA3221))

DT_COMPAT_TI_K3_PINCTRL := ti,k3-pinctrl

config DT_HAS_TI_K3_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_K3_PINCTRL))

DT_COMPAT_TI_LMP90077 := ti,lmp90077

config DT_HAS_TI_LMP90077_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90077))

DT_COMPAT_TI_LMP90078 := ti,lmp90078

config DT_HAS_TI_LMP90078_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90078))

DT_COMPAT_TI_LMP90079 := ti,lmp90079

config DT_HAS_TI_LMP90079_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90079))

DT_COMPAT_TI_LMP90080 := ti,lmp90080

config DT_HAS_TI_LMP90080_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90080))

DT_COMPAT_TI_LMP90097 := ti,lmp90097

config DT_HAS_TI_LMP90097_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90097))

DT_COMPAT_TI_LMP90098 := ti,lmp90098

config DT_HAS_TI_LMP90098_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90098))

DT_COMPAT_TI_LMP90099 := ti,lmp90099

config DT_HAS_TI_LMP90099_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90099))

DT_COMPAT_TI_LMP90100 := ti,lmp90100

config DT_HAS_TI_LMP90100_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90100))

DT_COMPAT_TI_LMP90XXX_GPIO := ti,lmp90xxx-gpio

config DT_HAS_TI_LMP90XXX_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LMP90XXX_GPIO))

DT_COMPAT_TI_LP3943 := ti,lp3943

config DT_HAS_TI_LP3943_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP3943))

DT_COMPAT_TI_LP5009 := ti,lp5009

config DT_HAS_TI_LP5009_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP5009))

DT_COMPAT_TI_LP5012 := ti,lp5012

config DT_HAS_TI_LP5012_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP5012))

DT_COMPAT_TI_LP5018 := ti,lp5018

config DT_HAS_TI_LP5018_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP5018))

DT_COMPAT_TI_LP5024 := ti,lp5024

config DT_HAS_TI_LP5024_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP5024))

DT_COMPAT_TI_LP5030 := ti,lp5030

config DT_HAS_TI_LP5030_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP5030))

DT_COMPAT_TI_LP5036 := ti,lp5036

config DT_HAS_TI_LP5036_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP5036))

DT_COMPAT_TI_LP5562 := ti,lp5562

config DT_HAS_TI_LP5562_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP5562))

DT_COMPAT_TI_LP5569 := ti,lp5569

config DT_HAS_TI_LP5569_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_LP5569))

DT_COMPAT_TI_MSP432P4XX_UART := ti,msp432p4xx-uart

config DT_HAS_TI_MSP432P4XX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_MSP432P4XX_UART))

DT_COMPAT_TI_OPT3001 := ti,opt3001

config DT_HAS_TI_OPT3001_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_OPT3001))

DT_COMPAT_TI_SN74HC595 := ti,sn74hc595

config DT_HAS_TI_SN74HC595_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_SN74HC595))

DT_COMPAT_TI_STELLARIS_ETHERNET := ti,stellaris-ethernet

config DT_HAS_TI_STELLARIS_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_STELLARIS_ETHERNET))

DT_COMPAT_TI_STELLARIS_FLASH_CONTROLLER := ti,stellaris-flash-controller

config DT_HAS_TI_STELLARIS_FLASH_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_STELLARIS_FLASH_CONTROLLER))

DT_COMPAT_TI_STELLARIS_GPIO := ti,stellaris-gpio

config DT_HAS_TI_STELLARIS_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_STELLARIS_GPIO))

DT_COMPAT_TI_STELLARIS_UART := ti,stellaris-uart

config DT_HAS_TI_STELLARIS_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_STELLARIS_UART))

DT_COMPAT_TI_TCA6424A := ti,tca6424a

config DT_HAS_TI_TCA6424A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TCA6424A))

DT_COMPAT_TI_TCA9538 := ti,tca9538

config DT_HAS_TI_TCA9538_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TCA9538))

DT_COMPAT_TI_TCA9546A := ti,tca9546a

config DT_HAS_TI_TCA9546A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TCA9546A))

DT_COMPAT_TI_TCA9548A := ti,tca9548a

config DT_HAS_TI_TCA9548A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TCA9548A))

DT_COMPAT_TI_TLA2021 := ti,tla2021

config DT_HAS_TI_TLA2021_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TLA2021))

DT_COMPAT_TI_TLC59108 := ti,tlc59108

config DT_HAS_TI_TLC59108_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TLC59108))

DT_COMPAT_TI_TLC5971 := ti,tlc5971

config DT_HAS_TI_TLC5971_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TLC5971))

DT_COMPAT_TI_TLV320DAC := ti,tlv320dac

config DT_HAS_TI_TLV320DAC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TLV320DAC))

DT_COMPAT_TI_TMP007 := ti,tmp007

config DT_HAS_TI_TMP007_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TMP007))

DT_COMPAT_TI_TMP108 := ti,tmp108

config DT_HAS_TI_TMP108_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TMP108))

DT_COMPAT_TI_TMP112 := ti,tmp112

config DT_HAS_TI_TMP112_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TMP112))

DT_COMPAT_TI_TMP116 := ti,tmp116

config DT_HAS_TI_TMP116_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TMP116))

DT_COMPAT_TI_TMP116_EEPROM := ti,tmp116-eeprom

config DT_HAS_TI_TMP116_EEPROM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TMP116_EEPROM))

DT_COMPAT_TI_TPS382X := ti,tps382x

config DT_HAS_TI_TPS382X_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_TPS382X))

DT_COMPAT_TI_VIM := ti,vim

config DT_HAS_TI_VIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_TI_VIM))

DT_COMPAT_U_BLOX_SARA_R4 := u-blox,sara-r4

config DT_HAS_U_BLOX_SARA_R4_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_U_BLOX_SARA_R4))

DT_COMPAT_ULTRACHIP_UC8176 := ultrachip,uc8176

config DT_HAS_ULTRACHIP_UC8176_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ULTRACHIP_UC8176))

DT_COMPAT_ULTRACHIP_UC8179 := ultrachip,uc8179

config DT_HAS_ULTRACHIP_UC8179_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ULTRACHIP_UC8179))

DT_COMPAT_USB_AUDIO := usb-audio

config DT_HAS_USB_AUDIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_USB_AUDIO))

DT_COMPAT_USB_AUDIO_HP := usb-audio-hp

config DT_HAS_USB_AUDIO_HP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_USB_AUDIO_HP))

DT_COMPAT_USB_AUDIO_HS := usb-audio-hs

config DT_HAS_USB_AUDIO_HS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_USB_AUDIO_HS))

DT_COMPAT_USB_AUDIO_MIC := usb-audio-mic

config DT_HAS_USB_AUDIO_MIC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_USB_AUDIO_MIC))

DT_COMPAT_USB_C_CONNECTOR := usb-c-connector

config DT_HAS_USB_C_CONNECTOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_USB_C_CONNECTOR))

DT_COMPAT_USB_NOP_XCEIV := usb-nop-xceiv

config DT_HAS_USB_NOP_XCEIV_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_USB_NOP_XCEIV))

DT_COMPAT_USB_ULPI_PHY := usb-ulpi-phy

config DT_HAS_USB_ULPI_PHY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_USB_ULPI_PHY))

DT_COMPAT_VEXRISCV_INTC0 := vexriscv-intc0

config DT_HAS_VEXRISCV_INTC0_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VEXRISCV_INTC0))

DT_COMPAT_VISHAY_VCNL4040 := vishay,vcnl4040

config DT_HAS_VISHAY_VCNL4040_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VISHAY_VCNL4040))

DT_COMPAT_VISHAY_VEML7700 := vishay,veml7700

config DT_HAS_VISHAY_VEML7700_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VISHAY_VEML7700))

DT_COMPAT_VND_ADC := vnd,adc

config DT_HAS_VND_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ADC))

DT_COMPAT_VND_ADC_TEMP_SENSOR := vnd,adc-temp-sensor

config DT_HAS_VND_ADC_TEMP_SENSOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ADC_TEMP_SENSOR))

DT_COMPAT_VND_ARRAY_HOLDER := vnd,array-holder

config DT_HAS_VND_ARRAY_HOLDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ARRAY_HOLDER))

DT_COMPAT_VND_BUSY_SIM := vnd,busy-sim

config DT_HAS_VND_BUSY_SIM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_BUSY_SIM))

DT_COMPAT_VND_CAN_CONTROLLER := vnd,can-controller

config DT_HAS_VND_CAN_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_CAN_CONTROLLER))

DT_COMPAT_VND_CAN_TRANSCEIVER := vnd,can-transceiver

config DT_HAS_VND_CAN_TRANSCEIVER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_CAN_TRANSCEIVER))

DT_COMPAT_VND_CHILD_BINDINGS := vnd,child-bindings

config DT_HAS_VND_CHILD_BINDINGS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_CHILD_BINDINGS))

DT_COMPAT_VND_CLOCK := vnd,clock

config DT_HAS_VND_CLOCK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_CLOCK))

DT_COMPAT_VND_DEVICE_WITH_PROPS := vnd,device-with-props

config DT_HAS_VND_DEVICE_WITH_PROPS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_DEVICE_WITH_PROPS))

DT_COMPAT_VND_DISABLED_COMPAT := vnd,disabled-compat

config DT_HAS_VND_DISABLED_COMPAT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_DISABLED_COMPAT))

DT_COMPAT_VND_DMA := vnd,dma

config DT_HAS_VND_DMA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_DMA))

DT_COMPAT_VND_EMUL_TESTER := vnd,emul-tester

config DT_HAS_VND_EMUL_TESTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_EMUL_TESTER))

DT_COMPAT_VND_ENUM_HOLDER := vnd,enum-holder

config DT_HAS_VND_ENUM_HOLDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ENUM_HOLDER))

DT_COMPAT_VND_ENUM_HOLDER_INST := vnd,enum-holder-inst

config DT_HAS_VND_ENUM_HOLDER_INST_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ENUM_HOLDER_INST))

DT_COMPAT_VND_ENUM_INT_REQUIRED_FALSE_HOLDER := vnd,enum-int-required-false-holder

config DT_HAS_VND_ENUM_INT_REQUIRED_FALSE_HOLDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ENUM_INT_REQUIRED_FALSE_HOLDER))

DT_COMPAT_VND_ENUM_REQUIRED_FALSE_HOLDER := vnd,enum-required-false-holder

config DT_HAS_VND_ENUM_REQUIRED_FALSE_HOLDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ENUM_REQUIRED_FALSE_HOLDER))

DT_COMPAT_VND_ENUM_REQUIRED_FALSE_HOLDER_INST := vnd,enum-required-false-holder-inst

config DT_HAS_VND_ENUM_REQUIRED_FALSE_HOLDER_INST_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_ENUM_REQUIRED_FALSE_HOLDER_INST))

DT_COMPAT_VND_GPIO := vnd,gpio

config DT_HAS_VND_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_GPIO))

DT_COMPAT_VND_GPIO_DEVICE := vnd,gpio-device

config DT_HAS_VND_GPIO_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_GPIO_DEVICE))

DT_COMPAT_VND_GPIO_EXPANDER := vnd,gpio-expander

config DT_HAS_VND_GPIO_EXPANDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_GPIO_EXPANDER))

DT_COMPAT_VND_GPIO_ONE_CELL := vnd,gpio-one-cell

config DT_HAS_VND_GPIO_ONE_CELL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_GPIO_ONE_CELL))

DT_COMPAT_VND_GREAT_GRANDCHILD_BINDINGS := vnd,great-grandchild-bindings

config DT_HAS_VND_GREAT_GRANDCHILD_BINDINGS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_GREAT_GRANDCHILD_BINDINGS))

DT_COMPAT_VND_I2C := vnd,i2c

config DT_HAS_VND_I2C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_I2C))

DT_COMPAT_VND_I2C_DEVICE := vnd,i2c-device

config DT_HAS_VND_I2C_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_I2C_DEVICE))

DT_COMPAT_VND_I2C_MUX := vnd,i2c-mux

config DT_HAS_VND_I2C_MUX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_I2C_MUX))

DT_COMPAT_VND_I3C := vnd,i3c

config DT_HAS_VND_I3C_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_I3C))

DT_COMPAT_VND_I3C_DEVICE := vnd,i3c-device

config DT_HAS_VND_I3C_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_I3C_DEVICE))

DT_COMPAT_VND_I3C_I2C_DEVICE := vnd,i3c-i2c-device

config DT_HAS_VND_I3C_I2C_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_I3C_I2C_DEVICE))

DT_COMPAT_VND_IEEE802154 := vnd,ieee802154

config DT_HAS_VND_IEEE802154_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_IEEE802154))

DT_COMPAT_VND_INPUT_DEVICE := vnd,input-device

config DT_HAS_VND_INPUT_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_INPUT_DEVICE))

DT_COMPAT_VND_INTC := vnd,intc

config DT_HAS_VND_INTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_INTC))

DT_COMPAT_VND_INTERRUPT_HOLDER := vnd,interrupt-holder

config DT_HAS_VND_INTERRUPT_HOLDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_INTERRUPT_HOLDER))

DT_COMPAT_VND_MBOX := vnd,mbox

config DT_HAS_VND_MBOX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_MBOX))

DT_COMPAT_VND_MBOX_ZERO_CELL := vnd,mbox-zero-cell

config DT_HAS_VND_MBOX_ZERO_CELL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_MBOX_ZERO_CELL))

DT_COMPAT_VND_MEMORY_ATTR := vnd,memory-attr

config DT_HAS_VND_MEMORY_ATTR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_MEMORY_ATTR))

DT_COMPAT_VND_PCIE := vnd,pcie

config DT_HAS_VND_PCIE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_PCIE))

DT_COMPAT_VND_PHANDLE_HOLDER := vnd,phandle-holder

config DT_HAS_VND_PHANDLE_HOLDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_PHANDLE_HOLDER))

DT_COMPAT_VND_PINCTRL := vnd,pinctrl

config DT_HAS_VND_PINCTRL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_PINCTRL))

DT_COMPAT_VND_PINCTRL_DEVICE := vnd,pinctrl-device

config DT_HAS_VND_PINCTRL_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_PINCTRL_DEVICE))

DT_COMPAT_VND_PINCTRL_TEST := vnd,pinctrl-test

config DT_HAS_VND_PINCTRL_TEST_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_PINCTRL_TEST))

DT_COMPAT_VND_PWM := vnd,pwm

config DT_HAS_VND_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_PWM))

DT_COMPAT_VND_REG_HOLDER := vnd,reg-holder

config DT_HAS_VND_REG_HOLDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_REG_HOLDER))

DT_COMPAT_VND_REG_HOLDER_64 := vnd,reg-holder-64

config DT_HAS_VND_REG_HOLDER_64_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_REG_HOLDER_64))

DT_COMPAT_VND_RESERVED_COMPAT := vnd,reserved-compat

config DT_HAS_VND_RESERVED_COMPAT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_RESERVED_COMPAT))

DT_COMPAT_VND_RESET := vnd,reset

config DT_HAS_VND_RESET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_RESET))

DT_COMPAT_VND_SERIAL := vnd,serial

config DT_HAS_VND_SERIAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_SERIAL))

DT_COMPAT_VND_SPI := vnd,spi

config DT_HAS_VND_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_SPI))

DT_COMPAT_VND_SPI_DEVICE := vnd,spi-device

config DT_HAS_VND_SPI_DEVICE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_SPI_DEVICE))

DT_COMPAT_VND_SPI_DEVICE_2 := vnd,spi-device-2

config DT_HAS_VND_SPI_DEVICE_2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_SPI_DEVICE_2))

DT_COMPAT_VND_STRING_ARRAY_TOKEN := vnd,string-array-token

config DT_HAS_VND_STRING_ARRAY_TOKEN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_STRING_ARRAY_TOKEN))

DT_COMPAT_VND_STRING_ARRAY_UNQUOTED := vnd,string-array-unquoted

config DT_HAS_VND_STRING_ARRAY_UNQUOTED_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_STRING_ARRAY_UNQUOTED))

DT_COMPAT_VND_STRING_TOKEN := vnd,string-token

config DT_HAS_VND_STRING_TOKEN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_STRING_TOKEN))

DT_COMPAT_VND_STRING_UNQUOTED := vnd,string-unquoted

config DT_HAS_VND_STRING_UNQUOTED_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_STRING_UNQUOTED))

DT_COMPAT_VND_W1 := vnd,w1

config DT_HAS_VND_W1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VND_W1))

DT_COMPAT_VOLTAGE_DIVIDER := voltage-divider

config DT_HAS_VOLTAGE_DIVIDER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_VOLTAGE_DIVIDER))

DT_COMPAT_WE_WSEN_HIDS := we,wsen-hids

config DT_HAS_WE_WSEN_HIDS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WE_WSEN_HIDS))

DT_COMPAT_WE_WSEN_ITDS := we,wsen-itds

config DT_HAS_WE_WSEN_ITDS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WE_WSEN_ITDS))

DT_COMPAT_WE_WSEN_PADS := we,wsen-pads

config DT_HAS_WE_WSEN_PADS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WE_WSEN_PADS))

DT_COMPAT_WE_WSEN_PDUS := we,wsen-pdus

config DT_HAS_WE_WSEN_PDUS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WE_WSEN_PDUS))

DT_COMPAT_WE_WSEN_TIDS := we,wsen-tids

config DT_HAS_WE_WSEN_TIDS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WE_WSEN_TIDS))

DT_COMPAT_WINSEN_MHZ19B := winsen,mhz19b

config DT_HAS_WINSEN_MHZ19B_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WINSEN_MHZ19B))

DT_COMPAT_WIZNET_W5500 := wiznet,w5500

config DT_HAS_WIZNET_W5500_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WIZNET_W5500))

DT_COMPAT_WNC_M14A2A := wnc,m14a2a

config DT_HAS_WNC_M14A2A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WNC_M14A2A))

DT_COMPAT_WORLDSEMI_WS2812_GPIO := worldsemi,ws2812-gpio

config DT_HAS_WORLDSEMI_WS2812_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WORLDSEMI_WS2812_GPIO))

DT_COMPAT_WORLDSEMI_WS2812_I2S := worldsemi,ws2812-i2s

config DT_HAS_WORLDSEMI_WS2812_I2S_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WORLDSEMI_WS2812_I2S))

DT_COMPAT_WORLDSEMI_WS2812_SPI := worldsemi,ws2812-spi

config DT_HAS_WORLDSEMI_WS2812_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_WORLDSEMI_WS2812_SPI))

DT_COMPAT_X_POWERS_AXP192 := x-powers,axp192

config DT_HAS_X_POWERS_AXP192_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_X_POWERS_AXP192))

DT_COMPAT_X_POWERS_AXP192_GPIO := x-powers,axp192-gpio

config DT_HAS_X_POWERS_AXP192_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_X_POWERS_AXP192_GPIO))

DT_COMPAT_X_POWERS_AXP192_REGULATOR := x-powers,axp192-regulator

config DT_HAS_X_POWERS_AXP192_REGULATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_X_POWERS_AXP192_REGULATOR))

DT_COMPAT_XEN_HVC_UART := xen,hvc-uart

config DT_HAS_XEN_HVC_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XEN_HVC_UART))

DT_COMPAT_XEN_XEN_4_15 := xen,xen-4.15

config DT_HAS_XEN_XEN_4_15_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XEN_XEN_4_15))

DT_COMPAT_XLNX_FPGA := xlnx,fpga

config DT_HAS_XLNX_FPGA_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_FPGA))

DT_COMPAT_XLNX_GEM := xlnx,gem

config DT_HAS_XLNX_GEM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_GEM))

DT_COMPAT_XLNX_PINCTRL_ZYNQ := xlnx,pinctrl-zynq

config DT_HAS_XLNX_PINCTRL_ZYNQ_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_PINCTRL_ZYNQ))

DT_COMPAT_XLNX_PS_GPIO := xlnx,ps-gpio

config DT_HAS_XLNX_PS_GPIO_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_PS_GPIO))

DT_COMPAT_XLNX_PS_GPIO_BANK := xlnx,ps-gpio-bank

config DT_HAS_XLNX_PS_GPIO_BANK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_PS_GPIO_BANK))

DT_COMPAT_XLNX_TTCPS := xlnx,ttcps

config DT_HAS_XLNX_TTCPS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_TTCPS))

DT_COMPAT_XLNX_XPS_GPIO_1_00_A := xlnx,xps-gpio-1.00.a

config DT_HAS_XLNX_XPS_GPIO_1_00_A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_GPIO_1_00_A))

DT_COMPAT_XLNX_XPS_GPIO_1_00_A_GPIO2 := xlnx,xps-gpio-1.00.a-gpio2

config DT_HAS_XLNX_XPS_GPIO_1_00_A_GPIO2_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_GPIO_1_00_A_GPIO2))

DT_COMPAT_XLNX_XPS_IIC_2_00_A := xlnx,xps-iic-2.00.a

config DT_HAS_XLNX_XPS_IIC_2_00_A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_IIC_2_00_A))

DT_COMPAT_XLNX_XPS_IIC_2_1 := xlnx,xps-iic-2.1

config DT_HAS_XLNX_XPS_IIC_2_1_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_IIC_2_1))

DT_COMPAT_XLNX_XPS_SPI_2_00_A := xlnx,xps-spi-2.00.a

config DT_HAS_XLNX_XPS_SPI_2_00_A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_SPI_2_00_A))

DT_COMPAT_XLNX_XPS_TIMEBASE_WDT_1_00_A := xlnx,xps-timebase-wdt-1.00.a

config DT_HAS_XLNX_XPS_TIMEBASE_WDT_1_00_A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_TIMEBASE_WDT_1_00_A))

DT_COMPAT_XLNX_XPS_TIMER_1_00_A := xlnx,xps-timer-1.00.a

config DT_HAS_XLNX_XPS_TIMER_1_00_A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_TIMER_1_00_A))

DT_COMPAT_XLNX_XPS_TIMER_1_00_A_PWM := xlnx,xps-timer-1.00.a-pwm

config DT_HAS_XLNX_XPS_TIMER_1_00_A_PWM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_TIMER_1_00_A_PWM))

DT_COMPAT_XLNX_XPS_UARTLITE_1_00_A := xlnx,xps-uartlite-1.00.a

config DT_HAS_XLNX_XPS_UARTLITE_1_00_A_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XPS_UARTLITE_1_00_A))

DT_COMPAT_XLNX_XUARTPS := xlnx,xuartps

config DT_HAS_XLNX_XUARTPS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_XUARTPS))

DT_COMPAT_XLNX_ZYNQ_OCM := xlnx,zynq-ocm

config DT_HAS_XLNX_ZYNQ_OCM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_ZYNQ_OCM))

DT_COMPAT_XLNX_ZYNQMP_IPI_MAILBOX := xlnx,zynqmp-ipi-mailbox

config DT_HAS_XLNX_ZYNQMP_IPI_MAILBOX_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XLNX_ZYNQMP_IPI_MAILBOX))

DT_COMPAT_XPTEK_XPT2046 := xptek,xpt2046

config DT_HAS_XPTEK_XPT2046_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_XPTEK_XPT2046))

DT_COMPAT_ZEPHYR_ADC_EMUL := zephyr,adc-emul

config DT_HAS_ZEPHYR_ADC_EMUL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_ADC_EMUL))

DT_COMPAT_ZEPHYR_BBRAM_EMUL := zephyr,bbram-emul

config DT_HAS_ZEPHYR_BBRAM_EMUL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_BBRAM_EMUL))

DT_COMPAT_ZEPHYR_BT_HCI_ENTROPY := zephyr,bt-hci-entropy

config DT_HAS_ZEPHYR_BT_HCI_ENTROPY_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_BT_HCI_ENTROPY))

DT_COMPAT_ZEPHYR_BT_HCI_SPI := zephyr,bt-hci-spi

config DT_HAS_ZEPHYR_BT_HCI_SPI_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_BT_HCI_SPI))

DT_COMPAT_ZEPHYR_BT_HCI_SPI_SLAVE := zephyr,bt-hci-spi-slave

config DT_HAS_ZEPHYR_BT_HCI_SPI_SLAVE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_BT_HCI_SPI_SLAVE))

DT_COMPAT_ZEPHYR_CAN_LOOPBACK := zephyr,can-loopback

config DT_HAS_ZEPHYR_CAN_LOOPBACK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_CAN_LOOPBACK))

DT_COMPAT_ZEPHYR_CDC_ACM_UART := zephyr,cdc-acm-uart

config DT_HAS_ZEPHYR_CDC_ACM_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_CDC_ACM_UART))

DT_COMPAT_ZEPHYR_CDC_ECM_ETHERNET := zephyr,cdc-ecm-ethernet

config DT_HAS_ZEPHYR_CDC_ECM_ETHERNET_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_CDC_ECM_ETHERNET))

DT_COMPAT_ZEPHYR_COREDUMP := zephyr,coredump

config DT_HAS_ZEPHYR_COREDUMP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_COREDUMP))

DT_COMPAT_ZEPHYR_COUNTER_WATCHDOG := zephyr,counter-watchdog

config DT_HAS_ZEPHYR_COUNTER_WATCHDOG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_COUNTER_WATCHDOG))

DT_COMPAT_ZEPHYR_DUMMY_DC := zephyr,dummy-dc

config DT_HAS_ZEPHYR_DUMMY_DC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_DUMMY_DC))

DT_COMPAT_ZEPHYR_EMU_EEPROM := zephyr,emu-eeprom

config DT_HAS_ZEPHYR_EMU_EEPROM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_EMU_EEPROM))

DT_COMPAT_ZEPHYR_ESPI_EMUL_CONTROLLER := zephyr,espi-emul-controller

config DT_HAS_ZEPHYR_ESPI_EMUL_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_ESPI_EMUL_CONTROLLER))

DT_COMPAT_ZEPHYR_FAKE_CAN := zephyr,fake-can

config DT_HAS_ZEPHYR_FAKE_CAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FAKE_CAN))

DT_COMPAT_ZEPHYR_FAKE_EEPROM := zephyr,fake-eeprom

config DT_HAS_ZEPHYR_FAKE_EEPROM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FAKE_EEPROM))

DT_COMPAT_ZEPHYR_FAKE_REGULATOR := zephyr,fake-regulator

config DT_HAS_ZEPHYR_FAKE_REGULATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FAKE_REGULATOR))

DT_COMPAT_ZEPHYR_FAKE_RTC := zephyr,fake-rtc

config DT_HAS_ZEPHYR_FAKE_RTC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FAKE_RTC))

DT_COMPAT_ZEPHYR_FLASH_DISK := zephyr,flash-disk

config DT_HAS_ZEPHYR_FLASH_DISK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FLASH_DISK))

DT_COMPAT_ZEPHYR_FSTAB := zephyr,fstab

config DT_HAS_ZEPHYR_FSTAB_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FSTAB))

DT_COMPAT_ZEPHYR_FSTAB_LITTLEFS := zephyr,fstab,littlefs

config DT_HAS_ZEPHYR_FSTAB_LITTLEFS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_FSTAB_LITTLEFS))

DT_COMPAT_ZEPHYR_GPIO_EMUL := zephyr,gpio-emul

config DT_HAS_ZEPHYR_GPIO_EMUL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_GPIO_EMUL))

DT_COMPAT_ZEPHYR_GPIO_EMUL_SDL := zephyr,gpio-emul-sdl

config DT_HAS_ZEPHYR_GPIO_EMUL_SDL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_GPIO_EMUL_SDL))

DT_COMPAT_ZEPHYR_GSM_PPP := zephyr,gsm-ppp

config DT_HAS_ZEPHYR_GSM_PPP_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_GSM_PPP))

DT_COMPAT_ZEPHYR_I2C_EMUL_CONTROLLER := zephyr,i2c-emul-controller

config DT_HAS_ZEPHYR_I2C_EMUL_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_I2C_EMUL_CONTROLLER))

DT_COMPAT_ZEPHYR_I2C_TARGET_EEPROM := zephyr,i2c-target-eeprom

config DT_HAS_ZEPHYR_I2C_TARGET_EEPROM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_I2C_TARGET_EEPROM))

DT_COMPAT_ZEPHYR_IEEE802154_UART_PIPE := zephyr,ieee802154-uart-pipe

config DT_HAS_ZEPHYR_IEEE802154_UART_PIPE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_IEEE802154_UART_PIPE))

DT_COMPAT_ZEPHYR_INPUT_LONGPRESS := zephyr,input-longpress

config DT_HAS_ZEPHYR_INPUT_LONGPRESS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_INPUT_LONGPRESS))

DT_COMPAT_ZEPHYR_INPUT_SDL_TOUCH := zephyr,input-sdl-touch

config DT_HAS_ZEPHYR_INPUT_SDL_TOUCH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_INPUT_SDL_TOUCH))

DT_COMPAT_ZEPHYR_IPC_ICMSG := zephyr,ipc-icmsg

config DT_HAS_ZEPHYR_IPC_ICMSG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_IPC_ICMSG))

DT_COMPAT_ZEPHYR_IPC_ICMSG_ME_FOLLOWER := zephyr,ipc-icmsg-me-follower

config DT_HAS_ZEPHYR_IPC_ICMSG_ME_FOLLOWER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_IPC_ICMSG_ME_FOLLOWER))

DT_COMPAT_ZEPHYR_IPC_ICMSG_ME_INITIATOR := zephyr,ipc-icmsg-me-initiator

config DT_HAS_ZEPHYR_IPC_ICMSG_ME_INITIATOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_IPC_ICMSG_ME_INITIATOR))

DT_COMPAT_ZEPHYR_IPC_OPENAMP_STATIC_VRINGS := zephyr,ipc-openamp-static-vrings

config DT_HAS_ZEPHYR_IPC_OPENAMP_STATIC_VRINGS_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_IPC_OPENAMP_STATIC_VRINGS))

DT_COMPAT_ZEPHYR_KSCAN_INPUT := zephyr,kscan-input

config DT_HAS_ZEPHYR_KSCAN_INPUT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_KSCAN_INPUT))

DT_COMPAT_ZEPHYR_LVGL_BUTTON_INPUT := zephyr,lvgl-button-input

config DT_HAS_ZEPHYR_LVGL_BUTTON_INPUT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_LVGL_BUTTON_INPUT))

DT_COMPAT_ZEPHYR_LVGL_POINTER_INPUT := zephyr,lvgl-pointer-input

config DT_HAS_ZEPHYR_LVGL_POINTER_INPUT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_LVGL_POINTER_INPUT))

DT_COMPAT_ZEPHYR_MEMORY_REGION := zephyr,memory-region

config DT_HAS_ZEPHYR_MEMORY_REGION_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_MEMORY_REGION))

DT_COMPAT_ZEPHYR_MMC_DISK := zephyr,mmc-disk

config DT_HAS_ZEPHYR_MMC_DISK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_MMC_DISK))

DT_COMPAT_ZEPHYR_MODBUS_SERIAL := zephyr,modbus-serial

config DT_HAS_ZEPHYR_MODBUS_SERIAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_MODBUS_SERIAL))

DT_COMPAT_ZEPHYR_NATIVE_POSIX_COUNTER := zephyr,native-posix-counter

config DT_HAS_ZEPHYR_NATIVE_POSIX_COUNTER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_NATIVE_POSIX_COUNTER))

DT_COMPAT_ZEPHYR_NATIVE_POSIX_CPU := zephyr,native-posix-cpu

config DT_HAS_ZEPHYR_NATIVE_POSIX_CPU_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_NATIVE_POSIX_CPU))

DT_COMPAT_ZEPHYR_NATIVE_POSIX_LINUX_CAN := zephyr,native-posix-linux-can

config DT_HAS_ZEPHYR_NATIVE_POSIX_LINUX_CAN_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_NATIVE_POSIX_LINUX_CAN))

DT_COMPAT_ZEPHYR_NATIVE_POSIX_RNG := zephyr,native-posix-rng

config DT_HAS_ZEPHYR_NATIVE_POSIX_RNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_NATIVE_POSIX_RNG))

DT_COMPAT_ZEPHYR_NATIVE_POSIX_UART := zephyr,native-posix-uart

config DT_HAS_ZEPHYR_NATIVE_POSIX_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_NATIVE_POSIX_UART))

DT_COMPAT_ZEPHYR_NATIVE_POSIX_UDC := zephyr,native-posix-udc

config DT_HAS_ZEPHYR_NATIVE_POSIX_UDC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_NATIVE_POSIX_UDC))

DT_COMPAT_ZEPHYR_NATIVE_TTY_UART := zephyr,native-tty-uart

config DT_HAS_ZEPHYR_NATIVE_TTY_UART_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_NATIVE_TTY_UART))

DT_COMPAT_ZEPHYR_PANEL_TIMING := zephyr,panel-timing

config DT_HAS_ZEPHYR_PANEL_TIMING_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_PANEL_TIMING))

DT_COMPAT_ZEPHYR_POWER_STATE := zephyr,power-state

config DT_HAS_ZEPHYR_POWER_STATE_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_POWER_STATE))

DT_COMPAT_ZEPHYR_PSA_CRYPTO_RNG := zephyr,psa-crypto-rng

config DT_HAS_ZEPHYR_PSA_CRYPTO_RNG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_PSA_CRYPTO_RNG))

DT_COMPAT_ZEPHYR_RETAINED_RAM := zephyr,retained-ram

config DT_HAS_ZEPHYR_RETAINED_RAM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_RETAINED_RAM))

DT_COMPAT_ZEPHYR_RETENTION := zephyr,retention

config DT_HAS_ZEPHYR_RETENTION_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_RETENTION))

DT_COMPAT_ZEPHYR_RTC_EMUL := zephyr,rtc-emul

config DT_HAS_ZEPHYR_RTC_EMUL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_RTC_EMUL))

DT_COMPAT_ZEPHYR_SDHC_SPI_SLOT := zephyr,sdhc-spi-slot

config DT_HAS_ZEPHYR_SDHC_SPI_SLOT_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SDHC_SPI_SLOT))

DT_COMPAT_ZEPHYR_SDL_DC := zephyr,sdl-dc

config DT_HAS_ZEPHYR_SDL_DC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SDL_DC))

DT_COMPAT_ZEPHYR_SDMMC_DISK := zephyr,sdmmc-disk

config DT_HAS_ZEPHYR_SDMMC_DISK_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SDMMC_DISK))

DT_COMPAT_ZEPHYR_SENSING := zephyr,sensing

config DT_HAS_ZEPHYR_SENSING_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SENSING))

DT_COMPAT_ZEPHYR_SENSING_PHY_3D_SENSOR := zephyr,sensing-phy-3d-sensor

config DT_HAS_ZEPHYR_SENSING_PHY_3D_SENSOR_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SENSING_PHY_3D_SENSOR))

DT_COMPAT_ZEPHYR_SIM_EEPROM := zephyr,sim-eeprom

config DT_HAS_ZEPHYR_SIM_EEPROM_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SIM_EEPROM))

DT_COMPAT_ZEPHYR_SIM_FLASH := zephyr,sim-flash

config DT_HAS_ZEPHYR_SIM_FLASH_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SIM_FLASH))

DT_COMPAT_ZEPHYR_SPI_BITBANG := zephyr,spi-bitbang

config DT_HAS_ZEPHYR_SPI_BITBANG_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SPI_BITBANG))

DT_COMPAT_ZEPHYR_SPI_EMUL_CONTROLLER := zephyr,spi-emul-controller

config DT_HAS_ZEPHYR_SPI_EMUL_CONTROLLER_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_SPI_EMUL_CONTROLLER))

DT_COMPAT_ZEPHYR_UART_EMUL := zephyr,uart-emul

config DT_HAS_ZEPHYR_UART_EMUL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_UART_EMUL))

DT_COMPAT_ZEPHYR_UDC_SKELETON := zephyr,udc-skeleton

config DT_HAS_ZEPHYR_UDC_SKELETON_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_UDC_SKELETON))

DT_COMPAT_ZEPHYR_UDC_VIRTUAL := zephyr,udc-virtual

config DT_HAS_ZEPHYR_UDC_VIRTUAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_UDC_VIRTUAL))

DT_COMPAT_ZEPHYR_UHC_VIRTUAL := zephyr,uhc-virtual

config DT_HAS_ZEPHYR_UHC_VIRTUAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_UHC_VIRTUAL))

DT_COMPAT_ZEPHYR_USB_C_VBUS_ADC := zephyr,usb-c-vbus-adc

config DT_HAS_ZEPHYR_USB_C_VBUS_ADC_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_USB_C_VBUS_ADC))

DT_COMPAT_ZEPHYR_W1_SERIAL := zephyr,w1-serial

config DT_HAS_ZEPHYR_W1_SERIAL_ENABLED
	def_bool $(dt_compat_enabled,$(DT_COMPAT_ZEPHYR_W1_SERIAL))
