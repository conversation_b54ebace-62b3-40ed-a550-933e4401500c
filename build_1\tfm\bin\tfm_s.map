Archive member included to satisfy reference by file (symbol)

platform/libplatform_s.a(system_nrf5340_application.o)
                              secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o (SystemInit)
platform/libplatform_s.a(exception_info.o)
                              secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o (store_and_dump_context)
platform/libplatform_s.a(tfm_hal_spm_logdev_peripheral.o)
                              platform/libplatform_s.a(exception_info.o) (tfm_hal_output_spm_log)
platform/libplatform_s.a(uart_stdout.o)
                              platform/libplatform_s.a(tfm_hal_spm_logdev_peripheral.o) (stdio_output_string)
platform/libplatform_s.a(Driver_USART.o)
                              platform/libplatform_s.a(uart_stdout.o) (Driver_USART1)
platform/libplatform_s.a(nrfx_uarte.o)
                              platform/libplatform_s.a(Driver_USART.o) (nrfx_uarte_init)
platform/libplatform_s.a(nrfx_glue.o)
                              platform/libplatform_s.a(nrfx_uarte.o) (nrfx_critical_section_enter)
secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memmove.o)
                              platform/libplatform_s.a(nrfx_uarte.o) (memmove)
secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memcpy.o)
                              platform/libplatform_s.a(exception_info.o) (memcpy)
secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memset.o)
                              c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp/crt0.o (memset)
secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
                              secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o (psa_framework_version)
secure_fw/partitions/lib/runtime/libtfm_sprt.a(sprt_partition_metadata_indicator.o)
                              secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o) (p_partition_metadata)
secure_fw/spm/libtfm_spm.a(utilities.o)
                              secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o (tfm_core_panic)
secure_fw/spm/libtfm_spm.a(main.o)
                              c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp/crt0.o (main)
secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
                              secure_fw/spm/libtfm_spm.a(main.o) (tfm_core_handler_mode)
secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
                              secure_fw/spm/libtfm_spm.a(main.o) (tfm_arch_set_secure_exception_priorities)
secure_fw/spm/libtfm_spm.a(ns_agent_tz_v80m.o)
                              secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/load_info_ns_agent_tz.o (ns_agent_tz_main)
secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
                              secure_fw/spm/libtfm_spm.a(main.o) (tfm_hal_set_up_static_boundaries)
secure_fw/spm/libtfm_spm.a(faults.o)
                              secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o (SPU_IRQHandler)
secure_fw/spm/libtfm_spm.a(target_cfg.o)
                              secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o) (sau_and_idau_cfg)
secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
                              secure_fw/spm/libtfm_spm.a(main.o) (tfm_core_validate_boot_data)
secure_fw/spm/libtfm_spm.a(tfm_arch.o)
                              secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o) (tfm_arch_free_msp_and_exc_ret)
secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                              secure_fw/spm/libtfm_spm.a(tfm_boot_data.o) (tfm_spm_partition_get_running_partition_id)
secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
                              secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o) (cross_call_entering_c)
secure_fw/spm/libtfm_spm.a(rom_loader.o)
                              secure_fw/spm/libtfm_spm.a(spm_ipc.o) (load_a_partition_assuredly)
secure_fw/spm/libtfm_spm.a(psa_api.o)
                              secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o) (spm_handle_programmer_errors)
secure_fw/spm/libtfm_spm.a(backend_ipc.o)
                              secure_fw/spm/libtfm_spm.a(spm_ipc.o) (backend_init_comp_assuredly)
secure_fw/spm/libtfm_spm.a(tfm_pools.o)
                              secure_fw/spm/libtfm_spm.a(spm_ipc.o) (tfm_pool_init)
secure_fw/spm/libtfm_spm.a(thread.o)
                              secure_fw/spm/libtfm_spm.a(backend_ipc.o) (thrd_set_query_callback)
secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o)
                              secure_fw/spm/libtfm_spm.a(spm_ipc.o) (tfm_nspm_get_current_client_id)
secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
                              secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o) (tfm_hal_get_ns_VTOR)
secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
                              secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o (tfm_crypto_init)
secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
                              secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o) (tfm_crypto_init_alloc)
secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_cipher.o)
                              secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o) (tfm_crypto_cipher_interface)
secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_hash.o)
                              secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o) (tfm_crypto_hash_interface)
secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_mac.o)
                              secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o) (tfm_crypto_mac_interface)
secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_aead.o)
                              secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o) (tfm_crypto_aead_interface)
secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_asymmetric.o)
                              secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o) (tfm_crypto_asymmetric_sign_interface)
secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_derivation.o)
                              secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o) (tfm_crypto_key_derivation_interface)
secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_management.o)
                              secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o) (tfm_crypto_key_management_interface)
secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_rng.o)
                              secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o) (tfm_crypto_random_interface)
secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
                              secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o) (tfm_crypto_library_get_info)
secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(intermedia_tfm_crypto.o)
                              secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o (tfm_sp_crypto_stack)
platform/libplatform_crypto_keys.a(crypto_keys.o)
                              secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o) (tfm_plat_builtin_key_get_desc_table_ptr)
secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
                              secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o) (mbedtls_memory_buffer_alloc_init)
secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
                              secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o) (mbedtls_platform_set_calloc_free)
secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
                              secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_rng.o) (mbedcrypto__psa_generate_random)
secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_client.o)
                              secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o) (mbedcrypto__psa_reset_key_attributes)
secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
                              secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o) (psa_is_valid_key_id)
secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
                              secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o) (psa_driver_wrapper_init)
secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
                              secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o (tfm_platform_service_sfn)
secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(intermedia_tfm_platform.o)
                              secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o (tfm_sp_platform_stack)
platform/libplatform_s.a(mpu_armv8m_drv.o)
                              secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o) (mpu_armv8m_enable)
platform/libplatform_s.a(spu.o)
                              secure_fw/spm/libtfm_spm.a(target_cfg.o) (spu_enable_interrupts)
platform/libplatform_s.a(tfm_hal_platform.o)
                              secure_fw/spm/libtfm_spm.a(main.o) (tfm_hal_platform_init)
platform/libplatform_s.a(dummy_otp.o)
                              secure_fw/spm/libtfm_spm.a(main.o) (tfm_plat_otp_init)
platform/libplatform_s.a(dummy_provisioning.o)
                              secure_fw/spm/libtfm_spm.a(main.o) (tfm_plat_provisioning_is_required)
platform/libplatform_s.a(tfm_platform_system.o)
                              secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o) (tfm_platform_hal_system_reset)
platform/libplatform_s.a(tfm_hal_reset_halt.o)
                              secure_fw/spm/libtfm_spm.a(utilities.o) (tfm_hal_system_reset)
platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
                              platform/libplatform_s.a(tfm_platform_system.o) (tfm_platform_hal_read_service)
secure_fw/partitions/lib/runtime/libtfm_sprt.a(sfn_common_thread.o)
                              secure_fw/spm/libtfm_spm.a(backend_ipc.o) (common_sfn_thread)
secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o)
                              secure_fw/spm/libtfm_spm.a(backend_ipc.o) (psa_api_cross)
secure_fw/partitions/lib/runtime/libtfm_sprt.a(dummy_tfm_sp_log_raw.o)
                              secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o) (printf)
secure_fw/spm/libtfm_spm.a(psa_call_api.o)
                              secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o) (tfm_spm_client_psa_call)
secure_fw/spm/libtfm_spm.a(psa_version_api.o)
                              secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o) (tfm_spm_client_psa_framework_version)
secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
                              secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o) (tfm_spm_partition_psa_read)
secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)
                              secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o) (tfm_builtin_key_loader_init)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
                              platform/libplatform_s.a(tfm_hal_platform.o) (nrf_cc3xx_platform_init)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
                              secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o) (nrf_cc3xx_platform_ctr_drbg_get)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_common.c.obj)
                              secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o) (mbedtls_platform_zeroize)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_ctr_drbg.c.obj) (cc_mbedtls_ctr_drbg_init)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_ctr_drbg.c.obj) (cc_mbedtls_entropy_init)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj) (CC_LibInitNoRng)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_hal.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj) (CC_HalInit)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj) (CC_PalInit)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_dma.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj) (CC_PalDmaInit)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_interrupt_ctrl.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_hal.c.obj) (CC_PalWaitInterruptRND)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mem.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj) (CC_PalMemCopyPlat)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mutex.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj) (CC_PalMutexCreate)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_pm.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj) (CC_PalPowerSaveModeInit)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj) (mbedtls_mutex_unlock)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_abort.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj) (CC_PalAbort)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj) (nrf_cc3xx_platform_hmac_drbg_init)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mutex.c.obj) (platform_mutex_apis)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd_trng90b.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj) (LLF_RND_RunTrngStartupTest)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(trng_api.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj) (mbedtls_hardware_poll)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj) (cc_mbedtls_aes_init)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256_alt.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj) (cc_mbedtls_sha256_init)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_hash_common.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256_alt.c.obj) (mbedtls_sha_process_internal)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj) (cc_mbedtls_sha256)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_hmac_drbg.c.obj) (cc_mbedtls_hmac_drbg_init)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_rng_plat.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj) (RNG_PLAT_SetUserRngParameters)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_trng.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_rng_plat.c.obj) (CC_PalTrngParamGet)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd_trng90b.c.obj) (LLF_RND_WaitRngInterrupt)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(driver_common.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj) (SetDataBuffersInfo)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hash_driver.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_hash_common.c.obj) (InitHashDrv)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_driver.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj) (ProcessAesDrv)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_driver.c.obj) (kmu_validate_slot_and_size)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_platform_keys.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj) (write_invalid_chacha20_key)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_util_cmac.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj) (UtilCmacBuildDataForDerivation)
C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_buff_attr.c.obj)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(driver_common.c.obj) (CC_PalDataBufferAttrGet)
c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
                              secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o) (cmse_check_address_range)
c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-exit.o)
                              c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp/crt0.o (exit)
c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-impure.o)
                              c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-exit.o) (_global_impure_ptr)
c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-init.o)
                              c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp/crt0.o (__libc_init_array)
c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-memcmp.o)
                              C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mem.c.obj) (memcmp)
c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libnosys.a(_exit.o)
                              c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-exit.o) (_exit)

Discarded input sections

 .text          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crti.o
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crti.o
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crti.o
 .data          0x0000000000000000        0x4 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtbegin.o
 .rodata        0x0000000000000000       0x24 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtbegin.o
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp/crt0.o
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp/crt0.o
 .ARM.extab     0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp/crt0.o
 .text          0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
 .data          0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
 .bss           0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
 .comment       0x0000000000000000       0x21 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
 .ARM.attributes
                0x0000000000000000       0x32 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
 .text          0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o
 .data          0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o
 .bss           0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o
 .text          0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o
 .data          0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o
 .bss           0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o
 .text          0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
 .data          0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
 .bss           0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
 .text          0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o
 .data          0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o
 .bss           0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o
 .text          0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o
 .data          0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o
 .bss           0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o
 .text          0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/load_info_ns_agent_tz.o
 .data          0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/load_info_ns_agent_tz.o
 .bss           0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/load_info_ns_agent_tz.o
 .text          0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .data          0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .bss           0x0000000000000000        0x0 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .text._close   0x0000000000000000        0x2 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .text._fstat   0x0000000000000000        0x2 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .text._getpid  0x0000000000000000        0x2 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .text._isatty  0x0000000000000000        0x2 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .text._kill    0x0000000000000000        0x2 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .text._lseek   0x0000000000000000        0x2 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .text._read    0x0000000000000000        0x2 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .text._write   0x0000000000000000        0x2 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .debug_info    0x0000000000000000       0xbc secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .debug_abbrev  0x0000000000000000       0x5e secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .debug_aranges
                0x0000000000000000       0x58 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .debug_ranges  0x0000000000000000       0x48 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .debug_line    0x0000000000000000      0x105 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .debug_str     0x0000000000000000      0x214 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .comment       0x0000000000000000       0x21 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .debug_frame   0x0000000000000000       0x90 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .ARM.attributes
                0x0000000000000000       0x32 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
 .text          0x0000000000000000        0x0 platform/libplatform_s.a(system_nrf5340_application.o)
 .data          0x0000000000000000        0x0 platform/libplatform_s.a(system_nrf5340_application.o)
 .bss           0x0000000000000000        0x0 platform/libplatform_s.a(system_nrf5340_application.o)
 .text.SystemCoreClockUpdate
                0x0000000000000000       0x20 platform/libplatform_s.a(system_nrf5340_application.o)
 .text.SystemStoreFICRNS
                0x0000000000000000       0x34 platform/libplatform_s.a(system_nrf5340_application.o)
 .text.SystemLockFICRNS
                0x0000000000000000       0x20 platform/libplatform_s.a(system_nrf5340_application.o)
 .text          0x0000000000000000        0x0 platform/libplatform_s.a(exception_info.o)
 .data          0x0000000000000000        0x0 platform/libplatform_s.a(exception_info.o)
 .bss           0x0000000000000000        0x0 platform/libplatform_s.a(exception_info.o)
 .text          0x0000000000000000        0x0 platform/libplatform_s.a(tfm_hal_spm_logdev_peripheral.o)
 .data          0x0000000000000000        0x0 platform/libplatform_s.a(tfm_hal_spm_logdev_peripheral.o)
 .bss           0x0000000000000000        0x0 platform/libplatform_s.a(tfm_hal_spm_logdev_peripheral.o)
 .text          0x0000000000000000        0x0 platform/libplatform_s.a(uart_stdout.o)
 .data          0x0000000000000000        0x0 platform/libplatform_s.a(uart_stdout.o)
 .bss           0x0000000000000000        0x0 platform/libplatform_s.a(uart_stdout.o)
 .text._write   0x0000000000000000        0x8 platform/libplatform_s.a(uart_stdout.o)
 .text.stdio_uninit
                0x0000000000000000       0x18 platform/libplatform_s.a(uart_stdout.o)
 .text          0x0000000000000000        0x0 platform/libplatform_s.a(Driver_USART.o)
 .data          0x0000000000000000        0x0 platform/libplatform_s.a(Driver_USART.o)
 .bss           0x0000000000000000        0x0 platform/libplatform_s.a(Driver_USART.o)
 .text          0x0000000000000000        0x0 platform/libplatform_s.a(nrfx_uarte.o)
 .data          0x0000000000000000        0x0 platform/libplatform_s.a(nrfx_uarte.o)
 .bss           0x0000000000000000        0x0 platform/libplatform_s.a(nrfx_uarte.o)
 .text.__nrfy_internal_uarte_event_handle.constprop.0.isra.0
                0x0000000000000000       0x2a platform/libplatform_s.a(nrfx_uarte.o)
 .text.__nrfy_internal_uarte_events_process.constprop.0
                0x0000000000000000       0x90 platform/libplatform_s.a(nrfx_uarte.o)
 .text.nrfx_uarte_init_check
                0x0000000000000000       0x1c platform/libplatform_s.a(nrfx_uarte.o)
 .text.nrfx_uarte_tx_in_progress
                0x0000000000000000       0x18 platform/libplatform_s.a(nrfx_uarte.o)
 .text.nrfx_uarte_reconfigure
                0x0000000000000000       0xa0 platform/libplatform_s.a(nrfx_uarte.o)
 .text.nrfx_uarte_int_trigger
                0x0000000000000000       0x60 platform/libplatform_s.a(nrfx_uarte.o)
 .text.nrfx_uarte_errorsrc_get
                0x0000000000000000       0x16 platform/libplatform_s.a(nrfx_uarte.o)
 .text.nrfx_uarte_rx_new_data_check
                0x0000000000000000       0x16 platform/libplatform_s.a(nrfx_uarte.o)
 .text.nrfx_uarte_1_irq_handler
                0x0000000000000000      0x300 platform/libplatform_s.a(nrfx_uarte.o)
 .text          0x0000000000000000        0x0 platform/libplatform_s.a(nrfx_glue.o)
 .data          0x0000000000000000        0x0 platform/libplatform_s.a(nrfx_glue.o)
 .bss           0x0000000000000000        0x0 platform/libplatform_s.a(nrfx_glue.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memmove.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memmove.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memmove.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memcpy.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memcpy.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memcpy.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memset.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memset.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memset.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
 .text.psa_skip
                0x0000000000000000       0x10 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
 .text.psa_rot_lifecycle_state
                0x0000000000000000       0x10 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sprt_partition_metadata_indicator.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sprt_partition_metadata_indicator.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sprt_partition_metadata_indicator.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(utilities.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(utilities.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(utilities.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(main.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(main.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(main.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(ns_agent_tz_v80m.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(ns_agent_tz_v80m.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(ns_agent_tz_v80m.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
 .rodata.partition_named_mmio_list
                0x0000000000000000        0x4 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(faults.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(faults.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(faults.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(target_cfg.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(target_cfg.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(target_cfg.o)
 .text.spu_periph_configure_to_non_secure
                0x0000000000000000        0x6 secure_fw/spm/libtfm_spm.a(target_cfg.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(psa_api.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(psa_api.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(psa_api.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_pools.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_pools.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_pools.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(thread.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(thread.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(thread.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
 .text.tfm_crypto_get_caller_id
                0x0000000000000000       0x10 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
 .text.tfm_crypto_operation_alloc
                0x0000000000000000       0x50 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
 .text.tfm_crypto_operation_release
                0x0000000000000000       0x4c secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
 .text.tfm_crypto_operation_lookup
                0x0000000000000000       0x48 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_cipher.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_cipher.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_cipher.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_hash.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_hash.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_hash.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_mac.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_mac.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_mac.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_aead.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_aead.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_aead.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_asymmetric.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_asymmetric.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_asymmetric.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_derivation.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_derivation.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_derivation.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_management.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_management.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_management.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_rng.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_rng.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_rng.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 .text.tfm_crypto_core_library_key_attributes_from_client
                0x0000000000000000       0x52 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 .text.tfm_crypto_core_library_key_attributes_to_client
                0x0000000000000000       0x24 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 .text.mbedtls_psa_platform_get_builtin_key
                0x0000000000000000       0x4a secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(intermedia_tfm_crypto.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(intermedia_tfm_crypto.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(intermedia_tfm_crypto.o)
 .text          0x0000000000000000        0x0 platform/libplatform_crypto_keys.a(crypto_keys.o)
 .data          0x0000000000000000        0x0 platform/libplatform_crypto_keys.a(crypto_keys.o)
 .bss           0x0000000000000000        0x0 platform/libplatform_crypto_keys.a(crypto_keys.o)
 .text.tfm_plat_builtin_key_get_policy_table_ptr
                0x0000000000000000        0x4 platform/libplatform_crypto_keys.a(crypto_keys.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .text.mbedtls_memory_buffer_set_verify
                0x0000000000000000        0xc secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .text.mbedtls_memory_buffer_alloc_verify
                0x0000000000000000        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .text.mbedtls_memory_buffer_alloc_free
                0x0000000000000000        0xc secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .text.mbedtls_calloc
                0x0000000000000000        0xc secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .text.mbedtls_platform_set_printf
                0x0000000000000000        0xc secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .text.mbedtls_platform_setup
                0x0000000000000000        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .text.mbedtls_platform_teardown
                0x0000000000000000        0x2 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .data.mbedtls_printf
                0x0000000000000000        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.key_type_is_raw_bytes
                0x0000000000000000       0x18 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_mac_key_can_do
                0x0000000000000000       0x40 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_key_policy_algorithm_intersection
                0x0000000000000000      0x42c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_key_algorithm_permits
                0x0000000000000000      0x1f8 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_aead_check_algorithm
                0x0000000000000000       0xa4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_key_derivation_check_input_type
                0x0000000000000000       0x60 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_sign_verify_check_alg
                0x0000000000000000       0x80 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_aead_final_checks
                0x0000000000000000       0x30 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_mac_finalize_alg_and_key_validation.isra.0
                0x0000000000000000       0xe0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_start_key_creation.constprop.0
                0x0000000000000000       0xa4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_validate_unstructured_key_bit_size
                0x0000000000000000       0x3e secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_allocate_buffer_to_slot
                0x0000000000000000       0x26 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_copy_key_material_into_slot
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_import_key_into_slot
                0x0000000000000000       0x3a secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_get_and_lock_key_slot_with_policy
                0x0000000000000000       0xe8 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_mac_compute_internal
                0x0000000000000000       0xd2 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_sign_internal
                0x0000000000000000       0xd6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_verify_internal
                0x0000000000000000       0x82 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_get_and_lock_transparent_key_slot_with_policy
                0x0000000000000000       0x40 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_destroy_key
                0x0000000000000000       0x46 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_get_key_attributes
                0x0000000000000000       0x46 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_export_key_internal
                0x0000000000000000       0x52 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_export_key
                0x0000000000000000       0x6a secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_export_public_key_internal
                0x0000000000000000       0x54 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_export_public_key
                0x0000000000000000       0x7c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_import_key
                0x0000000000000000       0xf2 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_copy_key
                0x0000000000000000      0x16c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_hash_abort
                0x0000000000000000       0x16 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_hash_setup
                0x0000000000000000       0x3e secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_hash_update
                0x0000000000000000       0x28 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_hash_finish
                0x0000000000000000       0x22 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_hash_verify
                0x0000000000000000       0x66 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_hash_compute
                0x0000000000000000       0x22 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_hash_compare
                0x0000000000000000       0x66 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_hash_clone
                0x0000000000000000       0x24 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_mac_abort
                0x0000000000000000       0x22 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_mac_setup
                0x0000000000000000       0xa8 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_mac_sign_setup
                0x0000000000000000       0x18 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_mac_verify_setup
                0x0000000000000000       0x18 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_mac_update
                0x0000000000000000       0x28 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_mac_sign_finish
                0x0000000000000000       0x6e secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_mac_verify_finish
                0x0000000000000000       0x40 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_mac_compute
                0x0000000000000000       0x2a secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_mac_verify
                0x0000000000000000       0x68 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_sign_message_builtin
                0x0000000000000000       0x98 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_sign_message
                0x0000000000000000       0x2c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_verify_message_builtin
                0x0000000000000000       0x94 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_verify_message
                0x0000000000000000       0x28 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_sign_hash_builtin
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_sign_hash
                0x0000000000000000       0x2c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_verify_hash_builtin
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_verify_hash
                0x0000000000000000       0x28 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_asymmetric_encrypt
                0x0000000000000000       0xb8 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_asymmetric_decrypt
                0x0000000000000000       0xb4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_cipher_abort
                0x0000000000000000       0x1c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_cipher_setup
                0x0000000000000000      0x14c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_cipher_encrypt_setup
                0x0000000000000000       0x18 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_cipher_decrypt_setup
                0x0000000000000000       0x18 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_cipher_set_iv
                0x0000000000000000       0x3c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_cipher_update
                0x0000000000000000       0x34 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_cipher_finish
                0x0000000000000000       0x38 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_cipher_decrypt
                0x0000000000000000      0x150 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_aead_encrypt
                0x0000000000000000       0x3a secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_aead_decrypt
                0x0000000000000000       0x3a secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_aead_abort
                0x0000000000000000       0x1c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_aead_setup
                0x0000000000000000       0x6a secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_aead_encrypt_setup
                0x0000000000000000       0x18 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_aead_decrypt_setup
                0x0000000000000000       0x18 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_aead_set_nonce
                0x0000000000000000       0x24 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_aead_set_lengths
                0x0000000000000000       0x38 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_aead_update_ad
                0x0000000000000000       0x48 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_aead_update
                0x0000000000000000       0x58 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_aead_finish
                0x0000000000000000       0x7a secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_aead_verify
                0x0000000000000000       0x48 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_key_derivation_abort
                0x0000000000000000       0x1a secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_key_derivation_input_internal
                0x0000000000000000       0x24 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_key_derivation_get_capacity
                0x0000000000000000       0x12 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_key_derivation_set_capacity
                0x0000000000000000       0x1c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_key_derivation_output_bytes
                0x0000000000000000       0x26 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_key_derivation_output_key
                0x0000000000000000      0x13e secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_key_derivation_setup
                0x0000000000000000       0xcc secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_key_derivation_input_bytes
                0x0000000000000000       0x12 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_key_derivation_input_key
                0x0000000000000000       0x66 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_key_derivation_input_integer
                0x0000000000000000       0x24 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_key_derivation_key_agreement
                0x0000000000000000       0xd4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_pake_setup
                0x0000000000000000       0x44 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_pake_output
                0x0000000000000000       0x1c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_pake_input
                0x0000000000000000       0x26 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_pake_abort
                0x0000000000000000       0x1c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_pake_set_role
                0x0000000000000000       0x40 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_pake_set_user
                0x0000000000000000       0x3a secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_pake_set_peer
                0x0000000000000000       0x3a secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_pake_set_password_key
                0x0000000000000000       0x9e secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_pake_get_implicit_key
                0x0000000000000000       0x5c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_cipher_generate_iv
                0x0000000000000000       0x74 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_cipher_encrypt
                0x0000000000000000      0x160 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_aead_generate_nonce
                0x0000000000000000       0xb6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_raw_key_agreement
                0x0000000000000000       0x90 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedtls_psa_get_random
                0x0000000000000000       0x14 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.psa_generate_key_internal
                0x0000000000000000       0x1e secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedcrypto__psa_generate_key
                0x0000000000000000      0x154 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text.mbedtls_psa_crypto_configure_entropy_sources
                0x0000000000000000        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .rodata.CSWTCH.484
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .rodata.CSWTCH.381
                0x0000000000000000       0x10 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .rodata.mbedtls_psa_random_state
                0x0000000000000000        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_client.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_client.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_client.o)
 .text.mbedcrypto__psa_reset_key_attributes
                0x0000000000000000        0x8 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_client.o)
 .debug_info    0x0000000000000000      0x2a5 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_client.o)
 .debug_abbrev  0x0000000000000000      0x135 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_client.o)
 .debug_loc     0x0000000000000000       0x25 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_client.o)
 .debug_aranges
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_client.o)
 .debug_ranges  0x0000000000000000       0x10 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_client.o)
 .debug_line    0x0000000000000000      0x29e secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_client.o)
 .debug_str     0x0000000000000000      0x38c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_client.o)
 .comment       0x0000000000000000       0x21 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_client.o)
 .debug_frame   0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_client.o)
 .ARM.attributes
                0x0000000000000000       0x32 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_client.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .text.psa_is_valid_key_id
                0x0000000000000000       0x2c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .text.psa_get_and_lock_key_slot_in_memory
                0x0000000000000000       0x84 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .text.psa_get_empty_key_slot
                0x0000000000000000       0x84 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .text.psa_get_and_lock_key_slot
                0x0000000000000000      0x154 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .text.psa_unlock_key_slot
                0x0000000000000000       0x14 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .text.psa_validate_key_location
                0x0000000000000000        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .text.psa_validate_key_persistence
                0x0000000000000000        0xe secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .text.mbedcrypto__psa_open_key
                0x0000000000000000       0x42 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .text.mbedcrypto__psa_close_key
                0x0000000000000000       0x42 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .text.mbedcrypto__psa_purge_key
                0x0000000000000000       0x3a secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .text.mbedtls_psa_get_stats
                0x0000000000000000       0x68 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_sign_message
                0x0000000000000000        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_verify_message
                0x0000000000000000        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_sign_hash
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_verify_hash
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_get_key_buffer_size_from_key_data
                0x0000000000000000        0xa secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_get_key_buffer_size
                0x0000000000000000       0x30 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_generate_key
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_import_key
                0x0000000000000000       0x30 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_export_key
                0x0000000000000000       0x28 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_export_public_key
                0x0000000000000000       0x28 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_get_builtin_key
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_copy_key
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_cipher_encrypt
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_cipher_decrypt
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_cipher_encrypt_setup
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_cipher_decrypt_setup
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_cipher_set_iv
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_cipher_update
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_cipher_finish
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_cipher_abort
                0x0000000000000000        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_hash_compute
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_hash_setup
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_hash_clone
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_hash_update
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_hash_finish
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_hash_abort
                0x0000000000000000        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_aead_encrypt
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_aead_decrypt
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_aead_encrypt_setup
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_aead_decrypt_setup
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_aead_set_nonce
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_aead_set_lengths
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_aead_update_ad
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_aead_update
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_aead_finish
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_aead_verify
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_aead_abort
                0x0000000000000000        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_mac_compute
                0x0000000000000000       0x34 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_mac_sign_setup
                0x0000000000000000       0x34 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_mac_verify_setup
                0x0000000000000000        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_mac_update
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_mac_sign_finish
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_mac_verify_finish
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_mac_abort
                0x0000000000000000        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_key_derivation_setup
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_key_derivation_set_capacity
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_key_derivation_input_bytes
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_key_derivation_input_integer
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_key_derivation_output_bytes
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_key_derivation_abort
                0x0000000000000000        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_key_agreement
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_pake_setup
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_pake_set_password_key
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_pake_set_user
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_pake_set_peer
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_pake_set_role
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_pake_output
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_pake_input
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_pake_get_implicit_key
                0x0000000000000000        0x6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_pake_abort
                0x0000000000000000        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_asymmetric_encrypt
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_asymmetric_decrypt
                0x0000000000000000       0x20 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text.psa_driver_wrapper_get_entropy
                0x0000000000000000        0xa secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
 .text.platform_sp_system_reset
                0x0000000000000000        0xa secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(intermedia_tfm_platform.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(intermedia_tfm_platform.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(intermedia_tfm_platform.o)
 .text          0x0000000000000000        0x0 platform/libplatform_s.a(mpu_armv8m_drv.o)
 .data          0x0000000000000000        0x0 platform/libplatform_s.a(mpu_armv8m_drv.o)
 .bss           0x0000000000000000        0x0 platform/libplatform_s.a(mpu_armv8m_drv.o)
 .text.mpu_armv8m_disable
                0x0000000000000000        0x8 platform/libplatform_s.a(mpu_armv8m_drv.o)
 .text          0x0000000000000000        0x0 platform/libplatform_s.a(spu.o)
 .data          0x0000000000000000        0x0 platform/libplatform_s.a(spu.o)
 .bss           0x0000000000000000        0x0 platform/libplatform_s.a(spu.o)
 .text          0x0000000000000000        0x0 platform/libplatform_s.a(tfm_hal_platform.o)
 .data          0x0000000000000000        0x0 platform/libplatform_s.a(tfm_hal_platform.o)
 .bss           0x0000000000000000        0x0 platform/libplatform_s.a(tfm_hal_platform.o)
 .text          0x0000000000000000        0x0 platform/libplatform_s.a(dummy_otp.o)
 .data          0x0000000000000000        0x0 platform/libplatform_s.a(dummy_otp.o)
 .bss           0x0000000000000000        0x0 platform/libplatform_s.a(dummy_otp.o)
 .text.tfm_plat_otp_read
                0x0000000000000000        0x6 platform/libplatform_s.a(dummy_otp.o)
 .text.tfm_plat_otp_write
                0x0000000000000000        0x6 platform/libplatform_s.a(dummy_otp.o)
 .text.tfm_plat_otp_get_size
                0x0000000000000000        0x6 platform/libplatform_s.a(dummy_otp.o)
 .text          0x0000000000000000        0x0 platform/libplatform_s.a(dummy_provisioning.o)
 .data          0x0000000000000000        0x0 platform/libplatform_s.a(dummy_provisioning.o)
 .bss           0x0000000000000000        0x0 platform/libplatform_s.a(dummy_provisioning.o)
 .text          0x0000000000000000        0x0 platform/libplatform_s.a(tfm_platform_system.o)
 .data          0x0000000000000000        0x0 platform/libplatform_s.a(tfm_platform_system.o)
 .bss           0x0000000000000000        0x0 platform/libplatform_s.a(tfm_platform_system.o)
 .text          0x0000000000000000        0x0 platform/libplatform_s.a(tfm_hal_reset_halt.o)
 .data          0x0000000000000000        0x0 platform/libplatform_s.a(tfm_hal_reset_halt.o)
 .bss           0x0000000000000000        0x0 platform/libplatform_s.a(tfm_hal_reset_halt.o)
 .text.tfm_hal_system_halt
                0x0000000000000000        0x6 platform/libplatform_s.a(tfm_hal_reset_halt.o)
 .text          0x0000000000000000        0x0 platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
 .data          0x0000000000000000        0x0 platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
 .bss           0x0000000000000000        0x0 platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sfn_common_thread.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sfn_common_thread.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sfn_common_thread.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(dummy_tfm_sp_log_raw.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(dummy_tfm_sp_log_raw.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(dummy_tfm_sp_log_raw.o)
 .text.printf   0x0000000000000000        0x8 secure_fw/partitions/lib/runtime/libtfm_sprt.a(dummy_tfm_sp_log_raw.o)
 .debug_info    0x0000000000000000       0x71 secure_fw/partitions/lib/runtime/libtfm_sprt.a(dummy_tfm_sp_log_raw.o)
 .debug_abbrev  0x0000000000000000       0x6e secure_fw/partitions/lib/runtime/libtfm_sprt.a(dummy_tfm_sp_log_raw.o)
 .debug_aranges
                0x0000000000000000       0x20 secure_fw/partitions/lib/runtime/libtfm_sprt.a(dummy_tfm_sp_log_raw.o)
 .debug_ranges  0x0000000000000000       0x10 secure_fw/partitions/lib/runtime/libtfm_sprt.a(dummy_tfm_sp_log_raw.o)
 .debug_line    0x0000000000000000       0x86 secure_fw/partitions/lib/runtime/libtfm_sprt.a(dummy_tfm_sp_log_raw.o)
 .debug_str     0x0000000000000000      0x162 secure_fw/partitions/lib/runtime/libtfm_sprt.a(dummy_tfm_sp_log_raw.o)
 .comment       0x0000000000000000       0x21 secure_fw/partitions/lib/runtime/libtfm_sprt.a(dummy_tfm_sp_log_raw.o)
 .debug_frame   0x0000000000000000       0x34 secure_fw/partitions/lib/runtime/libtfm_sprt.a(dummy_tfm_sp_log_raw.o)
 .ARM.attributes
                0x0000000000000000       0x32 secure_fw/partitions/lib/runtime/libtfm_sprt.a(dummy_tfm_sp_log_raw.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(psa_call_api.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(psa_call_api.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(psa_call_api.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(psa_version_api.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(psa_version_api.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(psa_version_api.o)
 .text          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
 .data          0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
 .bss           0x0000000000000000        0x0 secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
 .text          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)
 .data          0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)
 .bss           0x0000000000000000        0x0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)
 .text.psa_set_key_usage_flags
                0x0000000000000000       0x14 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)
 .text.tfm_builtin_key_loader_get_key_buffer_size
                0x0000000000000000       0x5c secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)
 .text.tfm_builtin_key_loader_get_builtin_key
                0x0000000000000000      0x1f4 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_init_hmac_drbg
                0x0000000000000000       0x44 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_init_no_rng
                0x0000000000000000       0x38 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_deinit
                0x0000000000000000       0x1c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_is_initialized
                0x0000000000000000       0x14 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_rng_is_initialized
                0x0000000000000000       0x14 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_get_nonce_seed
                0x0000000000000000       0x28 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_get_boot_seed
                0x0000000000000000       0x28 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_free
                0x0000000000000000       0x2c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_set_pr
                0x0000000000000000       0x28 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_set_reseed_interval
                0x0000000000000000       0x30 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_reseed
                0x0000000000000000       0x38 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_get_with_add
                0x0000000000000000       0x58 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_common.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_common.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_common.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_free
                0x0000000000000000       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_set_prediction_resistance
                0x0000000000000000        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_set_entropy_len
                0x0000000000000000        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_set_nonce_len
                0x0000000000000000       0x20 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_set_reseed_interval
                0x0000000000000000        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_update
                0x0000000000000000       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_reseed
                0x0000000000000000       0x44 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_random
                0x0000000000000000       0x14 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj)
 .text.cc_mbedtls_entropy_free
                0x0000000000000000       0x38 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj)
 .text.cc_mbedtls_entropy_add_source
                0x0000000000000000       0x64 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj)
 .text.cc_mbedtls_entropy_update_manual
                0x0000000000000000       0x4c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj)
 .text.cc_mbedtls_entropy_gather
                0x0000000000000000       0x48 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .text.CC_LibInitNoRng
                0x0000000000000000       0x28 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .text.CC_LibInit_HMAC_DRBG
                0x0000000000000000       0x74 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .text.CC_RandomSeedsFilled
                0x0000000000000000       0x18 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .text.CC_LibFini
                0x0000000000000000       0x18 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .text.CC_LibInitRngModule
                0x0000000000000000       0xbc C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .data.tfm_random_seed_buff
                0x0000000000000000        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .data.eits_random_nonce_buff
                0x0000000000000000        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .data.invalid_chacha_256_bit_key
                0x0000000000000000        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_hal.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_hal.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_hal.c.obj)
 .text.CC_HalTerminate
                0x0000000000000000        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_hal.c.obj)
 .text.CC_HalClearInterruptBitRNG
                0x0000000000000000        0xc C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_hal.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj)
 .data.CCApbFilteringRegMutex
                0x0000000000000000        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_dma.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_dma.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_dma.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_interrupt_ctrl.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_interrupt_ctrl.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_interrupt_ctrl.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mem.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mem.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mem.c.obj)
 .text.CC_PalSecMemCmp
                0x0000000000000000       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mem.c.obj)
 .text.CC_PalMemCmpPlat
                0x0000000000000000        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mem.c.obj)
 .text.CC_PalMemMovePlat
                0x0000000000000000        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mem.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mutex.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mutex.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mutex.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_pm.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_pm.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_pm.c.obj)
 .text.CC_PalPowerSaveModeStatus
                0x0000000000000000        0xc C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_pm.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
 .text.mutex_free
                0x0000000000000000        0xc C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
 .rodata.mbedtls_threading_set_alt.str1.4
                0x0000000000000000       0x44 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
 .text.mbedtls_threading_set_alt
                0x0000000000000000       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
 .text.mbedtls_threading_free_alt
                0x0000000000000000        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
 .data.mbedtls_mutex_free
                0x0000000000000000        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_abort.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_abort.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_abort.c.obj)
 .text.nrf_cc3xx_platform_set_abort
                0x0000000000000000       0x10 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_abort.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_init
                0x0000000000000000       0x60 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_free
                0x0000000000000000       0x2c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_set_pr
                0x0000000000000000       0x28 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_set_reseed_interval
                0x0000000000000000       0x30 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_reseed
                0x0000000000000000       0x38 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_get_with_add
                0x0000000000000000       0x58 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_get
                0x0000000000000000       0x44 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .bss.global_ctx
                0x0000000000000000      0x250 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .ARM.attributes
                0x0000000000000000       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.nrf_cc3xx_platform_set_mutexes
                0x0000000000000000       0x78 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd_trng90b.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd_trng90b.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_StartTrngHW
                0x0000000000000000      0x10c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd_trng90b.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(trng_api.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(trng_api.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(trng_api.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_setkey_dec
                0x0000000000000000       0x44 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_cbc
                0x0000000000000000       0x78 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_cfb128
                0x0000000000000000        0x8 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_cfb8
                0x0000000000000000        0x8 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_ctr
                0x0000000000000000       0x68 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_ofb
                0x0000000000000000       0x68 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
 .text.cc_mbedtls_internal_aes_encrypt
                0x0000000000000000       0x44 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
 .text.cc_mbedtls_internal_aes_decrypt
                0x0000000000000000       0x48 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256_alt.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256_alt.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256_alt.c.obj)
 .rodata.cc_mbedtls_sha256_clone.str1.4
                0x0000000000000000       0x15 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_clone
                0x0000000000000000       0x2c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256_alt.c.obj)
 .text.cc_mbedtls_internal_sha256_process
                0x0000000000000000       0x10 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256_alt.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_hash_common.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_hash_common.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_hash_common.c.obj)
 .text.mbedtls_sha_process_internal
                0x0000000000000000       0xe0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_hash_common.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .text.cc_hmac_sha256_starts.constprop.0
                0x0000000000000000      0x108 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_init
                0x0000000000000000       0x28 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_update
                0x0000000000000000      0x170 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .text.cc_hmac_drbg_reseed_core
                0x0000000000000000       0xac C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_seed_buf
                0x0000000000000000       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_reseed
                0x0000000000000000        0x8 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_seed
                0x0000000000000000       0x54 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_set_prediction_resistance
                0x0000000000000000        0x8 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_set_entropy_len
                0x0000000000000000        0x8 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_set_reseed_interval
                0x0000000000000000        0x8 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_random_with_add
                0x0000000000000000      0x144 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_random
                0x0000000000000000       0x4c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_free
                0x0000000000000000       0x40 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .bss.hmac_ctx  0x0000000000000000       0x80 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .ARM.attributes
                0x0000000000000000       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hmac_drbg_alt.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_rng_plat.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_rng_plat.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_rng_plat.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_trng.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_trng.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_trng.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd.c.obj)
 .text.Mult32x32
                0x0000000000000000       0x40 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd.c.obj)
 .text.Mult48x16
                0x0000000000000000       0x1c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd.c.obj)
 .text.LLF_RND_EntropyEstimateFull
                0x0000000000000000      0x52c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd.c.obj)
 .text.LLF_RND_GetCountRoscs
                0x0000000000000000       0x18 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd.c.obj)
 .text.LLF_RND_RndCprngt
                0x0000000000000000       0x58 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(driver_common.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(driver_common.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(driver_common.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hash_driver.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hash_driver.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hash_driver.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_driver.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_driver.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_driver.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj)
 .text.kmu_write_key_slot
                0x0000000000000000       0xd8 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj)
 .text.kmu_verify_kdf_input
                0x0000000000000000       0x24 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj)
 .text.kmu_convert_keybits_to_keysize
                0x0000000000000000       0x2c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj)
 .text.kmu_validate_kdr_slot_and_size
                0x0000000000000000       0x14 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj)
 .text.kmu_load_key_chacha20
                0x0000000000000000      0x108 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj)
 .text.kmu_shadow_key_derive
                0x0000000000000000       0x98 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_platform_keys.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_platform_keys.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_platform_keys.c.obj)
 .text.write_invalid_chacha20_key
                0x0000000000000000       0x40 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_platform_keys.c.obj)
 .text.cc_platform_prepare_chacha_key
                0x0000000000000000       0x4c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_platform_keys.c.obj)
 .text.cc_platform_load_chacha_key
                0x0000000000000000       0x68 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_platform_keys.c.obj)
 .text.cc_platform_cleanup_chacha_key
                0x0000000000000000       0x3c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_platform_keys.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_platform_keys.c.obj)
 .ARM.attributes
                0x0000000000000000       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_platform_keys.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_util_cmac.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_util_cmac.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_util_cmac.c.obj)
 .text.UtilCmacDeriveKey
                0x0000000000000000       0xf0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_util_cmac.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_buff_attr.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_buff_attr.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_buff_attr.c.obj)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
 .text          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-exit.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-exit.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-exit.o)
 .text          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-impure.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-impure.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-impure.o)
 .data._impure_ptr
                0x0000000000000000        0x4 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-impure.o)
 .text          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-init.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-init.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-init.o)
 .text          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-memcmp.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-memcmp.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-memcmp.o)
 .text.memcmp   0x0000000000000000       0x20 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-memcmp.o)
 .debug_frame   0x0000000000000000       0x28 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-memcmp.o)
 .ARM.attributes
                0x0000000000000000       0x32 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-memcmp.o)
 .text          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtend.o
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtend.o
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtend.o
 .rodata        0x0000000000000000       0x24 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtend.o
 .text          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtn.o
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtn.o
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtn.o
 .text          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libnosys.a(_exit.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libnosys.a(_exit.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libnosys.a(_exit.o)

Memory Configuration

Name             Origin             Length             Attributes
FLASH            0x0000000000000000 0x0000000000008000 xr
RAM              0x0000000020000000 0x0000000000008000 xrw
*default*        0x0000000000000000 0xffffffffffffffff

Linker script and memory map

LOAD c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crti.o
LOAD c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtbegin.o
LOAD c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp/crt0.o
LOAD secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
LOAD secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o
LOAD secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o
LOAD secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
LOAD secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o
LOAD secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o
LOAD secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/load_info_ns_agent_tz.o
LOAD secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/syscalls_stub.o
LOAD platform/libplatform_s.a
LOAD secure_fw/partitions/lib/runtime/libtfm_sprt.a
LOAD secure_fw/spm/libtfm_spm.a
LOAD secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a
LOAD platform/libplatform_crypto_keys.a
LOAD secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a
LOAD secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a
LOAD platform/libplatform_s.a
LOAD secure_fw/partitions/lib/runtime/libtfm_sprt.a
LOAD secure_fw/spm/libtfm_spm.a
LOAD secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a
LOAD platform/libplatform_crypto_keys.a
LOAD secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a
LOAD secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a
LOAD platform/libplatform_s.a
LOAD secure_fw/partitions/lib/runtime/libtfm_sprt.a
LOAD secure_fw/spm/libtfm_spm.a
LOAD secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a
LOAD platform/libplatform_crypto_keys.a
LOAD secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a
LOAD secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a
LOAD C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a
LOAD C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_oberon/lib/cortex-m33/soft-float/liboberon_mbedtls_3.0.13.a
LOAD secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libmbedcrypto_base.a
LOAD C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_oberon/lib/cortex-m33/soft-float/liboberon_3.0.13.a
START GROUP
LOAD c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a
LOAD c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a
END GROUP
START GROUP
LOAD c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a
LOAD c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a
END GROUP
LOAD c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtend.o
LOAD c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtn.o
                0x0000000000000800                __msp_stack_size__ = 0x800
START GROUP
LOAD c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a
LOAD c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a
LOAD c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libm.a
LOAD c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libnosys.a
LOAD c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a
END GROUP

.TFM_VECTORS    0x0000000000000000      0x154
                0x0000000000000000                __vectors_start__ = .
 *(.vectors)
 .vectors       0x0000000000000000      0x154 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o
                0x0000000000000000                __Vectors
                0x0000000000000154                . = ALIGN (0x4)
                0x0000000000000154                __vectors_end__ = .
                0x0000000000000001                ASSERT ((. <= (ADDR (.TFM_VECTORS) + 0x154)), .TFM_VECTORS section size overflow.)
                0x0000000000000154                . = (ADDR (.TFM_VECTORS) + 0x154)

.copy.table     0x0000000000000154       0x24
                0x0000000000000154                __copy_table_start__ = .
                0x0000000000000154        0x4 LONG 0x7c40 LOADADDR (.TFM_DATA)
                0x0000000000000158        0x4 LONG 0x20001e20 ADDR (.TFM_DATA)
                0x000000000000015c        0x4 LONG 0x4a (SIZEOF (.TFM_DATA) / 0x4)
                0x0000000000000160        0x4 LONG 0x3980 LOADADDR (.TFM_PSA_ROT_LINKER_DATA)
                0x0000000000000164        0x4 LONG 0x20000c40 ADDR (.TFM_PSA_ROT_LINKER_DATA)
                0x0000000000000168        0x4 LONG 0x0 (SIZEOF (.TFM_PSA_ROT_LINKER_DATA) / 0x4)
                0x000000000000016c        0x4 LONG 0x3980 LOADADDR (.TFM_APP_ROT_LINKER_DATA)
                0x0000000000000170        0x4 LONG 0x20000c20 ADDR (.TFM_APP_ROT_LINKER_DATA)
                0x0000000000000174        0x4 LONG 0x0 (SIZEOF (.TFM_APP_ROT_LINKER_DATA) / 0x4)
                0x0000000000000178                __copy_table_end__ = .

.zero.table     0x0000000000000178       0x20
                0x0000000000000178                __zero_table_start__ = .
                0x0000000000000178        0x4 LONG 0x20001f60 ADDR (.TFM_BSS)
                0x000000000000017c        0x4 LONG 0x6c7 (SIZEOF (.TFM_BSS) / 0x4)
                0x0000000000000180        0x4 LONG 0x20000c40 ADDR (.TFM_PSA_ROT_LINKER_BSS)
                0x0000000000000184        0x4 LONG 0x478 (SIZEOF (.TFM_PSA_ROT_LINKER_BSS) / 0x4)
                0x0000000000000188        0x4 LONG 0x20000c20 ADDR (.TFM_APP_ROT_LINKER_BSS)
                0x000000000000018c        0x4 LONG 0x8 (SIZEOF (.TFM_APP_ROT_LINKER_BSS) / 0x4)
                0x0000000000000190        0x4 LONG 0x20000c00 ADDR (.TFM_SP_META_PTR)
                0x0000000000000194        0x4 LONG 0x8 (SIZEOF (.TFM_SP_META_PTR) / 0x4)
                0x0000000000000198                __zero_table_end__ = .

.TFM_SP_LOAD_LIST
                0x0000000000000198       0xc4
 *(.part_load_priority_lowest)
 .part_load_priority_lowest
                0x0000000000000198       0x30 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/load_info_ns_agent_tz.o
                0x0000000000000198                tfm_sp_ns_agent_tz_load
 *(.part_load_priority_low)
 *(.part_load_priority_normal)
 .part_load_priority_normal
                0x00000000000001c8       0x4c secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o
                0x00000000000001c8                tfm_sp_crypto_load
 .part_load_priority_normal
                0x0000000000000214       0x48 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o
                0x0000000000000214                tfm_sp_platform_load
 *(.part_load_priority_high)
                0x0000000000000198                Image$$TFM_SP_LOAD_LIST$$RO$$Base = ADDR (.TFM_SP_LOAD_LIST)
                0x000000000000025c                Image$$TFM_SP_LOAD_LIST$$RO$$Limit = (ADDR (.TFM_SP_LOAD_LIST) + SIZEOF (.TFM_SP_LOAD_LIST))
                0x0000000000000260                . = ALIGN (0x20)
                0x0000000000000260                Image$$TFM_PSA_CODE_START$$Base = .

.TFM_PSA_ROT_LINKER
                0x0000000000000260      0x4e0
 *tfm_psa_rot_partition*:*(.text*)
 .text.tfm_crypto_clear_scratch
                0x0000000000000260       0x20 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
 .text.tfm_crypto_init
                0x0000000000000280       0x1c secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
                0x0000000000000280                tfm_crypto_init
 .text.tfm_crypto_api_dispatcher
                0x000000000000029c       0xa4 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
                0x000000000000029c                tfm_crypto_api_dispatcher
 .text.tfm_crypto_sfn
                0x0000000000000340      0x170 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
                0x0000000000000340                tfm_crypto_sfn
 .text.tfm_crypto_init_alloc
                0x00000000000004b0       0x14 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
                0x00000000000004b0                tfm_crypto_init_alloc
 .text.tfm_crypto_cipher_interface
                0x00000000000004c4        0x6 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_cipher.o)
                0x00000000000004c4                tfm_crypto_cipher_interface
 .text.tfm_crypto_hash_interface
                0x00000000000004ca        0x6 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_hash.o)
                0x00000000000004ca                tfm_crypto_hash_interface
 .text.tfm_crypto_mac_interface
                0x00000000000004d0        0x6 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_mac.o)
                0x00000000000004d0                tfm_crypto_mac_interface
 .text.tfm_crypto_aead_interface
                0x00000000000004d6        0x6 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_aead.o)
                0x00000000000004d6                tfm_crypto_aead_interface
 .text.tfm_crypto_asymmetric_sign_interface
                0x00000000000004dc        0x6 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_asymmetric.o)
                0x00000000000004dc                tfm_crypto_asymmetric_sign_interface
 .text.tfm_crypto_asymmetric_encrypt_interface
                0x00000000000004e2        0x6 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_asymmetric.o)
                0x00000000000004e2                tfm_crypto_asymmetric_encrypt_interface
 .text.tfm_crypto_key_derivation_interface
                0x00000000000004e8        0x6 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_derivation.o)
                0x00000000000004e8                tfm_crypto_key_derivation_interface
 .text.tfm_crypto_key_management_interface
                0x00000000000004ee        0x6 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_management.o)
                0x00000000000004ee                tfm_crypto_key_management_interface
 .text.tfm_crypto_random_interface
                0x00000000000004f4        0x8 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_rng.o)
                0x00000000000004f4                tfm_crypto_random_interface
 .text.tfm_crypto_library_key_id_init
                0x00000000000004fc        0x6 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
                0x00000000000004fc                tfm_crypto_library_key_id_init
 *fill*         0x0000000000000502        0x2 
 .text.tfm_crypto_library_get_info
                0x0000000000000504       0x1c secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
                0x0000000000000504                tfm_crypto_library_get_info
 .text.tfm_crypto_core_library_init
                0x0000000000000520       0x14 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
                0x0000000000000520                tfm_crypto_core_library_init
 .text.tfm_platform_service_sfn
                0x0000000000000534      0x114 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
                0x0000000000000534                tfm_platform_service_sfn
 .text.platform_sp_init
                0x0000000000000648        0x4 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
                0x0000000000000648                platform_sp_init
 .text.tfm_builtin_key_loader_init
                0x000000000000064c       0xe0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)
                0x000000000000064c                tfm_builtin_key_loader_init
 *tfm_psa_rot_partition*:*(.rodata*)
 .rodata.tfm_crypto_library_get_info.str1.1
                0x000000000000072c        0xf secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 *(TFM_*_PSA-ROT_ATTR_FN)
                0x0000000000000740                . = ALIGN (0x20)
 *fill*         0x000000000000073b        0x5 
                0x0000000000000260                Image$$TFM_PSA_ROT_LINKER$$RO$$Base = ADDR (.TFM_PSA_ROT_LINKER)
                0x0000000000000740                Image$$TFM_PSA_ROT_LINKER$$RO$$Limit = (ADDR (.TFM_PSA_ROT_LINKER) + SIZEOF (.TFM_PSA_ROT_LINKER))
                0x0000000000000260                Image$$TFM_PSA_ROT_LINKER$$Base = ADDR (.TFM_PSA_ROT_LINKER)
                0x0000000000000740                Image$$TFM_PSA_ROT_LINKER$$Limit = (ADDR (.TFM_PSA_ROT_LINKER) + SIZEOF (.TFM_PSA_ROT_LINKER))
                0x0000000000000740                Image$$TFM_PSA_CODE_END$$Base = .
                0x0000000000000740                Image$$TFM_APP_CODE_START$$Base = .

.TFM_APP_ROT_LINKER
                0x0000000000000740        0x0
 *tfm_app_rot_partition*:*(.text*)
 *tfm_app_rot_partition*:*(.rodata*)
 *(TFM_*_APP-ROT_ATTR_FN)
                0x0000000000000740                . = ALIGN (0x20)
                0x0000000000000740                Image$$TFM_APP_ROT_LINKER$$RO$$Base = ADDR (.TFM_APP_ROT_LINKER)
                0x0000000000000740                Image$$TFM_APP_ROT_LINKER$$RO$$Limit = (ADDR (.TFM_APP_ROT_LINKER) + SIZEOF (.TFM_APP_ROT_LINKER))
                0x0000000000000740                Image$$TFM_APP_ROT_LINKER$$Base = ADDR (.TFM_APP_ROT_LINKER)
                0x0000000000000740                Image$$TFM_APP_ROT_LINKER$$Limit = (ADDR (.TFM_APP_ROT_LINKER) + SIZEOF (.TFM_APP_ROT_LINKER))
                0x0000000000000740                Image$$TFM_APP_CODE_END$$Base = .

.ARM.extab
 *(.ARM.extab* .gnu.linkonce.armextab.*)
                0x0000000000000740                __exidx_start = .

.ARM.exidx      0x0000000000000740        0x8
 *(.ARM.exidx* .gnu.linkonce.armexidx.*)
 .ARM.exidx     0x0000000000000740        0x8 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp/crt0.o
                                         0x10 (size before relaxing)
                0x0000000000000748                __exidx_end = .

.ER_TFM_CODE    0x0000000000000750     0x3218
 *startup*(.text*)
 .text.default_irq_handler
                0x0000000000000750        0x4 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o
                0x0000000000000750                default_irq_handler
 *fill*         0x0000000000000754        0x4 
 .text.Reset_Handler
                0x0000000000000758       0xa4 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o
                0x0000000000000758                Reset_Handler
 .text.default_tfm_IRQHandler
                0x00000000000007fc       0x16 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o
                0x00000000000007fc                TIMER2_IRQHandler
                0x00000000000007fc                DebugMon_Handler
                0x00000000000007fc                SPIM4_IRQHandler
                0x00000000000007fc                RTC0_IRQHandler
                0x00000000000007fc                SERIAL1_IRQHandler
                0x00000000000007fc                SysTick_Handler
                0x00000000000007fc                PWM1_IRQHandler
                0x00000000000007fc                SERIAL3_IRQHandler
                0x00000000000007fc                NMI_Handler
                0x00000000000007fc                WDT0_IRQHandler
                0x00000000000007fc                I2S0_IRQHandler
                0x00000000000007fc                default_tfm_IRQHandler
                0x00000000000007fc                IPC_IRQHandler
                0x00000000000007fc                SERIAL2_IRQHandler
                0x00000000000007fc                PWM3_IRQHandler
                0x00000000000007fc                QDEC1_IRQHandler
                0x00000000000007fc                FPU_IRQHandler
                0x00000000000007fc                USBD_IRQHandler
                0x00000000000007fc                EGU3_IRQHandler
                0x00000000000007fc                KMU_IRQHandler
                0x00000000000007fc                CRYPTOCELL_IRQHandler
                0x00000000000007fc                EGU2_IRQHandler
                0x00000000000007fc                TIMER0_IRQHandler
                0x00000000000007fc                CACHE_IRQHandler
                0x00000000000007fc                EGU4_IRQHandler
                0x00000000000007fc                EGU0_IRQHandler
                0x00000000000007fc                GPIOTE1_IRQHandler
                0x00000000000007fc                TIMER1_IRQHandler
                0x00000000000007fc                PWM2_IRQHandler
                0x00000000000007fc                EGU1_IRQHandler
                0x00000000000007fc                EGU5_IRQHandler
                0x00000000000007fc                SAADC_IRQHandler
                0x00000000000007fc                GPIOTE0_IRQHandler
                0x00000000000007fc                QDEC0_IRQHandler
                0x00000000000007fc                PWM0_IRQHandler
                0x00000000000007fc                RTC1_IRQHandler
                0x00000000000007fc                SERIAL0_IRQHandler
                0x00000000000007fc                USBREGULATOR_IRQHandler
                0x00000000000007fc                NFCT_IRQHandler
                0x00000000000007fc                WDT1_IRQHandler
                0x00000000000007fc                QSPI_IRQHandler
                0x00000000000007fc                COMP_LPCOMP_IRQHandler
                0x00000000000007fc                CLOCK_POWER_IRQHandler
                0x00000000000007fc                PDM0_IRQHandler
 *libplatform_s*:*(.text*)
 *fill*         0x0000000000000812        0x2 
 .text.nrf53_errata_42
                0x0000000000000814       0x24 platform/libplatform_s.a(system_nrf5340_application.o)
 .text.SystemInit
                0x0000000000000838      0x1d0 platform/libplatform_s.a(system_nrf5340_application.o)
                0x0000000000000838                SystemInit
 .text.store_and_dump_context
                0x0000000000000a08       0xe8 platform/libplatform_s.a(exception_info.o)
                0x0000000000000a08                store_and_dump_context
 .text.tfm_hal_output_spm_log
                0x0000000000000af0        0x4 platform/libplatform_s.a(tfm_hal_spm_logdev_peripheral.o)
                0x0000000000000af0                tfm_hal_output_spm_log
 .text.stdio_output_string
                0x0000000000000af4       0x28 platform/libplatform_s.a(uart_stdout.o)
                0x0000000000000af4                stdio_output_string
 .text.stdio_init
                0x0000000000000b1c       0x2c platform/libplatform_s.a(uart_stdout.o)
                0x0000000000000b1c                stdio_init
 .text.ARM_USART_GetVersion
                0x0000000000000b48       0x20 platform/libplatform_s.a(Driver_USART.o)
 .text.ARM_USART_GetCapabilities
                0x0000000000000b68        0x4 platform/libplatform_s.a(Driver_USART.o)
 .text.ARM_USART_Transfer
                0x0000000000000b6c        0x6 platform/libplatform_s.a(Driver_USART.o)
 .text.ARM_USART_GetStatus
                0x0000000000000b72        0xc platform/libplatform_s.a(Driver_USART.o)
 .text.ARM_USART_SetModemControl
                0x0000000000000b7e        0x6 platform/libplatform_s.a(Driver_USART.o)
 .text.ARM_USART_GetModemStatus
                0x0000000000000b84        0xc platform/libplatform_s.a(Driver_USART.o)
 .text.ARM_USART1_PowerControl
                0x0000000000000b90        0xc platform/libplatform_s.a(Driver_USART.o)
 .text.ARM_USART1_GetTxCount
                0x0000000000000b9c        0xc platform/libplatform_s.a(Driver_USART.o)
 .text.ARM_USART1_GetRxCount
                0x0000000000000ba8        0xc platform/libplatform_s.a(Driver_USART.o)
 .text.ARM_USART1_Uninitialize
                0x0000000000000bb4       0x18 platform/libplatform_s.a(Driver_USART.o)
 .text.ARM_USART1_Receive
                0x0000000000000bcc       0x3c platform/libplatform_s.a(Driver_USART.o)
 .text.ARM_USARTx_Send.constprop.0
                0x0000000000000c08       0x78 platform/libplatform_s.a(Driver_USART.o)
 .text.ARM_USART1_Send
                0x0000000000000c80        0x4 platform/libplatform_s.a(Driver_USART.o)
 .text.ARM_USART1_Control
                0x0000000000000c84      0x190 platform/libplatform_s.a(Driver_USART.o)
 .text.uart_config_set_uart_pins
                0x0000000000000e14       0x3c platform/libplatform_s.a(Driver_USART.o)
                0x0000000000000e14                uart_config_set_uart_pins
 .text.ARM_USART1_Initialize
                0x0000000000000e50       0x70 platform/libplatform_s.a(Driver_USART.o)
 .text.nrf_gpio_pin_port_decode
                0x0000000000000ec0       0x24 platform/libplatform_s.a(nrfx_uarte.o)
 .text.is_tx_ready
                0x0000000000000ee4       0x1a platform/libplatform_s.a(nrfx_uarte.o)
 .text.user_handler
                0x0000000000000efe       0x22 platform/libplatform_s.a(nrfx_uarte.o)
 .text.user_handler_on_rx_done
                0x0000000000000f20       0x28 platform/libplatform_s.a(nrfx_uarte.o)
 .text.disable_hw_from_rx
                0x0000000000000f48       0x24 platform/libplatform_s.a(nrfx_uarte.o)
 .text.on_rx_disabled
                0x0000000000000f6c       0x5c platform/libplatform_s.a(nrfx_uarte.o)
 .text.nrfx_coredep_delay_us.part.0
                0x0000000000000fc8       0x20 platform/libplatform_s.a(nrfx_uarte.o)
 .text.rx_flush.part.0
                0x0000000000000fe8       0x66 platform/libplatform_s.a(nrfx_uarte.o)
 *fill*         0x000000000000104e        0x2 
 .text.nrf_gpio_cfg_input.constprop.0
                0x0000000000001050       0x28 platform/libplatform_s.a(nrfx_uarte.o)
 .text.disable_hw_from_tx
                0x0000000000001078       0x16 platform/libplatform_s.a(nrfx_uarte.o)
 *fill*         0x000000000000108e        0x2 
 .text.nrf_gpio_cfg_output
                0x0000000000001090       0x2c platform/libplatform_s.a(nrfx_uarte.o)
 .text.nrf_gpio_cfg_default
                0x00000000000010bc       0x2c platform/libplatform_s.a(nrfx_uarte.o)
 .text.tx_prepare_start.isra.0
                0x00000000000010e8       0x7e platform/libplatform_s.a(nrfx_uarte.o)
 *fill*         0x0000000000001166        0x2 
 .text.uarte_configure
                0x0000000000001168      0x190 platform/libplatform_s.a(nrfx_uarte.o)
 .text.nrfx_uarte_init
                0x00000000000012f8      0x154 platform/libplatform_s.a(nrfx_uarte.o)
                0x00000000000012f8                nrfx_uarte_init
 .text.nrfx_uarte_tx
                0x000000000000144c      0x228 platform/libplatform_s.a(nrfx_uarte.o)
                0x000000000000144c                nrfx_uarte_tx
 .text.nrfx_uarte_tx_abort
                0x0000000000001674       0xac platform/libplatform_s.a(nrfx_uarte.o)
                0x0000000000001674                nrfx_uarte_tx_abort
 .text.nrfx_uarte_rx_enable
                0x0000000000001720      0x10c platform/libplatform_s.a(nrfx_uarte.o)
                0x0000000000001720                nrfx_uarte_rx_enable
 .text.nrfx_uarte_rx_buffer_set
                0x000000000000182c      0x130 platform/libplatform_s.a(nrfx_uarte.o)
                0x000000000000182c                nrfx_uarte_rx_buffer_set
 .text.nrfx_uarte_rx_abort
                0x000000000000195c       0xc8 platform/libplatform_s.a(nrfx_uarte.o)
                0x000000000000195c                nrfx_uarte_rx_abort
 .text.nrfx_uarte_uninit
                0x0000000000001a24       0xcc platform/libplatform_s.a(nrfx_uarte.o)
                0x0000000000001a24                nrfx_uarte_uninit
 .text.nrfx_uarte_rx_ready
                0x0000000000001af0       0x3c platform/libplatform_s.a(nrfx_uarte.o)
                0x0000000000001af0                nrfx_uarte_rx_ready
 .text.nrfx_uarte_rx
                0x0000000000001b2c       0x74 platform/libplatform_s.a(nrfx_uarte.o)
                0x0000000000001b2c                nrfx_uarte_rx
 .text.nrfx_critical_section_enter
                0x0000000000001ba0       0x10 platform/libplatform_s.a(nrfx_glue.o)
                0x0000000000001ba0                nrfx_critical_section_enter
 .text.nrfx_critical_section_exit
                0x0000000000001bb0       0x14 platform/libplatform_s.a(nrfx_glue.o)
                0x0000000000001bb0                nrfx_critical_section_exit
 .text.mpu_armv8m_enable
                0x0000000000001bc4       0x34 platform/libplatform_s.a(mpu_armv8m_drv.o)
                0x0000000000001bc4                mpu_armv8m_enable
 .text.mpu_armv8m_region_enable
                0x0000000000001bf8       0x6a platform/libplatform_s.a(mpu_armv8m_drv.o)
                0x0000000000001bf8                mpu_armv8m_region_enable
 .text.mpu_armv8m_region_disable
                0x0000000000001c62       0x14 platform/libplatform_s.a(mpu_armv8m_drv.o)
                0x0000000000001c62                mpu_armv8m_region_disable
 .text.mpu_armv8m_clean
                0x0000000000001c76       0x22 platform/libplatform_s.a(mpu_armv8m_drv.o)
                0x0000000000001c76                mpu_armv8m_clean
 .text.spu_enable_interrupts
                0x0000000000001c98       0x10 platform/libplatform_s.a(spu.o)
                0x0000000000001c98                spu_enable_interrupts
 .text.spu_clear_events
                0x0000000000001ca8       0x24 platform/libplatform_s.a(spu.o)
                0x0000000000001ca8                spu_clear_events
 .text.spu_regions_reset_unlocked_secure
                0x0000000000001ccc       0x30 platform/libplatform_s.a(spu.o)
                0x0000000000001ccc                spu_regions_reset_unlocked_secure
 .text.spu_regions_flash_config
                0x0000000000001cfc       0x2c platform/libplatform_s.a(spu.o)
                0x0000000000001cfc                spu_regions_flash_config
 .text.spu_regions_sram_config
                0x0000000000001d28       0x34 platform/libplatform_s.a(spu.o)
                0x0000000000001d28                spu_regions_sram_config
 .text.spu_regions_flash_config_non_secure_callable
                0x0000000000001d5c       0x30 platform/libplatform_s.a(spu.o)
                0x0000000000001d5c                spu_regions_flash_config_non_secure_callable
 .text.spu_regions_flash_get_base_address_in_region
                0x0000000000001d8c        0x4 platform/libplatform_s.a(spu.o)
                0x0000000000001d8c                spu_regions_flash_get_base_address_in_region
 .text.spu_regions_flash_get_last_address_in_region
                0x0000000000001d90        0xa platform/libplatform_s.a(spu.o)
                0x0000000000001d90                spu_regions_flash_get_last_address_in_region
 .text.spu_regions_flash_get_start_id
                0x0000000000001d9a        0x4 platform/libplatform_s.a(spu.o)
                0x0000000000001d9a                spu_regions_flash_get_start_id
 .text.spu_regions_flash_get_last_id
                0x0000000000001d9e        0x4 platform/libplatform_s.a(spu.o)
                0x0000000000001d9e                spu_regions_flash_get_last_id
 .text.spu_regions_flash_get_region_size
                0x0000000000001da2        0x6 platform/libplatform_s.a(spu.o)
                0x0000000000001da2                spu_regions_flash_get_region_size
 .text.spu_regions_sram_get_base_address_in_region
                0x0000000000001da8        0xa platform/libplatform_s.a(spu.o)
                0x0000000000001da8                spu_regions_sram_get_base_address_in_region
 *fill*         0x0000000000001db2        0x2 
 .text.spu_regions_sram_get_last_address_in_region
                0x0000000000001db4        0xc platform/libplatform_s.a(spu.o)
                0x0000000000001db4                spu_regions_sram_get_last_address_in_region
 .text.spu_regions_sram_get_start_id
                0x0000000000001dc0        0x4 platform/libplatform_s.a(spu.o)
                0x0000000000001dc0                spu_regions_sram_get_start_id
 .text.spu_regions_sram_get_last_id
                0x0000000000001dc4        0x4 platform/libplatform_s.a(spu.o)
                0x0000000000001dc4                spu_regions_sram_get_last_id
 .text.spu_regions_sram_get_region_size
                0x0000000000001dc8        0x6 platform/libplatform_s.a(spu.o)
                0x0000000000001dc8                spu_regions_sram_get_region_size
 *fill*         0x0000000000001dce        0x2 
 .text.spu_peripheral_config_secure
                0x0000000000001dd0       0x1c platform/libplatform_s.a(spu.o)
                0x0000000000001dd0                spu_peripheral_config_secure
 .text.spu_peripheral_config_non_secure
                0x0000000000001dec       0x18 platform/libplatform_s.a(spu.o)
                0x0000000000001dec                spu_peripheral_config_non_secure
 .text.tfm_hal_platform_init
                0x0000000000001e04       0x30 platform/libplatform_s.a(tfm_hal_platform.o)
                0x0000000000001e04                tfm_hal_platform_init
 .text.tfm_plat_otp_init
                0x0000000000001e34        0x4 platform/libplatform_s.a(dummy_otp.o)
                0x0000000000001e34                tfm_plat_otp_init
 .text.tfm_plat_provisioning_is_required
                0x0000000000001e38        0x4 platform/libplatform_s.a(dummy_provisioning.o)
                0x0000000000001e38                tfm_plat_provisioning_is_required
 .text.tfm_plat_provisioning_perform
                0x0000000000001e3c        0x4 platform/libplatform_s.a(dummy_provisioning.o)
                0x0000000000001e3c                tfm_plat_provisioning_perform
 .text.tfm_plat_provisioning_check_for_dummy_keys
                0x0000000000001e40        0x2 platform/libplatform_s.a(dummy_provisioning.o)
                0x0000000000001e40                tfm_plat_provisioning_check_for_dummy_keys
 *fill*         0x0000000000001e42        0x2 
 .text.tfm_platform_hal_system_reset
                0x0000000000001e44       0x24 platform/libplatform_s.a(tfm_platform_system.o)
                0x0000000000001e44                tfm_platform_hal_system_reset
 .text.tfm_platform_hal_ioctl
                0x0000000000001e68       0x18 platform/libplatform_s.a(tfm_platform_system.o)
                0x0000000000001e68                tfm_platform_hal_ioctl
 .text.tfm_hal_system_reset
                0x0000000000001e80       0x24 platform/libplatform_s.a(tfm_hal_reset_halt.o)
                0x0000000000001e80                tfm_hal_system_reset
 .text.tfm_platform_hal_read_service
                0x0000000000001ea4       0x60 platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
                0x0000000000001ea4                tfm_platform_hal_read_service
 .text.tfm_platform_hal_gpio_service
                0x0000000000001f04       0x88 platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
                0x0000000000001f04                tfm_platform_hal_gpio_service
 *libtfm_spm*:*(.text*)
 .text.tfm_core_panic
                0x0000000000001f8c        0x4 secure_fw/spm/libtfm_spm.a(utilities.o)
                0x0000000000001f8c                tfm_core_panic
 .text.startup.main
                0x0000000000001f90       0x68 secure_fw/spm/libtfm_spm.a(main.o)
                0x0000000000001f90                main
 .text.tfm_core_svc_handler
                0x0000000000001ff8       0x7c secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
                0x0000000000001ff8                tfm_core_svc_handler
 .text.tfm_core_handler_mode
                0x0000000000002074        0x4 secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
                0x0000000000002074                tfm_core_handler_mode
 .text.arch_non_preempt_call
                0x0000000000002078       0x64 secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
                0x0000000000002078                arch_non_preempt_call
 .text.PendSV_Handler
                0x00000000000020dc       0x52 secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
                0x00000000000020dc                PendSV_Handler
 *fill*         0x000000000000212e        0x2 
 .text.SVC_Handler
                0x0000000000002130       0x68 secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
                0x0000000000002130                SVC_Handler
 .text.tfm_arch_set_secure_exception_priorities
                0x0000000000002198       0x28 secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
                0x0000000000002198                tfm_arch_set_secure_exception_priorities
 .text.tfm_arch_config_extensions
                0x00000000000021c0        0x2 secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
                0x00000000000021c0                tfm_arch_config_extensions
 *fill*         0x00000000000021c2        0x2 
 .text.ns_agent_tz_main
                0x00000000000021c4       0x30 secure_fw/spm/libtfm_spm.a(ns_agent_tz_v80m.o)
                0x00000000000021c4                ns_agent_tz_main
 .text.tfm_hal_bind_boundary
                0x00000000000021f4       0x64 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
                0x00000000000021f4                tfm_hal_bind_boundary
 .text.tfm_hal_activate_boundary
                0x0000000000002258       0x1c secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
                0x0000000000002258                tfm_hal_activate_boundary
 .text.tfm_hal_memory_check
                0x0000000000002274      0x126 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
                0x0000000000002274                tfm_hal_memory_check
 *fill*         0x000000000000239a        0x2 
 .text.mpu_init_cfg
                0x000000000000239c      0x11c secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
                0x000000000000239c                mpu_init_cfg
 .text.tfm_hal_set_up_static_boundaries
                0x00000000000024b8       0x2a secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
                0x00000000000024b8                tfm_hal_set_up_static_boundaries
 .text.tfm_hal_boundary_need_switch
                0x00000000000024e2       0x1a secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
                0x00000000000024e2                tfm_hal_boundary_need_switch
 .text.SPU_Handler
                0x00000000000024fc       0x38 secure_fw/spm/libtfm_spm.a(faults.o)
                0x00000000000024fc                SPU_Handler
 .text.SPU_IRQHandler
                0x0000000000002534       0x16 secure_fw/spm/libtfm_spm.a(faults.o)
                0x0000000000002534                SPU_IRQHandler
 *fill*         0x000000000000254a        0x2 
 .text.nrf_gpio_pin_control_select.constprop.0
                0x000000000000254c       0x1c secure_fw/spm/libtfm_spm.a(target_cfg.o)
 .text.enable_fault_handlers
                0x0000000000002568       0x14 secure_fw/spm/libtfm_spm.a(target_cfg.o)
                0x0000000000002568                enable_fault_handlers
 .text.system_reset_cfg
                0x000000000000257c       0x18 secure_fw/spm/libtfm_spm.a(target_cfg.o)
                0x000000000000257c                system_reset_cfg
 .text.init_debug
                0x0000000000002594       0x28 secure_fw/spm/libtfm_spm.a(target_cfg.o)
                0x0000000000002594                init_debug
 .text.nvic_interrupt_target_state_cfg
                0x00000000000025bc       0x40 secure_fw/spm/libtfm_spm.a(target_cfg.o)
                0x00000000000025bc                nvic_interrupt_target_state_cfg
 .text.nvic_interrupt_enable
                0x00000000000025fc       0x18 secure_fw/spm/libtfm_spm.a(target_cfg.o)
                0x00000000000025fc                nvic_interrupt_enable
 .text.sau_and_idau_cfg
                0x0000000000002614       0x20 secure_fw/spm/libtfm_spm.a(target_cfg.o)
                0x0000000000002614                sau_and_idau_cfg
 .text.spu_init_cfg
                0x0000000000002634       0x58 secure_fw/spm/libtfm_spm.a(target_cfg.o)
                0x0000000000002634                spu_init_cfg
 .text.spu_periph_init_cfg
                0x000000000000268c      0x230 secure_fw/spm/libtfm_spm.a(target_cfg.o)
                0x000000000000268c                spu_periph_init_cfg
 .text.spu_periph_configure_to_secure
                0x00000000000028bc        0x6 secure_fw/spm/libtfm_spm.a(target_cfg.o)
                0x00000000000028bc                spu_periph_configure_to_secure
 *fill*         0x00000000000028c2        0x2 
 .text.tfm_core_validate_boot_data
                0x00000000000028c4        0xc secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
                0x00000000000028c4                tfm_core_validate_boot_data
 .text.tfm_core_get_boot_data_handler
                0x00000000000028d0       0x34 secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
                0x00000000000028d0                tfm_core_get_boot_data_handler
 .text.tfm_arch_free_msp_and_exc_ret
                0x0000000000002904        0x8 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
                0x0000000000002904                tfm_arch_free_msp_and_exc_ret
 .text.tfm_arch_set_context_ret_code
                0x000000000000290c       0x14 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
                0x000000000000290c                tfm_arch_set_context_ret_code
 .text.tfm_arch_trigger_pendsv
                0x0000000000002920       0x18 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
                0x0000000000002920                tfm_arch_trigger_pendsv
 .text.tfm_arch_init_context
                0x0000000000002938       0x54 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
                0x0000000000002938                tfm_arch_init_context
 .text.tfm_arch_refresh_hardware_context
                0x000000000000298c       0x12 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
                0x000000000000298c                tfm_arch_refresh_hardware_context
 *fill*         0x000000000000299e        0x2 
 .text.connection_to_handle
                0x00000000000029a0       0x20 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x00000000000029a0                connection_to_handle
 .text.handle_to_connection
                0x00000000000029c0       0x10 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x00000000000029c0                handle_to_connection
 .text.spm_allocate_connection
                0x00000000000029d0       0x20 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x00000000000029d0                spm_allocate_connection
 .text.spm_validate_connection
                0x00000000000029f0       0x1c secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x00000000000029f0                spm_validate_connection
 .text.spm_free_connection
                0x0000000000002a0c       0x1c secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x0000000000002a0c                spm_free_connection
 .text.spm_get_handle_by_signal
                0x0000000000002a28       0x46 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x0000000000002a28                spm_get_handle_by_signal
 *fill*         0x0000000000002a6e        0x2 
 .text.tfm_spm_get_service_by_sid
                0x0000000000002a70       0x2c secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x0000000000002a70                tfm_spm_get_service_by_sid
 .text.tfm_spm_check_client_version
                0x0000000000002a9c       0x24 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x0000000000002a9c                tfm_spm_check_client_version
 .text.tfm_spm_check_authorization
                0x0000000000002ac0       0x48 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x0000000000002ac0                tfm_spm_check_authorization
 .text.spm_fill_message
                0x0000000000002b08       0x30 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x0000000000002b08                spm_fill_message
 .text.tfm_spm_partition_get_running_partition_id
                0x0000000000002b38       0x20 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x0000000000002b38                tfm_spm_partition_get_running_partition_id
 .text.spm_msg_handle_to_connection
                0x0000000000002b58       0x28 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x0000000000002b58                spm_msg_handle_to_connection
 .text.tfm_spm_is_ns_caller
                0x0000000000002b80       0x20 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x0000000000002b80                tfm_spm_is_ns_caller
 .text.tfm_spm_get_client_id
                0x0000000000002ba0       0x20 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x0000000000002ba0                tfm_spm_get_client_id
 .text.tfm_spm_init
                0x0000000000002bc0       0x74 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x0000000000002bc0                tfm_spm_init
 .text.update_caller_outvec_len
                0x0000000000002c34       0x24 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x0000000000002c34                update_caller_outvec_len
 .text.cross_call_entering_c
                0x0000000000002c58       0x2c secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
                0x0000000000002c58                cross_call_entering_c
 .text.cross_call_exiting_c
                0x0000000000002c84       0x30 secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
                0x0000000000002c84                cross_call_exiting_c
 .text.spm_interface_cross_dispatcher
                0x0000000000002cb4       0x3c secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
                0x0000000000002cb4                spm_interface_cross_dispatcher
 .text.load_a_partition_assuredly
                0x0000000000002cf0       0xd0 secure_fw/spm/libtfm_spm.a(rom_loader.o)
                0x0000000000002cf0                load_a_partition_assuredly
 .text.load_services_assuredly
                0x0000000000002dc0       0xc4 secure_fw/spm/libtfm_spm.a(rom_loader.o)
                0x0000000000002dc0                load_services_assuredly
 .text.load_irqs_assuredly
                0x0000000000002e84        0x2 secure_fw/spm/libtfm_spm.a(rom_loader.o)
                0x0000000000002e84                load_irqs_assuredly
 .text.spm_handle_programmer_errors
                0x0000000000002e86       0x18 secure_fw/spm/libtfm_spm.a(psa_api.o)
                0x0000000000002e86                spm_handle_programmer_errors
 .text.tfm_spm_get_lifecycle_state
                0x0000000000002e9e        0x4 secure_fw/spm/libtfm_spm.a(psa_api.o)
                0x0000000000002e9e                tfm_spm_get_lifecycle_state
 *fill*         0x0000000000002ea2        0x2 
 .text.tfm_spm_partition_psa_wait
                0x0000000000002ea4       0x38 secure_fw/spm/libtfm_spm.a(psa_api.o)
                0x0000000000002ea4                tfm_spm_partition_psa_wait
 .text.tfm_spm_partition_psa_get
                0x0000000000002edc       0x64 secure_fw/spm/libtfm_spm.a(psa_api.o)
                0x0000000000002edc                tfm_spm_partition_psa_get
 .text.tfm_spm_partition_psa_reply
                0x0000000000002f40       0x9a secure_fw/spm/libtfm_spm.a(psa_api.o)
                0x0000000000002f40                tfm_spm_partition_psa_reply
 .text.tfm_spm_partition_psa_panic
                0x0000000000002fda        0x4 secure_fw/spm/libtfm_spm.a(psa_api.o)
                0x0000000000002fda                tfm_spm_partition_psa_panic
 .text.query_state
                0x0000000000002fde       0x40 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
 *fill*         0x000000000000301e        0x2 
 .text.backend_init_comp_assuredly
                0x0000000000003020       0xe0 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
                0x0000000000003020                backend_init_comp_assuredly
 .text.backend_system_run
                0x0000000000003100       0x44 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
                0x0000000000003100                backend_system_run
 .text.backend_wait_signals
                0x0000000000003144       0x20 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
                0x0000000000003144                backend_wait_signals
 .text.backend_assert_signal
                0x0000000000003164       0x20 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
                0x0000000000003164                backend_assert_signal
 .text.backend_messaging
                0x0000000000003184       0x32 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
                0x0000000000003184                backend_messaging
 .text.backend_replying
                0x00000000000031b6       0x10 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
                0x00000000000031b6                backend_replying
 *fill*         0x00000000000031c6        0x2 
 .text.ipc_schedule
                0x00000000000031c8       0x80 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
                0x00000000000031c8                ipc_schedule
 .text.tfm_pool_init
                0x0000000000003248       0x5c secure_fw/spm/libtfm_spm.a(tfm_pools.o)
                0x0000000000003248                tfm_pool_init
 .text.tfm_pool_alloc
                0x00000000000032a4       0x16 secure_fw/spm/libtfm_spm.a(tfm_pools.o)
                0x00000000000032a4                tfm_pool_alloc
 .text.tfm_pool_free
                0x00000000000032ba        0xc secure_fw/spm/libtfm_spm.a(tfm_pools.o)
                0x00000000000032ba                tfm_pool_free
 .text.is_valid_chunk_data_in_pool
                0x00000000000032c6       0x28 secure_fw/spm/libtfm_spm.a(tfm_pools.o)
                0x00000000000032c6                is_valid_chunk_data_in_pool
 *fill*         0x00000000000032ee        0x2 
 .text.thrd_set_query_callback
                0x00000000000032f0        0xc secure_fw/spm/libtfm_spm.a(thread.o)
                0x00000000000032f0                thrd_set_query_callback
 .text.thrd_next
                0x00000000000032fc       0x40 secure_fw/spm/libtfm_spm.a(thread.o)
                0x00000000000032fc                thrd_next
 .text.thrd_set_state
                0x000000000000333c       0x28 secure_fw/spm/libtfm_spm.a(thread.o)
                0x000000000000333c                thrd_set_state
 .text.thrd_start
                0x0000000000003364       0x44 secure_fw/spm/libtfm_spm.a(thread.o)
                0x0000000000003364                thrd_start
 .text.thrd_start_scheduler
                0x00000000000033a8       0x1c secure_fw/spm/libtfm_spm.a(thread.o)
                0x00000000000033a8                thrd_start_scheduler
 .text.tfm_nspm_get_current_client_id
                0x00000000000033c4        0x6 secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o)
                0x00000000000033c4                tfm_nspm_get_current_client_id
 *fill*         0x00000000000033ca        0x2 
 .text.tfm_nspm_ctx_init
                0x00000000000033cc       0x18 secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o)
                0x00000000000033cc                tfm_nspm_ctx_init
 .text.tfm_hal_platform_common_init
                0x00000000000033e4       0x36 secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
                0x00000000000033e4                tfm_hal_platform_common_init
 *fill*         0x000000000000341a        0x2 
 .text.tfm_hal_get_ns_VTOR
                0x000000000000341c        0xc secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
                0x000000000000341c                tfm_hal_get_ns_VTOR
 .text.tfm_hal_get_ns_MSP
                0x0000000000003428        0xc secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
                0x0000000000003428                tfm_hal_get_ns_MSP
 .text.tfm_hal_get_ns_entry_point
                0x0000000000003434        0xc secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
                0x0000000000003434                tfm_hal_get_ns_entry_point
 .text.tfm_spm_client_psa_call
                0x0000000000003440      0x200 secure_fw/spm/libtfm_spm.a(psa_call_api.o)
                0x0000000000003440                tfm_spm_client_psa_call
 .text.tfm_spm_client_psa_framework_version
                0x0000000000003640        0x6 secure_fw/spm/libtfm_spm.a(psa_version_api.o)
                0x0000000000003640                tfm_spm_client_psa_framework_version
 .text.tfm_spm_client_psa_version
                0x0000000000003646       0x2c secure_fw/spm/libtfm_spm.a(psa_version_api.o)
                0x0000000000003646                tfm_spm_client_psa_version
 *fill*         0x0000000000003672        0x2 
 .text.tfm_spm_partition_psa_read
                0x0000000000003674       0x7c secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
                0x0000000000003674                tfm_spm_partition_psa_read
 .text.tfm_spm_partition_psa_skip
                0x00000000000036f0       0x46 secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
                0x00000000000036f0                tfm_spm_partition_psa_skip
 *fill*         0x0000000000003736        0x2 
 .text.tfm_spm_partition_psa_write
                0x0000000000003738       0x78 secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
                0x0000000000003738                tfm_spm_partition_psa_write
 *libplatform_s*:*(.rodata*)
 .rodata.CSWTCH.17
                0x00000000000037b0        0x4 platform/libplatform_s.a(system_nrf5340_application.o)
 .rodata.CSWTCH.5
                0x00000000000037b4        0x4 platform/libplatform_s.a(system_nrf5340_application.o)
 .rodata.CSWTCH.3
                0x00000000000037b8        0x4 platform/libplatform_s.a(system_nrf5340_application.o)
 .rodata.store_and_dump_context.str1.1
                0x00000000000037bc       0x71 platform/libplatform_s.a(exception_info.o)
 *fill*         0x000000000000382d        0x3 
 .rodata.Driver_USART1
                0x0000000000003830       0x38 platform/libplatform_s.a(Driver_USART.o)
                0x0000000000003830                Driver_USART1
 .rodata.UART1_pins
                0x0000000000003868       0x10 platform/libplatform_s.a(Driver_USART.o)
 .rodata.DriverVersion
                0x0000000000003878        0x4 platform/libplatform_s.a(Driver_USART.o)
 *fill*         0x000000000000387c        0x4 
 .rodata.delay_machine_code.0
                0x0000000000003880        0x6 platform/libplatform_s.a(nrfx_uarte.o)
 *fill*         0x0000000000003886        0x2 
 .rodata.ranges
                0x0000000000003888       0x20 platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
 *libtfm_spm*:*(.rodata*)
 .rodata.main.str1.1
                0x00000000000038a8       0x56 secure_fw/spm/libtfm_spm.a(main.o)
 .rodata.tfm_core_svc_handler.str1.1
                0x00000000000038fe       0x20 secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
 .rodata.SPU_Handler.str1.1
                0x000000000000391e       0x20 secure_fw/spm/libtfm_spm.a(faults.o)
 *fill*         0x000000000000393e        0x2 
 .rodata.memory_regions
                0x0000000000003940       0x1c secure_fw/spm/libtfm_spm.a(target_cfg.o)
                0x0000000000003940                memory_regions
 .rodata.serv_pool_ea
                0x000000000000395c        0x4 secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .rodata.part_pool_ea
                0x0000000000003960        0x4 secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .rodata.ldinf_ea
                0x0000000000003964        0x4 secure_fw/spm/libtfm_spm.a(rom_loader.o)

.tfm_secure_data_start
                0x0000000020000000        0x0
                0x0000000020000000                . = ABSOLUTE (0x20000000)

.tfm_bl2_shared_data
                0x0000000020000000      0x400
                0x0000000020000400                . = (. + 0x400)
 *fill*         0x0000000020000000      0x400 

.msp_stack      0x0000000020000400      0x7f8
                0x0000000020000bf8                . = (. + (__msp_stack_size__ - 0x8))
 *fill*         0x0000000020000400      0x7f8 
                0x0000000020000400                Image$$ARM_LIB_STACK$$ZI$$Base = ADDR (.msp_stack)
                0x0000000020000bf8                Image$$ARM_LIB_STACK$$ZI$$Limit = (ADDR (.msp_stack) + SIZEOF (.msp_stack))

.msp_stack_seal_res
                0x0000000020000bf8        0x8
                0x0000000020000c00                . = (. + 0x8)
 *fill*         0x0000000020000bf8        0x8 
                0x0000000020000bf8                __StackSeal = ADDR (.msp_stack_seal_res)

.TFM_SP_META_PTR
                0x0000000020000c00       0x20
 *(.bss.SP_META_PTR_SPRTL_INST)
 .bss.SP_META_PTR_SPRTL_INST
                0x0000000020000c00        0x4 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sprt_partition_metadata_indicator.o)
                0x0000000020000c00                p_partition_metadata
                0x0000000020000c20                . = ALIGN (0x20)
 *fill*         0x0000000020000c04       0x1c 
                0x0000000020000c00                Image$$TFM_SP_META_PTR$$ZI$$Base = ADDR (.TFM_SP_META_PTR)
                0x0000000020000c20                Image$$TFM_SP_META_PTR$$ZI$$Limit = (ADDR (.TFM_SP_META_PTR) + SIZEOF (.TFM_SP_META_PTR))
                0x0000000020000c20                . = ALIGN (0x20)
                0x0000000020000c20                Image$$TFM_APP_RW_STACK_START$$Base = .

.TFM_APP_ROT_LINKER_DATA
                0x0000000020000c20        0x0 load address 0x0000000000003980
 *tfm_app_rot_partition*:*(.data*)
 *(TFM_*_APP-ROT_ATTR_RW)
                0x0000000020000c20                . = ALIGN (0x4)
                0x0000000020000c20                Image$$TFM_APP_ROT_LINKER_DATA$$RW$$Base = ADDR (.TFM_APP_ROT_LINKER_DATA)
                0x0000000020000c20                Image$$TFM_APP_ROT_LINKER_DATA$$RW$$Limit = (ADDR (.TFM_APP_ROT_LINKER_DATA) + SIZEOF (.TFM_APP_ROT_LINKER_DATA))

.TFM_APP_ROT_LINKER_BSS
                0x0000000020000c20       0x20
                0x0000000020000c20                start_of_TFM_APP_ROT_LINKER = .
 *tfm_app_rot_partition*:*(.bss*)
 *tfm_app_rot_partition*:*(COMMON)
 *(TFM_*_APP-ROT_ATTR_ZI)
                0x0000000020000c24                . = (. + (. - start_of_TFM_APP_ROT_LINKER)?0x0:0x4)
 *fill*         0x0000000020000c20        0x4 
                0x0000000020000c40                . = ALIGN (0x20)
 *fill*         0x0000000020000c24       0x1c 
                0x0000000020000c20                Image$$TFM_APP_ROT_LINKER_DATA$$ZI$$Base = ADDR (.TFM_APP_ROT_LINKER_BSS)
                0x0000000020000c40                Image$$TFM_APP_ROT_LINKER_DATA$$ZI$$Limit = (ADDR (.TFM_APP_ROT_LINKER_BSS) + SIZEOF (.TFM_APP_ROT_LINKER_BSS))
                0x0000000020000c40                Image$$TFM_APP_RW_STACK_END$$Base = .
                0x0000000020000c40                Image$$TFM_PSA_RW_STACK_START$$Base = .

.TFM_PSA_ROT_LINKER_DATA
                0x0000000020000c40        0x0 load address 0x0000000000003980
 *tfm_psa_rot_partition*:*(.data*)
 *(TFM_*_PSA-ROT_ATTR_RW)
                0x0000000020000c40                . = ALIGN (0x4)
                0x0000000020000c40                Image$$TFM_PSA_ROT_LINKER_DATA$$RW$$Base = ADDR (.TFM_PSA_ROT_LINKER_DATA)
                0x0000000020000c40                Image$$TFM_PSA_ROT_LINKER_DATA$$RW$$Limit = (ADDR (.TFM_PSA_ROT_LINKER_DATA) + SIZEOF (.TFM_PSA_ROT_LINKER_DATA))

.TFM_PSA_ROT_LINKER_BSS
                0x0000000020000c40     0x11e0
                0x0000000020000c40                start_of_TFM_PSA_ROT_LINKER = .
 *tfm_psa_rot_partition*:*(.bss*)
 .bss.scratch   0x0000000020000c40      0x408 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
 .bss.operations
                0x0000000020001048       0x28 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
 .bss.mbedtls_mem_buf
                0x0000000020001070        0x1 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 .bss.mbedtls_version_full
                0x0000000020001071       0x12 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 *fill*         0x0000000020001083        0x5 
 .bss.tfm_sp_crypto_stack
                0x0000000020001088      0x800 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(intermedia_tfm_crypto.o)
                0x0000000020001088                tfm_sp_crypto_stack
 .bss.tfm_sp_platform_stack
                0x0000000020001888      0x500 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(intermedia_tfm_platform.o)
                0x0000000020001888                tfm_sp_platform_stack
 .bss.g_builtin_key_slots
                0x0000000020001d88       0x90 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)
 *tfm_psa_rot_partition*:*(COMMON)
 *(TFM_*_PSA-ROT_ATTR_ZI)
                0x0000000020001e18                . = (. + (. - start_of_TFM_PSA_ROT_LINKER)?0x0:0x4)
                0x0000000020001e20                . = ALIGN (0x20)
 *fill*         0x0000000020001e18        0x8 
                0x0000000020000c40                Image$$TFM_PSA_ROT_LINKER_DATA$$ZI$$Base = ADDR (.TFM_PSA_ROT_LINKER_BSS)
                0x0000000020001e20                Image$$TFM_PSA_ROT_LINKER_DATA$$ZI$$Limit = (ADDR (.TFM_PSA_ROT_LINKER_BSS) + SIZEOF (.TFM_PSA_ROT_LINKER_BSS))
                0x0000000020001e20                Image$$TFM_PSA_RW_STACK_END$$Base = .

.TFM_UNPRIV_CODE
                0x0000000000003980     0x35c0
 *(SFN)
 SFN            0x0000000000003980       0x88 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
                0x0000000000003980                __acle_se_tfm_psa_framework_version_veneer
                0x000000000000399e                __acle_se_tfm_psa_version_veneer
                0x00000000000039bc                __acle_se_tfm_psa_call_veneer
                0x00000000000039de                __acle_se_tfm_psa_connect_veneer
                0x00000000000039f0                __acle_se_tfm_psa_close_veneer
 *(.text*)
 .text          0x0000000000003a08       0x88 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtbegin.o
 .text          0x0000000000003a90       0x7c c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp/crt0.o
                0x0000000000003a90                _stack_init
                0x0000000000003a98                _start
                0x0000000000003a98                _mainCRTStartup
 .text.clear_caller_context
                0x0000000000003b0c        0x8 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
 .text.C_HardFault_Handler
                0x0000000000003b14        0x4 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
                0x0000000000003b14                C_HardFault_Handler
 .text.HardFault_Handler
                0x0000000000003b18       0x16 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
                0x0000000000003b18                HardFault_Handler
 .text.C_MemManage_Handler
                0x0000000000003b2e        0x4 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
                0x0000000000003b2e                C_MemManage_Handler
 .text.MemManage_Handler
                0x0000000000003b32       0x16 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
                0x0000000000003b32                MemManage_Handler
 .text.C_BusFault_Handler
                0x0000000000003b48        0x4 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
                0x0000000000003b48                C_BusFault_Handler
 .text.BusFault_Handler
                0x0000000000003b4c       0x16 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
                0x0000000000003b4c                BusFault_Handler
 .text.C_SecureFault_Handler
                0x0000000000003b62        0x4 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
                0x0000000000003b62                C_SecureFault_Handler
 .text.SecureFault_Handler
                0x0000000000003b66       0x16 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
                0x0000000000003b66                SecureFault_Handler
 .text.C_UsageFault_Handler
                0x0000000000003b7c        0x4 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
                0x0000000000003b7c                C_UsageFault_Handler
 .text.UsageFault_Handler
                0x0000000000003b80       0x16 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
                0x0000000000003b80                UsageFault_Handler
 .text.memmove  0x0000000000003b96       0x6c secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memmove.o)
                0x0000000000003b96                memmove
 .text.memcpy   0x0000000000003c02       0x4a secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memcpy.o)
                0x0000000000003c02                memcpy
 .text.memset   0x0000000000003c4c       0x3e secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memset.o)
                0x0000000000003c4c                memset
 *fill*         0x0000000000003c8a        0x2 
 .text.psa_framework_version
                0x0000000000003c8c       0x10 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
                0x0000000000003c8c                psa_framework_version
 .text.psa_version
                0x0000000000003c9c       0x10 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
                0x0000000000003c9c                psa_version
 .text.tfm_psa_call_pack
                0x0000000000003cac       0x14 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
                0x0000000000003cac                tfm_psa_call_pack
 .text.psa_wait
                0x0000000000003cc0       0x10 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
                0x0000000000003cc0                psa_wait
 .text.psa_get  0x0000000000003cd0       0x10 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
                0x0000000000003cd0                psa_get
 .text.psa_read
                0x0000000000003ce0       0x14 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
                0x0000000000003ce0                psa_read
 .text.psa_write
                0x0000000000003cf4       0x14 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
                0x0000000000003cf4                psa_write
 .text.psa_reply
                0x0000000000003d08       0x10 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
                0x0000000000003d08                psa_reply
 .text.psa_panic
                0x0000000000003d18       0x10 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
                0x0000000000003d18                psa_panic
 .text.tfm_plat_builtin_key_get_desc_table_ptr
                0x0000000000003d28        0x4 platform/libplatform_crypto_keys.a(crypto_keys.o)
                0x0000000000003d28                tfm_plat_builtin_key_get_desc_table_ptr
 .text.verify_header
                0x0000000000003d2c       0x40 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .text.verify_chain
                0x0000000000003d6c       0x3c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .text.buffer_alloc_calloc
                0x0000000000003da8       0xf4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .text.buffer_alloc_free
                0x0000000000003e9c       0xe8 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .text.mbedtls_memory_buffer_alloc_init
                0x0000000000003f84       0x64 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
                0x0000000000003f84                mbedtls_memory_buffer_alloc_init
 .text.platform_calloc_uninit
                0x0000000000003fe8        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .text.platform_free_uninit
                0x0000000000003fec        0x2 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 *fill*         0x0000000000003fee        0x2 
 .text.mbedtls_free
                0x0000000000003ff0        0xc secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
                0x0000000000003ff0                mbedtls_free
 .text.mbedtls_platform_set_calloc_free
                0x0000000000003ffc       0x14 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
                0x0000000000003ffc                mbedtls_platform_set_calloc_free
 .text.psa_remove_key_data_from_memory
                0x0000000000004010       0x1c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
                0x0000000000004010                psa_remove_key_data_from_memory
 .text.psa_wipe_key_slot
                0x000000000000402c       0x22 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
                0x000000000000402c                psa_wipe_key_slot
 *fill*         0x000000000000404e        0x2 
 .text.mbedcrypto__psa_generate_random
                0x0000000000004050       0x1c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
                0x0000000000004050                mbedcrypto__psa_generate_random
 .text.mbedtls_psa_crypto_free
                0x000000000000406c       0x24 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
                0x000000000000406c                mbedtls_psa_crypto_free
 .text.mbedcrypto__psa_crypto_init
                0x0000000000004090       0x3c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
                0x0000000000004090                mbedcrypto__psa_crypto_init
 .text.psa_initialize_key_slots
                0x00000000000040cc       0x18 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
                0x00000000000040cc                psa_initialize_key_slots
 .text.psa_wipe_all_key_slots
                0x00000000000040e4       0x2c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
                0x00000000000040e4                psa_wipe_all_key_slots
 .text.psa_driver_wrapper_init
                0x0000000000004110        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
                0x0000000000004110                psa_driver_wrapper_init
 .text.psa_driver_wrapper_free
                0x0000000000004114        0x2 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
                0x0000000000004114                psa_driver_wrapper_free
 .text.psa_driver_wrapper_init_random
                0x0000000000004116        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
                0x0000000000004116                psa_driver_wrapper_init_random
 .text.psa_driver_wrapper_get_random
                0x000000000000411a       0x22 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
                0x000000000000411a                psa_driver_wrapper_get_random
 .text.psa_driver_wrapper_free_random
                0x000000000000413c        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
                0x000000000000413c                psa_driver_wrapper_free_random
 .text.common_sfn_thread
                0x0000000000004140       0x88 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sfn_common_thread.o)
                0x0000000000004140                common_sfn_thread
 .text.nrf_cc3xx_platform_init
                0x00000000000041c8       0x44 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
                0x00000000000041c8                nrf_cc3xx_platform_init
 .text.nrf_cc3xx_platform_ctr_drbg_init
                0x000000000000420c       0x60 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
                0x000000000000420c                nrf_cc3xx_platform_ctr_drbg_init
 .text.nrf_cc3xx_platform_ctr_drbg_get
                0x000000000000426c       0x44 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
                0x000000000000426c                nrf_cc3xx_platform_ctr_drbg_get
 .text.mbedtls_zeroize_internal
                0x00000000000042b0       0x14 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_common.c.obj)
                0x00000000000042b0                mbedtls_zeroize_internal
 .text.mbedtls_platform_zeroize
                0x00000000000042c4       0x14 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_common.c.obj)
                0x00000000000042c4                mbedtls_platform_zeroize
 .text.block_cipher_df
                0x00000000000042d8      0x1d0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .text.ctr_drbg_update_internal
                0x00000000000044a8      0x174 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .text.mbedtls_ctr_drbg_reseed_internal
                0x000000000000461c       0xc4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_init
                0x00000000000046e0       0x2c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
                0x00000000000046e0                cc_mbedtls_ctr_drbg_init
 .text.cc_mbedtls_ctr_drbg_seed
                0x000000000000470c       0xa0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
                0x000000000000470c                cc_mbedtls_ctr_drbg_seed
 .text.cc_mbedtls_ctr_drbg_random_with_add
                0x00000000000047ac      0x1e4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
                0x00000000000047ac                cc_mbedtls_ctr_drbg_random_with_add
 .text.entropy_update
                0x0000000000004990       0x7c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj)
 .text.entropy_gather_internal.part.0
                0x0000000000004a0c       0x88 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj)
 .text.cc_mbedtls_entropy_init
                0x0000000000004a94       0x84 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj)
                0x0000000000004a94                cc_mbedtls_entropy_init
 .text.cc_mbedtls_entropy_func
                0x0000000000004b18      0x110 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj)
                0x0000000000004b18                cc_mbedtls_entropy_func
 .text.RndStartupTest.constprop.0
                0x0000000000004c28       0xac C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .text.CC_LibInit
                0x0000000000004cd4       0x74 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
                0x0000000000004cd4                CC_LibInit
 .text.CC_HalInit
                0x0000000000004d48        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_hal.c.obj)
                0x0000000000004d48                CC_HalInit
 .text.CC_HalClearInterruptBit
                0x0000000000004d4c       0x1c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_hal.c.obj)
                0x0000000000004d4c                CC_HalClearInterruptBit
 .text.CC_HalMaskInterrupt
                0x0000000000004d68        0xc C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_hal.c.obj)
                0x0000000000004d68                CC_HalMaskInterrupt
 .text.CC_HalWaitInterrupt
                0x0000000000004d74        0xc C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_hal.c.obj)
                0x0000000000004d74                CC_HalWaitInterrupt
 .text.CC_HalWaitInterruptRND
                0x0000000000004d80        0xc C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_hal.c.obj)
                0x0000000000004d80                CC_HalWaitInterruptRND
 .text.CC_PalInit
                0x0000000000004d8c       0x5c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj)
                0x0000000000004d8c                CC_PalInit
 .text.CC_PalTerminate
                0x0000000000004de8       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj)
                0x0000000000004de8                CC_PalTerminate
 .text.CC_PalDmaInit
                0x0000000000004e1c        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_dma.c.obj)
                0x0000000000004e1c                CC_PalDmaInit
 .text.CC_PalDmaTerminate
                0x0000000000004e20        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_dma.c.obj)
                0x0000000000004e20                CC_PalDmaTerminate
 .text.CC_PalWaitInterruptRND
                0x0000000000004e24       0x38 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_interrupt_ctrl.c.obj)
                0x0000000000004e24                CC_PalWaitInterruptRND
 .text.CC_PalWaitInterrupt
                0x0000000000004e5c       0x28 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_interrupt_ctrl.c.obj)
                0x0000000000004e5c                CC_PalWaitInterrupt
 .text.CC_PalMemCopyPlat
                0x0000000000004e84        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mem.c.obj)
                0x0000000000004e84                CC_PalMemCopyPlat
 .text.CC_PalMemSetPlat
                0x0000000000004e88        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mem.c.obj)
                0x0000000000004e88                CC_PalMemSetPlat
 .text.CC_PalMemSetZeroPlat
                0x0000000000004e8c        0x8 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mem.c.obj)
                0x0000000000004e8c                CC_PalMemSetZeroPlat
 .text.CC_PalMutexCreate
                0x0000000000004e94       0x14 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mutex.c.obj)
                0x0000000000004e94                CC_PalMutexCreate
 .text.CC_PalMutexDestroy
                0x0000000000004ea8       0x14 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mutex.c.obj)
                0x0000000000004ea8                CC_PalMutexDestroy
 .text.CC_PalMutexLock
                0x0000000000004ebc       0x10 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mutex.c.obj)
                0x0000000000004ebc                CC_PalMutexLock
 .text.CC_PalMutexUnlock
                0x0000000000004ecc       0x14 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mutex.c.obj)
                0x0000000000004ecc                CC_PalMutexUnlock
 .text.CC_PalPowerSaveModeInit
                0x0000000000004ee0       0x3c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_pm.c.obj)
                0x0000000000004ee0                CC_PalPowerSaveModeInit
 .text.CC_PalPowerSaveModeSelect
                0x0000000000004f1c       0x84 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_pm.c.obj)
                0x0000000000004f1c                CC_PalPowerSaveModeSelect
 .text.mutex_init
                0x0000000000004fa0        0xc C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
                0x0000000000004fa0                mutex_init
 .text.mutex_lock
                0x0000000000004fac        0xc C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
                0x0000000000004fac                mutex_lock
 .text.mutex_unlock
                0x0000000000004fb8        0xc C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
                0x0000000000004fb8                mutex_unlock
 .text.nrf_cc3xx_platform_abort
                0x0000000000004fc4       0x24 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_abort.c.obj)
 .text.CC_PalAbort
                0x0000000000004fe8       0x44 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_abort.c.obj)
                0x0000000000004fe8                CC_PalAbort
 .text.mutex_free
                0x000000000000502c       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.mutex_lock
                0x0000000000005060       0x48 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.mutex_unlock
                0x00000000000050a8       0x38 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.mutex_init
                0x00000000000050e0       0x20 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.startTrngHW
                0x0000000000005100      0x130 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_RepetitionCounterTest
                0x0000000000005230       0x64 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd_trng90b.c.obj)
                0x0000000000005230                LLF_RND_RepetitionCounterTest
 .text.LLF_RND_AdaptiveProportionTest
                0x0000000000005294       0x80 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd_trng90b.c.obj)
                0x0000000000005294                LLF_RND_AdaptiveProportionTest
 .text.getTrngSource
                0x0000000000005314      0x2a8 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_GetTrngSource
                0x00000000000055bc       0x18 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd_trng90b.c.obj)
                0x00000000000055bc                LLF_RND_GetTrngSource
 .text.LLF_RND_RunTrngStartupTest
                0x00000000000055d4       0x1c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd_trng90b.c.obj)
                0x00000000000055d4                LLF_RND_RunTrngStartupTest
 .text.mbedtls_hardware_poll
                0x00000000000055f0      0x104 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(trng_api.c.obj)
                0x00000000000055f0                mbedtls_hardware_poll
 .text.cc_mbedtls_aes_init
                0x00000000000056f4       0x20 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
                0x00000000000056f4                cc_mbedtls_aes_init
 .text.cc_mbedtls_aes_free
                0x0000000000005714        0xc C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
                0x0000000000005714                cc_mbedtls_aes_free
 .text.cc_mbedtls_aes_setkey_enc
                0x0000000000005720       0x44 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
                0x0000000000005720                cc_mbedtls_aes_setkey_enc
 .text.cc_mbedtls_aes_crypt_ecb
                0x0000000000005764       0x50 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
                0x0000000000005764                cc_mbedtls_aes_crypt_ecb
 .text.cc_mbedtls_sha256_init
                0x00000000000057b4       0x28 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256_alt.c.obj)
                0x00000000000057b4                cc_mbedtls_sha256_init
 .text.cc_mbedtls_sha256_free
                0x00000000000057dc        0xc C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256_alt.c.obj)
                0x00000000000057dc                cc_mbedtls_sha256_free
 .text.cc_mbedtls_sha256_starts
                0x00000000000057e8       0x28 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256_alt.c.obj)
                0x00000000000057e8                cc_mbedtls_sha256_starts
 .text.cc_mbedtls_sha256_update
                0x0000000000005810       0x54 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256_alt.c.obj)
                0x0000000000005810                cc_mbedtls_sha256_update
 .text.cc_mbedtls_sha256_finish
                0x0000000000005864       0x48 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256_alt.c.obj)
                0x0000000000005864                cc_mbedtls_sha256_finish
 .text.mbedtls_sha_starts_internal
                0x00000000000058ac       0x28 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_hash_common.c.obj)
                0x00000000000058ac                mbedtls_sha_starts_internal
 .text.mbedtls_sha_finish_internal
                0x00000000000058d4       0x5c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_hash_common.c.obj)
                0x00000000000058d4                mbedtls_sha_finish_internal
 .text.mbedtls_sha_update_internal
                0x0000000000005930      0x1ec C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_hash_common.c.obj)
                0x0000000000005930                mbedtls_sha_update_internal
 .text.cc_mbedtls_sha256
                0x0000000000005b1c       0x50 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256.c.obj)
                0x0000000000005b1c                cc_mbedtls_sha256
 .text.RNG_PLAT_SetUserRngParameters
                0x0000000000005b6c       0x74 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_rng_plat.c.obj)
                0x0000000000005b6c                RNG_PLAT_SetUserRngParameters
 .text.CC_PalTrngParamGet
                0x0000000000005be0       0xac C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_trng.c.obj)
                0x0000000000005be0                CC_PalTrngParamGet
 .text.LLF_RND_WaitRngInterrupt
                0x0000000000005c8c       0x20 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd.c.obj)
                0x0000000000005c8c                LLF_RND_WaitRngInterrupt
 .text.LLF_RND_GetRoscSampleCnt
                0x0000000000005cac       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd.c.obj)
                0x0000000000005cac                LLF_RND_GetRoscSampleCnt
 .text.LLF_RND_GetFastestRosc
                0x0000000000005ce0       0x20 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd.c.obj)
                0x0000000000005ce0                LLF_RND_GetFastestRosc
 .text.LLF_RND_TurnOffTrng
                0x0000000000005d00       0x24 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd.c.obj)
                0x0000000000005d00                LLF_RND_TurnOffTrng
 .text.SetDataBuffersInfo
                0x0000000000005d24       0x64 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(driver_common.c.obj)
                0x0000000000005d24                SetDataBuffersInfo
 .text.InitHashDrv
                0x0000000000005d88       0x50 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hash_driver.c.obj)
                0x0000000000005d88                InitHashDrv
 .text.ProcessHashDrv
                0x0000000000005dd8      0x210 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hash_driver.c.obj)
                0x0000000000005dd8                ProcessHashDrv
 .text.FinishHashDrv
                0x0000000000005fe8       0x74 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hash_driver.c.obj)
                0x0000000000005fe8                FinishHashDrv
 .text.InitAes  0x000000000000605c       0xc8 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_driver.c.obj)
 .text.LoadAesKey
                0x0000000000006124      0x114 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_driver.c.obj)
 .text.write_invalid_key
                0x0000000000006238       0x50 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_driver.c.obj)
                0x0000000000006238                write_invalid_key
 .text.ProcessAesDrv
                0x0000000000006288      0x338 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_driver.c.obj)
                0x0000000000006288                ProcessAesDrv
 .text.FinishAesDrv
                0x00000000000065c0      0x200 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_driver.c.obj)
                0x00000000000065c0                FinishAesDrv
 .text.kmu_validate_slot_and_size
                0x00000000000067c0       0x2c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj)
                0x00000000000067c0                kmu_validate_slot_and_size
 .text.kmu_validate_slot_and_size_no_kdr
                0x00000000000067ec       0x2c C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj)
                0x00000000000067ec                kmu_validate_slot_and_size_no_kdr
 .text.kmu_load_key_aes
                0x0000000000006818      0x200 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj)
                0x0000000000006818                kmu_load_key_aes
 .text.kmu_derive_cmac
                0x0000000000006a18      0x120 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj)
                0x0000000000006a18                kmu_derive_cmac
 .text.UtilCmacBuildDataForDerivation
                0x0000000000006b38       0xc0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_util_cmac.c.obj)
                0x0000000000006b38                UtilCmacBuildDataForDerivation
 .text.CC_PalDataBufferAttrGet
                0x0000000000006bf8        0x8 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_buff_attr.c.obj)
                0x0000000000006bf8                CC_PalDataBufferAttrGet
 .text          0x0000000000006c00       0xc8 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
                0x0000000000006c00                cmse_check_address_range
 .text.exit     0x0000000000006cc8       0x28 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-exit.o)
                0x0000000000006cc8                exit
 .text.__libc_init_array
                0x0000000000006cf0       0x48 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-init.o)
                0x0000000000006cf0                __libc_init_array
 .text._exit    0x0000000000006d38        0x4 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libnosys.a(_exit.o)
                0x0000000000006d38                _exit
 *(.init)
 .init          0x0000000000006d3c        0x4 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crti.o
                0x0000000000006d3c                _init
 .init          0x0000000000006d40        0x8 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtn.o
 *(.fini)
 .fini          0x0000000000006d48        0x4 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crti.o
                0x0000000000006d48                _fini
 .fini          0x0000000000006d4c        0x8 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtn.o
 *crtbegin.o(.ctors)
 *crtbegin?.o(.ctors)
 *(EXCLUDE_FILE(*crtend.o *crtend?.o) .ctors)
 *(SORT_BY_NAME(.ctors.*))
 *(.ctors)
 *crtbegin.o(.dtors)
 *crtbegin?.o(.dtors)
 *(EXCLUDE_FILE(*crtend.o *crtend?.o) .dtors)
 *(SORT_BY_NAME(.dtors.*))
 *(.dtors)
 *(.rodata*)
 .rodata.str1.1
                0x0000000000006d54        0xb secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o
 .rodata.str1.1
                0x0000000000006d5f       0x15 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o
 .rodata.psa_api_cross
                0x0000000000006d74       0x2c secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o)
                0x0000000000006d74                psa_api_cross
 .rodata.CSWTCH.10
                0x0000000000006da0       0x20 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
 .rodata.RndStartupTest.constprop.0.str1.4
                0x0000000000006dc0       0x6f C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 *fill*         0x0000000000006e2f        0x1 
 .rodata.CC_PalPowerSaveModeInit.str1.4
                0x0000000000006e30       0x20 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_pm.c.obj)
 .rodata.mutex_free.str1.4
                0x0000000000006e50       0x26 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 *fill*         0x0000000000006e76        0x2 
 .rodata.mutex_init.str1.4
                0x0000000000006e78       0x23 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 *fill*         0x0000000000006e9b        0x1 
 .rodata.mbedtls_hardware_poll.str1.4
                0x0000000000006e9c       0x16 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(trng_api.c.obj)
                                         0x6e (size before relaxing)
 *fill*         0x0000000000006eb2        0x2 
 .rodata.cc_mbedtls_aes_init.str1.4
                0x0000000000006eb4       0x13 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
 *fill*         0x0000000000006ec7        0x1 
 .rodata.cc_mbedtls_sha256_init.str1.4
                0x0000000000006ec8        0xe C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256_alt.c.obj)
 .rodata.ProcessHashDrv.str1.4
                0x0000000000006ed6       0x6f C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hash_driver.c.obj)
 *fill*         0x0000000000006ed6        0x2 
 .rodata.HASH_LARVAL_SHA256
                0x0000000000006ed8       0x20 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hash_driver.c.obj)
 .rodata.HASH_LARVAL_SHA224
                0x0000000000006ef8       0x20 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hash_driver.c.obj)
 .rodata.HASH_LARVAL_SHA1
                0x0000000000006f18       0x14 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hash_driver.c.obj)
 .rodata.ProcessAesDrv.str1.4
                0x0000000000006f2c       0x6f C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_driver.c.obj)
 .rodata._global_impure_ptr
                0x0000000000006f2c        0x4 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-impure.o)
                0x0000000000006f2c                _global_impure_ptr
 *(.eh_frame*)
 .eh_frame      0x0000000000006f30        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtbegin.o
 .eh_frame      0x0000000000006f30        0x4 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtend.o
                0x0000000000006fa0                . = ALIGN (0x20)
 *fill*         0x0000000000006f34        0xc 
                0x0000000000003980                Image$$TFM_UNPRIV_CODE$$RO$$Base = ADDR (.TFM_UNPRIV_CODE)
                0x0000000000006f40                Image$$TFM_UNPRIV_CODE$$RO$$Limit = (ADDR (.TFM_UNPRIV_CODE) + SIZEOF (.TFM_UNPRIV_CODE))

.glue_7         0x0000000000006f40        0x0
 .glue_7        0x0000000000006f40        0x0 linker stubs

.glue_7t        0x0000000000006f40        0x0
 .glue_7t       0x0000000000006f40        0x0 linker stubs

.vfp11_veneer   0x0000000000006f40        0x0
 .vfp11_veneer  0x0000000000006f40        0x0 linker stubs

.v4_bx          0x0000000000006f40        0x0
 .v4_bx         0x0000000000006f40        0x0 linker stubs

.iplt           0x0000000000006f40        0x0
 .iplt          0x0000000000006f40        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtbegin.o

.psa_interface_cross_call
                0x0000000000006f40       0x8c
 .psa_interface_cross_call
                0x0000000000006f40       0x8c secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o)
                0x0000000000006f40                psa_framework_version_cross
                0x0000000000006f48                psa_version_cross
                0x0000000000006f50                tfm_psa_call_pack_cross
                0x0000000000006f58                psa_wait_cross
                0x0000000000006f60                psa_get_cross
                0x0000000000006f68                psa_read_cross
                0x0000000000006f70                psa_skip_cross
                0x0000000000006f78                psa_write_cross
                0x0000000000006f80                psa_reply_cross
                0x0000000000006f88                psa_panic_cross
                0x0000000000006f90                psa_rot_lifecycle_state_cross

.rel.dyn        0x0000000000006fcc        0x0
 .rel.iplt      0x0000000000006fcc        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtbegin.o

.gnu.sgstubs    0x0000000000007c00       0x40
 *(.gnu.sgstubs*)
 .gnu.sgstubs.__stub
                0x0000000000007c00       0x28 linker stubs
                0x0000000000007c00                tfm_psa_framework_version_veneer
                0x0000000000007c08                tfm_psa_version_veneer
                0x0000000000007c10                tfm_psa_close_veneer
                0x0000000000007c18                tfm_psa_connect_veneer
                0x0000000000007c20                tfm_psa_call_veneer

.sgstubs_end    0x0000000000008000        0x0

.TFM_DATA       0x0000000020001e20      0x128 load address 0x0000000000007c40
 *(.data*)
 .data.ret_err  0x0000000020001e20        0x4 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
                0x0000000020001e20                ret_err
 .data.SystemCoreClock
                0x0000000020001e24        0x4 platform/libplatform_s.a(system_nrf5340_application.o)
                0x0000000020001e24                SystemCoreClock
 .data.UART1_Resources
                0x0000000020001e28       0x28 platform/libplatform_s.a(Driver_USART.o)
 .data.dev_mpu_s
                0x0000000020001e50        0x4 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
                0x0000000020001e50                dev_mpu_s
 .data.tfm_peripheral_uarte1
                0x0000000020001e54        0x8 secure_fw/spm/libtfm_spm.a(target_cfg.o)
                0x0000000020001e54                tfm_peripheral_uarte1
 .data.serv_pool_sa
                0x0000000020001e5c        0x4 secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .data.part_pool_sa
                0x0000000020001e60        0x4 secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .data.ldinf_sa
                0x0000000020001e64        0x4 secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .data.mbedtls_free_func
                0x0000000020001e68        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .data.mbedtls_calloc_func
                0x0000000020001e6c        0x4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .data.invalid_aes_256_bit_key
                0x0000000020001e70        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
                0x0000000020001e70                invalid_aes_256_bit_key
 .data.pCCRndCryptoMutex
                0x0000000020001e74        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj)
                0x0000000020001e74                pCCRndCryptoMutex
 .data.CCPowerMutex
                0x0000000020001e78        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj)
                0x0000000020001e78                CCPowerMutex
 .data.CCRndCryptoMutex
                0x0000000020001e7c        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj)
                0x0000000020001e7c                CCRndCryptoMutex
 .data.CCAsymCryptoMutex
                0x0000000020001e80        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj)
                0x0000000020001e80                CCAsymCryptoMutex
 .data.CCSymCryptoMutex
                0x0000000020001e84        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj)
                0x0000000020001e84                CCSymCryptoMutex
 .data.mbedtls_mutex_unlock
                0x0000000020001e88        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
                0x0000000020001e88                mbedtls_mutex_unlock
 .data.mbedtls_mutex_lock
                0x0000000020001e8c        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
                0x0000000020001e8c                mbedtls_mutex_lock
 .data.mbedtls_mutex_init
                0x0000000020001e90        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
                0x0000000020001e90                mbedtls_mutex_init
 .data.platform_abort_apis
                0x0000000020001e94        0x8 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_abort.c.obj)
                0x0000000020001e94                platform_abort_apis
 .data.platform_mutexes
                0x0000000020001e9c       0x14 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
                0x0000000020001e9c                platform_mutexes
 .data.platform_mutex_apis
                0x0000000020001eb0       0x10 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
                0x0000000020001eb0                platform_mutex_apis
 .data.power_mutex
                0x0000000020001ec0        0x8 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .data.rng_mutex
                0x0000000020001ec8        0x8 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .data.asym_mutex
                0x0000000020001ed0        0x8 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .data.sym_mutex
                0x0000000020001ed8        0x8 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .data.impure_data
                0x0000000020001ee0       0x60 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-impure.o)
                0x0000000020001f40                . = ALIGN (0x4)
                0x0000000020001f40                PROVIDE (__preinit_array_start = .)
 *(.preinit_array)
                0x0000000020001f40                PROVIDE (__preinit_array_end = .)
                0x0000000020001f40                . = ALIGN (0x4)
                0x0000000020001f40                PROVIDE (__init_array_start = .)
 *(SORT_BY_NAME(.init_array.*))
 *(.init_array)
 .init_array    0x0000000020001f40        0x4 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtbegin.o
                0x0000000020001f44                PROVIDE (__init_array_end = .)
                0x0000000020001f44                . = ALIGN (0x4)
                [!provide]                        PROVIDE (__fini_array_start = .)
 *(SORT_BY_NAME(.fini_array.*))
 *(.fini_array)
 .fini_array    0x0000000020001f44        0x4 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtbegin.o
                [!provide]                        PROVIDE (__fini_array_end = .)
 *(.jcr*)
                0x0000000020001f48                . = ALIGN (0x4)
                0x0000000020001e20                Image$$ER_TFM_DATA$$RW$$Base = ADDR (.TFM_DATA)
                0x0000000020001f48                Image$$ER_TFM_DATA$$RW$$Limit = (ADDR (.TFM_DATA) + SIZEOF (.TFM_DATA))

.tm_clone_table
                0x0000000020001f48        0x0 load address 0x0000000000007d68
 .tm_clone_table
                0x0000000020001f48        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtbegin.o
 .tm_clone_table
                0x0000000020001f48        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtend.o

.igot.plt       0x0000000020001f48        0x0 load address 0x0000000000007d68
 .igot.plt      0x0000000020001f48        0x0 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtbegin.o

.TFM_BSS        0x0000000020001f60     0x1b1c
                0x0000000020001f60                __bss_start__ = .
                0x0000000020001f60                __partition_runtime_start__ = .
 *(.bss.part_runtime_priority_lowest)
 .bss.part_runtime_priority_lowest
                0x0000000020001f60       0x4c secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/load_info_ns_agent_tz.o
 *(.bss.part_runtime_priority_low)
 *(.bss.part_runtime_priority_normal)
 .bss.part_runtime_priority_normal
                0x0000000020001fac       0x4c secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o
 .bss.part_runtime_priority_normal
                0x0000000020001ff8       0x4c secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o
 *(.bss.part_runtime_priority_high)
                0x0000000020002044                __partition_runtime_end__ = .
                0x0000000020002044                . = ALIGN (0x4)
                0x0000000020002044                __service_runtime_start__ = .
 *(.bss.serv_runtime_priority_lowest)
 *(.bss.serv_runtime_priority_low)
 *(.bss.serv_runtime_priority_normal)
 .bss.serv_runtime_priority_normal
                0x0000000020002044        0xc secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o
 .bss.serv_runtime_priority_normal
                0x0000000020002050        0xc secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o
 *(.bss.serv_runtime_priority_high)
                0x000000002000205c                __service_runtime_end__ = .
 *(.bss*)
 .bss           0x000000002000205c       0x1c c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtbegin.o
 *fill*         0x0000000020002078        0x8 
 .bss.ns_agent_tz_stack
                0x0000000020002080      0x400 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/load_info_ns_agent_tz.o
                0x0000000020002080                ns_agent_tz_stack
 .bss.exception_info
                0x0000000020002480       0x58 platform/libplatform_s.a(exception_info.o)
 .bss.m_cb      0x00000000200024d8       0x50 platform/libplatform_s.a(nrfx_uarte.o)
 .bss.in_critical_section
                0x0000000020002528        0x4 platform/libplatform_s.a(nrfx_glue.o)
 .bss.spm_boundary
                0x000000002000252c        0x4 secure_fw/spm/libtfm_spm.a(main.o)
                0x000000002000252c                spm_boundary
 .bss.scheduler_lock
                0x0000000020002530        0x4 secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
                0x0000000020002530                scheduler_lock
 .bss.n_configured_regions
                0x0000000020002534        0x4 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
 .bss.is_boot_data_valid
                0x0000000020002538        0x4 secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
 .bss.loop_index
                0x000000002000253c        0x4 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
 .bss.connection_pool_pool_buf
                0x0000000020002540      0x42c secure_fw/spm/libtfm_spm.a(spm_ipc.o)
 .bss.stateless_services_ref_tbl
                0x000000002000296c       0x80 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                0x000000002000296c                stateless_services_ref_tbl
 .bss.services_listhead
                0x00000000200029ec        0x8 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
 .bss.partition_meta_indicator_pos
                0x00000000200029f4        0x4 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
                0x00000000200029f4                partition_meta_indicator_pos
 .bss.p_spm_thread_context
                0x00000000200029f8        0x4 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
                0x00000000200029f8                p_spm_thread_context
 .bss.partition_listhead
                0x00000000200029fc        0x8 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
                0x00000000200029fc                partition_listhead
 .bss.query_state_cb
                0x0000000020002a04        0x4 secure_fw/spm/libtfm_spm.a(thread.o)
 .bss.p_rnbl_head
                0x0000000020002a08        0x4 secure_fw/spm/libtfm_spm.a(thread.o)
 .bss.p_thrd_head
                0x0000000020002a0c        0x4 secure_fw/spm/libtfm_spm.a(thread.o)
 .bss.p_curr_thrd
                0x0000000020002a10        0x4 secure_fw/spm/libtfm_spm.a(thread.o)
                0x0000000020002a10                p_curr_thrd
 .bss.heap      0x0000000020002a14       0x14 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .bss.global_data
                0x0000000020002a28        0x8 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .bss.global_data
                0x0000000020002a30      0x584 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .bss.nrf_cc3xx_platform_rng_initialized
                0x0000000020002fb4        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
 .bss.nrf_cc3xx_platform_initialized
                0x0000000020002fb8        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
 .bss.nrf_cc3xx_platform_ctr_drbg_global_ctx
                0x0000000020002fbc      0x1c0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
                0x0000000020002fbc                nrf_cc3xx_platform_ctr_drbg_global_ctx
 .bss.buf.0     0x000000002000317c      0x1a0 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .bss.seed.1    0x000000002000331c      0x180 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .bss.rndWorkbuff.0
                0x000000002000349c      0x220 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .bss.random_seed_filled
                0x00000000200036bc        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .bss.random_seed_buffer
                0x00000000200036c0       0x68 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .bss.use_count
                0x0000000020003728        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_pm.c.obj)
 .bss.power_mutex_int
                0x000000002000372c        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss.rng_mutex_int
                0x0000000020003730        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss.asym_mutex_int
                0x0000000020003734        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss.sym_mutex_int
                0x0000000020003738        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss.rndState.0
                0x000000002000373c        0x4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(trng_api.c.obj)
 .bss.trngParams.1
                0x0000000020003740       0x28 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(trng_api.c.obj)
 .bss.rndWorkBuffer.2
                0x0000000020003768      0x220 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(trng_api.c.obj)
 .bss.ctx.0     0x0000000020003988       0xf4 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256.c.obj)
 *(COMMON)
                0x0000000020003a7c                . = ALIGN (0x4)
                0x0000000020003a7c                __bss_end__ = .
                0x0000000020001f60                Image$$ER_TFM_DATA$$ZI$$Base = ADDR (.TFM_BSS)
                0x0000000020003a7c                Image$$ER_TFM_DATA$$ZI$$Limit = (ADDR (.TFM_BSS) + SIZEOF (.TFM_BSS))
                0x0000000020001f60                Image$$ER_PART_RT_POOL$$ZI$$Base = __partition_runtime_start__
                0x0000000020002044                Image$$ER_PART_RT_POOL$$ZI$$Limit = __partition_runtime_end__
                0x0000000020002044                Image$$ER_SERV_RT_POOL$$ZI$$Base = __service_runtime_start__
                0x000000002000205c                Image$$ER_SERV_RT_POOL$$ZI$$Limit = __service_runtime_end__
                0x0000000020001e20                Image$$ER_TFM_DATA$$Base = ADDR (.TFM_DATA)
                0x0000000020003a64                Image$$ER_TFM_DATA$$Limit = ((ADDR (.TFM_DATA) + SIZEOF (.TFM_DATA)) + SIZEOF (.TFM_BSS))
                0x0000000000007c00                Image$$ER_VENEER$$Base = ADDR (.gnu.sgstubs)
                0x0000000000008000                Image$$VENEER_ALIGN$$Limit = ADDR (.sgstubs_end)
                0x0000000000000001                ASSERT (((Image$$VENEER_ALIGN$$Limit - Image$$ER_VENEER$$Base) <= 0x400), Veneer region overflowed)
                0x0000000000008000                Load$$LR$$LR_NS_PARTITION$$Base = 0x8000
                0x0000000020000bf8                PROVIDE (__stack = Image$$ARM_LIB_STACK$$ZI$$Limit)
OUTPUT(bin\tfm_s.axf elf32-littlearm)
LOAD linker stubs

.ARM.attributes
                0x0000000000000000       0x30
 .ARM.attributes
                0x0000000000000000       0x22 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crti.o
 .ARM.attributes
                0x0000000000000022       0x32 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtbegin.o
 .ARM.attributes
                0x0000000000000054       0x20 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp/crt0.o
 .ARM.attributes
                0x0000000000000074       0x32 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o
 .ARM.attributes
                0x00000000000000a6       0x32 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o
 .ARM.attributes
                0x00000000000000d8       0x32 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
 .ARM.attributes
                0x000000000000010a       0x32 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o
 .ARM.attributes
                0x000000000000013c       0x32 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o
 .ARM.attributes
                0x000000000000016e       0x32 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/load_info_ns_agent_tz.o
 .ARM.attributes
                0x00000000000001a0       0x32 platform/libplatform_s.a(system_nrf5340_application.o)
 .ARM.attributes
                0x00000000000001d2       0x32 platform/libplatform_s.a(exception_info.o)
 .ARM.attributes
                0x0000000000000204       0x32 platform/libplatform_s.a(tfm_hal_spm_logdev_peripheral.o)
 .ARM.attributes
                0x0000000000000236       0x32 platform/libplatform_s.a(uart_stdout.o)
 .ARM.attributes
                0x0000000000000268       0x32 platform/libplatform_s.a(Driver_USART.o)
 .ARM.attributes
                0x000000000000029a       0x32 platform/libplatform_s.a(nrfx_uarte.o)
 .ARM.attributes
                0x00000000000002cc       0x32 platform/libplatform_s.a(nrfx_glue.o)
 .ARM.attributes
                0x00000000000002fe       0x32 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memmove.o)
 .ARM.attributes
                0x0000000000000330       0x32 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memcpy.o)
 .ARM.attributes
                0x0000000000000362       0x32 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memset.o)
 .ARM.attributes
                0x0000000000000394       0x32 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
 .ARM.attributes
                0x00000000000003c6       0x32 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sprt_partition_metadata_indicator.o)
 .ARM.attributes
                0x00000000000003f8       0x32 secure_fw/spm/libtfm_spm.a(utilities.o)
 .ARM.attributes
                0x000000000000042a       0x32 secure_fw/spm/libtfm_spm.a(main.o)
 .ARM.attributes
                0x000000000000045c       0x32 secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
 .ARM.attributes
                0x000000000000048e       0x32 secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
 .ARM.attributes
                0x00000000000004c0       0x32 secure_fw/spm/libtfm_spm.a(ns_agent_tz_v80m.o)
 .ARM.attributes
                0x00000000000004f2       0x32 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
 .ARM.attributes
                0x0000000000000524       0x32 secure_fw/spm/libtfm_spm.a(faults.o)
 .ARM.attributes
                0x0000000000000556       0x32 secure_fw/spm/libtfm_spm.a(target_cfg.o)
 .ARM.attributes
                0x0000000000000588       0x32 secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
 .ARM.attributes
                0x00000000000005ba       0x32 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
 .ARM.attributes
                0x00000000000005ec       0x32 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
 .ARM.attributes
                0x000000000000061e       0x32 secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
 .ARM.attributes
                0x0000000000000650       0x32 secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .ARM.attributes
                0x0000000000000682       0x32 secure_fw/spm/libtfm_spm.a(psa_api.o)
 .ARM.attributes
                0x00000000000006b4       0x32 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
 .ARM.attributes
                0x00000000000006e6       0x32 secure_fw/spm/libtfm_spm.a(tfm_pools.o)
 .ARM.attributes
                0x0000000000000718       0x32 secure_fw/spm/libtfm_spm.a(thread.o)
 .ARM.attributes
                0x000000000000074a       0x32 secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o)
 .ARM.attributes
                0x000000000000077c       0x32 secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
 .ARM.attributes
                0x00000000000007ae       0x32 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
 .ARM.attributes
                0x00000000000007e0       0x32 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
 .ARM.attributes
                0x0000000000000812       0x32 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_cipher.o)
 .ARM.attributes
                0x0000000000000844       0x32 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_hash.o)
 .ARM.attributes
                0x0000000000000876       0x32 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_mac.o)
 .ARM.attributes
                0x00000000000008a8       0x32 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_aead.o)
 .ARM.attributes
                0x00000000000008da       0x32 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_asymmetric.o)
 .ARM.attributes
                0x000000000000090c       0x32 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_derivation.o)
 .ARM.attributes
                0x000000000000093e       0x32 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_management.o)
 .ARM.attributes
                0x0000000000000970       0x32 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_rng.o)
 .ARM.attributes
                0x00000000000009a2       0x32 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 .ARM.attributes
                0x00000000000009d4       0x32 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(intermedia_tfm_crypto.o)
 .ARM.attributes
                0x0000000000000a06       0x32 platform/libplatform_crypto_keys.a(crypto_keys.o)
 .ARM.attributes
                0x0000000000000a38       0x32 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .ARM.attributes
                0x0000000000000a6a       0x32 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .ARM.attributes
                0x0000000000000a9c       0x32 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .ARM.attributes
                0x0000000000000ace       0x32 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .ARM.attributes
                0x0000000000000b00       0x32 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .ARM.attributes
                0x0000000000000b32       0x32 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
 .ARM.attributes
                0x0000000000000b64       0x32 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(intermedia_tfm_platform.o)
 .ARM.attributes
                0x0000000000000b96       0x32 platform/libplatform_s.a(mpu_armv8m_drv.o)
 .ARM.attributes
                0x0000000000000bc8       0x32 platform/libplatform_s.a(spu.o)
 .ARM.attributes
                0x0000000000000bfa       0x32 platform/libplatform_s.a(tfm_hal_platform.o)
 .ARM.attributes
                0x0000000000000c2c       0x32 platform/libplatform_s.a(dummy_otp.o)
 .ARM.attributes
                0x0000000000000c5e       0x32 platform/libplatform_s.a(dummy_provisioning.o)
 .ARM.attributes
                0x0000000000000c90       0x32 platform/libplatform_s.a(tfm_platform_system.o)
 .ARM.attributes
                0x0000000000000cc2       0x32 platform/libplatform_s.a(tfm_hal_reset_halt.o)
 .ARM.attributes
                0x0000000000000cf4       0x32 platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
 .ARM.attributes
                0x0000000000000d26       0x32 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sfn_common_thread.o)
 .ARM.attributes
                0x0000000000000d58       0x32 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o)
 .ARM.attributes
                0x0000000000000d8a       0x32 secure_fw/spm/libtfm_spm.a(psa_call_api.o)
 .ARM.attributes
                0x0000000000000dbc       0x32 secure_fw/spm/libtfm_spm.a(psa_version_api.o)
 .ARM.attributes
                0x0000000000000dee       0x32 secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
 .ARM.attributes
                0x0000000000000e20       0x32 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)
 .ARM.attributes
                0x0000000000000e52       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
 .ARM.attributes
                0x0000000000000e86       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .ARM.attributes
                0x0000000000000eba       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_common.c.obj)
 .ARM.attributes
                0x0000000000000eee       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .ARM.attributes
                0x0000000000000f22       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj)
 .ARM.attributes
                0x0000000000000f56       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .ARM.attributes
                0x0000000000000f8a       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_hal.c.obj)
 .ARM.attributes
                0x0000000000000fbe       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj)
 .ARM.attributes
                0x0000000000000ff2       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_dma.c.obj)
 .ARM.attributes
                0x0000000000001026       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_interrupt_ctrl.c.obj)
 .ARM.attributes
                0x000000000000105a       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mem.c.obj)
 .ARM.attributes
                0x000000000000108e       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mutex.c.obj)
 .ARM.attributes
                0x00000000000010c2       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_pm.c.obj)
 .ARM.attributes
                0x00000000000010f6       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
 .ARM.attributes
                0x000000000000112a       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_abort.c.obj)
 .ARM.attributes
                0x000000000000115e       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .ARM.attributes
                0x0000000000001192       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd_trng90b.c.obj)
 .ARM.attributes
                0x00000000000011c6       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(trng_api.c.obj)
 .ARM.attributes
                0x00000000000011fa       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
 .ARM.attributes
                0x000000000000122e       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256_alt.c.obj)
 .ARM.attributes
                0x0000000000001262       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_hash_common.c.obj)
 .ARM.attributes
                0x0000000000001296       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256.c.obj)
 .ARM.attributes
                0x00000000000012ca       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_rng_plat.c.obj)
 .ARM.attributes
                0x00000000000012fe       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_trng.c.obj)
 .ARM.attributes
                0x0000000000001332       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd.c.obj)
 .ARM.attributes
                0x0000000000001366       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(driver_common.c.obj)
 .ARM.attributes
                0x000000000000139a       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hash_driver.c.obj)
 .ARM.attributes
                0x00000000000013ce       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_driver.c.obj)
 .ARM.attributes
                0x0000000000001402       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj)
 .ARM.attributes
                0x0000000000001436       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_util_cmac.c.obj)
 .ARM.attributes
                0x000000000000146a       0x34 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_buff_attr.c.obj)
 .ARM.attributes
                0x000000000000149e       0x32 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
 .ARM.attributes
                0x00000000000014d0       0x32 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-exit.o)
 .ARM.attributes
                0x0000000000001502       0x32 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-impure.o)
 .ARM.attributes
                0x0000000000001534       0x32 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-init.o)
 .ARM.attributes
                0x0000000000001566       0x32 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtend.o
 .ARM.attributes
                0x0000000000001598       0x22 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/crtn.o
 .ARM.attributes
                0x00000000000015ba       0x32 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libnosys.a(_exit.o)

.comment        0x0000000000000000       0x40
 .comment       0x0000000000000000       0x20 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o
                                         0x21 (size before relaxing)
 .comment       0x0000000000000020       0x21 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o
 .comment       0x0000000000000020       0x21 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
 .comment       0x0000000000000020       0x21 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o
 .comment       0x0000000000000020       0x21 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o
 .comment       0x0000000000000020       0x21 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/load_info_ns_agent_tz.o
 .comment       0x0000000000000020       0x21 platform/libplatform_s.a(system_nrf5340_application.o)
 .comment       0x0000000000000020       0x21 platform/libplatform_s.a(exception_info.o)
 .comment       0x0000000000000020       0x21 platform/libplatform_s.a(tfm_hal_spm_logdev_peripheral.o)
 .comment       0x0000000000000020       0x21 platform/libplatform_s.a(uart_stdout.o)
 .comment       0x0000000000000020       0x21 platform/libplatform_s.a(Driver_USART.o)
 .comment       0x0000000000000020       0x21 platform/libplatform_s.a(nrfx_uarte.o)
 .comment       0x0000000000000020       0x21 platform/libplatform_s.a(nrfx_glue.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memmove.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memcpy.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memset.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sprt_partition_metadata_indicator.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(utilities.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(main.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(ns_agent_tz_v80m.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(faults.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(target_cfg.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(psa_api.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(tfm_pools.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(thread.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_cipher.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_hash.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_mac.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_aead.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_asymmetric.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_derivation.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_management.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_rng.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(intermedia_tfm_crypto.o)
 .comment       0x0000000000000020       0x21 platform/libplatform_crypto_keys.a(crypto_keys.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(intermedia_tfm_platform.o)
 .comment       0x0000000000000020       0x21 platform/libplatform_s.a(mpu_armv8m_drv.o)
 .comment       0x0000000000000020       0x21 platform/libplatform_s.a(spu.o)
 .comment       0x0000000000000020       0x21 platform/libplatform_s.a(tfm_hal_platform.o)
 .comment       0x0000000000000020       0x21 platform/libplatform_s.a(dummy_otp.o)
 .comment       0x0000000000000020       0x21 platform/libplatform_s.a(dummy_provisioning.o)
 .comment       0x0000000000000020       0x21 platform/libplatform_s.a(tfm_platform_system.o)
 .comment       0x0000000000000020       0x21 platform/libplatform_s.a(tfm_hal_reset_halt.o)
 .comment       0x0000000000000020       0x21 platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sfn_common_thread.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(psa_call_api.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(psa_version_api.o)
 .comment       0x0000000000000020       0x21 secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
 .comment       0x0000000000000020       0x21 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)
 .comment       0x0000000000000020       0x20 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform.c.obj)
                                         0x21 (size before relaxing)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_common.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(ctr_drbg.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(entropy.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_lib.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_hal.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_dma.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_interrupt_ctrl.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mem.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_mutex.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_pm.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(threading_alt.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_abort.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(nrf_cc3xx_platform_mutex.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd_trng90b.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(trng_api.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_alt.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256_alt.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(mbedtls_hash_common.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(sha256.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_rng_plat.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_trng.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(llf_rnd.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(driver_common.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(hash_driver.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(aes_driver.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(kmu_shared.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_util_cmac.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v2.5.99-dev1/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.18.a(cc_pal_buff_attr.c.obj)

.debug_info     0x0000000000000000    0x364dd
 .debug_info    0x0000000000000000      0x26f secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
 .debug_info    0x000000000000026f      0x77d secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o
 .debug_info    0x00000000000009ec      0x135 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o
 .debug_info    0x0000000000000b21      0x117 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
 .debug_info    0x0000000000000c38      0x63e secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o
 .debug_info    0x0000000000001276      0x621 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o
 .debug_info    0x0000000000001897      0x5b5 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/load_info_ns_agent_tz.o
 .debug_info    0x0000000000001e4c     0x1ac0 platform/libplatform_s.a(system_nrf5340_application.o)
 .debug_info    0x000000000000390c      0x92b platform/libplatform_s.a(exception_info.o)
 .debug_info    0x0000000000004237      0x12f platform/libplatform_s.a(tfm_hal_spm_logdev_peripheral.o)
 .debug_info    0x0000000000004366      0x7df platform/libplatform_s.a(uart_stdout.o)
 .debug_info    0x0000000000004b45     0x1cf9 platform/libplatform_s.a(Driver_USART.o)
 .debug_info    0x000000000000683e     0x96ca platform/libplatform_s.a(nrfx_uarte.o)
 .debug_info    0x000000000000ff08      0x110 platform/libplatform_s.a(nrfx_glue.o)
 .debug_info    0x0000000000010018      0x251 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memmove.o)
 .debug_info    0x0000000000010269      0x17e secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memcpy.o)
 .debug_info    0x00000000000103e7      0x173 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memset.o)
 .debug_info    0x000000000001055a      0x780 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
 .debug_info    0x0000000000010cda       0x8e secure_fw/partitions/lib/runtime/libtfm_sprt.a(sprt_partition_metadata_indicator.o)
 .debug_info    0x0000000000010d68       0x97 secure_fw/spm/libtfm_spm.a(utilities.o)
 .debug_info    0x0000000000010dff      0x41b secure_fw/spm/libtfm_spm.a(main.o)
 .debug_info    0x000000000001121a      0x2ef secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
 .debug_info    0x0000000000011509      0x9d0 secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
 .debug_info    0x0000000000011ed9       0xac secure_fw/spm/libtfm_spm.a(ns_agent_tz_v80m.o)
 .debug_info    0x0000000000011f85     0x1041 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
 .debug_info    0x0000000000012fc6      0x92b secure_fw/spm/libtfm_spm.a(faults.o)
 .debug_info    0x00000000000138f1     0x6736 secure_fw/spm/libtfm_spm.a(target_cfg.o)
 .debug_info    0x000000000001a027      0x806 secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
 .debug_info    0x000000000001a82d      0x51f secure_fw/spm/libtfm_spm.a(tfm_arch.o)
 .debug_info    0x000000000001ad4c      0xffa secure_fw/spm/libtfm_spm.a(spm_ipc.o)
 .debug_info    0x000000000001bd46      0x8b0 secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
 .debug_info    0x000000000001c5f6      0x8bf secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .debug_info    0x000000000001ceb5      0xa2a secure_fw/spm/libtfm_spm.a(psa_api.o)
 .debug_info    0x000000000001d8df     0x10d3 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
 .debug_info    0x000000000001e9b2      0x32e secure_fw/spm/libtfm_spm.a(tfm_pools.o)
 .debug_info    0x000000000001ece0      0x450 secure_fw/spm/libtfm_spm.a(thread.o)
 .debug_info    0x000000000001f130      0x4b1 secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o)
 .debug_info    0x000000000001f5e1      0x299 secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
 .debug_info    0x000000000001f87a      0xf44 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
 .debug_info    0x00000000000207be      0xa24 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
 .debug_info    0x00000000000211e2      0x1b7 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_cipher.o)
 .debug_info    0x0000000000021399      0x156 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_hash.o)
 .debug_info    0x00000000000214ef      0x1b7 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_mac.o)
 .debug_info    0x00000000000216a6      0x1b7 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_aead.o)
 .debug_info    0x000000000002185d      0x202 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_asymmetric.o)
 .debug_info    0x0000000000021a5f      0x1be secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_derivation.o)
 .debug_info    0x0000000000021c1d      0x1b7 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_management.o)
 .debug_info    0x0000000000021dd4      0x1c7 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_rng.o)
 .debug_info    0x0000000000021f9b      0x7c0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 .debug_info    0x000000000002275b       0xa0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(intermedia_tfm_crypto.o)
 .debug_info    0x00000000000227fb      0x324 platform/libplatform_crypto_keys.a(crypto_keys.o)
 .debug_info    0x0000000000022b1f      0x5ff secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .debug_info    0x000000000002311e      0x2fa secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .debug_info    0x0000000000023418     0x8200 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .debug_info    0x000000000002b618     0x10cc secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .debug_info    0x000000000002c6e4     0x2b73 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .debug_info    0x000000000002f257      0x579 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
 .debug_info    0x000000000002f7d0       0xa0 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(intermedia_tfm_platform.o)
 .debug_info    0x000000000002f870      0x5ad platform/libplatform_s.a(mpu_armv8m_drv.o)
 .debug_info    0x000000000002fe1d     0x105b platform/libplatform_s.a(spu.o)
 .debug_info    0x0000000000030e78      0x533 platform/libplatform_s.a(tfm_hal_platform.o)
 .debug_info    0x00000000000313ab      0x1ed platform/libplatform_s.a(dummy_otp.o)
 .debug_info    0x0000000000031598       0xef platform/libplatform_s.a(dummy_provisioning.o)
 .debug_info    0x0000000000031687      0x632 platform/libplatform_s.a(tfm_platform_system.o)
 .debug_info    0x0000000000031cb9      0x49f platform/libplatform_s.a(tfm_hal_reset_halt.o)
 .debug_info    0x0000000000032158      0x83d platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
 .debug_info    0x0000000000032995      0x5c6 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sfn_common_thread.o)
 .debug_info    0x0000000000032f5b      0x603 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o)
 .debug_info    0x000000000003355e      0xb06 secure_fw/spm/libtfm_spm.a(psa_call_api.o)
 .debug_info    0x0000000000034064      0x62e secure_fw/spm/libtfm_spm.a(psa_version_api.o)
 .debug_info    0x0000000000034692      0x885 secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
 .debug_info    0x0000000000034f17     0x15c6 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)

.debug_abbrev   0x0000000000000000     0x9243
 .debug_abbrev  0x0000000000000000      0x153 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
 .debug_abbrev  0x0000000000000153      0x271 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o
 .debug_abbrev  0x00000000000003c4       0xd1 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o
 .debug_abbrev  0x0000000000000495       0x99 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
 .debug_abbrev  0x000000000000052e      0x140 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o
 .debug_abbrev  0x000000000000066e      0x140 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o
 .debug_abbrev  0x00000000000007ae      0x11b secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/load_info_ns_agent_tz.o
 .debug_abbrev  0x00000000000008c9      0x28d platform/libplatform_s.a(system_nrf5340_application.o)
 .debug_abbrev  0x0000000000000b56      0x299 platform/libplatform_s.a(exception_info.o)
 .debug_abbrev  0x0000000000000def       0xb3 platform/libplatform_s.a(tfm_hal_spm_logdev_peripheral.o)
 .debug_abbrev  0x0000000000000ea2      0x27d platform/libplatform_s.a(uart_stdout.o)
 .debug_abbrev  0x000000000000111f      0x552 platform/libplatform_s.a(Driver_USART.o)
 .debug_abbrev  0x0000000000001671      0x640 platform/libplatform_s.a(nrfx_uarte.o)
 .debug_abbrev  0x0000000000001cb1       0x8d platform/libplatform_s.a(nrfx_glue.o)
 .debug_abbrev  0x0000000000001d3e      0x168 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memmove.o)
 .debug_abbrev  0x0000000000001ea6       0xc5 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memcpy.o)
 .debug_abbrev  0x0000000000001f6b       0xc0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memset.o)
 .debug_abbrev  0x000000000000202b      0x189 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
 .debug_abbrev  0x00000000000021b4       0x48 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sprt_partition_metadata_indicator.o)
 .debug_abbrev  0x00000000000021fc       0x65 secure_fw/spm/libtfm_spm.a(utilities.o)
 .debug_abbrev  0x0000000000002261      0x210 secure_fw/spm/libtfm_spm.a(main.o)
 .debug_abbrev  0x0000000000002471      0x1db secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
 .debug_abbrev  0x000000000000264c      0x1fb secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
 .debug_abbrev  0x0000000000002847       0x64 secure_fw/spm/libtfm_spm.a(ns_agent_tz_v80m.o)
 .debug_abbrev  0x00000000000028ab      0x4f3 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
 .debug_abbrev  0x0000000000002d9e      0x1f2 secure_fw/spm/libtfm_spm.a(faults.o)
 .debug_abbrev  0x0000000000002f90      0x403 secure_fw/spm/libtfm_spm.a(target_cfg.o)
 .debug_abbrev  0x0000000000003393      0x267 secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
 .debug_abbrev  0x00000000000035fa      0x275 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
 .debug_abbrev  0x000000000000386f      0x3c7 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
 .debug_abbrev  0x0000000000003c36      0x2f5 secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
 .debug_abbrev  0x0000000000003f2b      0x1f4 secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .debug_abbrev  0x000000000000411f      0x308 secure_fw/spm/libtfm_spm.a(psa_api.o)
 .debug_abbrev  0x0000000000004427      0x42e secure_fw/spm/libtfm_spm.a(backend_ipc.o)
 .debug_abbrev  0x0000000000004855      0x17f secure_fw/spm/libtfm_spm.a(tfm_pools.o)
 .debug_abbrev  0x00000000000049d4      0x269 secure_fw/spm/libtfm_spm.a(thread.o)
 .debug_abbrev  0x0000000000004c3d      0x146 secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o)
 .debug_abbrev  0x0000000000004d83      0x156 secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
 .debug_abbrev  0x0000000000004ed9      0x37b secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
 .debug_abbrev  0x0000000000005254      0x202 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
 .debug_abbrev  0x0000000000005456       0xd3 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_cipher.o)
 .debug_abbrev  0x0000000000005529       0xc4 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_hash.o)
 .debug_abbrev  0x00000000000055ed       0xd3 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_mac.o)
 .debug_abbrev  0x00000000000056c0       0xd3 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_aead.o)
 .debug_abbrev  0x0000000000005793       0xf0 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_asymmetric.o)
 .debug_abbrev  0x0000000000005883       0xd3 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_derivation.o)
 .debug_abbrev  0x0000000000005956       0xd3 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_management.o)
 .debug_abbrev  0x0000000000005a29       0xf3 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_rng.o)
 .debug_abbrev  0x0000000000005b1c      0x293 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 .debug_abbrev  0x0000000000005daf       0x5d secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(intermedia_tfm_crypto.o)
 .debug_abbrev  0x0000000000005e0c      0x137 platform/libplatform_crypto_keys.a(crypto_keys.o)
 .debug_abbrev  0x0000000000005f43      0x2e3 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .debug_abbrev  0x0000000000006226      0x216 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .debug_abbrev  0x000000000000643c      0x601 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .debug_abbrev  0x0000000000006a3d      0x4b4 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .debug_abbrev  0x0000000000006ef1      0x400 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .debug_abbrev  0x00000000000072f1      0x24d secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
 .debug_abbrev  0x000000000000753e       0x5d secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(intermedia_tfm_platform.o)
 .debug_abbrev  0x000000000000759b      0x1e5 platform/libplatform_s.a(mpu_armv8m_drv.o)
 .debug_abbrev  0x0000000000007780      0x2dd platform/libplatform_s.a(spu.o)
 .debug_abbrev  0x0000000000007a5d      0x1ba platform/libplatform_s.a(tfm_hal_platform.o)
 .debug_abbrev  0x0000000000007c17      0x147 platform/libplatform_s.a(dummy_otp.o)
 .debug_abbrev  0x0000000000007d5e       0x90 platform/libplatform_s.a(dummy_provisioning.o)
 .debug_abbrev  0x0000000000007dee      0x1e4 platform/libplatform_s.a(tfm_platform_system.o)
 .debug_abbrev  0x0000000000007fd2      0x12d platform/libplatform_s.a(tfm_hal_reset_halt.o)
 .debug_abbrev  0x00000000000080ff      0x334 platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
 .debug_abbrev  0x0000000000008433      0x1ca secure_fw/partitions/lib/runtime/libtfm_sprt.a(sfn_common_thread.o)
 .debug_abbrev  0x00000000000085fd      0x1ac secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o)
 .debug_abbrev  0x00000000000087a9      0x29e secure_fw/spm/libtfm_spm.a(psa_call_api.o)
 .debug_abbrev  0x0000000000008a47      0x190 secure_fw/spm/libtfm_spm.a(psa_version_api.o)
 .debug_abbrev  0x0000000000008bd7      0x1fe secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
 .debug_abbrev  0x0000000000008dd5      0x46e secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)

.debug_loc      0x0000000000000000    0x18c1b
 .debug_loc     0x0000000000000000       0x6f secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
 .debug_loc     0x000000000000006f       0x6e secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o
 .debug_loc     0x00000000000000dd      0x182 platform/libplatform_s.a(system_nrf5340_application.o)
 .debug_loc     0x000000000000025f      0x2af platform/libplatform_s.a(exception_info.o)
 .debug_loc     0x000000000000050e       0x4a platform/libplatform_s.a(tfm_hal_spm_logdev_peripheral.o)
 .debug_loc     0x0000000000000558       0xe8 platform/libplatform_s.a(uart_stdout.o)
 .debug_loc     0x0000000000000640      0x97a platform/libplatform_s.a(Driver_USART.o)
 .debug_loc     0x0000000000000fba     0x6e9a platform/libplatform_s.a(nrfx_uarte.o)
 .debug_loc     0x0000000000007e54      0x37e secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memmove.o)
 .debug_loc     0x00000000000081d2      0x23d secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memcpy.o)
 .debug_loc     0x000000000000840f      0x151 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memset.o)
 .debug_loc     0x0000000000008560      0x32e secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
 .debug_loc     0x000000000000888e       0x97 secure_fw/spm/libtfm_spm.a(main.o)
 .debug_loc     0x0000000000008925      0x154 secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
 .debug_loc     0x0000000000008a79      0x24e secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
 .debug_loc     0x0000000000008cc7      0x88e secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
 .debug_loc     0x0000000000009555       0x16 secure_fw/spm/libtfm_spm.a(faults.o)
 .debug_loc     0x000000000000956b      0x36c secure_fw/spm/libtfm_spm.a(target_cfg.o)
 .debug_loc     0x00000000000098d7      0x142 secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
 .debug_loc     0x0000000000009a19      0x265 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
 .debug_loc     0x0000000000009c7e      0x794 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
 .debug_loc     0x000000000000a412      0x25d secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
 .debug_loc     0x000000000000a66f      0x20e secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .debug_loc     0x000000000000a87d      0x408 secure_fw/spm/libtfm_spm.a(psa_api.o)
 .debug_loc     0x000000000000ac85      0x760 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
 .debug_loc     0x000000000000b3e5      0x2d9 secure_fw/spm/libtfm_spm.a(tfm_pools.o)
 .debug_loc     0x000000000000b6be      0x22c secure_fw/spm/libtfm_spm.a(thread.o)
 .debug_loc     0x000000000000b8ea       0x15 secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o)
 .debug_loc     0x000000000000b8ff       0x66 secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
 .debug_loc     0x000000000000b965      0xb05 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
 .debug_loc     0x000000000000c46a      0x2a6 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
 .debug_loc     0x000000000000c710       0x25 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_cipher.o)
 .debug_loc     0x000000000000c735       0x25 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_hash.o)
 .debug_loc     0x000000000000c75a       0x25 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_mac.o)
 .debug_loc     0x000000000000c77f       0x25 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_aead.o)
 .debug_loc     0x000000000000c7a4       0x25 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_asymmetric.o)
 .debug_loc     0x000000000000c7c9       0x25 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_derivation.o)
 .debug_loc     0x000000000000c7ee       0x25 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_management.o)
 .debug_loc     0x000000000000c813       0x92 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_rng.o)
 .debug_loc     0x000000000000c8a5      0x27d secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 .debug_loc     0x000000000000cb22       0x25 platform/libplatform_crypto_keys.a(crypto_keys.o)
 .debug_loc     0x000000000000cb47      0x46a secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .debug_loc     0x000000000000cfb1      0x105 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .debug_loc     0x000000000000d0b6     0x711c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .debug_loc     0x00000000000141d2      0xac6 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .debug_loc     0x0000000000014c98     0x1494 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .debug_loc     0x000000000001612c      0x2e8 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
 .debug_loc     0x0000000000016414      0x2d8 platform/libplatform_s.a(mpu_armv8m_drv.o)
 .debug_loc     0x00000000000166ec      0x98a platform/libplatform_s.a(spu.o)
 .debug_loc     0x0000000000017076       0x87 platform/libplatform_s.a(tfm_hal_platform.o)
 .debug_loc     0x00000000000170fd       0x4a platform/libplatform_s.a(dummy_otp.o)
 .debug_loc     0x0000000000017147       0xfd platform/libplatform_s.a(tfm_platform_system.o)
 .debug_loc     0x0000000000017244      0x3f6 platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
 .debug_loc     0x000000000001763a       0xd5 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sfn_common_thread.o)
 .debug_loc     0x000000000001770f      0x103 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o)
 .debug_loc     0x0000000000017812      0x573 secure_fw/spm/libtfm_spm.a(psa_call_api.o)
 .debug_loc     0x0000000000017d85       0x73 secure_fw/spm/libtfm_spm.a(psa_version_api.o)
 .debug_loc     0x0000000000017df8      0x305 secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
 .debug_loc     0x00000000000180fd      0xb1e secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)

.debug_aranges  0x0000000000000000     0x1590
 .debug_aranges
                0x0000000000000000       0x48 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
 .debug_aranges
                0x0000000000000048       0x28 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o
 .debug_aranges
                0x0000000000000070       0x20 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o
 .debug_aranges
                0x0000000000000090       0x68 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
 .debug_aranges
                0x00000000000000f8       0x18 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o
 .debug_aranges
                0x0000000000000110       0x18 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o
 .debug_aranges
                0x0000000000000128       0x18 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/load_info_ns_agent_tz.o
 .debug_aranges
                0x0000000000000140       0x40 platform/libplatform_s.a(system_nrf5340_application.o)
 .debug_aranges
                0x0000000000000180       0x20 platform/libplatform_s.a(exception_info.o)
 .debug_aranges
                0x00000000000001a0       0x20 platform/libplatform_s.a(tfm_hal_spm_logdev_peripheral.o)
 .debug_aranges
                0x00000000000001c0       0x38 platform/libplatform_s.a(uart_stdout.o)
 .debug_aranges
                0x00000000000001f8       0x98 platform/libplatform_s.a(Driver_USART.o)
 .debug_aranges
                0x0000000000000290      0x118 platform/libplatform_s.a(nrfx_uarte.o)
 .debug_aranges
                0x00000000000003a8       0x28 platform/libplatform_s.a(nrfx_glue.o)
 .debug_aranges
                0x00000000000003d0       0x20 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memmove.o)
 .debug_aranges
                0x00000000000003f0       0x20 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memcpy.o)
 .debug_aranges
                0x0000000000000410       0x20 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memset.o)
 .debug_aranges
                0x0000000000000430       0x70 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
 .debug_aranges
                0x00000000000004a0       0x18 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sprt_partition_metadata_indicator.o)
 .debug_aranges
                0x00000000000004b8       0x20 secure_fw/spm/libtfm_spm.a(utilities.o)
 .debug_aranges
                0x00000000000004d8       0x20 secure_fw/spm/libtfm_spm.a(main.o)
 .debug_aranges
                0x00000000000004f8       0x28 secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
 .debug_aranges
                0x0000000000000520       0x40 secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
 .debug_aranges
                0x0000000000000560       0x20 secure_fw/spm/libtfm_spm.a(ns_agent_tz_v80m.o)
 .debug_aranges
                0x0000000000000580       0x48 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
 .debug_aranges
                0x00000000000005c8       0x28 secure_fw/spm/libtfm_spm.a(faults.o)
 .debug_aranges
                0x00000000000005f0       0x70 secure_fw/spm/libtfm_spm.a(target_cfg.o)
 .debug_aranges
                0x0000000000000660       0x28 secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
 .debug_aranges
                0x0000000000000688       0x40 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
 .debug_aranges
                0x00000000000006c8       0x98 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
 .debug_aranges
                0x0000000000000760       0x30 secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
 .debug_aranges
                0x0000000000000790       0x30 secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .debug_aranges
                0x00000000000007c0       0x48 secure_fw/spm/libtfm_spm.a(psa_api.o)
 .debug_aranges
                0x0000000000000808       0x58 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
 .debug_aranges
                0x0000000000000860       0x38 secure_fw/spm/libtfm_spm.a(tfm_pools.o)
 .debug_aranges
                0x0000000000000898       0x40 secure_fw/spm/libtfm_spm.a(thread.o)
 .debug_aranges
                0x00000000000008d8       0x28 secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o)
 .debug_aranges
                0x0000000000000900       0x38 secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
 .debug_aranges
                0x0000000000000938       0x40 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
 .debug_aranges
                0x0000000000000978       0x38 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
 .debug_aranges
                0x00000000000009b0       0x20 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_cipher.o)
 .debug_aranges
                0x00000000000009d0       0x20 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_hash.o)
 .debug_aranges
                0x00000000000009f0       0x20 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_mac.o)
 .debug_aranges
                0x0000000000000a10       0x20 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_aead.o)
 .debug_aranges
                0x0000000000000a30       0x28 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_asymmetric.o)
 .debug_aranges
                0x0000000000000a58       0x20 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_derivation.o)
 .debug_aranges
                0x0000000000000a78       0x20 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_management.o)
 .debug_aranges
                0x0000000000000a98       0x20 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_rng.o)
 .debug_aranges
                0x0000000000000ab8       0x48 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 .debug_aranges
                0x0000000000000b00       0x18 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(intermedia_tfm_crypto.o)
 .debug_aranges
                0x0000000000000b18       0x28 platform/libplatform_crypto_keys.a(crypto_keys.o)
 .debug_aranges
                0x0000000000000b40       0x58 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .debug_aranges
                0x0000000000000b98       0x58 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .debug_aranges
                0x0000000000000bf0      0x370 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .debug_aranges
                0x0000000000000f60       0x80 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .debug_aranges
                0x0000000000000fe0      0x238 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .debug_aranges
                0x0000000000001218       0x30 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
 .debug_aranges
                0x0000000000001248       0x18 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(intermedia_tfm_platform.o)
 .debug_aranges
                0x0000000000001260       0x40 platform/libplatform_s.a(mpu_armv8m_drv.o)
 .debug_aranges
                0x00000000000012a0       0xa8 platform/libplatform_s.a(spu.o)
 .debug_aranges
                0x0000000000001348       0x20 platform/libplatform_s.a(tfm_hal_platform.o)
 .debug_aranges
                0x0000000000001368       0x38 platform/libplatform_s.a(dummy_otp.o)
 .debug_aranges
                0x00000000000013a0       0x30 platform/libplatform_s.a(dummy_provisioning.o)
 .debug_aranges
                0x00000000000013d0       0x28 platform/libplatform_s.a(tfm_platform_system.o)
 .debug_aranges
                0x00000000000013f8       0x28 platform/libplatform_s.a(tfm_hal_reset_halt.o)
 .debug_aranges
                0x0000000000001420       0x28 platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
 .debug_aranges
                0x0000000000001448       0x20 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sfn_common_thread.o)
 .debug_aranges
                0x0000000000001468       0x78 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o)
 .debug_aranges
                0x00000000000014e0       0x20 secure_fw/spm/libtfm_spm.a(psa_call_api.o)
 .debug_aranges
                0x0000000000001500       0x28 secure_fw/spm/libtfm_spm.a(psa_version_api.o)
 .debug_aranges
                0x0000000000001528       0x30 secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
 .debug_aranges
                0x0000000000001558       0x38 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)

.debug_ranges   0x0000000000000000     0x29e8
 .debug_ranges  0x0000000000000000       0x38 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
 .debug_ranges  0x0000000000000038       0x98 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o
 .debug_ranges  0x00000000000000d0       0x10 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o
 .debug_ranges  0x00000000000000e0       0x58 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
 .debug_ranges  0x0000000000000138       0x30 platform/libplatform_s.a(system_nrf5340_application.o)
 .debug_ranges  0x0000000000000168       0xb0 platform/libplatform_s.a(exception_info.o)
 .debug_ranges  0x0000000000000218       0x10 platform/libplatform_s.a(tfm_hal_spm_logdev_peripheral.o)
 .debug_ranges  0x0000000000000228       0x40 platform/libplatform_s.a(uart_stdout.o)
 .debug_ranges  0x0000000000000268      0x190 platform/libplatform_s.a(Driver_USART.o)
 .debug_ranges  0x00000000000003f8      0xba0 platform/libplatform_s.a(nrfx_uarte.o)
 .debug_ranges  0x0000000000000f98       0x18 platform/libplatform_s.a(nrfx_glue.o)
 .debug_ranges  0x0000000000000fb0       0x30 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memmove.o)
 .debug_ranges  0x0000000000000fe0       0x10 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memcpy.o)
 .debug_ranges  0x0000000000000ff0       0x10 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memset.o)
 .debug_ranges  0x0000000000001000       0x60 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
 .debug_ranges  0x0000000000001060       0x10 secure_fw/spm/libtfm_spm.a(utilities.o)
 .debug_ranges  0x0000000000001070       0x40 secure_fw/spm/libtfm_spm.a(main.o)
 .debug_ranges  0x00000000000010b0       0x18 secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
 .debug_ranges  0x00000000000010c8       0x30 secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
 .debug_ranges  0x00000000000010f8       0x10 secure_fw/spm/libtfm_spm.a(ns_agent_tz_v80m.o)
 .debug_ranges  0x0000000000001108      0x120 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
 .debug_ranges  0x0000000000001228       0x30 secure_fw/spm/libtfm_spm.a(faults.o)
 .debug_ranges  0x0000000000001258       0xa8 secure_fw/spm/libtfm_spm.a(target_cfg.o)
 .debug_ranges  0x0000000000001300       0x18 secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
 .debug_ranges  0x0000000000001318       0x60 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
 .debug_ranges  0x0000000000001378       0xb8 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
 .debug_ranges  0x0000000000001430       0x50 secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
 .debug_ranges  0x0000000000001480       0x20 secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .debug_ranges  0x00000000000014a0       0x38 secure_fw/spm/libtfm_spm.a(psa_api.o)
 .debug_ranges  0x00000000000014d8       0x88 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
 .debug_ranges  0x0000000000001560       0x28 secure_fw/spm/libtfm_spm.a(tfm_pools.o)
 .debug_ranges  0x0000000000001588       0x58 secure_fw/spm/libtfm_spm.a(thread.o)
 .debug_ranges  0x00000000000015e0       0x18 secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o)
 .debug_ranges  0x00000000000015f8       0x28 secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
 .debug_ranges  0x0000000000001620      0x118 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
 .debug_ranges  0x0000000000001738       0x28 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
 .debug_ranges  0x0000000000001760       0x10 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_cipher.o)
 .debug_ranges  0x0000000000001770       0x10 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_hash.o)
 .debug_ranges  0x0000000000001780       0x10 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_mac.o)
 .debug_ranges  0x0000000000001790       0x10 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_aead.o)
 .debug_ranges  0x00000000000017a0       0x18 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_asymmetric.o)
 .debug_ranges  0x00000000000017b8       0x10 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_derivation.o)
 .debug_ranges  0x00000000000017c8       0x10 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_management.o)
 .debug_ranges  0x00000000000017d8       0x10 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_rng.o)
 .debug_ranges  0x00000000000017e8       0x70 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 .debug_ranges  0x0000000000001858       0x18 platform/libplatform_crypto_keys.a(crypto_keys.o)
 .debug_ranges  0x0000000000001870       0x60 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .debug_ranges  0x00000000000018d0       0x48 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .debug_ranges  0x0000000000001918      0x6f8 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .debug_ranges  0x0000000000002010      0x158 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .debug_ranges  0x0000000000002168      0x228 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .debug_ranges  0x0000000000002390       0x20 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
 .debug_ranges  0x00000000000023b0       0x50 platform/libplatform_s.a(mpu_armv8m_drv.o)
 .debug_ranges  0x0000000000002400      0x1a0 platform/libplatform_s.a(spu.o)
 .debug_ranges  0x00000000000025a0       0x28 platform/libplatform_s.a(tfm_hal_platform.o)
 .debug_ranges  0x00000000000025c8       0x28 platform/libplatform_s.a(dummy_otp.o)
 .debug_ranges  0x00000000000025f0       0x20 platform/libplatform_s.a(dummy_provisioning.o)
 .debug_ranges  0x0000000000002610       0x18 platform/libplatform_s.a(tfm_platform_system.o)
 .debug_ranges  0x0000000000002628       0x18 platform/libplatform_s.a(tfm_hal_reset_halt.o)
 .debug_ranges  0x0000000000002640       0x90 platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
 .debug_ranges  0x00000000000026d0       0x38 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sfn_common_thread.o)
 .debug_ranges  0x0000000000002708       0x68 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o)
 .debug_ranges  0x0000000000002770       0x10 secure_fw/spm/libtfm_spm.a(psa_call_api.o)
 .debug_ranges  0x0000000000002780       0x18 secure_fw/spm/libtfm_spm.a(psa_version_api.o)
 .debug_ranges  0x0000000000002798       0x20 secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
 .debug_ranges  0x00000000000027b8      0x230 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)

.debug_line     0x0000000000000000    0x1b1f4
 .debug_line    0x0000000000000000      0x2c0 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
 .debug_line    0x00000000000002c0      0x394 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o
 .debug_line    0x0000000000000654      0x1e6 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o
 .debug_line    0x000000000000083a      0x1a1 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
 .debug_line    0x00000000000009db      0x40f secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o
 .debug_line    0x0000000000000dea      0x417 secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o
 .debug_line    0x0000000000001201      0x37e secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/load_info_ns_agent_tz.o
 .debug_line    0x000000000000157f      0x5c9 platform/libplatform_s.a(system_nrf5340_application.o)
 .debug_line    0x0000000000001b48      0x4a4 platform/libplatform_s.a(exception_info.o)
 .debug_line    0x0000000000001fec      0x191 platform/libplatform_s.a(tfm_hal_spm_logdev_peripheral.o)
 .debug_line    0x000000000000217d      0x296 platform/libplatform_s.a(uart_stdout.o)
 .debug_line    0x0000000000002413      0x92d platform/libplatform_s.a(Driver_USART.o)
 .debug_line    0x0000000000002d40     0x3caf platform/libplatform_s.a(nrfx_uarte.o)
 .debug_line    0x00000000000069ef      0x229 platform/libplatform_s.a(nrfx_glue.o)
 .debug_line    0x0000000000006c18      0x338 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memmove.o)
 .debug_line    0x0000000000006f50      0x2a1 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memcpy.o)
 .debug_line    0x00000000000071f1      0x2a2 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memset.o)
 .debug_line    0x0000000000007493      0x445 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
 .debug_line    0x00000000000078d8      0x17a secure_fw/partitions/lib/runtime/libtfm_sprt.a(sprt_partition_metadata_indicator.o)
 .debug_line    0x0000000000007a52       0xee secure_fw/spm/libtfm_spm.a(utilities.o)
 .debug_line    0x0000000000007b40      0x53f secure_fw/spm/libtfm_spm.a(main.o)
 .debug_line    0x000000000000807f      0x44f secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
 .debug_line    0x00000000000084ce      0x33a secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
 .debug_line    0x0000000000008808      0x184 secure_fw/spm/libtfm_spm.a(ns_agent_tz_v80m.o)
 .debug_line    0x000000000000898c      0xa47 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
 .debug_line    0x00000000000093d3      0x3cf secure_fw/spm/libtfm_spm.a(faults.o)
 .debug_line    0x00000000000097a2      0x798 secure_fw/spm/libtfm_spm.a(target_cfg.o)
 .debug_line    0x0000000000009f3a      0x578 secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
 .debug_line    0x000000000000a4b2      0x463 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
 .debug_line    0x000000000000a915      0xa26 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
 .debug_line    0x000000000000b33b      0x534 secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
 .debug_line    0x000000000000b86f      0x57e secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .debug_line    0x000000000000bded      0x7d2 secure_fw/spm/libtfm_spm.a(psa_api.o)
 .debug_line    0x000000000000c5bf      0xaa6 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
 .debug_line    0x000000000000d065      0x405 secure_fw/spm/libtfm_spm.a(tfm_pools.o)
 .debug_line    0x000000000000d46a      0x350 secure_fw/spm/libtfm_spm.a(thread.o)
 .debug_line    0x000000000000d7ba      0x279 secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o)
 .debug_line    0x000000000000da33      0x3eb secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
 .debug_line    0x000000000000de1e      0x794 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
 .debug_line    0x000000000000e5b2      0x596 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
 .debug_line    0x000000000000eb48      0x2a5 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_cipher.o)
 .debug_line    0x000000000000eded      0x28e secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_hash.o)
 .debug_line    0x000000000000f07b      0x2a2 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_mac.o)
 .debug_line    0x000000000000f31d      0x2a3 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_aead.o)
 .debug_line    0x000000000000f5c0      0x2bb secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_asymmetric.o)
 .debug_line    0x000000000000f87b      0x2ad secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_derivation.o)
 .debug_line    0x000000000000fb28      0x2ad secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_management.o)
 .debug_line    0x000000000000fdd5      0x297 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_rng.o)
 .debug_line    0x000000000001006c      0x615 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 .debug_line    0x0000000000010681      0x17e secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(intermedia_tfm_crypto.o)
 .debug_line    0x00000000000107ff      0x2ae platform/libplatform_crypto_keys.a(crypto_keys.o)
 .debug_line    0x0000000000010aad      0x719 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .debug_line    0x00000000000111c6      0x25f secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .debug_line    0x0000000000011425     0x3bfd secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .debug_line    0x0000000000015022      0xb26 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .debug_line    0x0000000000015b48      0xccf secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .debug_line    0x0000000000016817      0x4df secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
 .debug_line    0x0000000000016cf6      0x182 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(intermedia_tfm_platform.o)
 .debug_line    0x0000000000016e78      0x435 platform/libplatform_s.a(mpu_armv8m_drv.o)
 .debug_line    0x00000000000172ad      0x7b1 platform/libplatform_s.a(spu.o)
 .debug_line    0x0000000000017a5e      0x353 platform/libplatform_s.a(tfm_hal_platform.o)
 .debug_line    0x0000000000017db1      0x29f platform/libplatform_s.a(dummy_otp.o)
 .debug_line    0x0000000000018050      0x109 platform/libplatform_s.a(dummy_provisioning.o)
 .debug_line    0x0000000000018159      0x412 platform/libplatform_s.a(tfm_platform_system.o)
 .debug_line    0x000000000001856b      0x25e platform/libplatform_s.a(tfm_hal_reset_halt.o)
 .debug_line    0x00000000000187c9      0x6ef platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
 .debug_line    0x0000000000018eb8      0x3a5 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sfn_common_thread.o)
 .debug_line    0x000000000001925d      0x391 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o)
 .debug_line    0x00000000000195ee      0x79c secure_fw/spm/libtfm_spm.a(psa_call_api.o)
 .debug_line    0x0000000000019d8a      0x3d6 secure_fw/spm/libtfm_spm.a(psa_version_api.o)
 .debug_line    0x000000000001a160      0x625 secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
 .debug_line    0x000000000001a785      0xa6f secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)

.debug_str      0x0000000000000000     0xd339
 .debug_str     0x0000000000000000      0x2af secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
                                        0x314 (size before relaxing)
 .debug_str     0x00000000000002af      0x35c secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o
                                        0x512 (size before relaxing)
 .debug_str     0x000000000000060b       0x9b secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o
                                        0x2d5 (size before relaxing)
 .debug_str     0x00000000000006a6       0xbb secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
                                        0x2ab (size before relaxing)
 .debug_str     0x0000000000000761      0x2fb secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/crypto/auto_generated/load_info_tfm_crypto.o
                                        0x5a6 (size before relaxing)
 .debug_str     0x0000000000000a5c      0x14b secure_fw/CMakeFiles/tfm_s.dir/__/generated/secure_fw/partitions/platform/auto_generated/load_info_tfm_platform.o
                                        0x5ba (size before relaxing)
 .debug_str     0x0000000000000ba7       0xff secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/load_info_ns_agent_tz.o
                                        0x54c (size before relaxing)
 .debug_str     0x0000000000000ca6      0xade platform/libplatform_s.a(system_nrf5340_application.o)
                                        0xd5e (size before relaxing)
 .debug_str     0x0000000000001784      0x1bf platform/libplatform_s.a(exception_info.o)
                                        0x4ed (size before relaxing)
 .debug_str     0x0000000000001943       0x80 platform/libplatform_s.a(tfm_hal_spm_logdev_peripheral.o)
                                        0x242 (size before relaxing)
 .debug_str     0x00000000000019c3      0x2c8 platform/libplatform_s.a(uart_stdout.o)
                                        0x5d6 (size before relaxing)
 .debug_str     0x0000000000001c8b      0xdba platform/libplatform_s.a(Driver_USART.o)
                                       0x1476 (size before relaxing)
 .debug_str     0x0000000000002a45     0x15e5 platform/libplatform_s.a(nrfx_uarte.o)
                                       0x2582 (size before relaxing)
 .debug_str     0x000000000000402a       0x90 platform/libplatform_s.a(nrfx_glue.o)
                                        0x27a (size before relaxing)
 .debug_str     0x00000000000040ba       0x9e secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memmove.o)
                                        0x281 (size before relaxing)
 .debug_str     0x0000000000004158       0x56 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memcpy.o)
                                        0x25a (size before relaxing)
 .debug_str     0x00000000000041ae       0x69 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memset.o)
                                        0x25c (size before relaxing)
 .debug_str     0x0000000000004217      0x118 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
                                        0x400 (size before relaxing)
 .debug_str     0x000000000000432f       0x7d secure_fw/partitions/lib/runtime/libtfm_sprt.a(sprt_partition_metadata_indicator.o)
                                        0x228 (size before relaxing)
 .debug_str     0x00000000000043ac       0x6b secure_fw/spm/libtfm_spm.a(utilities.o)
                                        0x1ff (size before relaxing)
 .debug_str     0x0000000000004417      0x49a secure_fw/spm/libtfm_spm.a(main.o)
                                        0x6d8 (size before relaxing)
 .debug_str     0x00000000000048b1      0x13d secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
                                        0x380 (size before relaxing)
 .debug_str     0x00000000000049ee       0xd7 secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
                                        0x703 (size before relaxing)
 .debug_str     0x0000000000004ac5       0x74 secure_fw/spm/libtfm_spm.a(ns_agent_tz_v80m.o)
                                        0x21e (size before relaxing)
 .debug_str     0x0000000000004b39      0x968 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
                                        0xdfb (size before relaxing)
 .debug_str     0x00000000000054a1       0xb1 secure_fw/spm/libtfm_spm.a(faults.o)
                                        0x766 (size before relaxing)
 .debug_str     0x0000000000005552     0x1966 secure_fw/spm/libtfm_spm.a(target_cfg.o)
                                       0x2c10 (size before relaxing)
 .debug_str     0x0000000000006eb8      0x15c secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
                                        0x830 (size before relaxing)
 .debug_str     0x0000000000007014      0x1a6 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
                                        0x43e (size before relaxing)
 .debug_str     0x00000000000071ba      0x411 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
                                        0xa00 (size before relaxing)
 .debug_str     0x00000000000075cb      0x113 secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
                                        0x601 (size before relaxing)
 .debug_str     0x00000000000076de      0x21f secure_fw/spm/libtfm_spm.a(rom_loader.o)
                                        0x717 (size before relaxing)
 .debug_str     0x00000000000078fd      0x103 secure_fw/spm/libtfm_spm.a(psa_api.o)
                                        0x766 (size before relaxing)
 .debug_str     0x0000000000007a00      0x24d secure_fw/spm/libtfm_spm.a(backend_ipc.o)
                                        0x9cd (size before relaxing)
 .debug_str     0x0000000000007c4d       0x8a secure_fw/spm/libtfm_spm.a(tfm_pools.o)
                                        0x2fc (size before relaxing)
 .debug_str     0x0000000000007cd7       0xb6 secure_fw/spm/libtfm_spm.a(thread.o)
                                        0x3b9 (size before relaxing)
 .debug_str     0x0000000000007d8d       0xab secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o)
                                        0x3c5 (size before relaxing)
 .debug_str     0x0000000000007e38       0x9a secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
                                        0x552 (size before relaxing)
 .debug_str     0x0000000000007ed2      0xd1b secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
                                       0x1002 (size before relaxing)
 .debug_str     0x0000000000008bed      0x569 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
                                       0x10e4 (size before relaxing)
 .debug_str     0x0000000000009156       0x64 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_cipher.o)
                                        0x29d (size before relaxing)
 .debug_str     0x00000000000091ba       0x62 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_hash.o)
                                        0x24b (size before relaxing)
 .debug_str     0x000000000000921c       0x61 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_mac.o)
                                        0x297 (size before relaxing)
 .debug_str     0x000000000000927d       0x62 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_aead.o)
                                        0x299 (size before relaxing)
 .debug_str     0x00000000000092df       0x68 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_asymmetric.o)
                                        0x2d2 (size before relaxing)
 .debug_str     0x0000000000009347       0x6c secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_derivation.o)
                                        0x2b2 (size before relaxing)
 .debug_str     0x00000000000093b3       0x6c secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_management.o)
                                        0x2ad (size before relaxing)
 .debug_str     0x000000000000941f       0x8d secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_rng.o)
                                        0x291 (size before relaxing)
 .debug_str     0x00000000000094ac      0x3e9 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
                                        0x77f (size before relaxing)
 .debug_str     0x0000000000009895       0x81 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(intermedia_tfm_crypto.o)
                                        0x227 (size before relaxing)
 .debug_str     0x0000000000009916       0xd7 platform/libplatform_crypto_keys.a(crypto_keys.o)
                                        0x482 (size before relaxing)
 .debug_str     0x00000000000099ed      0x17c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
                                        0x371 (size before relaxing)
 .debug_str     0x0000000000009b69      0x134 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
                                        0x309 (size before relaxing)
 .debug_str     0x0000000000009c9d     0x1f16 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
                                       0x2fc3 (size before relaxing)
 .debug_str     0x000000000000bbb3      0x338 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
                                        0x974 (size before relaxing)
 .debug_str     0x000000000000beeb      0x23b secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
                                       0x14f2 (size before relaxing)
 .debug_str     0x000000000000c126      0x1dc secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
                                        0x47e (size before relaxing)
 .debug_str     0x000000000000c302       0x85 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(intermedia_tfm_platform.o)
                                        0x22d (size before relaxing)
 .debug_str     0x000000000000c387      0x14b platform/libplatform_s.a(mpu_armv8m_drv.o)
                                        0x52e (size before relaxing)
 .debug_str     0x000000000000c4d2      0x36e platform/libplatform_s.a(spu.o)
                                        0xa32 (size before relaxing)
 .debug_str     0x000000000000c840       0x8c platform/libplatform_s.a(tfm_hal_platform.o)
                                        0x481 (size before relaxing)
 .debug_str     0x000000000000c8cc       0xa2 platform/libplatform_s.a(dummy_otp.o)
                                        0x31f (size before relaxing)
 .debug_str     0x000000000000c96e       0x4b platform/libplatform_s.a(dummy_provisioning.o)
                                        0x2f4 (size before relaxing)
 .debug_str     0x000000000000c9b9      0x11c platform/libplatform_s.a(tfm_platform_system.o)
                                        0x545 (size before relaxing)
 .debug_str     0x000000000000cad5       0x75 platform/libplatform_s.a(tfm_hal_reset_halt.o)
                                        0x383 (size before relaxing)
 .debug_str     0x000000000000cb4a      0x1ad platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
                                        0x701 (size before relaxing)
 .debug_str     0x000000000000ccf7       0x91 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sfn_common_thread.o)
                                        0x3e0 (size before relaxing)
 .debug_str     0x000000000000cd88      0x157 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o)
                                        0x488 (size before relaxing)
 .debug_str     0x000000000000cedf       0xa6 secure_fw/spm/libtfm_spm.a(psa_call_api.o)
                                        0x746 (size before relaxing)
 .debug_str     0x000000000000cf85       0x9c secure_fw/spm/libtfm_spm.a(psa_version_api.o)
                                        0x533 (size before relaxing)
 .debug_str     0x000000000000d021       0xb6 secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
                                        0x641 (size before relaxing)
 .debug_str     0x000000000000d0d7      0x262 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)
                                        0xc60 (size before relaxing)

.debug_frame    0x0000000000000000     0x31b4
 .debug_frame   0x0000000000000000       0x70 secure_fw/CMakeFiles/tfm_s.dir/partitions/ns_agent_tz/psa_api_veneers_v80m.o
 .debug_frame   0x0000000000000070       0x38 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup.o
 .debug_frame   0x00000000000000a8       0x20 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/target/nordic_nrf/common/core/startup_nrf5340.o
 .debug_frame   0x00000000000000c8       0xb0 secure_fw/CMakeFiles/tfm_s.dir/__/platform/ext/common/faults.o
 .debug_frame   0x0000000000000178       0x68 platform/libplatform_s.a(system_nrf5340_application.o)
 .debug_frame   0x00000000000001e0       0x38 platform/libplatform_s.a(exception_info.o)
 .debug_frame   0x0000000000000218       0x20 platform/libplatform_s.a(tfm_hal_spm_logdev_peripheral.o)
 .debug_frame   0x0000000000000238       0x80 platform/libplatform_s.a(uart_stdout.o)
 .debug_frame   0x00000000000002b8      0x17c platform/libplatform_s.a(Driver_USART.o)
 .debug_frame   0x0000000000000434      0x3b8 platform/libplatform_s.a(nrfx_uarte.o)
 .debug_frame   0x00000000000007ec       0x30 platform/libplatform_s.a(nrfx_glue.o)
 .debug_frame   0x000000000000081c       0x3c secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memmove.o)
 .debug_frame   0x0000000000000858       0x2c secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memcpy.o)
 .debug_frame   0x0000000000000884       0x30 secure_fw/partitions/lib/runtime/libtfm_sprt.a(crt_memset.o)
 .debug_frame   0x00000000000008b4       0xe4 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_api_ipc.o)
 .debug_frame   0x0000000000000998       0x20 secure_fw/spm/libtfm_spm.a(utilities.o)
 .debug_frame   0x00000000000009b8       0x28 secure_fw/spm/libtfm_spm.a(main.o)
 .debug_frame   0x00000000000009e0       0x40 secure_fw/spm/libtfm_spm.a(tfm_core_svcalls_ipc.o)
 .debug_frame   0x0000000000000a20       0x60 secure_fw/spm/libtfm_spm.a(tfm_arch_v8m_main.o)
 .debug_frame   0x0000000000000a80       0x20 secure_fw/spm/libtfm_spm.a(ns_agent_tz_v80m.o)
 .debug_frame   0x0000000000000aa0       0xb0 secure_fw/spm/libtfm_spm.a(tfm_hal_isolation.o)
 .debug_frame   0x0000000000000b50       0x3c secure_fw/spm/libtfm_spm.a(faults.o)
 .debug_frame   0x0000000000000b8c       0xdc secure_fw/spm/libtfm_spm.a(target_cfg.o)
 .debug_frame   0x0000000000000c68       0x38 secure_fw/spm/libtfm_spm.a(tfm_boot_data.o)
 .debug_frame   0x0000000000000ca0       0x74 secure_fw/spm/libtfm_spm.a(tfm_arch.o)
 .debug_frame   0x0000000000000d14      0x1a0 secure_fw/spm/libtfm_spm.a(spm_ipc.o)
 .debug_frame   0x0000000000000eb4       0x70 secure_fw/spm/libtfm_spm.a(spm_cross_call.o)
 .debug_frame   0x0000000000000f24       0x70 secure_fw/spm/libtfm_spm.a(rom_loader.o)
 .debug_frame   0x0000000000000f94       0xb4 secure_fw/spm/libtfm_spm.a(psa_api.o)
 .debug_frame   0x0000000000001048      0x100 secure_fw/spm/libtfm_spm.a(backend_ipc.o)
 .debug_frame   0x0000000000001148       0x64 secure_fw/spm/libtfm_spm.a(tfm_pools.o)
 .debug_frame   0x00000000000011ac       0xa0 secure_fw/spm/libtfm_spm.a(thread.o)
 .debug_frame   0x000000000000124c       0x38 secure_fw/spm/libtfm_spm.a(tfm_spm_ns_ctx.o)
 .debug_frame   0x0000000000001284       0x58 secure_fw/spm/libtfm_spm.a(tfm_hal_platform_common.o)
 .debug_frame   0x00000000000012dc       0xac secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_init.o)
 .debug_frame   0x0000000000001388       0x8c secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_alloc.o)
 .debug_frame   0x0000000000001414       0x20 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_cipher.o)
 .debug_frame   0x0000000000001434       0x20 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_hash.o)
 .debug_frame   0x0000000000001454       0x20 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_mac.o)
 .debug_frame   0x0000000000001474       0x20 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_aead.o)
 .debug_frame   0x0000000000001494       0x30 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_asymmetric.o)
 .debug_frame   0x00000000000014c4       0x20 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_derivation.o)
 .debug_frame   0x00000000000014e4       0x20 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_key_management.o)
 .debug_frame   0x0000000000001504       0x20 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_rng.o)
 .debug_frame   0x0000000000001524       0xbc secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(crypto_library.o)
 .debug_frame   0x00000000000015e0       0x30 platform/libplatform_crypto_keys.a(crypto_keys.o)
 .debug_frame   0x0000000000001610       0xc8 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(memory_buffer_alloc.o)
 .debug_frame   0x00000000000016d8       0x90 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(platform.o)
 .debug_frame   0x0000000000001768      0xd2c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto.o)
 .debug_frame   0x0000000000002494      0x19c secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_slot_management.o)
 .debug_frame   0x0000000000002630      0x4f0 secure_fw/partitions/crypto/mbedcrypto/nrf_security_src/libcrypto_service_mbedcrypto.a(psa_crypto_driver_wrappers.o)
 .debug_frame   0x0000000000002b20       0x60 secure_fw/partitions/platform/libtfm_psa_rot_partition_platform.a(platform_sp.o)
 .debug_frame   0x0000000000002b80       0x78 platform/libplatform_s.a(mpu_armv8m_drv.o)
 .debug_frame   0x0000000000002bf8      0x140 platform/libplatform_s.a(spu.o)
 .debug_frame   0x0000000000002d38       0x28 platform/libplatform_s.a(tfm_hal_platform.o)
 .debug_frame   0x0000000000002d60       0x50 platform/libplatform_s.a(dummy_otp.o)
 .debug_frame   0x0000000000002db0       0x40 platform/libplatform_s.a(dummy_provisioning.o)
 .debug_frame   0x0000000000002df0       0x30 platform/libplatform_s.a(tfm_platform_system.o)
 .debug_frame   0x0000000000002e20       0x30 platform/libplatform_s.a(tfm_hal_reset_halt.o)
 .debug_frame   0x0000000000002e50       0x4c platform/libplatform_s.a(tfm_platform_hal_ioctl.o)
 .debug_frame   0x0000000000002e9c       0x38 secure_fw/partitions/lib/runtime/libtfm_sprt.a(sfn_common_thread.o)
 .debug_frame   0x0000000000002ed4       0xd0 secure_fw/partitions/lib/runtime/libtfm_sprt.a(psa_interface_cross.o)
 .debug_frame   0x0000000000002fa4       0x40 secure_fw/spm/libtfm_spm.a(psa_call_api.o)
 .debug_frame   0x0000000000002fe4       0x3c secure_fw/spm/libtfm_spm.a(psa_version_api.o)
 .debug_frame   0x0000000000003020       0x74 secure_fw/spm/libtfm_spm.a(psa_read_write_skip_api.o)
 .debug_frame   0x0000000000003094       0xa4 secure_fw/partitions/crypto/libtfm_psa_rot_partition_crypto.a(tfm_builtin_key_loader.o)
 .debug_frame   0x0000000000003138       0x28 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
 .debug_frame   0x0000000000003160       0x28 c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-exit.o)
 .debug_frame   0x0000000000003188       0x2c c:/ncs/toolchains/cf2149caf2/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/../../../../arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc_nano.a(lib_a-init.o)
