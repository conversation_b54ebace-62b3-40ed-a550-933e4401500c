/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_MDIO_H
#define Z_INCLUDE_SYSCALLS_MDIO_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern void z_impl_mdio_bus_enable(const struct device * dev);

__pinned_func
static inline void mdio_bus_enable(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_MDIO_BUS_ENABLE);
		return;
	}
#endif
	compiler_barrier();
	z_impl_mdio_bus_enable(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define mdio_bus_enable(dev) do { 	sys_port_trace_syscall_enter(K_SYSCALL_MDIO_BUS_ENABLE, mdio_bus_enable, dev); 	mdio_bus_enable(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_MDIO_BUS_ENABLE, mdio_bus_enable, dev); } while(false)
#endif
#endif


extern void z_impl_mdio_bus_disable(const struct device * dev);

__pinned_func
static inline void mdio_bus_disable(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		(void) arch_syscall_invoke1(parm0.x, K_SYSCALL_MDIO_BUS_DISABLE);
		return;
	}
#endif
	compiler_barrier();
	z_impl_mdio_bus_disable(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define mdio_bus_disable(dev) do { 	sys_port_trace_syscall_enter(K_SYSCALL_MDIO_BUS_DISABLE, mdio_bus_disable, dev); 	mdio_bus_disable(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_MDIO_BUS_DISABLE, mdio_bus_disable, dev); } while(false)
#endif
#endif


extern int z_impl_mdio_read(const struct device * dev, uint8_t prtad, uint8_t devad, uint16_t * data);

__pinned_func
static inline int mdio_read(const struct device * dev, uint8_t prtad, uint8_t devad, uint16_t * data)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t val; } parm1 = { .val = prtad };
		union { uintptr_t x; uint8_t val; } parm2 = { .val = devad };
		union { uintptr_t x; uint16_t * val; } parm3 = { .val = data };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_MDIO_READ);
	}
#endif
	compiler_barrier();
	return z_impl_mdio_read(dev, prtad, devad, data);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define mdio_read(dev, prtad, devad, data) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_MDIO_READ, mdio_read, dev, prtad, devad, data); 	syscall__retval = mdio_read(dev, prtad, devad, data); 	sys_port_trace_syscall_exit(K_SYSCALL_MDIO_READ, mdio_read, dev, prtad, devad, data, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_mdio_write(const struct device * dev, uint8_t prtad, uint8_t devad, uint16_t data);

__pinned_func
static inline int mdio_write(const struct device * dev, uint8_t prtad, uint8_t devad, uint16_t data)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t val; } parm1 = { .val = prtad };
		union { uintptr_t x; uint8_t val; } parm2 = { .val = devad };
		union { uintptr_t x; uint16_t val; } parm3 = { .val = data };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_MDIO_WRITE);
	}
#endif
	compiler_barrier();
	return z_impl_mdio_write(dev, prtad, devad, data);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define mdio_write(dev, prtad, devad, data) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_MDIO_WRITE, mdio_write, dev, prtad, devad, data); 	syscall__retval = mdio_write(dev, prtad, devad, data); 	sys_port_trace_syscall_exit(K_SYSCALL_MDIO_WRITE, mdio_write, dev, prtad, devad, data, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
