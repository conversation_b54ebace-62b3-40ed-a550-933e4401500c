/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_ERRNO_PRIVATE_H
#define Z_INCLUDE_SYSCALLS_ERRNO_PRIVATE_H




#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int * z_impl_z_errno(void);

__pinned_func
static inline int * z_errno(void)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		return (int *) arch_syscall_invoke0(K_SYSCALL_Z_ERRNO);
	}
#endif
	compiler_barrier();
	return z_impl_z_errno();
}


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
