#define CONFIG_LV_DPI_DEF 130
#define CONFIG_BT_HCI_ACL_FLOW_CONTROL 1
#define CONFIG_BT_HCI_VS_EXT 1
#define CONFIG_BOARD "nrf5340dk_nrf5340_cpunet"
#define CONFIG_FLASH_LOAD_SIZE 0x0
#define CONFIG_SRAM_SIZE 64
#define CONFIG_FLASH_LOAD_OFFSET 0x0
#define CONFIG_MBOX_NRFX_IPC 1
#define CONFIG_HEAP_MEM_POOL_SIZE 8192
#define CONFIG_BT_CTLR 1
#define CONFIG_NUM_IRQS 30
#define CONFIG_SOC_SERIES "nrf53"
#define CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC 32768
#define CONFIG_SOC "nRF5340_CPUNET_QKAA"
#define CONFIG_CLOCK_CONTROL_INIT_PRIORITY 30
#define CONFIG_FLASH_SIZE 256
#define CONFIG_FLASH_BASE_ADDRESS 0x1000000
#define CONFIG_ICACHE_LINE_SIZE 32
#define CONFIG_DCACHE_LINE_SIZE 32
#define CONFIG_ROM_START_OFFSET 0x0
#define CONFIG_PINCTRL 1
#define CONFIG_CLOCK_CONTROL 1
#define CONFIG_GPIO 1
#define CONFIG_SOC_HAS_TIMING_FUNCTIONS 1
#define CONFIG_ARCH_HAS_CUSTOM_BUSY_WAIT 1
#define CONFIG_LOG_DOMAIN_NAME "net"
#define CONFIG_NRF_RTC_TIMER 1
#define CONFIG_SYS_CLOCK_TICKS_PER_SEC 32768
#define CONFIG_BUILD_OUTPUT_HEX 1
#define CONFIG_SERIAL_INIT_PRIORITY 55
#define CONFIG_TINYCRYPT 1
#define CONFIG_SERIAL 1
#define CONFIG_MAIN_STACK_SIZE 512
#define CONFIG_PLATFORM_SPECIFIC_INIT 1
#define CONFIG_IDLE_STACK_SIZE 256
#define CONFIG_BUILD_OUTPUT_BIN 1
#define CONFIG_MP_MAX_NUM_CPUS 1
#define CONFIG_HAS_DTS 1
#define CONFIG_DT_HAS_ARDUINO_HEADER_R3_ENABLED 1
#define CONFIG_DT_HAS_ARM_ARMV8M_MPU_ENABLED 1
#define CONFIG_DT_HAS_ARM_CORTEX_M33_ENABLED 1
#define CONFIG_DT_HAS_ARM_V8M_NVIC_ENABLED 1
#define CONFIG_DT_HAS_FIXED_PARTITIONS_ENABLED 1
#define CONFIG_DT_HAS_GPIO_KEYS_ENABLED 1
#define CONFIG_DT_HAS_GPIO_LEDS_ENABLED 1
#define CONFIG_DT_HAS_MMIO_SRAM_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_MBOX_NRF_IPC_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_ACL_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_CCM_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_CLOCK_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_DPPIC_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_ECB_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_EGU_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_FICR_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_GPIO_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_GPIOTE_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_GPREGRET_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_IEEE802154_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_IPC_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_PINCTRL_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_POWER_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_RADIO_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_RNG_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_SWI_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_TEMP_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_UARTE_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_UICR_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_VMC_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF_WDT_ENABLED 1
#define CONFIG_DT_HAS_NORDIC_NRF53_FLASH_CONTROLLER_ENABLED 1
#define CONFIG_DT_HAS_SOC_NV_FLASH_ENABLED 1
#define CONFIG_DT_HAS_ZEPHYR_BT_HCI_ENTROPY_ENABLED 1
#define CONFIG_DT_HAS_ZEPHYR_IPC_OPENAMP_STATIC_VRINGS_ENABLED 1
#define CONFIG_DT_HAS_ZEPHYR_MEMORY_REGION_ENABLED 1
#define CONFIG_NUM_METAIRQ_PRIORITIES 0
#define CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE 512
#define CONFIG_BOOT_BANNER_STRING "Booting nRF Connect SDK"
#define CONFIG_WARN_EXPERIMENTAL 1
#define CONFIG_PRIVILEGED_STACK_SIZE 1024
#define CONFIG_BT_BUF_CMD_TX_COUNT 10
#define CONFIG_ENTROPY_GENERATOR 1
#define CONFIG_INIT_ARCH_HW_AT_BOOT 1
#define CONFIG_NORDIC_QSPI_NOR_FLASH_LAYOUT_PAGE_SIZE 2048
#define CONFIG_BT_BUF_ACL_TX_SIZE 27
#define CONFIG_BT_BUF_ACL_RX_SIZE 27
#define CONFIG_BT_CTLR_DATA_LENGTH_MAX 27
#define CONFIG_PM_PARTITION_SIZE_PROVISION 0x280
#define CONFIG_PM_PARTITION_SIZE_B0_IMAGE 0x7800
#define CONFIG_SB_VALIDATION_INFO_MAGIC 0x86518483
#define CONFIG_SB_VALIDATION_POINTER_MAGIC 0x6919b47e
#define CONFIG_SB_VALIDATION_INFO_CRYPTO_ID 1
#define CONFIG_SB_VALIDATION_INFO_VERSION 2
#define CONFIG_SB_VALIDATION_METADATA_OFFSET 0
#define CONFIG_SB_VALIDATE_FW_HASH 1
#define CONFIG_BT_MAX_CONN 16
#define CONFIG_BT_CTLR_DF_SUPPORT 1
#define CONFIG_BT_HCI_TX_STACK_SIZE 1024
#define CONFIG_BT_RX_STACK_SIZE 768
#define CONFIG_BT_CTLR_DF 1
#define CONFIG_BT_CTLR_DF_CONN_CTE_RSP 1
#define CONFIG_BT_CTLR_FAL_SIZE 8
#define CONFIG_BT_CENTRAL 1
#define CONFIG_BT_PERIPHERAL 1
#define CONFIG_BT_OBSERVER 1
#define CONFIG_BT_BROADCASTER 1
#define CONFIG_BT_CONN 1
#define CONFIG_BT_REMOTE_VERSION 1
#define CONFIG_BT_PHY_UPDATE 1
#define CONFIG_BT_DATA_LEN_UPDATE 1
#define CONFIG_BT_BUF_ACL_TX_COUNT 7
#define CONFIG_BT_BUF_ACL_RX_COUNT 3
#define CONFIG_BT_BUF_EVT_RX_SIZE 68
#define CONFIG_BT_BUF_EVT_RX_COUNT 3
#define CONFIG_BT_BUF_EVT_DISCARDABLE_SIZE 43
#define CONFIG_BT_BUF_CMD_TX_SIZE 65
#define CONFIG_BT_HAS_HCI_VS 1
#define CONFIG_BT_HCI_VS 1
#define CONFIG_BT_RPA 1
#define CONFIG_BT_ASSERT 1
#define CONFIG_BT_ASSERT_VERBOSE 1
#define CONFIG_BT_DEBUG_NONE 1
#define CONFIG_BT_HCI_TX_PRIO 7
#define CONFIG_BT_HCI_RESERVE 0
#define CONFIG_BT_RECV_BLOCKING 1
#define CONFIG_BT_RX_PRIO 8
#define CONFIG_BT_DRIVER_RX_HIGH_PRIO 6
#define CONFIG_PCD_VERSION_PAGE_BUF_SIZE 2046
#define CONFIG_NRF_CLOUD_CLIENT_ID_SRC_COMPILE_TIME 1
#define CONFIG_NRF_CLOUD_CLIENT_ID "my-client-id"
#define CONFIG_NRF_CLOUD_LOG_OUTPUT_LEVEL 1
#define CONFIG_NRF_CLOUD_LOG_BUF_SIZE 256
#define CONFIG_MPSL_THREAD_COOP_PRIO 8
#define CONFIG_MPSL_WORK_STACK_SIZE 1024
#define CONFIG_MPSL_TIMESLOT_SESSION_COUNT 0
#define CONFIG_PARTITION_MANAGER_ENABLED 1
#define CONFIG_FLASH_MAP_CUSTOM 1
#define CONFIG_SRAM_BASE_ADDRESS 0x21000000
#define CONFIG_PM_EXTERNAL_FLASH_BASE 0x0
#define CONFIG_PM_EXTERNAL_FLASH_PATH ""
#define CONFIG_PM_EXTERNAL_FLASH_SIZE_BITS 0
#define CONFIG_PM_SRAM_BASE 0x21000000
#define CONFIG_PM_SRAM_SIZE 0x10000
#define CONFIG_AUDIO_MODULE_NAME_SIZE 20
#define CONFIG_TFM_CONN_HANDLE_MAX_NUM 8
#define CONFIG_POSIX_MAX_FDS 4
#define CONFIG_NRF_ACL_FLASH_REGION_SIZE 0x800
#define CONFIG_FPROTECT_BLOCK_SIZE 0x800
#define CONFIG_RESET_ON_FATAL_ERROR 1
#define CONFIG_HW_UNIQUE_KEY_PARTITION_SIZE 0x800
#define CONFIG_NRFX_GPIOTE_NUM_OF_EVT_HANDLERS 1
#define CONFIG_ZTEST_MULTICORE_DEFAULT_SETTINGS 1
#define CONFIG_ZEPHYR_NRF_MODULE 1
#define CONFIG_WFA_QT_THREAD_STACK_SIZE 4096
#define CONFIG_ZEPHYR_WFA_QT_CONTROL_APP_MODULE 1
#define CONFIG_BOOT_SIGNATURE_KEY_FILE ""
#define CONFIG_DT_FLASH_WRITE_BLOCK_SIZE 4
#define CONFIG_ZEPHYR_MCUBOOT_MODULE 1
#define CONFIG_ZEPHYR_MBEDTLS_MODULE 1
#define CONFIG_ZEPHYR_TRUSTED_FIRMWARE_M_MODULE 1
#define CONFIG_ZEPHYR_CJSON_MODULE 1
#define CONFIG_ZEPHYR_AZURE_SDK_FOR_C_MODULE 1
#define CONFIG_ZEPHYR_CIRRUS_LOGIC_MODULE 1
#define CONFIG_ZEPHYR_OPENTHREAD_MODULE 1
#define CONFIG_ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE 1
#define CONFIG_ZEPHYR_CANOPENNODE_MODULE 1
#define CONFIG_ZEPHYR_CHRE_MODULE 1
#define CONFIG_HAS_CMSIS_CORE 1
#define CONFIG_HAS_CMSIS_CORE_M 1
#define CONFIG_ZEPHYR_CMSIS_MODULE 1
#define CONFIG_ZEPHYR_FATFS_MODULE 1
#define CONFIG_ZEPHYR_HAL_NORDIC_MODULE 1
#define CONFIG_HAS_NORDIC_DRIVERS 1
#define CONFIG_HAS_NRFX 1
#define CONFIG_NRFX_CLOCK 1
#define CONFIG_NRFX_CLOCK_LFXO_TWO_STAGE_ENABLED 1
#define CONFIG_NRFX_DPPI 1
#define CONFIG_NRFX_GPIOTE 1
#define CONFIG_NRFX_IPC 1
#define CONFIG_ZEPHYR_LIBLC3_MODULE 1
#define CONFIG_ZEPHYR_LITTLEFS_MODULE 1
#define CONFIG_ZEPHYR_LORAMAC_NODE_MODULE 1
#define CONFIG_LV_COLOR_DEPTH_16 1
#define CONFIG_LV_COLOR_DEPTH 16
#define CONFIG_LV_COLOR_MIX_ROUND_OFS 128
#define CONFIG_LV_COLOR_CHROMA_KEY_HEX 0x00FF00
#define CONFIG_LV_MEM_SIZE_KILOBYTES 32
#define CONFIG_LV_MEM_ADDR 0x0
#define CONFIG_LV_MEM_BUF_MAX_NUM 16
#define CONFIG_LV_DISP_DEF_REFR_PERIOD 30
#define CONFIG_LV_INDEV_DEF_READ_PERIOD 30
#define CONFIG_LV_DRAW_COMPLEX 1
#define CONFIG_LV_SHADOW_CACHE_SIZE 0
#define CONFIG_LV_CIRCLE_CACHE_SIZE 4
#define CONFIG_LV_LAYER_SIMPLE_BUF_SIZE 24576
#define CONFIG_LV_IMG_CACHE_DEF_SIZE 0
#define CONFIG_LV_GRADIENT_MAX_STOPS 2
#define CONFIG_LV_GRAD_CACHE_DEF_SIZE 0
#define CONFIG_LV_DISP_ROT_MAX_BUF 10240
#define CONFIG_LV_USE_ASSERT_NULL 1
#define CONFIG_LV_USE_ASSERT_MALLOC 1
#define CONFIG_LV_ASSERT_HANDLER_INCLUDE "assert.h"
#define CONFIG_LV_USE_USER_DATA 1
#define CONFIG_LV_ATTRIBUTE_MEM_ALIGN_SIZE 1
#define CONFIG_LV_FONT_MONTSERRAT_14 1
#define CONFIG_LV_FONT_DEFAULT_MONTSERRAT_14 1
#define CONFIG_LV_USE_FONT_PLACEHOLDER 1
#define CONFIG_LV_TXT_ENC_UTF8 1
#define CONFIG_LV_TXT_BREAK_CHARS " ,.;:-_"
#define CONFIG_LV_TXT_LINE_BREAK_LONG_LEN 0
#define CONFIG_LV_TXT_COLOR_CMD "#"
#define CONFIG_LV_USE_ARC 1
#define CONFIG_LV_USE_BAR 1
#define CONFIG_LV_USE_BTN 1
#define CONFIG_LV_USE_BTNMATRIX 1
#define CONFIG_LV_USE_CANVAS 1
#define CONFIG_LV_USE_CHECKBOX 1
#define CONFIG_LV_USE_DROPDOWN 1
#define CONFIG_LV_USE_IMG 1
#define CONFIG_LV_USE_LABEL 1
#define CONFIG_LV_LABEL_TEXT_SELECTION 1
#define CONFIG_LV_LABEL_LONG_TXT_HINT 1
#define CONFIG_LV_USE_LINE 1
#define CONFIG_LV_USE_ROLLER 1
#define CONFIG_LV_ROLLER_INF_PAGES 7
#define CONFIG_LV_USE_SLIDER 1
#define CONFIG_LV_USE_SWITCH 1
#define CONFIG_LV_USE_TEXTAREA 1
#define CONFIG_LV_TEXTAREA_DEF_PWD_SHOW_TIME 1500
#define CONFIG_LV_USE_TABLE 1
#define CONFIG_LV_USE_ANIMIMG 1
#define CONFIG_LV_USE_CALENDAR 1
#define CONFIG_LV_USE_CALENDAR_HEADER_ARROW 1
#define CONFIG_LV_USE_CALENDAR_HEADER_DROPDOWN 1
#define CONFIG_LV_USE_CHART 1
#define CONFIG_LV_USE_COLORWHEEL 1
#define CONFIG_LV_USE_IMGBTN 1
#define CONFIG_LV_USE_KEYBOARD 1
#define CONFIG_LV_USE_LED 1
#define CONFIG_LV_USE_LIST 1
#define CONFIG_LV_USE_MENU 1
#define CONFIG_LV_USE_METER 1
#define CONFIG_LV_USE_MSGBOX 1
#define CONFIG_LV_USE_SPAN 1
#define CONFIG_LV_SPAN_SNIPPET_STACK_SIZE 64
#define CONFIG_LV_USE_SPINBOX 1
#define CONFIG_LV_USE_SPINNER 1
#define CONFIG_LV_USE_TABVIEW 1
#define CONFIG_LV_USE_TILEVIEW 1
#define CONFIG_LV_USE_WIN 1
#define CONFIG_LV_USE_THEME_DEFAULT 1
#define CONFIG_LV_THEME_DEFAULT_GROW 1
#define CONFIG_LV_THEME_DEFAULT_TRANSITION_TIME 80
#define CONFIG_LV_USE_THEME_BASIC 1
#define CONFIG_LV_USE_FLEX 1
#define CONFIG_LV_USE_GRID 1
#define CONFIG_LV_USE_SNAPSHOT 1
#define CONFIG_LV_BUILD_EXAMPLES 1
#define CONFIG_ZEPHYR_LVGL_MODULE 1
#define CONFIG_ZEPHYR_LZ4_MODULE 1
#define CONFIG_ZEPHYR_NANOPB_MODULE 1
#define CONFIG_ZEPHYR_PICOLIBC_MODULE 1
#define CONFIG_ZEPHYR_SEGGER_MODULE 1
#define CONFIG_HAS_SEGGER_RTT 1
#define CONFIG_ZEPHYR_TRACERECORDER_MODULE 1
#define CONFIG_ZEPHYR_UOSCORE_UEDHOC_MODULE 1
#define CONFIG_ZEPHYR_ZCBOR_MODULE 1
#define CONFIG_ZEPHYR_ZSCILIB_MODULE 1
#define CONFIG_NRF_MODEM_SHMEM_CTRL_SIZE 0x4e8
#define CONFIG_NRF_802154_SOURCE_NRFXLIB 1
#define CONFIG_LC3_ENC_CHAN_MAX 1
#define CONFIG_LC3_DEC_CHAN_MAX 1
#define CONFIG_LC3_ENC_SAMPLE_RATE_8KHZ_SUPPORT 1
#define CONFIG_LC3_ENC_SAMPLE_RATE_16KHZ_SUPPORT 1
#define CONFIG_LC3_ENC_SAMPLE_RATE_24KHZ_SUPPORT 1
#define CONFIG_LC3_ENC_SAMPLE_RATE_32KHZ_SUPPORT 1
#define CONFIG_LC3_ENC_SAMPLE_RATE_441KHZ_SUPPORT 1
#define CONFIG_LC3_ENC_SAMPLE_RATE_48KHZ_SUPPORT 1
#define CONFIG_LC3_DEC_SAMPLE_RATE_8KHZ_SUPPORT 1
#define CONFIG_LC3_DEC_SAMPLE_RATE_16KHZ_SUPPORT 1
#define CONFIG_LC3_DEC_SAMPLE_RATE_24KHZ_SUPPORT 1
#define CONFIG_LC3_DEC_SAMPLE_RATE_32KHZ_SUPPORT 1
#define CONFIG_LC3_DEC_SAMPLE_RATE_441KHZ_SUPPORT 1
#define CONFIG_LC3_DEC_SAMPLE_RATE_48KHZ_SUPPORT 1
#define CONFIG_ZEPHYR_NRFXLIB_MODULE 1
#define CONFIG_ZEPHYR_CONNECTEDHOMEIP_MODULE 1
#define CONFIG_LIBMETAL 1
#define CONFIG_LIBMETAL_SRC_PATH "libmetal"
#define CONFIG_OPENAMP 1
#define CONFIG_OPENAMP_SRC_PATH "open-amp"
#define CONFIG_OPENAMP_MASTER 1
#define CONFIG_OPENAMP_SLAVE 1
#define CONFIG_TINYCRYPT_AES 1
#define CONFIG_BOARD_REVISION "$BOARD_REVISION"
#define CONFIG_BOARD_NRF5340DK_NRF5340_CPUNET 1
#define CONFIG_DOMAIN_CPUAPP_BOARD "nrf5340dk_nrf5340_cpuapp"
#define CONFIG_SOC_SERIES_NRF53X 1
#define CONFIG_CPU_HAS_ARM_MPU 1
#define CONFIG_HAS_SWO 1
#define CONFIG_SOC_FAMILY "nordic_nrf"
#define CONFIG_GPIO_INIT_PRIORITY 40
#define CONFIG_SOC_FAMILY_NRF 1
#define CONFIG_HAS_HW_NRF_ACL 1
#define CONFIG_HAS_HW_NRF_CCM 1
#define CONFIG_HAS_HW_NRF_CCM_LFLEN_8BIT 1
#define CONFIG_HAS_HW_NRF_CCM_HEADERMASK 1
#define CONFIG_HAS_HW_NRF_CLOCK 1
#define CONFIG_HAS_HW_NRF_DPPIC 1
#define CONFIG_HAS_HW_NRF_ECB 1
#define CONFIG_HAS_HW_NRF_EGU0 1
#define CONFIG_HAS_HW_NRF_GPIO0 1
#define CONFIG_HAS_HW_NRF_GPIO1 1
#define CONFIG_HAS_HW_NRF_GPIOTE 1
#define CONFIG_HAS_HW_NRF_IPC 1
#define CONFIG_HAS_HW_NRF_NVMC_PE 1
#define CONFIG_HAS_HW_NRF_POWER 1
#define CONFIG_HAS_HW_NRF_RADIO_BLE_2M 1
#define CONFIG_HAS_HW_NRF_RADIO_BLE_CODED 1
#define CONFIG_HAS_HW_NRF_RADIO_DFE 1
#define CONFIG_HAS_HW_NRF_RADIO_IEEE802154 1
#define CONFIG_HAS_HW_NRF_RNG 1
#define CONFIG_HAS_HW_NRF_SWI0 1
#define CONFIG_HAS_HW_NRF_SWI1 1
#define CONFIG_HAS_HW_NRF_SWI2 1
#define CONFIG_HAS_HW_NRF_SWI3 1
#define CONFIG_HAS_HW_NRF_TEMP 1
#define CONFIG_HAS_HW_NRF_UARTE0 1
#define CONFIG_HAS_HW_NRF_VMC 1
#define CONFIG_HAS_HW_NRF_WDT0 1
#define CONFIG_SOC_NRF5340_CPUNET 1
#define CONFIG_SOC_NRF5340_CPUNET_QKAA 1
#define CONFIG_SOC_NRF53_ANOMALY_160_WORKAROUND_NEEDED 1
#define CONFIG_SOC_NRF53_ANOMALY_160_WORKAROUND 1
#define CONFIG_SOC_NRF53_RTC_PRETICK 1
#define CONFIG_SOC_NRF53_RTC_PRETICK_IPC_CH_FROM_NET 10
#define CONFIG_SOC_NRF53_RTC_PRETICK_IPC_CH_TO_NET 11
#define CONFIG_NRF_ENABLE_CACHE 1
#define CONFIG_NRF_RTC_TIMER_USER_CHAN_COUNT 0
#define CONFIG_NRF_SOC_SECURE_SUPPORTED 1
#define CONFIG_NRF_APPROTECT_USE_UICR 1
#define CONFIG_SOC_COMPATIBLE_NRF 1
#define CONFIG_SOC_COMPATIBLE_NRF53X 1
#define CONFIG_SOC_COMPATIBLE_NRF5340_CPUNET 1
#define CONFIG_ARCH "arm"
#define CONFIG_CPU_CORTEX 1
#define CONFIG_ARM_ON_ENTER_CPU_IDLE_HOOK 1
#define CONFIG_ARM_ON_ENTER_CPU_IDLE_PREPARE_HOOK 1
#define CONFIG_ARM_ON_EXIT_CPU_IDLE 1
#define CONFIG_CPU_CORTEX_M 1
#define CONFIG_ISA_THUMB2 1
#define CONFIG_ASSEMBLER_ISA_THUMB2 1
#define CONFIG_COMPILER_ISA_THUMB2 1
#define CONFIG_STACK_ALIGN_DOUBLE_WORD 1
#define CONFIG_FAULT_DUMP 2
#define CONFIG_BUILTIN_STACK_GUARD 1
#define CONFIG_ARM_STACK_PROTECTION 1
#define CONFIG_FP16 1
#define CONFIG_FP16_IEEE 1
#define CONFIG_CPU_CORTEX_M33 1
#define CONFIG_CPU_CORTEX_M_HAS_SYSTICK 1
#define CONFIG_CPU_CORTEX_M_HAS_DWT 1
#define CONFIG_CPU_CORTEX_M_HAS_BASEPRI 1
#define CONFIG_CPU_CORTEX_M_HAS_VTOR 1
#define CONFIG_CPU_CORTEX_M_HAS_SPLIM 1
#define CONFIG_CPU_CORTEX_M_HAS_PROGRAMMABLE_FAULT_PRIOS 1
#define CONFIG_CPU_CORTEX_M_HAS_CMSE 1
#define CONFIG_ARMV7_M_ARMV8_M_MAINLINE 1
#define CONFIG_ARMV8_M_MAINLINE 1
#define CONFIG_GEN_ISR_TABLES 1
#define CONFIG_NULL_POINTER_EXCEPTION_DETECTION_NONE 1
#define CONFIG_GEN_IRQ_VECTOR_TABLE 1
#define CONFIG_ARM_MPU 1
#define CONFIG_ARM_MPU_REGION_MIN_ALIGN_AND_SIZE 32
#define CONFIG_CUSTOM_SECTION_MIN_ALIGN_SIZE 32
#define CONFIG_ARM 1
#define CONFIG_ARCH_IS_SET 1
#define CONFIG_LITTLE_ENDIAN 1
#define CONFIG_HW_STACK_PROTECTION 1
#define CONFIG_KOBJECT_TEXT_AREA 256
#define CONFIG_KOBJECT_DATA_AREA_RESERVE_EXTRA_PERCENT 100
#define CONFIG_KOBJECT_RODATA_AREA_EXTRA_BYTES 16
#define CONFIG_GEN_PRIV_STACKS 1
#define CONFIG_ARCH_IRQ_VECTOR_TABLE_ALIGN 4
#define CONFIG_IRQ_VECTOR_TABLE_JUMP_BY_ADDRESS 1
#define CONFIG_GEN_SW_ISR_TABLE 1
#define CONFIG_ARCH_SW_ISR_TABLE_ALIGN 4
#define CONFIG_GEN_IRQ_START_VECTOR 0
#define CONFIG_ARCH_HAS_SINGLE_THREAD_SUPPORT 1
#define CONFIG_ARCH_HAS_TIMING_FUNCTIONS 1
#define CONFIG_ARCH_HAS_STACK_PROTECTION 1
#define CONFIG_ARCH_HAS_USERSPACE 1
#define CONFIG_ARCH_HAS_EXECUTABLE_PAGE_BIT 1
#define CONFIG_ARCH_HAS_RAMFUNC_SUPPORT 1
#define CONFIG_ARCH_HAS_NESTED_EXCEPTION_DETECTION 1
#define CONFIG_ARCH_SUPPORTS_COREDUMP 1
#define CONFIG_ARCH_SUPPORTS_ARCH_HW_INIT 1
#define CONFIG_ARCH_HAS_EXTRA_EXCEPTION_INFO 1
#define CONFIG_ARCH_HAS_THREAD_LOCAL_STORAGE 1
#define CONFIG_ARCH_HAS_SUSPEND_TO_RAM 1
#define CONFIG_ARCH_HAS_THREAD_ABORT 1
#define CONFIG_ARCH_HAS_CODE_DATA_RELOCATION 1
#define CONFIG_CPU_HAS_MPU 1
#define CONFIG_MPU 1
#define CONFIG_MPU_REQUIRES_NON_OVERLAPPING_REGIONS 1
#define CONFIG_MPU_GAP_FILLING 1
#define CONFIG_SRAM_REGION_PERMISSIONS 1
#define CONFIG_TOOLCHAIN_HAS_BUILTIN_FFS 1
#define CONFIG_MULTITHREADING 1
#define CONFIG_NUM_COOP_PRIORITIES 16
#define CONFIG_NUM_PREEMPT_PRIORITIES 15
#define CONFIG_MAIN_THREAD_PRIORITY 0
#define CONFIG_COOP_ENABLED 1
#define CONFIG_PREEMPT_ENABLED 1
#define CONFIG_PRIORITY_CEILING -127
#define CONFIG_ISR_STACK_SIZE 2048
#define CONFIG_THREAD_STACK_INFO 1
#define CONFIG_LIBC_ERRNO 1
#define CONFIG_ERRNO 1
#define CONFIG_SCHED_DUMB 1
#define CONFIG_WAITQ_DUMB 1
#define CONFIG_BOOT_BANNER 1
#define CONFIG_BOOT_DELAY 0
#define CONFIG_SYSTEM_WORKQUEUE_PRIORITY -1
#define CONFIG_BARRIER_OPERATIONS_ARCH 1
#define CONFIG_ATOMIC_OPERATIONS_BUILTIN 1
#define CONFIG_TIMESLICING 1
#define CONFIG_TIMESLICE_SIZE 0
#define CONFIG_TIMESLICE_PRIORITY 0
#define CONFIG_POLL 1
#define CONFIG_NUM_MBOX_ASYNC_MSGS 10
#define CONFIG_KERNEL_MEM_POOL 1
#define CONFIG_ARCH_HAS_CUSTOM_SWAP_TO_MAIN 1
#define CONFIG_SWAP_NONATOMIC 1
#define CONFIG_SYS_CLOCK_EXISTS 1
#define CONFIG_TIMEOUT_64BIT 1
#define CONFIG_SYS_CLOCK_MAX_TIMEOUT_DAYS 365
#define CONFIG_XIP 1
#define CONFIG_KERNEL_INIT_PRIORITY_OBJECTS 30
#define CONFIG_KERNEL_INIT_PRIORITY_DEFAULT 40
#define CONFIG_KERNEL_INIT_PRIORITY_DEVICE 50
#define CONFIG_APPLICATION_INIT_PRIORITY 90
#define CONFIG_STACK_POINTER_RANDOM 0
#define CONFIG_MP_NUM_CPUS 1
#define CONFIG_TICKLESS_KERNEL 1
#define CONFIG_TOOLCHAIN_SUPPORTS_THREAD_LOCAL_STORAGE 1
#define CONFIG_THREAD_LOCAL_STORAGE 1
#define CONFIG_CLOCK_CONTROL_NRF 1
#define CONFIG_CLOCK_CONTROL_NRF_K32SRC_XTAL 1
#define CONFIG_CLOCK_CONTROL_NRF_K32SRC_50PPM 1
#define CONFIG_CLOCK_CONTROL_NRF_ACCURACY 50
#define CONFIG_CONSOLE 1
#define CONFIG_CONSOLE_INPUT_MAX_LINE_LEN 128
#define CONFIG_CONSOLE_HAS_DRIVER 1
#define CONFIG_CONSOLE_INIT_PRIORITY 60
#define CONFIG_UART_CONSOLE 1
#define CONFIG_ENTROPY_INIT_PRIORITY 50
#define CONFIG_ENTROPY_NRF5_RNG 1
#define CONFIG_ENTROPY_NRF5_BIAS_CORRECTION 1
#define CONFIG_ENTROPY_NRF5_THR_POOL_SIZE 8
#define CONFIG_ENTROPY_NRF5_THR_THRESHOLD 4
#define CONFIG_ENTROPY_NRF5_ISR_POOL_SIZE 16
#define CONFIG_ENTROPY_NRF5_ISR_THRESHOLD 12
#define CONFIG_ENTROPY_HAS_DRIVER 1
#define CONFIG_GPIO_NRFX 1
#define CONFIG_GPIO_NRFX_INTERRUPT 1
#define CONFIG_INTC_INIT_PRIORITY 40
#define CONFIG_1ST_LEVEL_INTERRUPT_BITS 8
#define CONFIG_2ND_LEVEL_INTERRUPT_BITS 8
#define CONFIG_3RD_LEVEL_INTERRUPT_BITS 8
#define CONFIG_MBOX 1
#define CONFIG_MBOX_INIT_PRIORITY 40
#define CONFIG_PINCTRL_STORE_REG 1
#define CONFIG_PINCTRL_NRF 1
#define CONFIG_SERIAL_HAS_DRIVER 1
#define CONFIG_SERIAL_SUPPORT_ASYNC 1
#define CONFIG_SERIAL_SUPPORT_INTERRUPT 1
#define CONFIG_UART_USE_RUNTIME_CONFIGURE 1
#define CONFIG_UART_NRFX 1
#define CONFIG_UART_NRFX_UARTE 1
#define CONFIG_UART_0_ENHANCED_POLL_OUT 1
#define CONFIG_UART_0_NRF_TX_BUFFER_SIZE 32
#define CONFIG_UART_ENHANCED_POLL_OUT 1
#define CONFIG_SYSTEM_CLOCK_INIT_PRIORITY 0
#define CONFIG_TICKLESS_CAPABLE 1
#define CONFIG_SYSTEM_TIMER_HAS_DISABLE_SUPPORT 1
#define CONFIG_SYSTEM_CLOCK_WAIT_FOR_STABILITY 1
#define CONFIG_FULL_LIBC_SUPPORTED 1
#define CONFIG_MINIMAL_LIBC_SUPPORTED 1
#define CONFIG_NEWLIB_LIBC_SUPPORTED 1
#define CONFIG_PICOLIBC_SUPPORTED 1
#define CONFIG_PICOLIBC 1
#define CONFIG_HAS_NEWLIB_LIBC_NANO 1
#define CONFIG_COMMON_LIBC_ABORT 1
#define CONFIG_COMMON_LIBC_MALLOC 1
#define CONFIG_COMMON_LIBC_MALLOC_ARENA_SIZE -1
#define CONFIG_COMMON_LIBC_CALLOC 1
#define CONFIG_COMMON_LIBC_REALLOCARRAY 1
#define CONFIG_PICOLIBC_HEAP_SIZE -2
#define CONFIG_PICOLIBC_IO_LONG_LONG 1
#define CONFIG_STDOUT_CONSOLE 1
#define CONFIG_NOTIFY 1
#define CONFIG_ONOFF 1
#define CONFIG_REBOOT 1
#define CONFIG_CBPRINTF_COMPLETE 1
#define CONFIG_CBPRINTF_FULL_INTEGRAL 1
#define CONFIG_CBPRINTF_N_SPECIFIER 1
#define CONFIG_SYS_HEAP_ALLOC_LOOPS 3
#define CONFIG_SYS_HEAP_AUTO 1
#define CONFIG_SEM_VALUE_MAX 32767
#define CONFIG_MAX_TIMER_COUNT 5
#define CONFIG_TIMER_CREATE_WAIT 100
#define CONFIG_TIMER_DELAYTIMER_MAX 20
#define CONFIG_ACPI_HID_LEN_MAX 12
#define CONFIG_LIBGCC_RTLIB 1
#define CONFIG_BT 1
#define CONFIG_BT_HCI 1
#define CONFIG_BT_HCI_RAW 1
#define CONFIG_BT_HCI_RAW_RESERVE 1
#define CONFIG_BT_CONN_TX 1
#define CONFIG_BT_CTLR_LE_ENC_SUPPORT 1
#define CONFIG_BT_CTLR_CONN_PARAM_REQ_SUPPORT 1
#define CONFIG_BT_CTLR_EXT_REJ_IND_SUPPORT 1
#define CONFIG_BT_CTLR_PER_INIT_FEAT_XCHG_SUPPORT 1
#define CONFIG_BT_CTLR_DATA_LEN_UPDATE_SUPPORT 1
#define CONFIG_BT_CTLR_PRIVACY_SUPPORT 1
#define CONFIG_BT_CTLR_EXT_SCAN_FP_SUPPORT 1
#define CONFIG_BT_CTLR_PHY_UPDATE_SUPPORT 1
#define CONFIG_BT_CTLR_PHY_2M_SUPPORT 1
#define CONFIG_BT_CTLR_PHY_CODED_SUPPORT 1
#define CONFIG_BT_CTLR_ADV_EXT_SUPPORT 1
#define CONFIG_BT_CTLR_ADV_PERIODIC_SUPPORT 1
#define CONFIG_BT_CTLR_SYNC_PERIODIC_SUPPORT 1
#define CONFIG_BT_CTLR_ADV_ISO_SUPPORT 1
#define CONFIG_BT_CTLR_SYNC_ISO_SUPPORT 1
#define CONFIG_BT_CTLR_CENTRAL_ISO_SUPPORT 1
#define CONFIG_BT_CTLR_PERIPHERAL_ISO_SUPPORT 1
#define CONFIG_BT_CTLR_CHAN_SEL_2_SUPPORT 1
#define CONFIG_BT_CTLR_MIN_USED_CHAN_SUPPORT 1
#define CONFIG_BT_CTLR_SCA_UPDATE_SUPPORT 1
#define CONFIG_BT_CTLR_CONN_RSSI_SUPPORT 1
#define CONFIG_BT_LL_SW_SPLIT 1
#define CONFIG_BT_CTLR_CRYPTO 1
#define CONFIG_BT_CTLR_HCI_VS_BUILD_INFO ""
#define CONFIG_BT_CTLR_DUP_FILTER_LEN 16
#define CONFIG_BT_CTLR_RX_BUFFERS 6
#define CONFIG_BT_CTLR_TX_PWR_0 1
#define CONFIG_BT_CTLR_TX_PWR_DBM 0
#define CONFIG_BT_CTLR_LE_ENC 1
#define CONFIG_BT_CTLR_CONN_PARAM_REQ 1
#define CONFIG_BT_CTLR_EXT_REJ_IND 1
#define CONFIG_BT_CTLR_PER_INIT_FEAT_XCHG 1
#define CONFIG_BT_CTLR_LE_PING 1
#define CONFIG_BT_CTLR_DATA_LENGTH 1
#define CONFIG_BT_CTLR_PHY 1
#define CONFIG_BT_CTLR_MIN_USED_CHAN 1
#define CONFIG_BT_CTLR_CONN_RSSI 1
#define CONFIG_BT_CTLR_FILTER_ACCEPT_LIST 1
#define CONFIG_BT_CTLR_PRIVACY 1
#define CONFIG_BT_CTLR_RL_SIZE 8
#define CONFIG_BT_CTLR_EXT_SCAN_FP 1
#define CONFIG_BT_CTLR_PHY_2M 1
#define CONFIG_BT_CTLR_PHY_CODED 1
#define CONFIG_BT_CTLR_CHAN_SEL_2 1
#define CONFIG_BT_CTLR_DF_CTE_TX_SUPPORT 1
#define CONFIG_BT_CTLR_DF_CTE_RX_SUPPORT 1
#define CONFIG_BT_CTLR_DF_CTE_RX_SAMPLE_1US_SUPPORT 1
#define CONFIG_BT_CTLR_DF_ANT_SWITCH_2US_SUPPORT 1
#define CONFIG_BT_CTLR_DF_ANT_SWITCH_1US_SUPPORT 1
#define CONFIG_BT_CTLR_CTEINLINE_SUPPORT 1
#define CONFIG_BT_CTLR_DF_CTE_TX 1
#define CONFIG_BT_CTLR_DF_CTE_RX_SAMPLE_1US 1
#define CONFIG_BT_CTLR_DF_ANT_SWITCH_1US 1
#define CONFIG_BT_CTLR_DF_ANT_SWITCH_TX 1
#define CONFIG_BT_CTLR_DF_ANT_SWITCH_RX 1
#define CONFIG_BT_CTLR_DF_CTE_RX 1
#define CONFIG_BT_CTLR_DF_CONN_CTE_TX 1
#define CONFIG_BT_CTLR_DF_CONN_CTE_RX 1
#define CONFIG_BT_CTLR_DF_MAX_ANT_SW_PATTERN_LEN 12
#define CONFIG_BT_CTLR_DF_INIT_ANT_SEL_GPIOS 1
#define CONFIG_BT_CTLR_DF_SWITCH_OFFSET 0
#define CONFIG_BT_CTLR_DF_SAMPLE_OFFSET_PHY_1M_SAMPLING_1US 1
#define CONFIG_BT_CTLR_DF_SAMPLE_OFFSET_PHY_2M_SAMPLING_1US 15
#define CONFIG_BT_CTLR_DF_SAMPLE_OFFSET_PHY_1M_SAMPLING_2US 6
#define CONFIG_BT_CTLR_DF_SAMPLE_OFFSET_PHY_2M_SAMPLING_2US 20
#define CONFIG_BT_CTLR_DF_PHYEND_OFFSET_COMPENSATION_ENABLE 1
#define CONFIG_BT_CTLR_DF_IQ_SAMPLES_CONVERT_USE_8_LSB 1
#define CONFIG_BT_LLL_VENDOR_NORDIC 1
#define CONFIG_BT_CTLR_XTAL_ADVANCED_SUPPORT 1
#define CONFIG_BT_CTLR_SCHED_ADVANCED_SUPPORT 1
#define CONFIG_BT_CTLR_TIFS_HW_SUPPORT 1
#define CONFIG_BT_CTLR_ULL_LLL_PRIO_SUPPORT 1
#define CONFIG_BT_CTLR_RX_PRIO_STACK_SIZE 448
#define CONFIG_BT_CTLR_COMPANY_ID 0x05F1
#define CONFIG_BT_CTLR_SUBVERSION_NUMBER 0xFFFF
#define CONFIG_BT_CTLR_AD_DATA_BACKUP 1
#define CONFIG_BT_CTLR_CHECK_SAME_PEER_CONN 1
#define CONFIG_BT_CTLR_ADVANCED_FEATURES 1
#define CONFIG_BT_CTLR_ADV_DATA_BUF_MAX 1
#define CONFIG_BT_CTLR_OPTIMIZE_FOR_SPEED 1
#define CONFIG_BT_CTLR_XTAL_ADVANCED 1
#define CONFIG_BT_CTLR_XTAL_THRESHOLD 1500
#define CONFIG_BT_CTLR_SCHED_ADVANCED 1
#define CONFIG_BT_CTLR_ASSERT_OVERHEAD_START 1
#define CONFIG_BT_CTLR_CENTRAL_SPACING 0
#define CONFIG_BT_CTLR_CENTRAL_RESERVE_MAX 1
#define CONFIG_BT_CTLR_EVENT_OVERHEAD_RESERVE_MAX 1
#define CONFIG_BT_CTLR_SLOT_RESERVATION_UPDATE 1
#define CONFIG_BT_CTLR_LLL_PRIO 0
#define CONFIG_BT_CTLR_ULL_HIGH_PRIO 1
#define CONFIG_BT_CTLR_ULL_LOW_PRIO 1
#define CONFIG_BT_CTLR_RADIO_ENABLE_FAST 1
#define CONFIG_BT_CTLR_PARAM_CHECK 1
#define CONFIG_BT_CTLR_LLCP_CONN 16
#define CONFIG_BT_CTLR_LLCP_TX_PER_CONN_TX_CTRL_BUF_NUM_MAX 4
#define CONFIG_BT_CTLR_LLCP_PER_CONN_TX_CTRL_BUF_NUM 4
#define CONFIG_BT_CTLR_LLCP_COMMON_TX_CTRL_BUF_NUM 0
#define CONFIG_BT_CTLR_LLCP_LOCAL_PROC_CTX_BUF_NUM 6
#define CONFIG_BT_CTLR_LLCP_REMOTE_PROC_CTX_BUF_NUM 16
#define CONFIG_BT_CTLR_LLID_DATA_START_EMPTY 1
#define CONFIG_BT_CTLR_THROUGHPUT 1
#define CONFIG_BT_CTLR_FORCE_MD_COUNT 1
#define CONFIG_BT_CTLR_FORCE_MD_AUTO 1
#define CONFIG_BT_CTLR_CONN_RANDOM_FORCE 1
#define CONFIG_BT_MAYFLY_YIELD_AFTER_CALL 1
#define CONFIG_BT_TICKER_UPDATE 1
#define CONFIG_BT_TICKER_NEXT_SLOT_GET 1
#define CONFIG_BT_TICKER_REMAINDER_GET 1
#define CONFIG_BT_TICKER_NEXT_SLOT_GET_MATCH 1
#define CONFIG_BT_TICKER_EXT 1
#define CONFIG_BT_CTLR_DTM_HCI_SUPPORT 1
#define CONFIG_BT_COMPANY_ID 0x05F1
#define CONFIG_PRINTK 1
#define CONFIG_EARLY_CONSOLE 1
#define CONFIG_ASSERT_VERBOSE 1
#define CONFIG_IPC_SERVICE 1
#define CONFIG_IPC_SERVICE_REG_BACKEND_PRIORITY 46
#define CONFIG_IPC_SERVICE_BACKEND_RPMSG 1
#define CONFIG_IPC_SERVICE_BACKEND_RPMSG_WQ_STACK_SIZE 1024
#define CONFIG_IPC_SERVICE_BACKEND_RPMSG_NUM_ENDPOINTS_PER_INSTANCE 2
#define CONFIG_IPC_SERVICE_RPMSG 1
#define CONFIG_IPC_SERVICE_STATIC_VRINGS 1
#define CONFIG_IPC_SERVICE_STATIC_VRINGS_MEM_ALIGNMENT 4
#define CONFIG_NET_BUF 1
#define CONFIG_ENTROPY_DEVICE_RANDOM_GENERATOR 1
#define CONFIG_CSPRING_ENABLED 1
#define CONFIG_HARDWARE_DEVICE_CS_GENERATOR 1
#define CONFIG_COVERAGE_GCOV_HEAP_SIZE 16384
#define CONFIG_TOOLCHAIN_ZEPHYR_0_16 1
#define CONFIG_TOOLCHAIN_ZEPHYR_SUPPORTS_THREAD_LOCAL_STORAGE 1
#define CONFIG_LINKER_ORPHAN_SECTION_WARN 1
#define CONFIG_HAS_FLASH_LOAD_OFFSET 1
#define CONFIG_LD_LINKER_SCRIPT_SUPPORTED 1
#define CONFIG_LD_LINKER_TEMPLATE 1
#define CONFIG_KERNEL_ENTRY "__start"
#define CONFIG_LINKER_SORT_BY_ALIGNMENT 1
#define CONFIG_SRAM_OFFSET 0x0
#define CONFIG_LINKER_GENERIC_SECTIONS_PRESENT_AT_BOOT 1
#define CONFIG_LINKER_LAST_SECTION_ID 1
#define CONFIG_LINKER_LAST_SECTION_ID_PATTERN 0xE015E015
#define CONFIG_LINKER_USE_RELAX 1
#define CONFIG_SIZE_OPTIMIZATIONS 1
#define CONFIG_COMPILER_TRACK_MACRO_EXPANSION 1
#define CONFIG_COMPILER_COLOR_DIAGNOSTICS 1
#define CONFIG_FORTIFY_SOURCE_COMPILE_TIME 1
#define CONFIG_COMPILER_OPT ""
#define CONFIG_RUNTIME_ERROR_CHECKS 1
#define CONFIG_KERNEL_BIN_NAME "zephyr"
#define CONFIG_OUTPUT_STAT 1
#define CONFIG_OUTPUT_PRINT_MEMORY_USAGE 1
#define CONFIG_BUILD_OUTPUT_STRIP_PATHS 1
#define CONFIG_CHECK_INIT_PRIORITIES 1
#define CONFIG_WARN_DEPRECATED 1
#define CONFIG_EXPERIMENTAL 1
#define CONFIG_ENFORCE_ZEPHYR_STDINT 1
#define CONFIG_COMPAT_INCLUDES 1
