#cmakedefine TFM_ISOLATION_LEVEL @TFM_ISOLATION_LEVEL@

#cmakedefine CONFIG_TFM_SPM_BACKEND @CONFIG_TFM_SPM_BACKEND@

#cmakedefine01 TFM_PARTITION_NS_AGENT_MAILBOX

#cmakedefine01 TFM_PARTITION_PROTECTED_STORAGE

#cmakedefine01 TFM_PARTITION_INTERNAL_TRUSTED_STORAGE

#cmakedefine01 TFM_PARTITION_CRYPTO

#cmakedefine01 TFM_PARTITION_PLATFORM

#cmakedefine01 TFM_PARTITION_INITIAL_ATTESTATION

#cmakedefine01 TFM_PARTITION_FIRMWARE_UPDATE

#cmakedefine PS_STACK_SIZE @PS_STACK_SIZE@

#cmakedefine ITS_STACK_SIZE @ITS_STACK_SIZE@

#cmakedefine CRYPTO_STACK_SIZE @CRYPTO_STACK_SIZE@

#cmakedefine PLATFORM_SP_STACK_SIZE @PLATFORM_SP_STACK_SIZE@

#cmakedefine ATTEST_STACK_SIZE @ATTEST_STACK_SIZE@

#cmakedefine FWU_STACK_SIZE @FWU_STACK_SIZE@

