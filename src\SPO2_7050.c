
// #include "database.h"
// #include "AWT_SENSORS_DATA.h"
// #include "event_handler.h"
// #include "AS7050_sensor.h"
// #include "errorDefinitions.h"
// #include "as7050_app_manager.h"
// #include "as7050_chiplib.h"
// #include "bio_spo2_a0_typedefs.h"
// #include "Awt_lsm6dsox.h"
// #include "7050_calibrate.h"
#include <zephyr/as7058/database.h>
#include <zephyr/as7058/AS7058_sensor.h>
#include <zephyr/As7050/as7050_app_manager/inc/app_mngr/as7050_app_manager.h>
#include <zephyr/As7050/bio-app-handler/inc/spo2_a0/bio_spo2_a0_typedefs.h>
#include <zephyr/As7050/inc/as7050_chiplib.h>

#define SPO2_DEBUG_PRINT 					0
#define SPO2_MIN 							60
#define SPO2_MAX 							100
#define SKIP_VALUES 						10
#define SPO2_AVG_VALUES						5
#define INALID_DATA_COUNT 					200
#define SPO2_Reading_Timeout 				10000
#define SPO2_LOW 							95
#define BASE_VALUE 							80
#define ACCELROMETER 						1

void Extract_Ppg_Data(SENSOR_DATA_STRUCT *RAW);
static void Spo2_Outlier_Elimination(uint8_t data[],uint32_t* Calcualted_SPO2);
static uint32_t RetrieveSP02(uint32_t *spo2_data);
static uint32_t WaitSPO2Samples();

static uint8_t g_ready_for_execution = FALSE;
static uint8_t Spo2_Values[SPO2_AVG_VALUES];
int Valid_Spo2 = 95;
static err_code_t result = ERR_SUCCESS;

#if RAW_DATA
extern RAW_VITAL_DATA PSRAM_SECTION rawWireless;
uint32_t algo_cnt = 0;  // Spo2 values over wireless
#endif

static void as7050_callback(err_code_t error, uint8_t *p_data, uint16_t data_num, agc_status_t *p_agc_status,
		uint8_t agc_status_num, void *p_cb_param)
{
	err_code_t result = ERR_SUCCESS;
	uint32_t ready_for_execution = 0;

	// This is an unused argument, mark it to avoid failures on static code analysis.
	M_UNUSED_PARAM(p_cb_param);

	// Handle the error argument.
	if (ERR_SUCCESS != error) {
		result = error;
	}

	// Transfer the ADC data and the AGC status from the Chip Library to the Application Manager.
	if (ERR_SUCCESS == result) {
		// Chip Library and Application Manager use different structures for AGC status information.
		// This conversion code assumes that dual-channel AGC is used.
		result = as7050_appmgr_set_input(p_data, data_num, p_agc_status, agc_status_num, NULL, 0, &ready_for_execution);
	}

	if (ERR_SUCCESS == result) {
		// The Application Manager indicates if it is ready for execution
		// every time new input data is provided. This can be used in a real
		// implementation to efficiently signal an Application Manager
		// process to execute when it is ready for execution.
		// Set the 'ready for execution' flag.
		if (ready_for_execution && !g_ready_for_execution){
			g_ready_for_execution = TRUE;
		}
	} else {
		// handle errors from Application Manager
	}
}


static _Bool As7050_Spo2_Init(void)
{
	err_code_t result = ERR_SUCCESS;
#if RAW_DATA
	algo_cnt = 0;
#endif
	// Initialization
	result = as7050_initialize(as7050_callback, NULL, NULL);
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	result = as7050_appmgr_initialize();
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	// configuration of AFE group
	const as7050_config_afe_t afe_config = {
			.reg_vals.afe_dac0l = 0,
			.reg_vals.afe_dac1l = 0,
			.reg_vals.afe_dach = 0,
			.reg_vals.afe_cfga = 0,
			.reg_vals.afe_cfgb = 0,
			.reg_vals.afe_gsr = 0,
	};
	result =
			as7050_set_reg_group(AS7050_REG_GROUP_ID_AFE, (uint8_t *)afe_config.reg_buffer, sizeof(as7050_config_afe_t));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	// configuration of AMP group
	const as7050_config_amp_t amp_config = {
			.reg_vals.ecg_amp_cfga = 0,
			.reg_vals.ecg_amp_cfgb = 0x23,
			.reg_vals.ecg_amp_cfgc = 0x30,
			.reg_vals.ecg_amp_cfge = 0x01,
	};
	result =
			as7050_set_reg_group(AS7050_REG_GROUP_ID_AMP, (uint8_t *)amp_config.reg_buffer, sizeof(as7050_config_amp_t));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	// configuration of AOC group
	const as7050_config_aoc_t aoc_config = {
			.reg_vals.aoc_ios_ppg1 = 0xFF,
			.reg_vals.aoc_ios_ppg2 = 0xFF,
			.reg_vals.aoc_ios_ppg3 = 0xFF,
			.reg_vals.aoc_ios_ppg4 = 0xFF,
			.reg_vals.aoc_ios_ppg5 = 0xFF,
			.reg_vals.aoc_ios_ppg6 = 0xFF,
			.reg_vals.aoc_ios_ppg7 = 0xFF,
			.reg_vals.aoc_ios_ppg8 = 0xFF,
			.reg_vals.aoc_ppg_thh = 0xB9,
			.reg_vals.aoc_ppg_thl = 0x82,
			.reg_vals.aoc_ppg_cfg = 0,
			.reg_vals.aoc_ios_ecg = 0,
			.reg_vals.aoc_ecg_thh = 0xB9,
			.reg_vals.aoc_ecg_thl = 0x82,
			.reg_vals.aoc_ecg_cfg = 0,
			.reg_vals.aoc_ios_ledoff = 0xFF,
	};
	result =
			as7050_set_reg_group(AS7050_REG_GROUP_ID_AOC, (uint8_t *)aoc_config.reg_buffer, sizeof(as7050_config_aoc_t));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	// configuration of CTRL group
	const as7050_config_ctrl_t ctrl_config = {
			.reg_vals.control = 1,
	};
	result =
			as7050_set_reg_group(AS7050_REG_GROUP_ID_CTRL, (uint8_t *)ctrl_config.reg_buffer, sizeof(as7050_config_ctrl_t));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	// configuration of ECG group
	const as7050_config_ecg_t ecg_config = {
			.reg_vals.ecg_source = 0,
			.reg_vals.ecg_mod_cfga = 0,
	};
	result =
			as7050_set_reg_group(AS7050_REG_GROUP_ID_ECG, (uint8_t *)ecg_config.reg_buffer, sizeof(as7050_config_ecg_t));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	// configuration of FIFO group
	const as7050_config_fifo_t fifo_config = {
			.reg_vals.fifo_ctrl = 0,
			.reg_vals.fifo_threshold = 8,
	};
	result =
			as7050_set_reg_group(AS7050_REG_GROUP_ID_FIFO, (uint8_t *)fifo_config.reg_buffer, sizeof(as7050_config_fifo_t));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	// configuration of GPIO group
	const as7050_config_gpio_t gpio_config = {
			.reg_vals.gpio1_cfg = 0,
			.reg_vals.gpio2_cfg = 0,
			.reg_vals.gpio1_cfgb = 0,
			.reg_vals.gpio2_cfgb = 0,
			.reg_vals.gpio_io = 0,
	};
	result =
			as7050_set_reg_group(AS7050_REG_GROUP_ID_GPIO, (uint8_t *)gpio_config.reg_buffer, sizeof(as7050_config_gpio_t));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	// configuration of IIR group
	const as7050_config_iir_t iir_config = {
			.reg_vals.iir_cfg = 0,
			/* iir_coeff_data_sos will not be initialized here */
	};
	result =
			as7050_set_reg_group(AS7050_REG_GROUP_ID_IIR, (uint8_t *)iir_config.reg_buffer, sizeof(as7050_config_iir_t));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	// configuration of LED group
	const as7050_config_led_t led_config = {
			.reg_vals.lowvds_wait = 0,
			.reg_vals.led1_ictrl = ReadRedLedCurrent(),
			.reg_vals.led2_ictrl = ReadIRLedCurrent(),
			.reg_vals.led3_ictrl = ReadRedLedCurrent(),
			.reg_vals.led4_ictrl = ReadIRLedCurrent(),
			.reg_vals.led5_ictrl = 0x00,
			.reg_vals.led6_ictrl = 0x00,
			.reg_vals.led7_ictrl = 0,
			.reg_vals.led8_ictrl = 0,
			.reg_vals.led_init = 10,
			.reg_vals.led_ppg1 = 0x0A, //Red
			.reg_vals.led_ppg2 = 0x05, //IR
			.reg_vals.led_ppg3 = 0,
			.reg_vals.led_ppg4 = 0,
			.reg_vals.led_ppg5 = 0,
			.reg_vals.led_ppg6 = 0,
			.reg_vals.led_ppg7 = 0,
			.reg_vals.led_ppg8 = 0,
			.reg_vals.led_tia = 0,
			.reg_vals.led_mode = 15,
	};
	result =
			as7050_set_reg_group(AS7050_REG_GROUP_ID_LED, (uint8_t *)led_config.reg_buffer, sizeof(as7050_config_led_t));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}


	// configuration of PD group
	const as7050_config_pd_t pd_config = {
			.reg_vals.pdsel_cfg = 0,
			.reg_vals.pd_ppg1 = 0x30,
			.reg_vals.pd_ppg2 = 0x30,
			.reg_vals.pd_ppg3 = 0x30,
			.reg_vals.pd_ppg4 = 0,
			.reg_vals.pd_ppg5 = 0,
			.reg_vals.pd_ppg6 = 0,
			.reg_vals.pd_ppg7 = 0,
			.reg_vals.pd_ppg8 = 0,
			.reg_vals.pd_tia = 0,
	};
	result = as7050_set_reg_group(AS7050_REG_GROUP_ID_PD, (uint8_t *)pd_config.reg_buffer, sizeof(as7050_config_pd_t));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	const as7050_config_ppg_t ppg_config = {    //4uA ADC FSR   64uA DAC FSR
			.reg_vals.ppg_mod_cfga = 0xC6,
			.reg_vals.ppg_mod_cfgb = 0,
			.reg_vals.ppg_mod_cfgc = 0x07,
			.reg_vals.ppg_mod_cfgd = 0x04,
			.reg_vals.ppg_mod_cfge = 0x0F,
	};
	result =
			as7050_set_reg_group(AS7050_REG_GROUP_ID_PPG, (uint8_t *)ppg_config.reg_buffer, sizeof(as7050_config_ppg_t));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	// configuration of REF group
	const as7050_config_ref_t ref_config = {
			.reg_vals.ref_cfga = 0xAC,
			.reg_vals.ref_cfgb = 0x02,
	};
	result =
			as7050_set_reg_group(AS7050_REG_GROUP_ID_REF, (uint8_t *)ref_config.reg_buffer, sizeof(as7050_config_ref_t));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	// configuration of SEQ group
	const as7050_config_seq_t seq_config = {
			.reg_vals.cgb_cfg = 0x07,
			.reg_vals.seq_sample = 0x64,
			.reg_vals.seq_ppga = 0x02,
			.reg_vals.seq_ppgb = 0x01,
			.reg_vals.seq_mode = 0x80,
	};
	result =
			as7050_set_reg_group(AS7050_REG_GROUP_ID_SEQ, (uint8_t *)seq_config.reg_buffer, sizeof(as7050_config_seq_t));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	// configuration of SINC group
	const as7050_config_sinc_t sinc_config = {
			.reg_vals.sinc_ppg_cfga = 0x84,
			.reg_vals.sinc_ppg_cfgb = 0x03,
			.reg_vals.sinc_ppg_cfgc = 0x00,
			.reg_vals.sinc_ecg_cfga = 0x00,
			.reg_vals.sinc_ecg_cfgb = 0x01,
			.reg_vals.sinc_ecg_cfgc = 0x00,
			.reg_vals.ovs_cfg = 0x00,
	};
	result =
			as7050_set_reg_group(AS7050_REG_GROUP_ID_SINC, (uint8_t *)sinc_config.reg_buffer, sizeof(as7050_config_sinc_t));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	// configuration of STANDBY group
	const as7050_config_standby_t standby_config = {
			.reg_vals.standby_cfga = 0x35,
			.reg_vals.standby_cfgb = 0x01,
	};
	result = as7050_set_reg_group(AS7050_REG_GROUP_ID_STANDBY, (uint8_t *)standby_config.reg_buffer,
			sizeof(as7050_config_standby_t));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	// configuration of TIA group
	const as7050_config_tia_t tia_config = {    //16uA
			.reg_vals.pd_offset_cfg = 4,
			.reg_vals.tia_cfga = 0,
			.reg_vals.tia_cfgb = 0,
			.reg_vals.tia_cfgc = 0,
	};
	result =
			as7050_set_reg_group(AS7050_REG_GROUP_ID_TIA, (uint8_t *)tia_config.reg_buffer, sizeof(as7050_config_tia_t));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	const agc_configuration_t agc_config[2] = {
			{
					.mode = AGC_MODE_DEFAULT,                        // Selects the AGC algorithm mode.
					.led_control_mode = AGC_AMPL_CNTL_MODE_DISABLED, // Selects the LED amplitude control mode.
					.channel = AS7050_CHANNEL_PPG_1,                 // Selects the PPG channel that is controlled.
					.led_current_min = 0,        // Lower bound of the LED current range as a register value.
					.led_current_max = 25,        // Upper bound of the LED current range as a register value.
					.rel_amplitude_min_x100 = 10, // Lower bound of the targeted PPG signal amplitude, relative to the size of
					// the target PGG signal range. The unit of this field is percent. The minimum
					// valid value is 0, the maximum valid value is 100.
					.rel_amplitude_max_x100 =
							90, // Upper bound of the targeted PPG signal amplitude, relative to the size of the target PGG signal
							// range. The unit of this field is percent. This value must not be less than
							// rel_amplitude_min_x100. The maximum valid value is 100.
							.rel_amplitude_motion_x100 =
									100,            // Minimum PPG signal amplitude at which the PPG signal is considered a motion artifact.
									// The threshold is relative to the size of the target PPG signal range. The unit of
									// this field is percent. This value must not be less than rel_amplitude_max_x100.
									// The maximum valid value is 100.
									.num_led_steps = 20, // Number of steps the LED current range is partitioned in. When the AGC algorithm
									// determines that the LED current needs to be increased or decreased and the bounds of
									// the LED current range are not yet reached, the LED current is adjusted by one step.
									// If led_control_mode is set to AGC_AMPL_CNTL_MODE_AUTO, this value must not be zero
									// and must not be greater than the size of the LED current range.
									.threshold_min = 150000, // Lower bound of the target PPG signal range. The unit of this field is ADC
									// counts.
									.threshold_max = 350000, // Upper bound of the target PPG signal range. The unit of this field is ADC
									// counts. This value must be greater than threshold_min. */
			},
			{
					.mode = AGC_MODE_DEFAULT,                        // Selects the AGC algorithm mode.
					.led_control_mode = AGC_AMPL_CNTL_MODE_DISABLED, // Selects the LED amplitude control mode.
					.channel = AS7050_CHANNEL_PPG_2,                 // Selects the PPG channel that is controlled.
					.led_current_min = 0,         // Lower bound of the LED current range as a register value.
					.led_current_max = 40,        // Upper bound of the LED current range as a register value.
					.rel_amplitude_min_x100 = 10, // Lower bound of the targeted PPG signal amplitude, relative to the size of
					// the target PGG signal range. The unit of this field is percent. The minimum
					// valid value is 0, the maximum valid value is 100.
					.rel_amplitude_max_x100 =
							90, // Upper bound of the targeted PPG signal amplitude, relative to the size of the target PGG signal
							// range. The unit of this field is percent. This value must not be less than
							// rel_amplitude_min_x100. The maximum valid value is 100.
							.rel_amplitude_motion_x100 =
									100,            // Minimum PPG signal amplitude at which the PPG signal is considered a motion artifact.
									// The threshold is relative to the size of the target PPG signal range. The unit of
									// this field is percent. This value must not be less than rel_amplitude_max_x100.
									// The maximum valid value is 100.
									.num_led_steps = 20, // Number of steps the LED current range is partitioned in. When the AGC algorithm
									// determines that the LED current needs to be increased or decreased and the bounds of
									// the LED current range are not yet reached, the LED current is adjusted by one step.
									// If led_control_mode is set to AGC_AMPL_CNTL_MODE_AUTO, this value must not be zero
									// and must not be greater than the size of the LED current range.
									.threshold_min = 150000, // Lower bound of the target PPG signal range. The unit of this field is ADC
									// counts.
									.threshold_max = 350000, // Upper bound of the target PPG signal range. The unit of this field is ADC
									// counts. This value must be greater than threshold_min. */
			}};


	result = as7050_set_agc_config(agc_config, 2);

	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	as7050_appmgr_channel_id_t spo2_channels[BIO_SPO2_A0_SIGNAL_NUM] = {
			[BIO_SPO2_A0_SIGNAL_PPG_RED] = AS7050_CHANNEL_PPG_1,
			[BIO_SPO2_A0_SIGNAL_PPG_IR] = AS7050_CHANNEL_PPG_2,
			[BIO_SPO2_A0_SIGNAL_AMBIENT] = AS7050_CHANNEL_PPG_3,
	};
	result = as7050_appmgr_set_signal_routing(AS7050_APPMGR_APP_ID_SPO2_A0, spo2_channels, BIO_SPO2_A0_SIGNAL_NUM);
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	result = as7050_appmgr_enable_apps(AS7050_APPMGR_APP_FLAG_SPO2_A0);
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	// Note: The SpO2 calibration parameters are optical stack dependent.
	bio_spo2_a0_configuration_t spo2_config = {
			.a = 0,
			.b = 6247,
			.c = 9351,
			.dc_comp_red = 495,
			.dc_comp_ir = 495,
	};
	result = as7050_appmgr_configure_app(AS7050_APPMGR_APP_ID_SPO2_A0, &spo2_config, sizeof(spo2_config));
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	as7050_meas_config_t meas_config;
	result = as7050_get_measurement_config(&meas_config);
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	as7050_appmgr_channel_id_t agc_channels[] = {agc_config[0].channel, agc_config[1].channel};
	result = as7050_appmgr_start_processing(meas_config, 1, agc_channels, 2);
	if (ERR_SUCCESS != result) {
		goto ERROR;
	}

	return VITAL_SUCCESS;

	ERROR:
	reportError(result,__SPO2);
	return VITAL_FAIL;

}

void Spo2_Outlier_Elimination(uint8_t data[],uint32_t* Calcualted_SPO2)
{
	float q1 =0, q2 =0, iqr =0,ul=0,ll=0,Mean_SPO2=0;
	int validSpo2Count =0;


	//Arraging elements in ascending order
	for (int j = 0; j < SPO2_AVG_VALUES - 1; j++)
	{
		if (data[j] > data[j + 1])
		{

			int temp = data[j];
			data[j] = data[j + 1];
			data[j + 1] = temp;
			j = -1;
		}
	}

	q1 = data [((SPO2_AVG_VALUES+1)*1/4)];
	q2 = data [(SPO2_AVG_VALUES * 3/4)];

	iqr= q2 - q1;

	ll = q1-1.5*(iqr);
	ul = q2+1.5*(iqr);

	for(int j=0;j<SPO2_AVG_VALUES;j++)
	{
		if(data[j]>=ll && data[j]<=ul)
		{
			Mean_SPO2 += data[j];
			validSpo2Count++;
		}
	}

	Mean_SPO2 =Mean_SPO2/validSpo2Count;
	*Calcualted_SPO2 = Mean_SPO2 + 0.5; // RoundOff

	// check for data  validity
	if(*Calcualted_SPO2 <= BASE_VALUE)
	{
		*Calcualted_SPO2 = Valid_Spo2;
	}
	else
	{
		Valid_Spo2 = *Calcualted_SPO2;
	}
}

// _Bool As7050_Spo2_Data_Collection(SENSOR_DATA_STRUCT* SPO2_REQ,sensor_callback SPO2_Callback)
// {
// #if RAW_DATA
// 	memset(rawWireless.algoOutput,0,sizeof(rawWireless.algoOutput));
// #endif
// #if SPO2_DEBUG_PRINT
// 	uint32_t   debug_counter                  =0;
// #endif
// 	uint32_t   Skip_Samples                   =0;
// 	uint32_t   Spo2_Counter                   =0;
// 	uint32_t   invalid_spo2                   =0;
// 	uint32_t   counter                        =0;
// 	uint32_t   indicate                       =1;
// 	uint32_t   ret                            =0;
// 	uint32_t   spo2_data                      =0;
// 	uint32_t   spo2_average_data              =0;



// 	//	 skipping samples to stabilise the SPO2 values
// 	while(Skip_Samples < SKIP_VALUES)
// 	{
// 		Skip_Samples++;

// 		ret = WaitSPO2Samples();
// 		if(ret == 0)
// 		{
// #if SPO2_DEBUG_PRINT
// 			SEGGER_RTT_printf(0,"WaitForDataSamples failed\n");
// #endif
// 			goto ERROR;
// 		}
// 		else if (ret == 1)
// 		{
// 			RetrieveSP02(&spo2_data);
// 		}
// 	}


// #if SPO2_DEBUG_PRINT
// 	SEGGER_RTT_printf(0,"Skip done\n");
// #endif
// 	while(1){
// #if SPO2_DEBUG_PRINT
// 		SEGGER_RTT_printf(0,"debug_counter = %d\n",++debug_counter);
// #endif

// 		ret = WaitSPO2Samples();
// 		if(ret==0)
// 		{
// #if SPO2_DEBUG_PRINT
// 			SEGGER_RTT_printf(0,"WaitForDataSamples failed\n");
// #endif
// 			goto ERROR;
// 		}
// 		else if (ret ==1)
// 		{
// #if SPO2_DEBUG_PRINT
// 			SEGGER_RTT_printf(0,"WaitForDataSamples  executed with success\n");
// #endif
// 			if(!RetrieveSP02(&spo2_data))
// 			{
// 				// (3) Signal invalid data
// 				invalid_spo2++;
// #if SPO2_DEBUG_PRINT
// 				SEGGER_RTT_printf(0,"RetrieveSP02 failed invalid count  %d\n",invalid_spo2);
// #endif
// 				if(invalid_spo2 == INALID_DATA_COUNT)
// 				{
// #if SPO2_DEBUG_PRINT
// 					SEGGER_RTT_printf(0,"RetrieveSP02 failed invalid count maxed\n");
// #endif
// 					result = ERR_SENSOR_INVALID_DATA;
// 					goto ERROR;
// 				}
// 				else
// 				{
// #if SPO2_DEBUG_PRINT
// 					SEGGER_RTT_printf(0,"RetrieveSP02 failed retrying\n");
// #endif
// 					continue;
// 				}
// 			}
// 			else
// 			{
// #if SPO2_DEBUG_PRINT
// 				SEGGER_RTT_printf(0,"RetrieveSP02  executed with success [%d]\n",spo2_data);
// #endif
// 				Spo2_Values[Spo2_Counter++] = spo2_data;
// #if RAW_DATA
// 				rawWireless.algoOutput[algo_cnt++] = spo2_data;
// #endif

// 				if((Spo2_Counter == SPO2_AVG_VALUES))
// 				{
// 					Spo2_Counter = 0;
// 					Spo2_Outlier_Elimination(Spo2_Values,&spo2_average_data);
// 					invalid_spo2 = 0;	//reset the invalid counter once we have a valid data
// 					updateSpo2Rate(spo2_average_data);
// #if SPO2_DEBUG_PRINT
// 					SEGGER_RTT_printf(0,"SP02 averaging  [%d]\n",spo2_average_data);
// #endif
// 					if(indicate)
// 					{
// #if SPO2_DEBUG_PRINT
// 						SEGGER_RTT_printf(0,"Data Collection Triggered\n");
// #endif
// 						indicate = 0;
// 						//DATA COLLECTION STATE of SPO2 state machine completed
// 						SPO2_Callback(DC_STATE);
// 					}
// 				}
// 					counter++;
// 					if(counter >5) break;
// 			}

// 		}
// 	}

// 	SPO2_REQ->vitals.SPO2.spo2value = spo2_average_data;
// 	return VITAL_SUCCESS;

// 	ERROR:
// 	reportError(result,__SPO2);
// 	return VITAL_FAIL;
// }

// _Bool As7050_Spo2_Data_Collection_C(SENSOR_DATA_STRUCT* SPO2_REQ,sensor_callback SPO2_Callback)
// {
// #if RAW_DATA
// 	memset(rawWireless.algoOutput,0,sizeof(rawWireless.algoOutput));
// #endif
// #if SPO2_DEBUG_PRINT
// 	uint32_t   debug_counter                  =0;
// #endif
// 	uint32_t   Skip_Samples                   =0;
// 	uint32_t   Spo2_Counter                   =0;
// 	uint32_t   invalid_spo2                   =0;
// 	uint32_t   indicate                       =1;
// 	uint32_t   ret                            =0;
// 	uint32_t   spo2_data                      =0;
// 	uint32_t   spo2_average_data              =0;



// 	//	 skipping samples to stabilise the SPO2 values
// 	while(Skip_Samples < SKIP_VALUES)
// 	{
// 		Skip_Samples++;

// 		ret = WaitSPO2Samples();
// 		if(ret == 0)
// 		{
// #if SPO2_DEBUG_PRINT
// 			SEGGER_RTT_printf(0,"WaitForDataSamples failed\n");
// #endif
// 			goto ERROR;
// 		}
// 		else if (ret == 1)
// 		{
// 			RetrieveSP02(&spo2_data);
// 		}
// 	}


// #if SPO2_DEBUG_PRINT
// 	SEGGER_RTT_printf(0,"Skip done\n");
// #endif
// 	while(1){
// #if SPO2_DEBUG_PRINT
// 		SEGGER_RTT_printf(0,"debug_counter = %d\n",++debug_counter);
// #endif

// 		ret = WaitSPO2Samples();
// 		if(ret==0)
// 		{
// #if SPO2_DEBUG_PRINT
// 			SEGGER_RTT_printf(0,"WaitForDataSamples failed\n");
// #endif
// 			goto ERROR;
// 		}
// 		else if (ret ==1)
// 		{
// #if SPO2_DEBUG_PRINT
// 			SEGGER_RTT_printf(0,"WaitForDataSamples  executed with success\n");
// #endif
// 			if(!RetrieveSP02(&spo2_data))
// 			{
// 				// (3) Signal invalid data
// 				invalid_spo2++;
// #if SPO2_DEBUG_PRINT
// 				SEGGER_RTT_printf(0,"RetrieveSP02 failed invalid count  %d\n",invalid_spo2);
// #endif
// 				if(invalid_spo2 == INALID_DATA_COUNT)
// 				{
// #if SPO2_DEBUG_PRINT
// 					SEGGER_RTT_printf(0,"RetrieveSP02 failed invalid count maxed\n");
// #endif
// 					result = ERR_SENSOR_INVALID_DATA;
// 					goto ERROR;
// 				}
// 				else
// 				{
// #if SPO2_DEBUG_PRINT
// 					SEGGER_RTT_printf(0,"RetrieveSP02 failed retrying\n");
// #endif
// 					continue;
// 				}
// 			}
// 			else
// 			{
// #if SPO2_DEBUG_PRINT
// 				SEGGER_RTT_printf(0,"RetrieveSP02  executed with success [%d]\n",spo2_data);
// #endif
// 				Spo2_Values[Spo2_Counter++] = spo2_data;
// #if RAW_DATA
// 				rawWireless.algoOutput[algo_cnt++] = spo2_data;
// #endif

// 				if((Spo2_Counter == SPO2_AVG_VALUES))
// 				{
// 					Spo2_Counter = 0;
// 					Spo2_Outlier_Elimination(Spo2_Values,&spo2_average_data);
// 					invalid_spo2 = 0;	//reset the invalid counter once we have a valid data
// 					updateSpo2Rate(spo2_average_data);
// #if SPO2_DEBUG_PRINT
// 					SEGGER_RTT_printf(0,"SP02 averaging  [%d]\n",spo2_average_data);
// #endif
// 					if(indicate)
// 					{
// #if SPO2_DEBUG_PRINT
// 						SEGGER_RTT_printf(0,"Data Collection Triggered\n");
// #endif
// 						indicate = 0;
// 						//DATA COLLECTION STATE of SPO2 state machine completed
// 						SPO2_Callback(DC_STATE);
// 					}
// 				}
// 			}
// 		}
// 	}

// 	SPO2_REQ->vitals.SPO2.spo2value = spo2_average_data;
// 	return VITAL_SUCCESS;

// 	ERROR:
// 	reportError(result,__SPO2);
// 	return VITAL_FAIL;
// }

_Bool As7050_Spo2_Start_Measurement(void)
{

	err_code_t result = ERR_SUCCESS;

	result = as7050_start_measurement();

	if (ERR_SUCCESS != result) {

		goto ERROR;
	}

	return VITAL_SUCCESS;

	ERROR:
	reportError(result,__SPO2);
	return VITAL_FAIL;

}

_Bool As7050_Spo2_Stop_Measurement(void)
{
	Accelerometer_Disable();
	err_code_t result = ERR_SUCCESS;
	//Stop measurement on the Chip Library
	result = as7050_stop_measurement();
	if (ERR_SUCCESS != result) {
#if SPO2_DEBUG_PRINT
		SEGGER_RTT_printf(0,"Error on as7050_stop_measurement: %d\n", result);
#endif
		goto ERROR;
	}

	return VITAL_SUCCESS;

	ERROR:
	reportError(result,__SPO2);
	return VITAL_FAIL;
}

_Bool As7050_Spo2_DeInit(void)
{
	err_code_t result = ERR_SUCCESS;
	result = as7050_appmgr_shutdown();
	if (ERR_SUCCESS != result) {
#if SPO2_DEBUG_PRINT
		SEGGER_RTT_printf(0,"Error on as7050_app_shutdown: %d\n", result);
#endif
		goto ERROR;
	}

	result = as7050_shutdown();
	if (ERR_SUCCESS != result) {
#if SPO2_DEBUG_PRINT
		SEGGER_RTT_printf(0,"Error on as7050_shutdown: %d\n", result);
#endif
		goto ERROR;
	}

	return VITAL_SUCCESS;

	ERROR:
	reportError(result,__SPO2);
	return VITAL_FAIL;
}

/******************************************************************************
 *                               Main                                         *
 ******************************************************************************/

_Bool As7050_Spo2(SENSOR_DATA_STRUCT* SPO2_REQ,sensor_callback SPO2_Callback,measurement_mode mode)
{
	_Bool Result = VITAL_FAIL; // to say whether the measurement is success or not

#if 0
	Result = Calibrate_7050_PPG();
	if(Result != VITAL_SUCCESS)
	{
		goto ERROR;
	}
#endif

	Result = As7050_Spo2_Init();
	if(Result != VITAL_SUCCESS)
	{
		goto ERROR;
	}

#if ACCELROMETER
	Init_LSM_ACC();
#endif

	Result = As7050_Spo2_Start_Measurement();
	if(Result != VITAL_SUCCESS)
	{
		goto ERROR;
	}
	//START STATE of spo2 state machine completed
	SPO2_Callback(STT_STATE);

	if(mode == VITAL_MODE_SINGLE)
		Result = As7050_Spo2_Data_Collection(SPO2_REQ,SPO2_Callback);
	else
		Result = As7050_Spo2_Data_Collection_C(SPO2_REQ,SPO2_Callback);

#if RAW_DATA
	Extract_Ppg_Data(SPO2_REQ);
	UPDATE_DATABASE_WITH_RAWDATA(SPO2_REQ);
#endif
	if(Result != VITAL_SUCCESS)
	{
		goto ERROR;
	}

	//Stop measurement on the Chip Library
	Result = As7050_Spo2_Stop_Measurement();
	if(Result != VITAL_SUCCESS)
	{
		goto ERROR;
	}

	//Data processing state of SPO2 state machine completed
	SPO2_Callback(DP_STATE);

	Result = As7050_Spo2_DeInit();
	if(Result != VITAL_SUCCESS)
	{
		goto ERROR;
	}

	return VITAL_SUCCESS;

	ERROR:
	Result = As7050_Spo2_DeInit();
	return VITAL_FAIL;
}


static uint32_t WaitSPO2Samples()
{
	uint32_t app_data_available = FALSE;

	do
	{
		uint32_t pulNotificationValue = 0;
		BaseType_t xReturn;
#if SPO2_DEBUG_PRINT
		SEGGER_RTT_printf(0,"In WaitForDataSamples waiting for interrupt -TaskNotify\n");
#endif
// 		xReturn = xTaskNotifyWait(0x00,0xffffffff,&pulNotificationValue,SPO2_Reading_Timeout); //TODO UI_Live change the signalling mechanism
// 		if(xReturn == pdFALSE)
// 		{
// #if SPO2_DEBUG_PRINT
// 			SEGGER_RTT_printf(0,"In WaitForDataSamples waiting for interrupt -TaskNotify timedout \n");
// #endif
// 			result = ERR_INTERRUPT_TIMEOUT;
// 			return 0;
// 		}

#if SPO2_DEBUG_PRINT
		SEGGER_RTT_printf(0,"In WaitForDataSamples interrupt_callback called \n");
#endif
		result = SPO2_interrupt_handler();
#if SPO2_DEBUG_PRINT
		SEGGER_RTT_printf(0,"In  interrupt_callback return %d\n",result);
#endif
		if (ERR_SUCCESS != result)
		{
#if SPO2_DEBUG_PRINT
			SEGGER_RTT_printf(0,"In WaitForDataSamples interrupt_callback failed \n");
#endif
			return 0;
		}

		if(g_ready_for_execution)
		{
#if SPO2_DEBUG_PRINT
			SEGGER_RTT_printf(0,"In WaitForDataSamples as7050_appmgr_execute called \n");
#endif
			result = as7050_appmgr_execute(&app_data_available);
			g_ready_for_execution = FALSE;
			if (ERR_SUCCESS != result)
			{
#if SPO2_DEBUG_PRINT
				SEGGER_RTT_printf(0,"In WaitForDataSamples as7050_appmgr_execute failed \n");
#endif
				return 0;
			}

		}


	}while(!app_data_available);
#if SPO2_DEBUG_PRINT
	SEGGER_RTT_printf(0,"In WaitForDataSamples returned %d \n",app_data_available);
#endif
	return 1;
}




static uint32_t RetrieveSP02(uint32_t *spo2_data)
{
	bio_spo2_a0_output_t spo2_output;
	uint16_t output_size = sizeof(bio_spo2_a0_output_t);
#if SPO2_DEBUG_PRINT
	SEGGER_RTT_printf(0," Inside RetrieveSP02 calling as7050_appmgr_get_output \n");
#endif
	result = as7050_appmgr_get_output(AS7050_APPMGR_APP_ID_SPO2_A0, &spo2_output, &output_size);
	if (ERR_SUCCESS != result)
	{
#if SPO2_DEBUG_PRINT
		SEGGER_RTT_printf(0,"Inside RetrieveSP02 calling as7050_appmgr_get_output failed \n");
#endif
		return 0;
	}

	// Check if the SPO2 result is valid
	if (!spo2_output.status)
	{
#if SPO2_DEBUG_PRINT
		SEGGER_RTT_printf(0,"SPO2: %d   HR: %d Rvalue = %d   PI = %d   Quality = %d   ac_comp_red = %d  dc_comp_red = %d  ac_comp_ir = %d   dc_comp_ir = %d\n" , spo2_output.spo2 / 100,spo2_output.heart_rate/10,spo2_output.average_r,spo2_output.pi,spo2_output.quality,spo2_output.ac_comp_red,spo2_output.dc_comp_red,spo2_output.ac_comp_ir,spo2_output.dc_comp_ir);
#endif

		if ((spo2_output.quality >=30 )&& (spo2_output.pi >0))
		{
			*spo2_data = spo2_output.spo2 / 100;
			*spo2_data = ((*spo2_data) > 100)?100:*spo2_data;
			return 1;
		}
#if SPO2_DEBUG_PRINT

		SEGGER_RTT_printf(0,"RetrieveSP02 returning success \n");
#endif

		return 0;
	}
	else
	{
#if SPO2_DEBUG_PRINT
		SEGGER_RTT_printf(0,"RetrieveSP02 returning failure \n");
#endif
		return 0;
	}

}

