# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.21

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: Trusted Firmware M
# Configurations: MinSizeRel
# =============================================================================
# =============================================================================

#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for compiling C files.

rule C_COMPILER__tfm_fih_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__tfm_fih_MinSizeRel
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__tfm_s_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C executable.

rule C_EXECUTABLE_LINKER__tfm_s_MinSizeRel
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $FLAGS -mcpu=cortex-m33+nodsp+nofp $LINK_FLAGS $in -o $TARGET_FILE $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking C executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__tfm_s_veneers_MinSizeRel
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__tfm_spm_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__tfm_spm_MinSizeRel
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__tfm_sprt_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__tfm_sprt_MinSizeRel
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__tfm_psa_rot_partition_crypto_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__tfm_psa_rot_partition_crypto_MinSizeRel
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__crypto_service_mbedcrypto_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__crypto_service_mbedcrypto_MinSizeRel
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS @$RSP_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__mbedcrypto_base_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__mbedcrypto_base_MinSizeRel
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__tfm_psa_rot_partition_platform_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__tfm_psa_rot_partition_platform_MinSizeRel
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__platform_s_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__platform_s_MinSizeRel
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__platform_crypto_keys_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__platform_crypto_keys_MinSizeRel
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__platform_ns_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER__platform_ns_MinSizeRel
  command = cmd.exe /C "$PRE_LINK && C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe -E rm -f $TARGET_FILE && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__tfm_s_scatter_MinSizeRel
  depfile = $DEP_FILE
  deps = gcc
  command = C:\ncs\toolchains\cf2149caf2\opt\zephyr-sdk\arm-zephyr-eabi\bin\arm-zephyr-eabi-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = C:\ncs\toolchains\cf2149caf2\opt\bin\cmake.exe --regenerate-during-build -SC:\ncs\v2.5.99-dev1\modules\tee\tf-m\trusted-firmware-m -BC:\Users\<USER>\Music\nordic-nRF-as7058\build_1\tfm
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = C:\ncs\toolchains\cf2149caf2\opt\bin\ninja.exe $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = C:\ncs\toolchains\cf2149caf2\opt\bin\ninja.exe -t targets
  description = All primary targets available:

