# Generated file used for preloading a child image
set(CMAKE_BUILD_TYPE "" CACHE INTERNAL "NCS child image controlled")
set(CMAKE_VERBOSE_MAKEFILE "FALSE" CACHE INTERNAL "NCS child image controlled")
set(BOARD_DIR "C:/ncs/v2.5.99-dev1/zephyr/boards/arm/nrf5340dk_nrf5340" CACHE INTERNAL "NCS child image controlled")
set(ZEPHYR_TOOLCHAIN_VARIANT "zephyr" CACHE INTERNAL "NCS child image controlled")
set(NCS_TOOLCHAIN_VERSION "NONE" CACHE INTERNAL "NCS child image controlled")
set(PM_DOMAINS "CPUNET" CACHE INTERNAL "NCS child image controlled")
set(CPUNET_PM_DOMAIN_DYNAMIC_PARTITION "hci_rpmsg" CACHE INTERNAL "NCS child image controlled")
set(WEST_PYTHON "C:/ncs/toolchains/cf2149caf2/opt/bin/python.exe" CACHE INTERNAL "NCS child image controlled")
set(BOARD "nrf5340dk_nrf5340_cpunet" CACHE INTERNAL "NCS child image controlled")
set(DOMAIN "CPUNET" CACHE INTERNAL "NCS child image controlled")
set(DTC_OVERLAY_FILE "C:/Users/<USER>/Music/nordic-nRF-as7058/child_image/hci_rpmsg/boards/nrf5340dk_nrf5340_cpunet.overlay" CACHE INTERNAL "NCS child image controlled")
set(OVERLAY_CONFIG "C:/ncs/v2.5.99-dev1/nrf/subsys/partition_manager/partition_manager_enabled.conf;C:/Users/<USER>/Music/nordic-nRF-as7058/child_image/hci_rpmsg.conf" CACHE INTERNAL "NCS child image controlled")
