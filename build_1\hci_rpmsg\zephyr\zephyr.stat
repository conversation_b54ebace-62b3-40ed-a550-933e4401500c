ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x100424d
  Start of program headers:          52 (bytes into file)
  Start of section headers:          2882984 (bytes into file)
  Flags:                             0x5000200, Version5 EABI, soft-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         6
  Size of section headers:           40 (bytes)
  Number of section headers:         33
  Section header string table index: 32

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        01000000 000100 0000b8 00 WAX  0   0  4
  [ 2] text              PROGBITS        010000b8 0001b8 01d6b4 00  AX  0   0  8
  [ 3] .ARM.exidx        ARM_EXIDX       0101d76c 01d86c 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        0101d774 01d874 000078 00   A  0   0  4
  [ 5] device_area       PROGBITS        0101d7ec 01d8ec 00008c 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        0101d878 01d978 0000f0 00  WA  0   0  4
  [ 7] log_const_area    PROGBITS        0101d968 01da68 0000b8 00   A  0   0  4
  [ 8] tbss              NOBITS          0101da20 01db20 000004 00 WAT  0   0  4
  [ 9] rodata            PROGBITS        0101da20 01db20 001af4 00   A  0   0 16
  [10] .ramfunc          PROGBITS        21000000 020064 000000 00   W  0   0  1
  [11] datas             PROGBITS        21000000 01f618 000959 00  WA  0   0  8
  [12] device_states     PROGBITS        21000959 01ff71 00000e 00  WA  0   0  1
  [13] k_heap_area       PROGBITS        21000968 01ff80 000014 00  WA  0   0  4
  [14] k_sem_area        PROGBITS        2100097c 01ff94 000018 00  WA  0   0  4
  [15] k_queue_area      PROGBITS        21000994 01ffac 000030 00  WA  0   0  4
  [16] net_buf_pool_area PROGBITS        210009c4 01ffdc 000084 00  WA  0   0  4
  [17] bss               NOBITS          21000a48 020068 008d26 00  WA  0   0  8
  [18] noinit            NOBITS          21009770 020068 0040b8 00  WA  0   0  8
  [19] .comment          PROGBITS        00000000 020064 000020 01  MS  0   0  1
  [20] .debug_aranges    PROGBITS        00000000 020088 004108 00      0   0  8
  [21] .debug_info       PROGBITS        00000000 024190 1437c0 00      0   0  1
  [22] .debug_abbrev     PROGBITS        00000000 167950 021443 00      0   0  1
  [23] .debug_line       PROGBITS        00000000 188d93 05f3aa 00      0   0  1
  [24] .debug_frame      PROGBITS        00000000 1e8140 00b21c 00      0   0  4
  [25] .debug_str        PROGBITS        00000000 1f335c 01e460 01  MS  0   0  1
  [26] .debug_loc        PROGBITS        00000000 2117bc 0799fe 00      0   0  1
  [27] .debug_ranges     PROGBITS        00000000 28b1c0 013680 00      0   0  8
  [28] .ARM.attributes   ARM_ATTRIBUTES  00000000 29e840 000036 00      0   0  1
  [29] .last_section     PROGBITS        0101ff5c 020060 000004 00   A  0   0  1
  [30] .symtab           SYMTAB          00000000 29e878 014710 10     31 3456  4
  [31] .strtab           STRTAB          00000000 2b2f88 00ccc4 00      0   0  1
  [32] .shstrtab         STRTAB          00000000 2bfc4c 00015a 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x01d86c 0x0101d76c 0x0101d76c 0x00008 0x00008 R   0x4
  LOAD           0x000100 0x01000000 0x01000000 0x1f514 0x1f514 RWE 0x10
  LOAD           0x01f618 0x21000000 0x0101f514 0x00a48 0x00a48 RW  0x8
  LOAD           0x020060 0x0101ff5c 0x0101ff5c 0x00004 0x00004 R   0x1
  LOAD           0x000000 0x21000a48 0x21000a48 0x00000 0x0cde0 RW  0x8
  TLS            0x01db20 0x0101da20 0x0101da20 0x00000 0x00004 R   0x4

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table log_const_area rodata 
   02     datas device_states k_heap_area k_sem_area k_queue_area net_buf_pool_area 
   03     .last_section 
   04     bss noinit 
   05     tbss 
