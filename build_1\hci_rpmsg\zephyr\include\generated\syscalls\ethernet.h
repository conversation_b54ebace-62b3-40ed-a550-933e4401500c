/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_ETHERNET_H
#define Z_INCLUDE_SYSCALLS_ETHERNET_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern const struct device * z_impl_net_eth_get_ptp_clock_by_index(int index);

__pinned_func
static inline const struct device * net_eth_get_ptp_clock_by_index(int index)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; int val; } parm0 = { .val = index };
		return (const struct device *) arch_syscall_invoke1(parm0.x, K_SYSCALL_NET_ETH_GET_PTP_CLOCK_BY_INDEX);
	}
#endif
	compiler_barrier();
	return z_impl_net_eth_get_ptp_clock_by_index(index);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define net_eth_get_ptp_clock_by_index(index) ({ 	const struct device * syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_NET_ETH_GET_PTP_CLOCK_BY_INDEX, net_eth_get_ptp_clock_by_index, index); 	syscall__retval = net_eth_get_ptp_clock_by_index(index); 	sys_port_trace_syscall_exit(K_SYSCALL_NET_ETH_GET_PTP_CLOCK_BY_INDEX, net_eth_get_ptp_clock_by_index, index, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
